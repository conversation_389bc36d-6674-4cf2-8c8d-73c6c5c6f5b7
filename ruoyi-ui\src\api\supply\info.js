import request from '@/utils/request'

// 查询相关方人员列表
export function listInfo(query) {
  return request({
    url: '/web/supply/userinfo/get/list',
    method: 'get',
    params: query
  })
}

// 查询相关方人员详细
export function getInfo(id) {
  return request({
    url: '/web/supply/userinfo/get/' + id,
    method: 'get'
  })
}

// 新增相关方人员
export function addInfo(data) {
  return request({
    url: '/web/supply/userinfo/add',
    method: 'post',
    data: data
  })
}

// 修改相关方人员
export function updateInfo(data) {
  return request({
    url: '/web/supply/userinfo/update',
    method: 'put',
    data: data
  })
}

// 删除相关方人员
export function delInfo(id) {
  return request({
    url: '/web/supply/userinfo/delete/' + id,
    method: 'delete'
  })
}

// 导出相关方人员
export function exportInfo(query) {
  return request({
    url: '/web/supply/userinfo/export',
    method: 'post',
    data: query
  })
}

// 导入相关方人员
export function importInfo(formData) {
  return request({
    url: '/web/supply/userinfo/import',
    method: 'post',
    data: formData,
    headers: { 'Content-Type': 'multipart/form-data' }
  })
}

// 下载导入模板
export function downloadTemplate() {
  return request({
    url: '/web/supply/userinfo/importTemplate',
    method: 'post'
  })
}

