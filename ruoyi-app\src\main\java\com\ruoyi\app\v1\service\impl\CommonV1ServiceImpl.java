package com.ruoyi.app.v1.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.mchange.v2.lang.ObjectUtils;
import com.ruoyi.app.domain.WXUser;
import com.ruoyi.app.jw.domain.LeaderInfo;
import com.ruoyi.app.jw.mapper.LeaderInfoMapper;
import com.ruoyi.app.jw.service.ILeaderInfoService;
import com.ruoyi.app.qs.domain.QsInfo;
import com.ruoyi.app.qs.service.impl.QsInfoServiceImpl;
import com.ruoyi.app.v1.domain.OrganizationUserInfo;
import com.ruoyi.app.v1.mapper.AppCommonV1Mapper;
import com.ruoyi.app.v1.mapper.TTeamCheckUserMapper;
import com.ruoyi.app.v1.service.ICommonV1Service;
import com.ruoyi.app.v1.service.IOrganizationUserInfoService;
import com.ruoyi.app.vehicleAccess.enums.CarRole;
import com.ruoyi.app.vehicleAccess.mapper.XctgVehicleAuditUserRoleMapper;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.constant.HttpStatus;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginBody;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.enums.UserStatus;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.MessageUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.http.HttpUtils;
import com.ruoyi.common.utils.sign.AesUtils;
import com.ruoyi.common.utils.sign.Base64;
import com.ruoyi.common.utils.spring.SpringUtils;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.framework.manager.AsyncManager;
import com.ruoyi.framework.manager.factory.AsyncFactory;
import com.ruoyi.framework.web.service.QYTemplateMessageService;
import com.ruoyi.framework.web.service.TemplateMessageService;
import com.ruoyi.system.mapper.SysRoleMapper;
import com.ruoyi.system.service.impl.SysConfigServiceImpl;
import com.ruoyi.system.service.impl.SysUserServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.net.ConnectException;
import java.net.SocketTimeoutException;
import java.net.URL;
import java.net.URLConnection;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 通用功能业务层处理
 *
 * <AUTHOR>
 * @date 2021-06-16
 */
@Service
public class CommonV1ServiceImpl implements ICommonV1Service {
    private static final Logger log = LoggerFactory.getLogger(CommonV1ServiceImpl.class);

    /**
     * 配置文件中appid
     */
    @Value("${wx.xctg.appid}")
    private String appid;

    /**
     * 配置文件中secret
     */
    @Value("${wx.xctg.secret}")
    private String secret;

    /**
     * 微信获取openId的url
     */
    @Value("${wx.jsCodeUrl}")
    private String jsCodeUrl;

    /**
     * 直接获取用户手机号的url
     */
    @Value("${wx.userPhoneNumberUrl}")
    private String userPhoneNumberUrl;

    /**
     * 获取access_token url
     */
    @Value("${wx.credentialUrl}")
    private String credentialUrl;

    @Value("${wx.getUnlimitedQRCode}")
    private String unlimitedQRCodeUrl;

    @Autowired
    private AppCommonV1Mapper appCommonMapper;


    @Autowired
    private RedisCache redisCache;

    @Autowired
    private SysUserServiceImpl sysUserService;

    @Autowired
    private QYTemplateMessageService qywxService;


    @Autowired
    private SysConfigServiceImpl sysConfigService;

    @Autowired
    private TemplateMessageService messageService;

    @Autowired
    private XctgVehicleAuditUserRoleMapper xctgVehicleAuditUserRoleMapper;

    @Autowired
    private IOrganizationUserInfoService organizationUserInfoService;

    @Autowired
    private TTeamCheckUserMapper tTeamCheckUserMapper;

    @Autowired
    private LeaderInfoMapper leaderInfoMapper;

    /**
     * 新生成token取代原先的token
     *
     * @param oldToken 原先token值
     * @param code     小程序code转化openid
     * @return
     */
    public AjaxResult getToken(String oldToken, String code) {
        try {
            if (code == null || StringUtils.isEmpty(code)) {
                return AjaxResult.error("code为空");
            }
            // 使用code换取openId,unionId
            String rspStr = HttpUtils.sendGet(jsCodeUrl, "appid=" + appid + "&secret=" + secret + "&js_code=" + code + "&grant_type=authorization_code", Constants.UTF8);
            JSONObject obj = JSONObject.parseObject(rspStr);
            if (obj.get("errcode") == null) {
                String openId = obj.getString("openid");
                String unionId = obj.getString("unionid");
                // 检测openId是否存在，不存在则新建
                WXUser user = appCommonMapper.selectWXUserByOpenId(openId);
                // 姓名昵称
                String nickName = "";
                // 工号
                String workNo = "";
                String isAgree = "0";
                if (user == null) {
                    // 新微信用户
                    WXUser newUser = new WXUser();
                    newUser.setOpenId(openId);
                    newUser.setCreateTime(DateUtils.getNowDate());
                    newUser.setUpdateTime(DateUtils.getNowDate());
                    newUser.setUnionId(unionId);
                    appCommonMapper.insertWXUser(newUser);
                    AsyncManager.me().execute(AsyncFactory.recordLogininfor(openId, Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.app.regist")));
                } else {
                    user.setUpdateTime(DateUtils.getNowDate());
                    //更新unionid
                    user.setUnionId(unionId);
                    appCommonMapper.updateTimeWXUser(user);
                    workNo = user.getWorkNo();
                    isAgree = user.getIsAgree();
                    nickName = appCommonMapper.selectNickNameByWorkNo(workNo);
                    if (user.getStatus().equals("1")) {
                        //生成token用于外部应用操作
                        String UUIDExternal = IdUtils.simpleUUID();
                        redisCache.setCacheObject("UUID:" + UUIDExternal, openId, 2, TimeUnit.HOURS);
                        JSONObject dataJson = new JSONObject();
                        dataJson.put("token", UUIDExternal);
                        dataJson.put("isAgree", isAgree);
                        // 删除之前的token记录
                        redisCache.deleteObject("UUID:" + oldToken);
                        AsyncManager.me().execute(AsyncFactory.recordLogininfor((workNo == null ? openId : (workNo + "(" + nickName + ")")), Constants.LOGIN_FAIL, MessageUtils.message("user.login.app.forbid")));
                        return AjaxResult.error(HttpStatus.NOT_ALLOW_LOGIN, "禁止访问该小程序", dataJson);
                    } else {
                        // 暂未实名登录时，登录日志显示openId
                        AsyncManager.me().execute(AsyncFactory.recordLogininfor((workNo == null ? openId : (workNo + "(" + nickName + ")")), Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.app.success")));
                    }
                }
                // 生成UUID 用于后续的业务操作 token有效期2小时
                String UUID = IdUtils.simpleUUID();
                redisCache.setCacheObject("UUID:" + UUID, openId, 2, TimeUnit.HOURS);
                JSONObject dataJson = new JSONObject();
                dataJson.put("token", UUID);
                dataJson.put("nickName", nickName);
                dataJson.put("workNo", workNo);
                dataJson.put("isAgree", isAgree);
                Map<String, Object> msgConfig = appCommonMapper.selectConfigByOpenId(openId);
                dataJson.put("msgConfig", msgConfig);

                String isSerious = sysConfigService.selectConfigByKey("yq.index.isSerious");
                dataJson.put("isSerious", isSerious);
                // 删除之前的token记录
                redisCache.deleteObject("UUID:" + oldToken);
                // 获取企业微信下的userId
                if (user.getUserId() == null) {
                    String usrId = qywxService.getUserId(openId);
                    if (StringUtils.isNotNullAndNotEmpty(usrId)) {
                        // 更新企业微信下获取到的userId
                        appCommonMapper.updateUserIdByOpenId(openId, usrId);
                    }
                }
                return AjaxResult.success(dataJson);
            } else {
                return AjaxResult.error(obj.getString("errmsg"));
            }
        } catch (Exception e) {
            return AjaxResult.error("发生未知错误");
        }

    }

    public AjaxResult loginByXC(String token, LoginBody loginBody) {
        try {
            // 获取真实的openId
            String openId = redisCache.getCacheObject("UUID:" + token);
            if (token == null || openId == null)
                return AjaxResult.error(HttpStatus.NOT_IDENTIFY, "身份无法识别，请在设置中重新进入小程序");
            // 登录的工号
            String workNo = loginBody.getUsername();
            // 密码
            String password = loginBody.getPassword();
            // 移动应用系统用户校验
            SysUser sysUser = sysUserService.selectUserByUserName(loginBody.getUsername());
            if (StringUtils.isNull(sysUser)) {
                return AjaxResult.error("登录工号：{} 不存在.", workNo);
            } else if (UserStatus.DELETED.getCode().equals(sysUser.getDelFlag())) {
                return AjaxResult.error("登录工号：{} 已被删除.", workNo);
            } else if (UserStatus.DISABLE.getCode().equals(sysUser.getStatus())) {
                return AjaxResult.error("登录工号：{} 已被停用.", workNo);
            }
            // 校验工号是否已被其他账号使用
            List<WXUser> userList = appCommonMapper.selectWXUserListByWorkNo(workNo);
            if (userList.size() > 1) {
                return AjaxResult.error("该工号已被其他多个微信号绑定");
            } else if (userList.size() == 1) {
                WXUser user = userList.get(0);
                if (!user.getOpenId().equals(openId)) {
                    return AjaxResult.error("该工号已被其他微信号绑定");
                }
            }
            // 密码校验
            if (!SecurityUtils.matchesPassword(password, sysUser.getPassword())) {
                if (sysUserService.loginInMes(workNo, password).equals("PASS")) {
                    // 密码重置为产销密码
                    sysUserService.resetUserPwd(workNo, SecurityUtils.encryptPassword(password));
                } else {
                    return AjaxResult.error("账号密码不匹配");
                }
            }
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(workNo + "(" + sysUser.getNickName() + ")", Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.app.success")));

            // 匹配到合适的账户 与openId进行关联
            WXUser user = new WXUser();
            user.setOpenId(openId);
            user.setWorkNo(workNo);
            user.setUpdateTime(DateUtils.getNowDate());
            appCommonMapper.updateWorkNoByOpenId(user);
            // 拼接返回数据
            JSONObject dataJson = new JSONObject();
            dataJson.put("nickName", sysUser.getNickName());
            dataJson.put("workNo", workNo);
            return AjaxResult.success(dataJson);
        } catch (Exception e) {
            return AjaxResult.error("发生未知错误");
        }
    }

    public AjaxResult getPermission(String token) {
        try {
            // 获取真实的openId
            String openId = redisCache.getCacheObject("UUID:" + token);
            if (token == null || openId == null)
                return AjaxResult.error(HttpStatus.NOT_IDENTIFY, "身份无法识别，请在设置中重新进入小程序");
            WXUser user = appCommonMapper.selectWXUserByOpenId(openId);
            String workNo = user.getWorkNo();
            if (workNo == null)
                return AjaxResult.error(HttpStatus.NOT_WORKNO, "工号识别失败");
            SysUser sysUser = sysUserService.selectUserByUserName(workNo);
            if (sysUser.getStatus().equals("1"))
                return AjaxResult.error(HttpStatus.NOT_USING, "工号已停用");

            List<String> list = appCommonMapper.getPermissionList(workNo);

            List<String> PASSPermissionList = appCommonMapper.getPASSPermissionList(workNo);

            Map<String, List<String>> result = new HashMap<>();
            // 销售权限
            if (list.contains("xcerp") || list.contains("productMark") || list.contains("overdueUser")) {
                List<String> hashResult = new ArrayList<>();
                // 担保单发起
                if (list.contains("guaranteeAdd"))
                    hashResult.add("guaranteeAdd");
                // 担保单审核
                if (list.contains("guarantee"))
                    hashResult.add("guarantee");
                // 担保单审核历史
                if (list.contains("guaranteedeal"))
                    hashResult.add("guaranteedeal");
                // 销售库存
                if (list.contains("stock"))
                    hashResult.add("stock");
                // 库存详情
                if (list.contains("saleDetail"))
                    hashResult.add("saleDetail");
                // 客户余额
                if (list.contains("freeAmt"))
                    hashResult.add("freeAmt");
                // 现货查询
                if (list.contains("goodsInStock"))
                    hashResult.add("goodsInStock");
                // 棒线外贸合同跟踪
                if (list.contains("foreignContract"))
                    hashResult.add("foreignContract");
                // 棒线内贸合同跟踪
                if (list.contains("domesticContract"))
                    hashResult.add("domesticContract");
                // 板材库存汇总
                if (list.contains("sheetAll"))
                    hashResult.add("sheetAll");
                // 板材库存详情
                if (list.contains("sheetDetail"))
                    hashResult.add("sheetDetail");
                // 板材合同跟踪(外贸)
                if (list.contains("sheetContractFor"))
                    hashResult.add("sheetContractFor");
                // 板材合同跟踪(内贸)
                if (list.contains("sheetContractDom"))
                    hashResult.add("sheetContractDom");
                // 开单统计(棒材)
                if (list.contains("billingStatistics"))
                    hashResult.add("billingStatistics");
                // 开单统计(板材)
                if (list.contains("sheetBilling"))
                    hashResult.add("sheetBilling");
                // 生产准发查询
                if (list.contains("productionApproval"))
                    hashResult.add("productionApproval");
                // 月末货款跟踪
                if (list.contains("loanTrackQuery"))
                    hashResult.add("loanTrackQuery");
                // 催缴合同查询
                if (list.contains("paymentContract"))
                    hashResult.add("paymentContract");
                // 资金收入日报
                if (list.contains("fundIncome"))
                    hashResult.add("fundIncome");
                // 提单信息一览
                if (list.contains("ladingOverview"))
                    hashResult.add("ladingOverview");
                // 库存未完成量
                if (list.contains("stockUndo"))
                    hashResult.add("stockUndo");
                // 棒线代储库存
                if (list.contains("BXActingStorage"))
                    hashResult.add("BXActingStorage");
                // 板材代储库存
                if (list.contains("BCActingStorage"))
                    hashResult.add("BCActingStorage");
                // 代轧替代标记
                if (list.contains("productMark"))
                    hashResult.add("productMark");
                // 逾期客户控制
                if (list.contains("overdueUser"))
                    hashResult.add("overdueUser");
                // 银亮材催缴画面
                if (list.contains("promptMaterial"))
                    hashResult.add("promptMaterial");
                if (list.contains("tbxbc02")) {
                    hashResult.add("tbxbc02");
                }
                // 物资库可视化
                if (list.contains("alloyScan"))
                    hashResult.add("alloyScan");
                result.put("xcerp", hashResult);
            }
            // 经营分析权限
            if (list.contains("prodForm")) {
                List<String> hashResult = new ArrayList<>();
                // 产销跟踪日报权限
                if (list.contains("xcprodnew"))
                    hashResult.add("xcprodnew");
                // 生产经营日报
                if (list.contains("outputform"))
                    hashResult.add("outputform");
                // 公司产能同比
                if (list.contains("monthProdSaleForm"))
                    hashResult.add("monthProdSaleForm");
                // 日产填报
                if (list.contains("dailyProd"))
                    hashResult.add("dailyProd");
                result.put("prodForm", hashResult);
            }
//            // 客户走访栏目权限
//            if (list.contains("visit")) {
//                List<String> hashResult = new ArrayList<>();
//                // 客户走访编制权限
//                if (list.contains("visitPlan"))
//                    hashResult.add("visitPlan");
//                // 客户走访审核权限
//                if (list.contains("visitVerify"))
//                    hashResult.add("visitVerify");
//                // 客户走访所有记录
//                if (list.contains("visitAll"))
//                    hashResult.add("visitAll");
//                result.put("visit", hashResult);
//            }
            // 销售走访权限 来自集团应用
            if (PASSPermissionList.contains("saleVisit")) {
                List<String> hashResult = new ArrayList<>();
                // 客户走访编制权限
                if (PASSPermissionList.contains("saleVisitPlan"))
                    hashResult.add("visitPlan");
                // 客户走访审核权限
                if (PASSPermissionList.contains("saleVisitVerify"))
                    hashResult.add("visitVerify");
                // 客户走访所有记录
                if (PASSPermissionList.contains("saleVisitAll"))
                    hashResult.add("visitAll");
                result.put("visit", hashResult);
            }
//            // 文件报批
//            if (list.contains("fileApprove")) {
//                List<String> hashResult = new ArrayList<>();
//                // 文件报批
//                if (list.contains("fileApproveAdd"))
//                    hashResult.add("fileApproveAdd");
//                // 文件审批
//                if (list.contains("fileExamine"))
//                    hashResult.add("fileExamine");
//                // 历史文件
//                if (list.contains("fileHistory"))
//                    hashResult.add("fileHistory");
//                result.put("fileApprove", hashResult);
//            }
            // 文件报批
            if (PASSPermissionList.contains("saleFile")) {
                List<String> hashResult = new ArrayList<>();
                // 文件报批
                if (PASSPermissionList.contains("saleFileApproval"))
                    hashResult.add("fileApproveAdd");
                // 文件审批
                if (PASSPermissionList.contains("saleFileExamine"))
                    hashResult.add("fileExamine");
                // 历史文件
                if (PASSPermissionList.contains("saleFileHistory"))
                    hashResult.add("fileHistory");
                result.put("fileApprove", hashResult);
            }
//            // 文件报批
//            if (list.contains("fileApprove")) {
//                List<String> hashResult = new ArrayList<>();
//                // 文件报批
//                if (list.contains("fileApproveAdd"))
//                    hashResult.add("fileApproveAdd");
//                // 文件审批
//                if (list.contains("fileExamine"))
//                    hashResult.add("fileExamine");
//                // 历史文件
//                if (list.contains("fileHistory"))
//                    hashResult.add("fileHistory");
//                result.put("fileApprove", hashResult);
//            }
            //打卡签到
            if (PASSPermissionList.contains("saleCheck")) {
                List<String> hashResult = new ArrayList<>();
                if (PASSPermissionList.contains("saleCheckIn"))
                    hashResult.add("saleCheckIn");
                result.put("saleCheck", hashResult);
            }

            //采购测算
            if (PASSPermissionList.contains("purchasingCenter")) {
                List<String> hashResult = new ArrayList<>();
                if (PASSPermissionList.contains("orePriceCalculate"))
                    hashResult.add("orePriceCalculate");
                if (PASSPermissionList.contains("coalPriceCalculate"))
                    hashResult.add("coalPriceCalculate");
                if (PASSPermissionList.contains("alloyPriceCalculate"))
                    hashResult.add("alloyPriceCalculate");
                if (PASSPermissionList.contains("basisPriceCalculate"))
                    hashResult.add("basisPriceCalculate");
                if (PASSPermissionList.contains("profitPriceCalculate"))
                    hashResult.add("profitPriceCalculate");
                if (PASSPermissionList.contains("logisticsCalculate"))
                    hashResult.add("logisticsCalculate");
                result.put("purchasingCenter", hashResult);
            }

            //计算机点检模块
            if (list.contains("xctgDevice")) {
                List<String> hashResult = new ArrayList<>();
                if (list.contains("myItDevice"))
                    hashResult.add("myItDevice");
                result.put("xctgDevice", hashResult);
            }

            //食堂点餐
            List<String> caterHashResult = new ArrayList<>();
            if (list.contains("cater")) {
                if (list.contains("appOrder"))
                    caterHashResult.add("appOrder");
                if (list.contains("appOrderList"))
                    caterHashResult.add("appOrderList");
                if (list.contains("appEndOrder"))
                    caterHashResult.add("appEndOrder");
            }
            result.put("cater", caterHashResult);

            //环保监测
            List<String> envirMonHashResult = new ArrayList<>();
            if (list.contains("envirMon")) {
                if (list.contains("waringList"))
                    envirMonHashResult.add("waringList");
                if (list.contains("waringChart"))
                    envirMonHashResult.add("waringChart");
                if (list.contains("actualTimeMonitor"))
                    envirMonHashResult.add("actualTimeMonitor");
                if (list.contains("uavJob"))
                    envirMonHashResult.add("uavJob");
            }
            result.put("envirMon", envirMonHashResult);

            //能源用钢
            List<String> energySteelResult = new ArrayList<>();
            if (list.contains("energySteel")) {
                if (list.contains("energySteelMessage"))
                    energySteelResult.add("energySteelMessage");

            }
            result.put("energySteel", energySteelResult);

            //吨钢承包
            List<String> dgcbResult = new ArrayList<>();
            if (list.contains("dgcb")) {
                if (list.contains("supplyList"))
                    dgcbResult.add("supplyList");
                if (list.contains("dgContractList"))
                    dgcbResult.add("dgContractList");
                if (list.contains("materialsList"))
                    dgcbResult.add("materialsList");
                if (list.contains("dgcbRepair"))
                    dgcbResult.add("dgcbRepair");
                if (list.contains("receive"))
                    dgcbResult.add("receive");
                if (list.contains("guard"))
                    dgcbResult.add("guard");
                if (list.contains("materialCheck"))
                    dgcbResult.add("materialCheck");
                if (list.contains("materialStorage"))
                    dgcbResult.add("materialStorage");
                if (list.contains("materialCorrect"))
                    dgcbResult.add("materialCorrect");
                if (list.contains("RepairFactoryApprove"))
                    dgcbResult.add("RepairFactoryApprove");
            }
            result.put("dgcb", dgcbResult);


            //吨钢承包
            List<String> saleScore = new ArrayList<>();
            if (list.contains("saleScore")) {
                if (list.contains("saleScoreApp"))
                    saleScore.add("saleScoreApp");
            }
            result.put("saleScore", saleScore);

            //生产流转卡
            List<String> productionCardHashResult = new ArrayList<>();
            if (list.contains("productionCard")) {
                if (list.contains("sizingOpt"))
                    productionCardHashResult.add("sizingOpt");
                if (list.contains("YLSizingOpt"))
                    productionCardHashResult.add("YLSizingOpt");
            }

            result.put("productionCard", productionCardHashResult);

            //车辆进出厂权限
            List<String> passportResult = new ArrayList<>();
            List<String> mysCarRoles = xctgVehicleAuditUserRoleMapper.selectUserRoleEntityByUserName(workNo);

            //生产流转卡
            List<String> eventTrackHashResult = new ArrayList<>();
            if (list.contains("eventTrack")) {
                if (list.contains("trackList"))
                    eventTrackHashResult.add("trackList");
                if (list.contains("trackToDoList"))
                    eventTrackHashResult.add("trackToDoList");
            }
            result.put("eventTrack", eventTrackHashResult);

            // 以师带徒
            List<String> apprenticeList = new ArrayList<>();
            if (list.contains("apprentice")) {
                if (list.contains("apprenticeSubmit"))
                    apprenticeList.add("apprenticeSubmit");
                if (list.contains("apprenticeEvaluate"))
                    apprenticeList.add("apprenticeEvaluate");
                if (list.contains("apprenticeLeader"))
                    apprenticeList.add("apprenticeLeader");
                if (list.contains("apprenticedeptLeader"))
                    apprenticeList.add("apprenticedeptLeader");
                if (list.contains("apprenticeBusinessLeader"))
                    apprenticeList.add("apprenticeBusinessLeader");
            }
            result.put("apprentice", apprenticeList);

            // 绩效考核
            List<String> assessList = new ArrayList<>();
            if (list.contains("lateralAssess")) {
                assessList.add("lateralAssess");
            }
            if (list.contains("assessCheck")) {
                assessList.add("assessCheck");
            }
            result.put("assess", assessList);

            // 积分榜-物管部
            List<String> wgbPointsList = new ArrayList<>();
            if(list.contains("wgbPoints")){
                if (list.contains("apply")) {
                    wgbPointsList.add("apply");
                }
                if (list.contains("approve")) {
                    wgbPointsList.add("approve");
                }
            }
            result.put("wgbPointsList", wgbPointsList);


            //添加角色
            List<String> carRoles = new ArrayList<>();
            for (CarRole item : CarRole.values()) {
                carRoles.add(item.getCode());
            }
            // 交集
            List<String> intersection = mysCarRoles.stream().filter(item -> carRoles.contains(item)).collect(Collectors.toList());
            if (intersection.size() > 0) {
                if (mysCarRoles.contains("client")) {
                    passportResult.add("client");
                }
                if (mysCarRoles.contains("leader") || mysCarRoles.contains("branchLeader") || mysCarRoles.contains("principal")) {
                    passportResult.add("passportVerify");
                }
                if (mysCarRoles.contains("leader")) {
                    passportResult.add("passportVerifyGrant");
                }
                if (mysCarRoles.contains("client") || mysCarRoles.contains("leader") || mysCarRoles.contains("branchLeader") || mysCarRoles.contains("principal")) {
                    passportResult.add("carPassportHistory");
                }
                if (mysCarRoles.contains("entranceGuard")) {
                    passportResult.add("entranceGuard");
                }
                if (mysCarRoles.contains("improveDep") || mysCarRoles.contains("officeStaff")) {
                    passportResult.add("improveDep");
                }
                if (mysCarRoles.contains("handleWithdraw")) {
                    passportResult.add("handleWithdraw");
                }
            }
            result.put("passport", passportResult);

            /*
             * 脱单工程
             * */
            List<String> roleList = SpringUtils.getBean(SysRoleMapper.class).selectRolesByUserName(workNo).stream().map(role -> role.getRoleKey()).collect(Collectors.toList());
            List<String> qsList = new ArrayList<>();
            if (roleList.contains("QsInfo")) {
                qsList.add("qsInfoAll");
            }
            if (list.contains("qsInfo"))
                qsList.add("qsInfo");
            if (list.contains("qsTrace") && !roleList.contains("QsInfo"))
                qsList.add("qsTrace");
            QsInfo qsInfo = SpringUtils.getBean(QsInfoServiceImpl.class).getInfoByUserName(workNo);
            if (Objects.nonNull(qsInfo) && StringUtils.isNotBlank(qsInfo.getMaritalStatus()) && StringUtils.isNotBlank(qsInfo.getIsSingle()) && StringUtils.isNotBlank(qsInfo.getQsWilling())) {
                if (qsInfo.getMaritalStatus().equals("0") && qsInfo.getIsSingle().equals("1") && qsInfo.getQsWilling().equals("1"))
                    qsList.add("qsActivity");
            } else if (qsList.size() > 0) qsList.add("qsActivity");

            if (Objects.nonNull(qsInfo) && (StringUtils.isNotBlank(qsInfo.getTradeUnionName()) || qsList.size() > 0))
                result.put("qs", qsList);

            // 工会服务
            List<String> orgList = new ArrayList<>();
            if (list.contains("eventManager"))
                orgList.add("eventManager");
            OrganizationUserInfo organizationUserInfo = new OrganizationUserInfo();
            organizationUserInfo.setWorkNo(workNo);
            List<OrganizationUserInfo> orlist = organizationUserInfoService.findList(organizationUserInfo);
            if (orlist.size() > 0) {
                orgList.add("event");
                if (orlist.get(0).getChargeOrganizationIdlist().length > 0) {
                    orgList.add("eventDirector");
                }

            }
            result.put("laborUnion", orgList);


            // 班组问题对策库-审核
            String[] teamCheckUserDepts = tTeamCheckUserMapper.getDeptNamesByWorkNo(workNo);
            result.put("teamSolutionCheck", Arrays.asList(teamCheckUserDepts.clone()));


            //铁水调度
            List<String> tsdd = new ArrayList<>();
            if (list.contains("tsdd")) {
                if (list.contains("tsBigScreen"))
                    tsdd.add("tsBigScreen");
            }

            result.put("tsdd", tsdd);

            if (list.contains("transferOrder")) {
                List<String> hashResult = new ArrayList<>();
                hashResult.add("transferOrder");
                result.put("transferOrder", hashResult);
            }

            // 其他模块
            List<String> hashResult = new ArrayList<>();
            // 打卡签到
//            if (list.contains("sign"))
//                hashResult.add("sign");
            if (list.contains("voteConfig"))
                hashResult.add("voteConfig");
            if (list.contains("voteResult"))
                hashResult.add("voteResult");
            if (list.contains("dailyList"))
                hashResult.add("dailyList");
            if (list.contains("businessTripInsert"))
                hashResult.add("businessTripInsert");
            if (list.contains("businessTripVerify"))
                hashResult.add("businessTripVerify");
            if (list.contains("businessTripHistory"))
                hashResult.add("businessTripHistory");
            if (list.contains("businessTripList"))
                hashResult.add("businessTripList");
            if (list.contains("troubleList"))
                hashResult.add("troubleList");
            if (list.contains("patrolRecord"))
                hashResult.add("patrolRecord");
            if (list.contains("interestedParty"))
                hashResult.add("interestedParty");
            if (list.contains("teamSolution"))
                hashResult.add("teamSolution");
            if (list.contains("hbWebSystem"))
                hashResult.add("hbWebSystem");

            // 线上请假
            hashResult.add("leaveOnline");
            hashResult.add("leaveApprove");
            if (list.contains("leaveDeptCollect")) {
                hashResult.add("leaveCollect");
            }

            // 内部人才市场
            hashResult.add("hrPostInfo");
            if(list.contains("hrPostApply")){
                hashResult.add("hrPostApply");
            }
            if(list.contains("hrPostCheck")){
                hashResult.add("hrPostCheck");
            }


            // 薪资考勤
            hashResult.add("wages");


            // 个人有关事项填报
//            LeaderInfo leaderInfo = leaderInfoMapper.selectLeaderBasicsByWorkNo(workNo);
//            if(StringUtils.isNotNull(leaderInfo)){
//                hashResult.add("jwLeaderInfo");
//            }

            //违章罚款
            List<String> penaltyResult = new ArrayList<>();
            if (list.contains("penalty")) {
                if (list.contains("addViolate"))
                    penaltyResult.add("addViolate");
                if (list.contains("violateList"))
                    penaltyResult.add("violateList");
                if (list.contains("violateWaitApproveList"))
                    penaltyResult.add("violateWaitApproveList");
            }
            result.put("penalty", penaltyResult);

            //货车预约
            List<String> truckResult = new ArrayList<>();
            if (list.contains("truck")) {
                if (list.contains("truckApply"))
                    truckResult.add("truckApply");
                if (list.contains("truckVerify"))
                    truckResult.add("truckVerify");
                if (list.contains("truckHistory"))
                    truckResult.add("truckHistory");
            }
            result.put("truck", truckResult);

            //废钢预约
            List<String> scrapSteelResult = new ArrayList<>();
            if (list.contains("scrapSteelPermit")) {
                if (list.contains("scrapSteelVerify"))
                    scrapSteelResult.add("scrapSteelVerify");
                if (list.contains("scrapSteelList"))
                    scrapSteelResult.add("scrapSteelList");
            }
            result.put("scrapSteel", scrapSteelResult);

            //访客
            List<String> steelVisitorResult = new ArrayList<>();
            if (list.contains("specialSteelVisitor")) {
                if (list.contains("visitorApplication"))
                    steelVisitorResult.add("visitorApplication");
                if (list.contains("visitorApproval"))
                    steelVisitorResult.add("visitorApproval");
            }
            //默认拥有受访权限
            steelVisitorResult.add("visitorApplyList");
            result.put("steelVisitor", steelVisitorResult);

            //检修事业部
            List<String> repairBusinessResult = new ArrayList<>();
            if (list.contains("repairBusiness")) {
                if (list.contains("repairWorkOrderFilling")) {
                    repairBusinessResult.add("repairWorkOrderFilling");
                }
                if (list.contains("repairWorkOrderList")) {
                    repairBusinessResult.add("repairWorkOrderList");
                }
                if (list.contains("repairInspectionFilling")) {
                    repairBusinessResult.add("repairInspectionFilling");
                }
                if (list.contains("repairInspectionList")) {
                    repairBusinessResult.add("repairInspectionList");
                }
            }
            result.put("repairBusiness", repairBusinessResult);

            //汽吊用车
            List<String> craneResult = new ArrayList<>();
            if (list.contains("crane")) {
                if (list.contains("craneApply")){
                    craneResult.add("craneApply");
                }
                if (list.contains("craneList")){
                    craneResult.add("craneList");
                }
                if (list.contains("craneApproveList")){
                    craneResult.add("craneApproveList");
                }
            }
            result.put("crane", craneResult);

            //运营信息
            List<String> yyxxResult = new ArrayList<>();
            if (list.contains("dataReport")) {
                if (list.contains("answerReport")){
                    yyxxResult.add("answerReport");
                }
                if (list.contains("dataReportExamine")){
                    yyxxResult.add("dataReportExamine");
                }

            }
            result.put("yyxx", yyxxResult);

            //合金车辆预约
            List<String> matAlloyResult = new ArrayList<>();
            if (list.contains("matMgmtAlloy")) {
                if (list.contains("matAlloyAllList")) {
                    matAlloyResult.add("matAlloyAllList");
                }
                if (list.contains("matAlloyVerify")) {
                    matAlloyResult.add("matAlloyVerify");
                }
                if (list.contains("matAlloyAssign")) {
                    matAlloyResult.add("matAlloyAssign");
                }
                if (list.contains("matAlloyCheckIn")) {
                    matAlloyResult.add("matAlloyCheckIn");
                }
                if (list.contains("matAlloyGuardScan")) {
                    matAlloyResult.add("matAlloyGuardScan");
                }
                if (list.contains("matAlloyConfirm")) {
                    matAlloyResult.add("matAlloyConfirm");
                }
            }
            result.put("matMgmtAlloy", matAlloyResult);

            //提单预约
            List<String> billReserveResult = new ArrayList<>();
            if (list.contains("ladingBill")) {
                if (list.contains("driverReserveLog")) {
                    billReserveResult.add("driverReserveLog");
                }
            }
            result.put("billReserve", billReserveResult);

            // 申发运日报查看
//            List<String> tbxbc02List = new ArrayList<>();
//            if (list.contains("tbxbc02")) {
//                tbxbc02List.add("tbxbc02");
//            }
//            result.put("tbxbc02", tbxbc02List);


            // 员工服务
            result.put("other", hashResult);
            return AjaxResult.success(result);

        } catch (Exception e) {
            return AjaxResult.error("发生错误");
        }
    }

    /**
     * 根据token 获得openid
     */
    public String getOpenId(String token) {
        return redisCache.getCacheObject("UUID:" + token);
    }


    /**
     * 根据token 获得工号
     */
    public String getWorkNoByToken(String token) {
        String openId = redisCache.getCacheObject("UUID:" + token);
        WXUser user = appCommonMapper.selectWXUserByOpenId(openId);
        if (user != null) {
            return user.getWorkNo();
        } else {
            return null;
        }
    }


    /**
     * 根据工号 获得姓名
     */
    public String getNickNameByWorkNo(String workNo) {
        return appCommonMapper.selectNickNameByWorkNo(workNo);
    }

    /**
     * 根据工号 查询手机号
     *
     * @param workNo
     * @return
     */
    public String getPhoneByWorkNo(String workNo) {
        return appCommonMapper.getPhoneByWorkNo(workNo);
    }
//
//    public AjaxResult getSubCompanyAndSubOperator() {
//        List<Map<String, Object>> result = new ArrayList<Map<String, Object>>();
//        List<Map> companyList = saleStockMapper.selectSubCompany();
//        List<XctgSaleStock> list = saleStockMapper.selectSubCompanyAndSubOperator();
//        for (Map map : companyList) {
//            Map<String, Object> company = new HashMap<>();
//            company.put("companyName", map.get("NAME"));
//            company.put("companyNo", map.get("VALUE"));
//            List<Map<String, Object>> operators = new ArrayList<Map<String, Object>>();
//            for (XctgSaleStock stock : list) {
//                if (stock.getCompanyNo().equals(map.get("VALUE"))) {
//                    Map<String, Object> operator = new HashMap<>();
//                    operator.put("operatorNo", stock.getOperatorNo());
//                    operator.put("operatorName", stock.getOperatorName());
//                    operators.add(operator);
//                }
//            }
//            company.put("operators", operators);
//            result.add(company);
//        }
//        return AjaxResult.success(result);
//    }
//
//    public String getNotice() {
//        return commonMapper.getNotice();
//    }

    /**
     * 获取用户手机号
     *
     * @param encryptedData
     * @param iv
     * @param code
     */
    public String getPhone(String encryptedData, String iv, String code) {
        String phone = null;
        try {
            // code换取session_key
            String rspStr = HttpUtils.sendGet(jsCodeUrl, "appid=" + appid + "&secret=" + secret + "&js_code=" + code + "&grant_type=authorization_code", Constants.UTF8);
            JSONObject obj = JSONObject.parseObject(rspStr);
            if (obj.get("errcode") == null) {
                // 获取到会话id
                String sessionKey = obj.getString("session_key");
                String openId = obj.getString("openid");
                String decryptData = AesUtils.decryptData(encryptedData, sessionKey, iv);
                JSONObject decryptObj = JSONObject.parseObject(decryptData);
                if (decryptObj.get("phoneNumber") != null) {
                    // 获取手机号
                    phone = decryptObj.getString("phoneNumber");
                    appCommonMapper.updatePhoneByOpenId(phone, openId);
                }
            }
            return phone;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }


    }

    public String getPhoneNumber(String code, String openId) {
        String phone = null;
        try {
            String accessToken = messageService.getAppAccessToken();
            // 数据拼接
            JSONObject postData = new JSONObject();
            postData.put("code", code);
            String rspStr = HttpUtils.sendPost(userPhoneNumberUrl + "?access_token=" + accessToken, postData.toJSONString());
            JSONObject obj = JSONObject.parseObject(rspStr);
            if (obj.getInteger("errcode") == 0) {
                JSONObject phoneInfo = obj.getJSONObject("phone_info");
                if (null != phoneInfo) {
                    phone = phoneInfo.getString("purePhoneNumber");
                    appCommonMapper.updatePhoneByOpenId(phone, openId);
                }
            } else if (obj.getInteger("errcode") == 40001 && redisCache.getCacheObject("access_token:xcapp:retry_time") == null) {
                //400001代表token失效且距离上次获取token失效时间超过60s
                log.info("微信access_token失效，重置token...");
                //重置失败获取间隔时间
                redisCache.setCacheObject("access_token:xcapp:retry_time", "1", 60, TimeUnit.SECONDS);
                //重新获取一次
                redisCache.deleteObject("access_token:xcapp");
                return getPhoneNumber(code, openId);
            }
            return phone;
        } catch (Exception e) {
            log.info("获取手机号失败...");
            e.printStackTrace();
            return null;
        }


    }

    @Override
    public int updateCWechatPushByOpenId(String CWechatPush, String token) {
        String openId = getOpenId(token);

        return appCommonMapper.updateCWechatPushByOpenId(CWechatPush, openId);
    }

    @Override
    public int updateIsAgreeByOpenId(String isAgree, String token) {
        String openId = getOpenId(token);

        return appCommonMapper.updateIsAgreeByOpenId(isAgree, openId);
    }

    //后在getToken中调用
    @Override
    public Map<String, Object> getConfigByOpenId(String token) {
        String openId = getOpenId(token);
        return appCommonMapper.selectConfigByOpenId(openId);
    }

    @Override
    public int updateStatusWXUserByWorkNo(String workNo) {
        List<WXUser> wxUsers = appCommonMapper.selectWXUserListByWorkNo(workNo);
        if (wxUsers.size() > 0) {
            for (WXUser item : wxUsers) {
                item.setStatus("1");
                item.setRemark("工号注销，禁用内部应用功能");
                appCommonMapper.updateStatusByOpenId(item);
            }
        }
        return wxUsers.size();
    }

    /**
     * 生成小程序二维码（数量不限）
     *
     * @param page  默认是主页，页面 page，例如 pages/index/index，根路径前不要填加 /，不能携带参数
     * @param scene 页面参数请放在scene字段里
     * @return
     */
    @Override
    public String getwxacodeunlimit(String page, String scene) {
        String accessToken = messageService.getAppAccessToken();
        JSONObject param = new JSONObject();
        param.put("page", page);
        param.put("scene", scene);
        param.put("check_path", true);   //默认是true，检查page 是否存在，为 true 时 page 必须是已经发布的小程序存在的页面
//        param.put("env_version","trial");   // 要打开的小程序版本。正式版为 "release"，体验版为 "trial"，开发版为 "develop"。默认是正式版。
        byte[] res = HttpUtils.postJsonForByte(unlimitedQRCodeUrl + "?access_token=" + accessToken, param.toJSONString());
//        byte[] by = res.getBytes();
        String base64Str = Base64.encode(res);
        return base64Str;
    }


}
