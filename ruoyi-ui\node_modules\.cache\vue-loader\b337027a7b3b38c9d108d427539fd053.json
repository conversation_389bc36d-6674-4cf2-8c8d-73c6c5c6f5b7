{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\qualityCost\\companySummary3\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\qualityCost\\companySummary3\\index.vue", "mtime": 1756099891081}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBsaXN0Q29tcGFueVN1bW1hcnlEYXRhIH0gZnJvbSAiQC9hcGkvcXVhbGl0eUNvc3QvY29tcGFueVN1bW1hcnkxIjsNCmltcG9ydCB7IHRlc3RGYWN0b3J5RGF0YSwgdGVzdEZhY3RvcnlEYXRhV2l0aFJlYWxWYWx1ZXMsIHRlc3REYXRhTWFwcGluZyB9IGZyb20gIi4vdGVzdERhdGEiOw0KDQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogIkNvbXBhbnlTdW1tYXJ5MSIsDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIC8vIOmBrue9qeWxgg0KICAgICAgbG9hZGluZzogZmFsc2UsDQogICAgICAvLyDotKjph4/miJDmnKzmlbDmja7liJfooagNCiAgICAgIHF1YWxpdHlDb3N0TGlzdDogW10sDQogICAgICAvLyDlkIjlubbljZXlhYPmoLzphY3nva4NCiAgICAgIG1lcmdlQ2VsbHM6IFtdLA0KICAgICAgLy8g6KGo5qC86auY5bqmDQogICAgICB0YWJsZUhlaWdodDogbnVsbCwNCiAgICAgIC8vIOaYr+WQpumcgOimgea7muWKqOadoQ0KICAgICAgbmVlZFNjcm9sbGJhcjogdHJ1ZSwNCiAgICAgIC8vIOW5tOS7vemAieaLqQ0KICAgICAgc2VsZWN0ZWRZZWFyOiBuZXcgRGF0ZSgpLmdldEZ1bGxZZWFyKCksDQogICAgICAvLyDlubTku73pgInpobkNCiAgICAgIHllYXJPcHRpb25zOiBbXSwNCiAgICAgIC8vIOexu+Wei+mAieaLqQ0KICAgICAgc2VsZWN0ZWRUeXBlOiAn6ISx5ZCI5ZCMJywNCiAgICAgIC8vIOexu+Wei+mAiemhuQ0KICAgICAgdHlwZU9wdGlvbnM6IFsn5pS55YikJywgJ+aKpeW6nycsICfohLHlkIjlkIwnLCAn6YCA6LSnJ10NCiAgICB9Ow0KICB9LA0KICBjb21wdXRlZDogew0KICAgIC8qKiDlrrnlmajmoLflvI8gKi8NCiAgICBjb250YWluZXJTdHlsZSgpIHsNCiAgICAgIHJldHVybiB7DQogICAgICAgIGhlaWdodDogdGhpcy50YWJsZUhlaWdodCA/IGAke3RoaXMudGFibGVIZWlnaHR9cHhgIDogJ2F1dG8nDQogICAgICB9Ow0KICAgIH0sDQogICAgLyoqIOihqOagvOWuueWZqOagt+W8jyAqLw0KICAgIHRhYmxlQ29udGFpbmVyU3R5bGUoKSB7DQogICAgICByZXR1cm4gew0KICAgICAgICBoZWlnaHQ6IHRoaXMudGFibGVIZWlnaHQgPyBgJHt0aGlzLnRhYmxlSGVpZ2h0IC0gODB9cHhgIDogJ2F1dG8nDQogICAgICB9Ow0KICAgIH0NCiAgfSwNCiAgbW91bnRlZCgpIHsNCiAgICB0aGlzLmNhbGN1bGF0ZVRhYmxlSGVpZ2h0KCk7DQogICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoJ3Jlc2l6ZScsIHRoaXMuY2FsY3VsYXRlVGFibGVIZWlnaHQpOw0KICAgIHRoaXMuZ2VuZXJhdGVZZWFyT3B0aW9ucygpOw0KICB9LA0KICBiZWZvcmVEZXN0cm95KCkgew0KICAgIHdpbmRvdy5yZW1vdmVFdmVudExpc3RlbmVyKCdyZXNpemUnLCB0aGlzLmNhbGN1bGF0ZVRhYmxlSGVpZ2h0KTsNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIGdldENvbXBhbnlTdW1tYXJ5RGF0YSgpIHsNCiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7DQogICAgICANCiAgICAgIGNvbnN0IHBhcmFtcyA9IHsNCiAgICAgICAgeWVhck1vbnRoOiB0aGlzLnNlbGVjdGVkWWVhciwNCiAgICAgICAgY29tcGFueVN1bW1hcnlUeXBlOiB0aGlzLnNlbGVjdGVkVHlwZQ0KICAgICAgfTsNCg0KICAgICAgbGlzdENvbXBhbnlTdW1tYXJ5RGF0YShwYXJhbXMpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICBjb25zb2xlLmxvZygnbGlzdENvbXBhbnlTdW1tYXJ5RGF0YTonLCByZXNwb25zZSk7DQogICAgICAgIGlmIChyZXNwb25zZS5kYXRhKSB7DQogICAgICAgICAgLy8g5aSE55CG5YiG5Y6C5pWw5o2u5qC85byPDQogICAgICAgICAgdGhpcy5wcm9jZXNzRmFjdG9yeURhdGEocmVzcG9uc2UuZGF0YSk7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy5xdWFsaXR5Q29zdExpc3QgPSBbXTsNCiAgICAgICAgfQ0KICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgIH0pLmNhdGNoKGVycm9yID0+IHsNCiAgICAgICAgY29uc29sZS5lcnJvcign6I635Y+W5pWw5o2u5aSx6LSlOicsIGVycm9yKTsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign6I635Y+W6LSo6YeP5oiQ5pys5pWw5o2u5aSx6LSlJyk7DQogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgICAgfSk7DQogICAgfSwNCg0KICAgIC8qKiDlpITnkIbliIbljoLmlbDmja7moLzlvI8gKi8NCiAgICBwcm9jZXNzRmFjdG9yeURhdGEocmF3RGF0YSkgew0KICAgICAgY29uc29sZS5sb2coJ+W8gOWni+WkhOeQhuWIhuWOguaVsOaNrjonLCByYXdEYXRhKTsNCiAgICAgIA0KICAgICAgLy8g5qOA5p+l5piv5ZCm5Li65YiG5Y6C5pWw5o2u5qC85byPDQogICAgICBpZiAodGhpcy5pc0ZhY3RvcnlEYXRhRm9ybWF0KHJhd0RhdGEpKSB7DQogICAgICAgIGNvbnNvbGUubG9nKCfmo4DmtYvliLDliIbljoLmlbDmja7moLzlvI/vvIzlvIDlp4vlpITnkIYuLi4nKTsNCiAgICAgICAgDQogICAgICAgIC8vIOi9rOaNouS4uuihqOagvOagvOW8jw0KICAgICAgICBjb25zdCB0YWJsZURhdGEgPSB0aGlzLmNvbnZlcnRGYWN0b3J5RGF0YVRvVGFibGUocmF3RGF0YSk7DQogICAgICAgIA0KICAgICAgICAvLyDmm7TmlrDooajmoLzmlbDmja4NCiAgICAgICAgdGhpcy51cGRhdGVUYWJsZVdpdGhGYWN0b3J5RGF0YSh0YWJsZURhdGEpOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgY29uc29sZS5sb2coJ+S4jeaYr+WIhuWOguaVsOaNruagvOW8j++8jOS9v+eUqOm7mOiupOWkhOeQhicpOw0KICAgICAgICB0aGlzLnF1YWxpdHlDb3N0TGlzdCA9IFtdOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvKiog5qOA5p+l5piv5ZCm5Li65YiG5Y6C5pWw5o2u5qC85byPICovDQogICAgaXNGYWN0b3J5RGF0YUZvcm1hdChkYXRhKSB7DQogICAgICBpZiAoIWRhdGEgfHwgdHlwZW9mIGRhdGEgIT09ICdvYmplY3QnKSByZXR1cm4gZmFsc2U7DQogICAgICANCiAgICAgIGNvbnN0IGZhY3RvcnlLZXlzID0gT2JqZWN0LmtleXMoZGF0YSk7DQogICAgICBpZiAoZmFjdG9yeUtleXMubGVuZ3RoID09PSAwKSByZXR1cm4gZmFsc2U7DQogICAgICANCiAgICAgIGNvbnN0IGZpcnN0RmFjdG9yeSA9IGRhdGFbZmFjdG9yeUtleXNbMF1dOw0KICAgICAgaWYgKCFmaXJzdEZhY3RvcnkgfHwgdHlwZW9mIGZpcnN0RmFjdG9yeSAhPT0gJ29iamVjdCcpIHJldHVybiBmYWxzZTsNCiAgICAgIA0KICAgICAgY29uc3QgbW9udGhLZXlzID0gT2JqZWN0LmtleXMoZmlyc3RGYWN0b3J5KTsNCiAgICAgIGlmIChtb250aEtleXMubGVuZ3RoID09PSAwKSByZXR1cm4gZmFsc2U7DQogICAgICANCiAgICAgIGNvbnN0IGZpcnN0TW9udGggPSBmaXJzdEZhY3RvcnlbbW9udGhLZXlzWzBdXTsNCiAgICAgIHJldHVybiBmaXJzdE1vbnRoICYmIA0KICAgICAgICAgICAgIHR5cGVvZiBmaXJzdE1vbnRoID09PSAnb2JqZWN0JyAmJiANCiAgICAgICAgICAgICAnY29zdEV4JyBpbiBmaXJzdE1vbnRoICYmIA0KICAgICAgICAgICAgICdjb3N0VG9uJyBpbiBmaXJzdE1vbnRoOw0KICAgIH0sDQoNCiAgICAvKiog5riF55CG5qih5ouf5pWw5o2uICovDQogICAgY2xlYW5Nb2NrRGF0YShkYXRhKSB7DQogICAgICBjb25zdCBjbGVhbmVkRGF0YSA9IHt9Ow0KICAgICAgDQogICAgICBPYmplY3Qua2V5cyhkYXRhKS5mb3JFYWNoKGZhY3RvcnlOYW1lID0+IHsNCiAgICAgICAgY29uc3QgZmFjdG9yeURhdGEgPSBkYXRhW2ZhY3RvcnlOYW1lXTsNCiAgICAgICAgY29uc3QgdmFsaWRNb250aHMgPSB7fTsNCiAgICAgICAgDQogICAgICAgIE9iamVjdC5rZXlzKGZhY3RvcnlEYXRhKS5mb3JFYWNoKG1vbnRoS2V5ID0+IHsNCiAgICAgICAgICBjb25zdCBtb250aERhdGEgPSBmYWN0b3J5RGF0YVttb250aEtleV07DQogICAgICAgICAgDQogICAgICAgICAgLy8g5qOA5p+l5piv5ZCm5Li65qih5ouf5pWw5o2uDQogICAgICAgICAgaWYgKCF0aGlzLmlzTW9ja0RhdGEobW9udGhEYXRhKSkgew0KICAgICAgICAgICAgdmFsaWRNb250aHNbbW9udGhLZXldID0gbW9udGhEYXRhOw0KICAgICAgICAgIH0NCiAgICAgICAgfSk7DQogICAgICAgIA0KICAgICAgICAvLyDlj6rmnInlvZPliIbljoLmnInmnInmlYjmlbDmja7ml7bmiY3kv53nlZkNCiAgICAgICAgaWYgKE9iamVjdC5rZXlzKHZhbGlkTW9udGhzKS5sZW5ndGggPiAwKSB7DQogICAgICAgICAgY2xlYW5lZERhdGFbZmFjdG9yeU5hbWVdID0gdmFsaWRNb250aHM7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgICAgDQogICAgICBjb25zb2xlLmxvZygn5riF55CG5ZCO55qE5pWw5o2uOicsIGNsZWFuZWREYXRhKTsNCiAgICAgIHJldHVybiBjbGVhbmVkRGF0YTsNCiAgICB9LA0KDQogICAgLyoqIOWIpOaWreaYr+WQpuS4uuaooeaLn+aVsOaNriAqLw0KICAgIGlzTW9ja0RhdGEobW9udGhEYXRhKSB7DQogICAgICByZXR1cm4gKA0KICAgICAgICBtb250aERhdGEuY29zdENlbnRlck5hbWUgPT09IG51bGwgJiYNCiAgICAgICAgbW9udGhEYXRhLmNvc3RFeCA9PT0gMCAmJg0KICAgICAgICBtb250aERhdGEuY29zdFRvbiA9PT0gMCAmJg0KICAgICAgICBtb250aERhdGEueWVhck1vbnRoID09PSBudWxsDQogICAgICApOw0KICAgIH0sDQoNCiAgICAvKiog5bCG5YiG5Y6C5pWw5o2u6L2s5o2i5Li66KGo5qC85qC85byPICovDQogICAgY29udmVydEZhY3RvcnlEYXRhVG9UYWJsZShmYWN0b3J5RGF0YSkgew0KICAgICAgY29uc3QgdGFibGVEYXRhID0gW107DQogICAgICANCiAgICAgIE9iamVjdC5rZXlzKGZhY3RvcnlEYXRhKS5mb3JFYWNoKGZhY3RvcnlOYW1lID0+IHsNCiAgICAgICAgY29uc3QgZmFjdG9yeU1vbnRocyA9IGZhY3RvcnlEYXRhW2ZhY3RvcnlOYW1lXTsNCiAgICAgICAgDQogICAgICAgIE9iamVjdC5rZXlzKGZhY3RvcnlNb250aHMpLmZvckVhY2gobW9udGhLZXkgPT4gew0KICAgICAgICAgIGNvbnN0IG1vbnRoRGF0YSA9IGZhY3RvcnlNb250aHNbbW9udGhLZXldOw0KICAgICAgICAgIA0KICAgICAgICAgIC8vIOWIm+W7uuihqOagvOihjOaVsOaNrg0KICAgICAgICAgIGNvbnN0IHJvd0RhdGEgPSB7DQogICAgICAgICAgICBmYWN0b3J5TmFtZTogZmFjdG9yeU5hbWUsDQogICAgICAgICAgICBtb250aDogbW9udGhLZXksDQogICAgICAgICAgICBjb3N0RXg6IG1vbnRoRGF0YS5jb3N0RXggfHwgMCwNCiAgICAgICAgICAgIGNvc3RUb246IG1vbnRoRGF0YS5jb3N0VG9uIHx8IDAsDQogICAgICAgICAgICB5ZWFyTW9udGg6IG1vbnRoRGF0YS55ZWFyTW9udGgsDQogICAgICAgICAgICBjb3N0Q2VudGVyTmFtZTogbW9udGhEYXRhLmNvc3RDZW50ZXJOYW1lDQogICAgICAgICAgfTsNCiAgICAgICAgICANCiAgICAgICAgICB0YWJsZURhdGEucHVzaChyb3dEYXRhKTsNCiAgICAgICAgfSk7DQogICAgICB9KTsNCiAgICAgIA0KICAgICAgY29uc29sZS5sb2coJ+i9rOaNouWQjueahOihqOagvOaVsOaNrjonLCB0YWJsZURhdGEpOw0KICAgICAgcmV0dXJuIHRhYmxlRGF0YTsNCiAgICB9LA0KDQogICAgLyoqIOabtOaWsOihqOagvOaYvuekuuWIhuWOguaVsOaNriAqLw0KICAgIHVwZGF0ZVRhYmxlV2l0aEZhY3RvcnlEYXRhKHRhYmxlRGF0YSkgew0KICAgICAgaWYgKHRhYmxlRGF0YS5sZW5ndGggPT09IDApIHsNCiAgICAgICAgdGhpcy5xdWFsaXR5Q29zdExpc3QgPSBbXTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KICAgICAgDQogICAgICAvLyDojrflj5bmiYDmnInliIbljoLlkI3np7ANCiAgICAgIGNvbnN0IGZhY3RvcnlOYW1lcyA9IFsuLi5uZXcgU2V0KHRhYmxlRGF0YS5tYXAocm93ID0+IHJvdy5mYWN0b3J5TmFtZSkpXTsNCiAgICAgIA0KICAgICAgLy8g5Yib5bu66KGo5qC86KGM5pWw5o2uDQogICAgICB0aGlzLnF1YWxpdHlDb3N0TGlzdCA9IGZhY3RvcnlOYW1lcy5tYXAoZmFjdG9yeU5hbWUgPT4gew0KICAgICAgICBjb25zdCByb3dEYXRhID0gew0KICAgICAgICAgIGNvbXBhbnk6IGZhY3RvcnlOYW1lDQogICAgICAgIH07DQogICAgICAgIA0KICAgICAgICAvLyDliJ3lp4vljJbmiYDmnInmnIjku73nmoTmlbDmja7kuLowDQogICAgICAgIGNvbnN0IG1vbnRocyA9IFsnamFudWFyeScsICdmZWJydWFyeScsICdtYXJjaCcsICdhcHJpbCcsICdtYXknLCAnanVuZScsDQogICAgICAgICAgICAgICAgICAgICAgICdqdWx5JywgJ2F1Z3VzdCcsICdzZXB0ZW1iZXInLCAnb2N0b2JlcicsICdub3ZlbWJlcicsICdkZWNlbWJlciddOw0KICAgICAgICANCiAgICAgICAgbW9udGhzLmZvckVhY2goKG1vbnRoLCBpbmRleCkgPT4gew0KICAgICAgICAgIGNvbnN0IG1vbnRoTnVtYmVyID0gaW5kZXggKyAxOw0KICAgICAgICAgIGNvbnN0IG1vbnRoRGF0YSA9IHRhYmxlRGF0YS5maW5kKHJvdyA9PiANCiAgICAgICAgICAgIHJvdy5mYWN0b3J5TmFtZSA9PT0gZmFjdG9yeU5hbWUgJiYgcm93Lm1vbnRoID09PSBtb250aE51bWJlci50b1N0cmluZygpDQogICAgICAgICAgKTsNCiAgICAgICAgICANCiAgICAgICAgICBpZiAobW9udGhEYXRhKSB7DQogICAgICAgICAgICByb3dEYXRhW2Ake21vbnRofUFtb3VudGBdID0gbW9udGhEYXRhLmNvc3RFeDsNCiAgICAgICAgICAgIHJvd0RhdGFbYCR7bW9udGh9UGVyVG9uYF0gPSBtb250aERhdGEuY29zdFRvbjsNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgcm93RGF0YVtgJHttb250aH1BbW91bnRgXSA9IDA7DQogICAgICAgICAgICByb3dEYXRhW2Ake21vbnRofVBlclRvbmBdID0gMDsNCiAgICAgICAgICB9DQogICAgICAgIH0pOw0KICAgICAgICANCiAgICAgICAgLy8g6K6h566X5bm05bqm57Sv6K6hDQogICAgICAgIHJvd0RhdGEueWVhcmx5VG90YWxBbW91bnQgPSBtb250aHMucmVkdWNlKChzdW0sIG1vbnRoKSA9PiBzdW0gKyAocm93RGF0YVtgJHttb250aH1BbW91bnRgXSB8fCAwKSwgMCk7DQogICAgICAgIHJvd0RhdGEueWVhcmx5VG90YWxQZXJUb24gPSBtb250aHMucmVkdWNlKChzdW0sIG1vbnRoKSA9PiBzdW0gKyAocm93RGF0YVtgJHttb250aH1QZXJUb25gXSB8fCAwKSwgMCk7DQogICAgICAgIA0KICAgICAgICByZXR1cm4gcm93RGF0YTsNCiAgICAgIH0pOw0KICAgICAgDQogICAgICBjb25zb2xlLmxvZygn5pu05paw5ZCO55qE6KGo5qC85pWw5o2uOicsIHRoaXMucXVhbGl0eUNvc3RMaXN0KTsNCiAgICB9LA0KDQoNCiAgICAvKiog5qC85byP5YyW6LSn5biBICovDQogICAgZm9ybWF0Q3VycmVuY3kodmFsdWUpIHsNCiAgICAgIGlmICh2YWx1ZSA9PT0gbnVsbCB8fCB2YWx1ZSA9PT0gdW5kZWZpbmVkIHx8IHZhbHVlID09PSAnJyB8fCBOdW1iZXIodmFsdWUpID09PSAwKSB7DQogICAgICAgIHJldHVybiAnLSc7DQogICAgICB9DQogICAgICBjb25zdCBudW0gPSBOdW1iZXIodmFsdWUpOw0KICAgICAgLy8g6YeR6aKd5pi+56S65Li65YWD77yM5LiN6L2s5o2i5Li65LiHDQogICAgICByZXR1cm4gbnVtLnRvTG9jYWxlU3RyaW5nKCd6aC1DTicsIHsNCiAgICAgICAgbWluaW11bUZyYWN0aW9uRGlnaXRzOiAyLA0KICAgICAgICBtYXhpbXVtRnJhY3Rpb25EaWdpdHM6IDINCiAgICAgIH0pOw0KICAgIH0sDQoNCiAgICAvKiog5qC85byP5YyW5ZCo5L2NICovDQogICAgZm9ybWF0VG9uKHZhbHVlKSB7DQogICAgICBpZiAodmFsdWUgPT09IG51bGwgfHwgdmFsdWUgPT09IHVuZGVmaW5lZCB8fCB2YWx1ZSA9PT0gJycgfHwgTnVtYmVyKHZhbHVlKSA9PT0gMCkgew0KICAgICAgICByZXR1cm4gJy0nOw0KICAgICAgfQ0KICAgICAgY29uc3QgbnVtID0gTnVtYmVyKHZhbHVlKTsNCiAgICAgIC8vIOWQqOS9jeaYvuekuuS4uuWQqO+8jOS/neeVmTLkvY3lsI/mlbANCiAgICAgIHJldHVybiBudW0udG9Mb2NhbGVTdHJpbmcoJ3poLUNOJywgew0KICAgICAgICBtaW5pbXVtRnJhY3Rpb25EaWdpdHM6IDIsDQogICAgICAgIG1heGltdW1GcmFjdGlvbkRpZ2l0czogMg0KICAgICAgfSk7DQogICAgfSwNCg0KICAgIC8qKiDorqHnrpfooajmoLzpq5jluqYgKi8NCiAgICBjYWxjdWxhdGVUYWJsZUhlaWdodCgpIHsNCiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgY29uc3Qgd2luZG93SGVpZ2h0ID0gd2luZG93LmlubmVySGVpZ2h0Ow0KICAgICAgICAvLyDlh4/ljrvpobXpnaLlpLTpg6jjgIHmoIfpopjnrYnpq5jluqbvvIzlpKfnuqYxMDBweA0KICAgICAgICBjb25zdCBhdmFpbGFibGVIZWlnaHQgPSB3aW5kb3dIZWlnaHQgLSAxMDA7DQogICAgICAgIC8vIOiuvue9ruihqOagvOacgOWkp+mrmOW6pu+8jOacgOWwjzQwMHB477yM5pyA5aSn5LiN6LaF6L+H5Y+v55So6auY5bqm55qEOTAlDQogICAgICAgIHRoaXMudGFibGVIZWlnaHQgPSBNYXRoLm1heCg0MDAsIE1hdGgubWluKDgwMCwgYXZhaWxhYmxlSGVpZ2h0ICogMC45KSk7DQogICAgICAgIHRoaXMubmVlZFNjcm9sbGJhciA9IHRoaXMucXVhbGl0eUNvc3RMaXN0Lmxlbmd0aCA+IDg7DQogICAgICB9KTsNCiAgICB9LA0KDQogICAgLyoqIOeUn+aIkOW5tOS7vemAiemhuSAqLw0KICAgIGdlbmVyYXRlWWVhck9wdGlvbnMoKSB7DQogICAgICBjb25zdCBjdXJyZW50WWVhciA9IG5ldyBEYXRlKCkuZ2V0RnVsbFllYXIoKTsNCiAgICAgIGZvciAobGV0IGkgPSAyMDAwOyBpIDw9IGN1cnJlbnRZZWFyOyBpKyspIHsNCiAgICAgICAgdGhpcy55ZWFyT3B0aW9ucy5wdXNoKGkpOw0KICAgICAgfQ0KICAgICAgdGhpcy5nZXRDb21wYW55U3VtbWFyeURhdGEoKQ0KICAgIH0sDQoNCiAgICAgICAgIC8qKiDlubTku73pgInmi6nlj5jljJYgKi8NCiAgICAgaGFuZGxlWWVhckNoYW5nZSh5ZWFyKSB7DQogICAgICAgdGhpcy5zZWxlY3RlZFllYXIgPSB5ZWFyOw0KICAgICAgIC8vIOmHjeaWsOiOt+WPluaVsOaNrg0KICAgICAgIHRoaXMuZ2V0Q29tcGFueVN1bW1hcnlEYXRhKCk7DQogICAgIH0sDQoNCiAgICAgLyoqIOexu+Wei+mAieaLqeWPmOWMliAqLw0KICAgICBoYW5kbGVUeXBlQ2hhbmdlKHR5cGUpIHsNCiAgICAgICB0aGlzLnNlbGVjdGVkVHlwZSA9IHR5cGU7DQogICAgICAgLy8g6YeN5paw6I635Y+W5pWw5o2uDQogICAgICAgdGhpcy5nZXRDb21wYW55U3VtbWFyeURhdGEoKTsNCiAgICAgfSwNCg0KICAgICAvKiog5rWL6K+V5YiG5Y6C5pWw5o2u5aSE55CGICovDQogICAgIHRlc3RGYWN0b3J5RGF0YVByb2Nlc3NpbmcoKSB7DQogICAgICAgY29uc29sZS5sb2coJ+W8gOWni+a1i+ivleWIhuWOguaVsOaNruWkhOeQhi4uLicpOw0KICAgICAgIA0KICAgICAgIC8vIOa1i+ivlTE6IOe6r+aooeaLn+aVsOaNrg0KICAgICAgIGNvbnNvbGUubG9nKCc9PT0g5rWL6K+VMTog57qv5qih5ouf5pWw5o2uID09PScpOw0KICAgICAgIHRoaXMucHJvY2Vzc0ZhY3RvcnlEYXRhKHRlc3RGYWN0b3J5RGF0YSk7DQogICAgICAgDQogICAgICAgLy8g562J5b6FMuenkuWQjua1i+ivleecn+WunuaVsOaNrg0KICAgICAgIHNldFRpbWVvdXQoKCkgPT4gew0KICAgICAgICAgY29uc29sZS5sb2coJz09PSDmtYvor5UyOiDljIXlkKvnnJ/lrp7mlbDmja4gPT09Jyk7DQogICAgICAgICB0aGlzLnByb2Nlc3NGYWN0b3J5RGF0YSh0ZXN0RmFjdG9yeURhdGFXaXRoUmVhbFZhbHVlcyk7DQogICAgICAgfSwgMjAwMCk7DQogICAgICAgDQogICAgICAgLy8g562J5b6FNOenkuWQjua1i+ivleaVsOaNruaYoOWwhA0KICAgICAgIHNldFRpbWVvdXQoKCkgPT4gew0KICAgICAgICAgY29uc29sZS5sb2coJz09PSDmtYvor5UzOiDmlbDmja7mmKDlsITpqozor4EgPT09Jyk7DQogICAgICAgICB0aGlzLnByb2Nlc3NGYWN0b3J5RGF0YSh0ZXN0RGF0YU1hcHBpbmcpOw0KICAgICAgICAgdGhpcy52ZXJpZnlEYXRhTWFwcGluZygpOw0KICAgICAgIH0sIDQwMDApOw0KICAgICB9LA0KDQogICAgIC8qKiDpqozor4HmlbDmja7mmKDlsIQgKi8NCiAgICAgdmVyaWZ5RGF0YU1hcHBpbmcoKSB7DQogICAgICAgY29uc29sZS5sb2coJ+mqjOivgeaVsOaNruaYoOWwhC4uLicpOw0KICAgICAgIA0KICAgICAgIGlmICh0aGlzLnF1YWxpdHlDb3N0TGlzdC5sZW5ndGggPiAwKSB7DQogICAgICAgICB0aGlzLnF1YWxpdHlDb3N0TGlzdC5mb3JFYWNoKChyb3csIGluZGV4KSA9PiB7DQogICAgICAgICAgIGNvbnNvbGUubG9nKGDnrKwke2luZGV4ICsgMX3ooYwgLSDliIbljoI6ICR7cm93LmNvbXBhbnl9YCk7DQogICAgICAgICAgIGNvbnNvbGUubG9nKGAgIOS4gOaciOmHkeminTogJHtyb3cuamFudWFyeUFtb3VudH0sIOS4gOaciOWQqOS9jTogJHtyb3cuamFudWFyeVBlclRvbn1gKTsNCiAgICAgICAgICAgY29uc29sZS5sb2coYCAg5LqM5pyI6YeR6aKdOiAke3Jvdy5mZWJydWFyeUFtb3VudH0sIOS6jOaciOWQqOS9jTogJHtyb3cuZmVicnVhcnlQZXJUb259YCk7DQogICAgICAgICAgIGNvbnNvbGUubG9nKGAgIOS4ieaciOmHkeminTogJHtyb3cubWFyY2hBbW91bnR9LCDkuInmnIjlkKjkvY06ICR7cm93Lm1hcmNoUGVyVG9ufWApOw0KICAgICAgICAgfSk7DQogICAgICAgfQ0KICAgICB9DQogIH0NCn07DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4SA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/qualityCost/companySummary3", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <!-- 质量成本表格 -->\r\n    <div :style=\"containerStyle\">\r\n      <!-- 标题和产量信息 -->\r\n      <div style=\"position: relative; width: 100%; display: flex; justify-content: center; margin: 20px 0;\">\r\n        <h3 style=\"text-align: center; margin: 0;\">各分厂改判汇总表</h3>\r\n        <div style=\"position: absolute; right: 0; bottom: -5px; font-size: 16px; font-weight: bold; color: #333;\">\r\n          <!-- 类型选择框 -->\r\n          <!-- <el-select \r\n            v-model=\"selectedType\" \r\n            placeholder=\"选择类型\" \r\n            style=\"width: 100px; margin-right: 10px;\"\r\n            @change=\"handleTypeChange\">\r\n            <el-option\r\n              v-for=\"type in typeOptions\"\r\n              :key=\"type\"\r\n              :label=\"type\"\r\n              :value=\"type\">\r\n            </el-option>\r\n          </el-select> -->\r\n          <!-- 年份选择框 -->\r\n          <el-select \r\n            v-model=\"selectedYear\" \r\n            placeholder=\"选择年份\" \r\n            style=\"width: 100px; margin-right: 20px;\"\r\n            @change=\"handleYearChange\">\r\n            <el-option\r\n              v-for=\"year in yearOptions\"\r\n              :key=\"year\"\r\n              :label=\"year + '年'\"\r\n              :value=\"year\">\r\n            </el-option>\r\n          </el-select>\r\n         \r\n        </div>\r\n      </div>\r\n\r\n      <div :style=\"tableContainerStyle\">\r\n        <vxe-table\r\n          :loading=\"loading\"\r\n          :data=\"qualityCostList\"\r\n          border\r\n          v-bind=\"tableHeight ? { height: tableHeight } : {}\"\r\n          :class=\"{ 'no-scroll': !needScrollbar }\"\r\n          style=\"table-layout: fixed; width: 100%; margin: 0 auto; padding: 0;\"\r\n          :header-cell-style=\"{ backgroundColor: '#f5f7fa', color: '#303133' }\"\r\n          :merge-cells=\"mergeCells\"\r\n          stripe\r\n          :auto-resize=\"!tableHeight\"\r\n          :scroll-y=\"{enabled: needScrollbar}\"\r\n          :scroll-x=\"{enabled: true}\">\r\n          <vxe-column title=\"分厂\" align=\"center\" field=\"company\" width=\"10%\" fixed=\"left\">\r\n            <template #default=\"{ row }\">\r\n              <span :style=\"{ fontWeight: 'bold' }\">\r\n                {{ row.company }}\r\n              </span>\r\n            </template>\r\n          </vxe-column>\r\n\r\n          <!-- 一月分组 -->\r\n          <vxe-colgroup title=\"一月\" align=\"center\">\r\n            <vxe-column title=\"金额（元）\" align=\"center\" field=\"januaryAmount\" width=\"7%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatCurrency(row.januaryAmount) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n            <vxe-column title=\"吨位\" align=\"center\" field=\"januaryPerTon\" width=\"7%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatTon(row.januaryPerTon) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n          </vxe-colgroup>\r\n\r\n          <!-- 二月分组 -->\r\n          <vxe-colgroup title=\"二月\" align=\"center\">\r\n            <vxe-column title=\"金额（元）\" align=\"center\" field=\"februaryAmount\" width=\"7%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatCurrency(row.februaryAmount) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n            <vxe-column title=\"吨位\" align=\"center\" field=\"februaryPerTon\" width=\"7%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatTon(row.februaryPerTon) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n          </vxe-colgroup>\r\n\r\n          <!-- 三月分组 -->\r\n          <vxe-colgroup title=\"三月\" align=\"center\">\r\n            <vxe-column title=\"金额（元）\" align=\"center\" field=\"marchAmount\" width=\"7%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatCurrency(row.marchAmount) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n            <vxe-column title=\"吨位\" align=\"center\" field=\"marchPerTon\" width=\"7%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatTon(row.marchPerTon) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n          </vxe-colgroup>\r\n\r\n          <!-- 四月分组 -->\r\n          <vxe-colgroup title=\"四月\" align=\"center\">\r\n            <vxe-column title=\"金额（元）\" align=\"center\" field=\"aprilAmount\" width=\"7%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatCurrency(row.aprilAmount) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n            <vxe-column title=\"吨位\" align=\"center\" field=\"aprilPerTon\" width=\"7%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatTon(row.aprilPerTon) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n          </vxe-colgroup>\r\n\r\n          <!-- 五月分组 -->\r\n          <vxe-colgroup title=\"五月\" align=\"center\">\r\n            <vxe-column title=\"金额（元）\" align=\"center\" field=\"mayAmount\" width=\"7%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatCurrency(row.mayAmount) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n            <vxe-column title=\"吨位\" align=\"center\" field=\"mayPerTon\" width=\"7%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatTon(row.mayPerTon) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n          </vxe-colgroup>\r\n\r\n          <!-- 六月分组 -->\r\n          <vxe-colgroup title=\"六月\" align=\"center\">\r\n            <vxe-column title=\"金额（元）\" align=\"center\" field=\"juneAmount\" width=\"7%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatCurrency(row.juneAmount) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n            <vxe-column title=\"吨位\" align=\"center\" field=\"junePerTon\" width=\"7%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatTon(row.junePerTon) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n          </vxe-colgroup>\r\n\r\n          <!-- 七月分组 -->\r\n          <vxe-colgroup title=\"七月\" align=\"center\">\r\n            <vxe-column title=\"金额（元）\" align=\"center\" field=\"julyAmount\" width=\"7%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatCurrency(row.julyAmount) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n            <vxe-column title=\"吨位\" align=\"center\" field=\"julyPerTon\" width=\"7%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatTon(row.julyPerTon) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n          </vxe-colgroup>\r\n\r\n          <!-- 八月分组 -->\r\n          <vxe-colgroup title=\"八月\" align=\"center\">\r\n            <vxe-column title=\"金额（元）\" align=\"center\" field=\"augustAmount\" width=\"7%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatCurrency(row.augustAmount) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n            <vxe-column title=\"吨位\" align=\"center\" field=\"augustPerTon\" width=\"7%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatTon(row.augustPerTon) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n          </vxe-colgroup>\r\n\r\n          <!-- 九月分组 -->\r\n          <vxe-colgroup title=\"九月\" align=\"center\">\r\n            <vxe-column title=\"金额（元）\" align=\"center\" field=\"septemberAmount\" width=\"7%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatCurrency(row.septemberAmount) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n            <vxe-column title=\"吨位\" align=\"center\" field=\"septemberPerTon\" width=\"7%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatTon(row.septemberPerTon) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n          </vxe-colgroup>\r\n\r\n          <!-- 十月分组 -->\r\n          <vxe-colgroup title=\"十月\" align=\"center\">\r\n            <vxe-column title=\"金额（元）\" align=\"center\" field=\"octoberAmount\" width=\"7%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatCurrency(row.octoberAmount) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n            <vxe-column title=\"吨位\" align=\"center\" field=\"octoberPerTon\" width=\"7%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatTon(row.octoberPerTon) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n          </vxe-colgroup>\r\n\r\n          <!-- 十一月分组 -->\r\n          <vxe-colgroup title=\"十一月\" align=\"center\">\r\n            <vxe-column title=\"金额（元）\" align=\"center\" field=\"novemberAmount\" width=\"7%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatCurrency(row.novemberAmount) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n            <vxe-column title=\"吨位\" align=\"center\" field=\"novemberPerTon\" width=\"7%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatTon(row.novemberPerTon) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n          </vxe-colgroup>\r\n\r\n          <!-- 十二月分组 -->\r\n          <vxe-colgroup title=\"十二月\" align=\"center\">\r\n            <vxe-column title=\"金额（元）\" align=\"center\" field=\"decemberAmount\" width=\"7%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatCurrency(row.decemberAmount) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n            <vxe-column title=\"吨位\" align=\"center\" field=\"decemberPerTon\" width=\"7%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatTon(row.decemberPerTon) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n          </vxe-colgroup>\r\n\r\n          <!-- 年度累计分组 -->\r\n          <vxe-colgroup title=\"年度累计\" align=\"center\">\r\n            <vxe-column title=\"金额（元）\" align=\"center\" field=\"yearlyTotalAmount\" width=\"8%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'bold' }\">\r\n                  {{ formatCurrency(row.yearlyTotalAmount) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n            <vxe-column title=\"吨位\" align=\"center\" field=\"yearlyTotalPerTon\" width=\"8%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'bold' }\">\r\n                  {{ formatTon(row.yearlyTotalPerTon) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n          </vxe-colgroup>\r\n        </vxe-table>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listCompanySummaryData } from \"@/api/qualityCost/companySummary1\";\r\nimport { testFactoryData, testFactoryDataWithRealValues, testDataMapping } from \"./testData\";\r\n\r\n\r\nexport default {\r\n  name: \"CompanySummary1\",\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: false,\r\n      // 质量成本数据列表\r\n      qualityCostList: [],\r\n      // 合并单元格配置\r\n      mergeCells: [],\r\n      // 表格高度\r\n      tableHeight: null,\r\n      // 是否需要滚动条\r\n      needScrollbar: true,\r\n      // 年份选择\r\n      selectedYear: new Date().getFullYear(),\r\n      // 年份选项\r\n      yearOptions: [],\r\n      // 类型选择\r\n      selectedType: '脱合同',\r\n      // 类型选项\r\n      typeOptions: ['改判', '报废', '脱合同', '退货']\r\n    };\r\n  },\r\n  computed: {\r\n    /** 容器样式 */\r\n    containerStyle() {\r\n      return {\r\n        height: this.tableHeight ? `${this.tableHeight}px` : 'auto'\r\n      };\r\n    },\r\n    /** 表格容器样式 */\r\n    tableContainerStyle() {\r\n      return {\r\n        height: this.tableHeight ? `${this.tableHeight - 80}px` : 'auto'\r\n      };\r\n    }\r\n  },\r\n  mounted() {\r\n    this.calculateTableHeight();\r\n    window.addEventListener('resize', this.calculateTableHeight);\r\n    this.generateYearOptions();\r\n  },\r\n  beforeDestroy() {\r\n    window.removeEventListener('resize', this.calculateTableHeight);\r\n  },\r\n  methods: {\r\n    getCompanySummaryData() {\r\n      this.loading = true;\r\n      \r\n      const params = {\r\n        yearMonth: this.selectedYear,\r\n        companySummaryType: this.selectedType\r\n      };\r\n\r\n      listCompanySummaryData(params).then(response => {\r\n        console.log('listCompanySummaryData:', response);\r\n        if (response.data) {\r\n          // 处理分厂数据格式\r\n          this.processFactoryData(response.data);\r\n        } else {\r\n          this.qualityCostList = [];\r\n        }\r\n        this.loading = false;\r\n      }).catch(error => {\r\n        console.error('获取数据失败:', error);\r\n        this.$message.error('获取质量成本数据失败');\r\n        this.loading = false;\r\n      });\r\n    },\r\n\r\n    /** 处理分厂数据格式 */\r\n    processFactoryData(rawData) {\r\n      console.log('开始处理分厂数据:', rawData);\r\n      \r\n      // 检查是否为分厂数据格式\r\n      if (this.isFactoryDataFormat(rawData)) {\r\n        console.log('检测到分厂数据格式，开始处理...');\r\n        \r\n        // 转换为表格格式\r\n        const tableData = this.convertFactoryDataToTable(rawData);\r\n        \r\n        // 更新表格数据\r\n        this.updateTableWithFactoryData(tableData);\r\n      } else {\r\n        console.log('不是分厂数据格式，使用默认处理');\r\n        this.qualityCostList = [];\r\n      }\r\n    },\r\n\r\n    /** 检查是否为分厂数据格式 */\r\n    isFactoryDataFormat(data) {\r\n      if (!data || typeof data !== 'object') return false;\r\n      \r\n      const factoryKeys = Object.keys(data);\r\n      if (factoryKeys.length === 0) return false;\r\n      \r\n      const firstFactory = data[factoryKeys[0]];\r\n      if (!firstFactory || typeof firstFactory !== 'object') return false;\r\n      \r\n      const monthKeys = Object.keys(firstFactory);\r\n      if (monthKeys.length === 0) return false;\r\n      \r\n      const firstMonth = firstFactory[monthKeys[0]];\r\n      return firstMonth && \r\n             typeof firstMonth === 'object' && \r\n             'costEx' in firstMonth && \r\n             'costTon' in firstMonth;\r\n    },\r\n\r\n    /** 清理模拟数据 */\r\n    cleanMockData(data) {\r\n      const cleanedData = {};\r\n      \r\n      Object.keys(data).forEach(factoryName => {\r\n        const factoryData = data[factoryName];\r\n        const validMonths = {};\r\n        \r\n        Object.keys(factoryData).forEach(monthKey => {\r\n          const monthData = factoryData[monthKey];\r\n          \r\n          // 检查是否为模拟数据\r\n          if (!this.isMockData(monthData)) {\r\n            validMonths[monthKey] = monthData;\r\n          }\r\n        });\r\n        \r\n        // 只有当分厂有有效数据时才保留\r\n        if (Object.keys(validMonths).length > 0) {\r\n          cleanedData[factoryName] = validMonths;\r\n        }\r\n      });\r\n      \r\n      console.log('清理后的数据:', cleanedData);\r\n      return cleanedData;\r\n    },\r\n\r\n    /** 判断是否为模拟数据 */\r\n    isMockData(monthData) {\r\n      return (\r\n        monthData.costCenterName === null &&\r\n        monthData.costEx === 0 &&\r\n        monthData.costTon === 0 &&\r\n        monthData.yearMonth === null\r\n      );\r\n    },\r\n\r\n    /** 将分厂数据转换为表格格式 */\r\n    convertFactoryDataToTable(factoryData) {\r\n      const tableData = [];\r\n      \r\n      Object.keys(factoryData).forEach(factoryName => {\r\n        const factoryMonths = factoryData[factoryName];\r\n        \r\n        Object.keys(factoryMonths).forEach(monthKey => {\r\n          const monthData = factoryMonths[monthKey];\r\n          \r\n          // 创建表格行数据\r\n          const rowData = {\r\n            factoryName: factoryName,\r\n            month: monthKey,\r\n            costEx: monthData.costEx || 0,\r\n            costTon: monthData.costTon || 0,\r\n            yearMonth: monthData.yearMonth,\r\n            costCenterName: monthData.costCenterName\r\n          };\r\n          \r\n          tableData.push(rowData);\r\n        });\r\n      });\r\n      \r\n      console.log('转换后的表格数据:', tableData);\r\n      return tableData;\r\n    },\r\n\r\n    /** 更新表格显示分厂数据 */\r\n    updateTableWithFactoryData(tableData) {\r\n      if (tableData.length === 0) {\r\n        this.qualityCostList = [];\r\n        return;\r\n      }\r\n      \r\n      // 获取所有分厂名称\r\n      const factoryNames = [...new Set(tableData.map(row => row.factoryName))];\r\n      \r\n      // 创建表格行数据\r\n      this.qualityCostList = factoryNames.map(factoryName => {\r\n        const rowData = {\r\n          company: factoryName\r\n        };\r\n        \r\n        // 初始化所有月份的数据为0\r\n        const months = ['january', 'february', 'march', 'april', 'may', 'june',\r\n                       'july', 'august', 'september', 'october', 'november', 'december'];\r\n        \r\n        months.forEach((month, index) => {\r\n          const monthNumber = index + 1;\r\n          const monthData = tableData.find(row => \r\n            row.factoryName === factoryName && row.month === monthNumber.toString()\r\n          );\r\n          \r\n          if (monthData) {\r\n            rowData[`${month}Amount`] = monthData.costEx;\r\n            rowData[`${month}PerTon`] = monthData.costTon;\r\n          } else {\r\n            rowData[`${month}Amount`] = 0;\r\n            rowData[`${month}PerTon`] = 0;\r\n          }\r\n        });\r\n        \r\n        // 计算年度累计\r\n        rowData.yearlyTotalAmount = months.reduce((sum, month) => sum + (rowData[`${month}Amount`] || 0), 0);\r\n        rowData.yearlyTotalPerTon = months.reduce((sum, month) => sum + (rowData[`${month}PerTon`] || 0), 0);\r\n        \r\n        return rowData;\r\n      });\r\n      \r\n      console.log('更新后的表格数据:', this.qualityCostList);\r\n    },\r\n\r\n\r\n    /** 格式化货币 */\r\n    formatCurrency(value) {\r\n      if (value === null || value === undefined || value === '' || Number(value) === 0) {\r\n        return '-';\r\n      }\r\n      const num = Number(value);\r\n      // 金额显示为元，不转换为万\r\n      return num.toLocaleString('zh-CN', {\r\n        minimumFractionDigits: 2,\r\n        maximumFractionDigits: 2\r\n      });\r\n    },\r\n\r\n    /** 格式化吨位 */\r\n    formatTon(value) {\r\n      if (value === null || value === undefined || value === '' || Number(value) === 0) {\r\n        return '-';\r\n      }\r\n      const num = Number(value);\r\n      // 吨位显示为吨，保留2位小数\r\n      return num.toLocaleString('zh-CN', {\r\n        minimumFractionDigits: 2,\r\n        maximumFractionDigits: 2\r\n      });\r\n    },\r\n\r\n    /** 计算表格高度 */\r\n    calculateTableHeight() {\r\n      this.$nextTick(() => {\r\n        const windowHeight = window.innerHeight;\r\n        // 减去页面头部、标题等高度，大约100px\r\n        const availableHeight = windowHeight - 100;\r\n        // 设置表格最大高度，最小400px，最大不超过可用高度的90%\r\n        this.tableHeight = Math.max(400, Math.min(800, availableHeight * 0.9));\r\n        this.needScrollbar = this.qualityCostList.length > 8;\r\n      });\r\n    },\r\n\r\n    /** 生成年份选项 */\r\n    generateYearOptions() {\r\n      const currentYear = new Date().getFullYear();\r\n      for (let i = 2000; i <= currentYear; i++) {\r\n        this.yearOptions.push(i);\r\n      }\r\n      this.getCompanySummaryData()\r\n    },\r\n\r\n         /** 年份选择变化 */\r\n     handleYearChange(year) {\r\n       this.selectedYear = year;\r\n       // 重新获取数据\r\n       this.getCompanySummaryData();\r\n     },\r\n\r\n     /** 类型选择变化 */\r\n     handleTypeChange(type) {\r\n       this.selectedType = type;\r\n       // 重新获取数据\r\n       this.getCompanySummaryData();\r\n     },\r\n\r\n     /** 测试分厂数据处理 */\r\n     testFactoryDataProcessing() {\r\n       console.log('开始测试分厂数据处理...');\r\n       \r\n       // 测试1: 纯模拟数据\r\n       console.log('=== 测试1: 纯模拟数据 ===');\r\n       this.processFactoryData(testFactoryData);\r\n       \r\n       // 等待2秒后测试真实数据\r\n       setTimeout(() => {\r\n         console.log('=== 测试2: 包含真实数据 ===');\r\n         this.processFactoryData(testFactoryDataWithRealValues);\r\n       }, 2000);\r\n       \r\n       // 等待4秒后测试数据映射\r\n       setTimeout(() => {\r\n         console.log('=== 测试3: 数据映射验证 ===');\r\n         this.processFactoryData(testDataMapping);\r\n         this.verifyDataMapping();\r\n       }, 4000);\r\n     },\r\n\r\n     /** 验证数据映射 */\r\n     verifyDataMapping() {\r\n       console.log('验证数据映射...');\r\n       \r\n       if (this.qualityCostList.length > 0) {\r\n         this.qualityCostList.forEach((row, index) => {\r\n           console.log(`第${index + 1}行 - 分厂: ${row.company}`);\r\n           console.log(`  一月金额: ${row.januaryAmount}, 一月吨位: ${row.januaryPerTon}`);\r\n           console.log(`  二月金额: ${row.februaryAmount}, 二月吨位: ${row.februaryPerTon}`);\r\n           console.log(`  三月金额: ${row.marchAmount}, 三月吨位: ${row.marchPerTon}`);\r\n         });\r\n       }\r\n     }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n/* 页面容器样式 */\r\n.app-container {\r\n  position: relative;\r\n  padding: 20px;\r\n}\r\n\r\n/* 表格容器样式 */\r\n.table-container {\r\n  margin-top: 20px;\r\n}\r\n\r\n/* 表格滚动容器 */\r\n.table-scroll-container {\r\n  width: 100%;\r\n  position: relative;\r\n  border: 1px solid #ebeef5;\r\n  border-radius: 4px;\r\n}\r\n\r\n/* 表格单元格内容 */\r\n.table-cell-content {\r\n  padding: 10px 0;\r\n}\r\n</style>\r\n\r\n<style>\r\n/* 非scoped样式，确保能够覆盖vxe-table的默认样式 */\r\n\r\n/* 强制覆盖所有滚动条样式 - 15px粗细，淡蓝色主题 */\r\n.vxe-table ::-webkit-scrollbar {\r\n  width: 15px !important;\r\n  height: 15px !important;\r\n}\r\n\r\n.vxe-table ::-webkit-scrollbar-track {\r\n  background: #e3f2fd !important;\r\n  border-radius: 7px !important;\r\n}\r\n\r\n.vxe-table ::-webkit-scrollbar-thumb {\r\n  background: #64b5f6 !important;\r\n  border-radius: 7px !important;\r\n  border: none !important;\r\n}\r\n\r\n.vxe-table ::-webkit-scrollbar-thumb:hover {\r\n  background: #42a5f5 !important;\r\n}\r\n\r\n.vxe-table ::-webkit-scrollbar-thumb:active {\r\n  background: #2196f3 !important;\r\n}\r\n\r\n.vxe-table ::-webkit-scrollbar-corner {\r\n  background: #e3f2fd !important;\r\n}\r\n\r\n/* vxe-table 表格边框和样式 */\r\n.vxe-table ::v-deep .vxe-table {\r\n  border: 1px solid #ebeef5;\r\n}\r\n\r\n.vxe-table ::v-deep .vxe-table--border-line {\r\n  border-color: #ebeef5;\r\n}\r\n\r\n/* 表头样式 */\r\n.vxe-table ::v-deep .vxe-table--header-wrapper .vxe-table--header {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n.vxe-table ::v-deep .vxe-table--header .vxe-header--column {\r\n  background-color: #f5f7fa;\r\n  color: #303133;\r\n  font-weight: 500;\r\n}\r\n\r\n/* 表头分组样式 */\r\n.vxe-table ::v-deep .vxe-table--header .vxe-header--group {\r\n  background-color: #e8f4fd !important;\r\n  color: #1890ff !important;\r\n  font-weight: 600 !important;\r\n  border: 1px solid #d1e7ff !important;\r\n}\r\n\r\n/* 确保分组表头显示 */\r\n.vxe-table ::v-deep .vxe-table--header .vxe-header--group .vxe-header--group-title {\r\n  background-color: #e8f4fd !important;\r\n  color: #1890ff !important;\r\n  font-weight: 600 !important;\r\n  padding: 8px 4px !important;\r\n  text-align: center !important;\r\n}\r\n\r\n/* 子列表头样式 */\r\n.vxe-table ::v-deep .vxe-table--header .vxe-header--column {\r\n  background-color: #f5f7fa !important;\r\n  color: #303133 !important;\r\n  font-weight: 500 !important;\r\n  border: 1px solid #ebeef5 !important;\r\n}\r\n\r\n/* 确保表头分组正确显示 */\r\n.vxe-table ::v-deep .vxe-table--header-wrapper {\r\n  border: 1px solid #ebeef5 !important;\r\n}\r\n\r\n.vxe-table ::v-deep .vxe-table--header {\r\n  border-bottom: 1px solid #ebeef5 !important;\r\n}\r\n\r\n/* 强制显示分组表头 */\r\n.vxe-table ::v-deep .vxe-table--header .vxe-header--group {\r\n  display: table-cell !important;\r\n  vertical-align: middle !important;\r\n}\r\n\r\n/* 分组表头文字样式 */\r\n.vxe-table ::v-deep .vxe-header--group-title {\r\n  display: block !important;\r\n  width: 100% !important;\r\n  text-align: center !important;\r\n  font-size: 14px !important;\r\n  line-height: 1.5 !important;\r\n}\r\n\r\n/* 确保表头分组边框显示 */\r\n.vxe-table ::v-deep .vxe-table--header .vxe-header--group {\r\n  border-right: 1px solid #d1e7ff !important;\r\n  border-bottom: 1px solid #d1e7ff !important;\r\n}\r\n\r\n/* 最后一个分组表头右边框 */\r\n.vxe-table ::v-deep .vxe-table--header .vxe-header--group:last-child {\r\n  border-right: 1px solid #ebeef5 !important;\r\n}\r\n\r\n/* 确保表头文字居中 */\r\n.vxe-table ::v-deep .vxe-table--header .vxe-header--column .vxe-cell--title {\r\n  text-align: center !important;\r\n  display: flex !important;\r\n  align-items: center !important;\r\n  justify-content: center !important;\r\n  height: 100% !important;\r\n}\r\n</style> "]}]}