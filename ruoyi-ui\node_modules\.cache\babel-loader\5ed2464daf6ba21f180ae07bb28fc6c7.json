{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\dataReport\\answer\\answerShow.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\dataReport\\answer\\answerShow.vue", "mtime": 1756099891059}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\babel.config.js", "mtime": 1688548084091}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_form", "require", "_input", "_interopRequireDefault", "_answer", "_dimensionality", "_auth", "_axios", "xlsx", "_interopRequireWildcard", "name", "components", "answerInput", "data", "pickerOptions", "disabledDate", "time", "getTime", "Date", "now", "frequencyOptions", "queryParams", "_defineProperty2", "default", "formQuestion", "undefined", "fcDate", "dimensionalityId", "formType", "dimensionalityNames", "dimensionalityName", "specialFcDate", "drawerShow", "stickyTop", "loading", "showSearch", "answerList", "formData", "row", "rootList", "userInfo", "datesave", "pathsave", "deptName", "dateValue", "queryImport", "startDate", "endDate", "rootId", "count", "importOpen", "SpecialImportOpen", "connectOpen", "preViewOpen", "upload", "isUploading", "headers", "Authorization", "getToken", "url", "process", "env", "VUE_APP_BASE_API", "uploadSpecial", "excelHtml", "searchopen", "excelData", "exceltitle", "customBlobContent", "mounted", "JSON", "parse", "stringify", "$store", "state", "user", "created", "$route", "query", "initData", "methods", "onDateChange", "console", "log", "clickNode", "$event", "node", "target", "parentElement", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "click", "changeEvent", "params", "$form", "$refs", "formRef", "updateStatus", "inputChange", "val", "formValue", "handleScroll", "isSticky", "window", "scrollY", "_this", "getAllRootList", "then", "res", "i", "length", "value", "deptCode", "getList", "initData1", "_this2", "getAllRootListForAnswer", "_this3", "formFrequency", "answerstatuslistAdmin", "list", "formId", "dimensionalityPath", "dimensionalityPaths", "Pathset", "for<PERSON>ach", "x", "includes", "push", "originalName", "sortKey", "title", "group", "filter", "item", "$forceUpdate", "handleQuery", "label", "handleDateChange", "reset<PERSON><PERSON>y", "handlePreview", "_this4", "type", "downloadXlsx", "_objectSpread2", "blob", "reader", "FileReader", "readAsA<PERSON>y<PERSON><PERSON>er", "onload", "evt", "result", "ints", "Uint8Array", "slice", "size", "workBook", "read", "sheetNames", "SheetNames", "sheetName", "workSheet", "Sheets", "excelTable", "utils", "sheet_to_json", "tableThead", "Array", "from", "Object", "keys", "map", "handlePreview1", "_this5", "$notify", "error", "message", "handleUpload", "_ref", "file", "FormData", "append", "axios", "post", "handleSubmit", "_this6", "processedLists", "reduce", "acc", "current", "concat", "status", "parseFloat", "allLists", "newAdd", "msgSuccess", "handleFileUploadProgress", "handleFileSuccess", "response", "code", "$modal", "msgError", "downloadTemplate", "downloadFile", "downloadTemplateSpecial", "downloadConnectTemplate", "containsSubstring", "substring", "string", "aloneList"], "sources": ["src/views/dataReport/answer/answerShow.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <el-form\r\n      :model=\"queryParams\"\r\n      ref=\"queryForm\"\r\n      :inline=\"true\"\r\n      v-show=\"showSearch\"\r\n    >\r\n      <el-row :gutter=\"20\" style=\"margin: 20px\">\r\n        <el-form-item label=\"报表名称\" prop=\"dimensionalityId\">\r\n          <el-select\r\n            v-model=\"queryParams.dimensionalityId\"\r\n            placeholder=\"请选择\"\r\n            filterable\r\n            @change=\"handleQuery\"\r\n          >\r\n            <el-option\r\n              v-for=\"item in rootList\"\r\n              :key=\"item.value\"\r\n              :label=\"item.label\"\r\n              :value=\"item.value\"\r\n            >\r\n            </el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"填报时间\" v-if=\"count == '1'\"  >\r\n          <el-date-picker\r\n            v-model=\"queryParams.fcDate\"\r\n            value-format=\"yyyy-MM-dd\"\r\n            type=\"date\"\r\n            :default-value=\"new Date()\"\r\n            :picker-options=\"pickerOptions\"\r\n            placeholder=\"选择时间\"\r\n            @change=\"handleDateChange\"\r\n          >\r\n          </el-date-picker>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"填报时间\" v-if=\"count == '0'\">\r\n          <el-date-picker\r\n            v-model=\"queryParams.fcDate\"\r\n            value-format=\"yyyy-MM-01\"\r\n            type=\"month\"\r\n            :default-value=\"new Date()\"\r\n            :picker-options=\"pickerOptions\"\r\n            placeholder=\"选择时间\"\r\n            @change=\"handleDateChange\"\r\n          >\r\n          </el-date-picker>\r\n        </el-form-item>\r\n        <el-form-item label=\"填报问题\" prop=\"formQuestion\">\r\n          <el-input\r\n            v-model=\"queryParams.formQuestion\"\r\n            placeholder=\"请输入填报问题\"\r\n            clearable\r\n            size=\"small\"\r\n            @keyup.enter.native=\"handleQuery\"\r\n          />\r\n        </el-form-item>\r\n\r\n        <el-form-item>\r\n          <el-button\r\n            type=\"cyan\"\r\n            icon=\"el-icon-search\"\r\n            size=\"mini\"\r\n            @click=\"handleQuery\"\r\n            >搜索</el-button\r\n          >\r\n          <el-form-item>\r\n\r\n\r\n\r\n          </el-form-item>\r\n          <!-- <el-button\r\n            type=\"warning\"\r\n            icon=\"el-icon-download\"\r\n            size=\"mini\"\r\n            @click=\"importOpen = true\"\r\n            >数据导入导出</el-button\r\n          > -->\r\n        </el-form-item>\r\n      </el-row>\r\n      <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-button v-if=\"aloneList(dimensionalityName)  || containsSubstring('安全责任工资',dimensionalityName) \"\r\n            type=\"danger\"\r\n            icon=\"el-icon-download\"\r\n            size=\"mini\"\r\n            @click=\"SpecialImportOpen = true\"\r\n            >单周期报表导出</el-button\r\n          >\r\n\r\n          <el-button v-if=\"aloneList(dimensionalityName) || containsSubstring('安全责任工资',dimensionalityName) \"\r\n            type=\"warning\"\r\n            icon=\"el-icon-search\"\r\n            size=\"mini\"\r\n            @click=\"handlePreview\"\r\n            >数据预览</el-button\r\n          >\r\n          <el-button v-if=\"queryParams.dimensionalityId == 456 \"\r\n            type=\"danger\"\r\n            icon=\"el-icon-download\"\r\n            size=\"mini\"\r\n            @click=\"connectOpen = true\"\r\n            >格式化报表导出</el-button\r\n          >\r\n          <el-button v-if=\"queryParams.dimensionalityId == 456 \"\r\n            type=\"warning\"\r\n            icon=\"el-icon-search\"\r\n            size=\"mini\"\r\n            @click=\"preViewOpen = true\"\r\n            >格式化数据预览</el-button\r\n          >\r\n          <!-- <el-button \r\n            type=\"danger\"\r\n            icon=\"el-icon-download\"\r\n            size=\"mini\"\r\n            @click=\"SpecialImportOpen = true\"\r\n            >报表导出测试</el-button\r\n          > -->\r\n      </el-row>\r\n      <el-row :gutter=\"10\" class=\"mb8\">\r\n        <!-- <el-col :span=\"1.5\">管理部门：{{deptName}}\r\n        </el-col> -->\r\n        <right-toolbar\r\n          :showSearch.sync=\"showSearch\"\r\n          @queryTable=\"getList\"\r\n        ></right-toolbar>\r\n      </el-row>\r\n    </el-form>\r\n\r\n    <vxe-form\r\n      ref=\"formRef\"\r\n      :data=\"formData\"\r\n      @submit=\"handleSubmit\"\r\n      border\r\n      title-background\r\n      vertical-align=\"center\"\r\n      title-width=\"300\"\r\n      title-bold\r\n    >\r\n      <vxe-form-group\r\n        v-for=\"(group, index) in answerList\"\r\n        :key=\"index\"\r\n        span=\"24\"\r\n        :title=\"group.title\"\r\n        title-bold\r\n        vertical\r\n      >\r\n        <vxe-form-item\r\n          v-for=\"(question, qIndex) in group.list\"\r\n          :key=\"qIndex\"\r\n          :title=\"question.formQuestion\"\r\n          :field=\"answerList[index].list[qIndex].formValue\"\r\n          :span=\"question.formType == '3' ? 24 : 12\"\r\n          :item-render=\"{}\"\r\n        >\r\n          <template #default=\"params\">\r\n            <vxe-tag\r\n              v-if=\"question.status == '0'\"\r\n              status=\"primary\"\r\n              content=\"主要颜色\"\r\n              >待审核</vxe-tag\r\n            >\r\n            <vxe-tag\r\n              v-if=\"question.status == '1'\"\r\n              status=\"warning\"\r\n              content=\"信息颜色\"\r\n              >审核中</vxe-tag\r\n            >\r\n            <vxe-tag\r\n              v-if=\"question.status == '2'\"\r\n              status=\"success\"\r\n              content=\"信息颜色\"\r\n              >审核完成</vxe-tag\r\n            >\r\n            <vxe-tag\r\n              v-if=\"question.status == '3'\"\r\n              status=\"danger\"\r\n              content=\"警告颜色\"\r\n              >驳回理由: {{ question.assessment }}</vxe-tag\r\n            >\r\n\r\n            <vxe-input\r\n              v-if=\"question.formType == '0'\"\r\n              v-model=\"answerList[index].list[qIndex].formValue\"\r\n              @change=\"changeEvent(params)\"\r\n              type=\"integer\"\r\n            ></vxe-input>\r\n            <vxe-input\r\n              v-if=\"question.formType == '1'\"\r\n              v-model=\"answerList[index].list[qIndex].formValue\"\r\n              @change=\"changeEvent(params)\"\r\n              type=\"'float'\"\r\n            ></vxe-input>\r\n            <vxe-input\r\n              v-if=\"question.formType == '2'\"\r\n              v-model=\"answerList[index].list[qIndex].formValue\"\r\n              @change=\"changeEvent(params)\"\r\n              type=\"text\"\r\n            ></vxe-input>\r\n            <vxe-textarea\r\n              v-if=\"question.formType == '3'\"\r\n              v-model=\"answerList[index].list[qIndex].formValue\"\r\n              :placeholder=\"question.formQuestion\"\r\n            ></vxe-textarea>\r\n            <vxe-text v-if=\"question.formNote != null\" status=\"warning\"\r\n              >问题指标:{{ question.formNote }}<br\r\n            /></vxe-text>\r\n            <vxe-text v-if=\"question.formNote1 != null\" status=\"warning\"\r\n              >问题备注:{{ question.formNote1 }}<br\r\n            /></vxe-text>\r\n            <vxe-text v-if=\"question.maximum != null\" status=\"primary\"\r\n              >最大值:{{ question.maximum }}<br\r\n            /></vxe-text>\r\n            <vxe-text v-if=\"question.minimum != null\" status=\"primary\"\r\n              >最小值:{{ question.minimum }}<br\r\n            /></vxe-text>\r\n            <vxe-text v-if=\"question.formUnit != null\" status=\"primary\"\r\n              >问题单位:{{ question.formUnit }}<br\r\n            /></vxe-text>\r\n            <vxe-tag\r\n              v-if=\"\r\n                question.formValue != null &&\r\n                question.formValue != '' &&\r\n                (question.formType == '0' || question.formType == '1') &&\r\n                ((question.minimum != null &&\r\n                  question.formValue < question.minimum) ||\r\n                  (question.maximum != null &&\r\n                    question.formValue > question.maximum))\r\n              \"\r\n              status=\"danger\"\r\n              content=\"警告颜色\"\r\n              >输入值超出预计范围，请输入原因和改进措施</vxe-tag\r\n            >\r\n            <vxe-textarea\r\n              v-if=\"\r\n                question.formValue != null &&\r\n                question.formValue != '' &&\r\n                (question.formType == '0' || question.formType == '1') &&\r\n                ((question.minimum != null &&\r\n                  question.formValue < question.minimum) ||\r\n                  (question.maximum != null &&\r\n                    question.formValue > question.maximum))\r\n              \"\r\n              v-model=\"answerList[index].list[qIndex].reason\"\r\n              @change=\"changeEvent(params)\"\r\n              placeholder=\"请填写原因\"\r\n            ></vxe-textarea>\r\n            <vxe-textarea\r\n              v-if=\"\r\n                question.formValue != null &&\r\n                question.formValue != '' &&\r\n                (question.formType == '0' || question.formType == '1') &&\r\n                ((question.minimum != null &&\r\n                  question.formValue < question.minimum) ||\r\n                  (question.maximum != null &&\r\n                    question.formValue > question.maximum))\r\n              \"\r\n              v-model=\"answerList[index].list[qIndex].measure\"\r\n              @change=\"changeEvent(params)\"\r\n              placeholder=\"请输入改进措施\"\r\n            ></vxe-textarea>\r\n          </template>\r\n        </vxe-form-item>\r\n      </vxe-form-group>\r\n\r\n      <!-- <vxe-form-item align=\"center\" span=\"24\" :item-render=\"{}\">\r\n        <template #default>\r\n          <vxe-button\r\n            type=\"submit\"\r\n            status=\"primary\"\r\n            content=\"提交\"\r\n          ></vxe-button>\r\n        </template>\r\n      </vxe-form-item> -->\r\n    </vxe-form>\r\n\r\n    <el-dialog\r\n      title=\"选择导出范围\"\r\n      :visible.sync=\"importOpen\"\r\n      width=\"400px\"\r\n      append-to-body\r\n      destroy-on-close\r\n    >\r\n      <span>数据日期范围：</span>\r\n      <el-date-picker\r\n        v-model=\"dateValue\"\r\n        type=\"daterange\"\r\n        range-separator=\"至\"\r\n        start-placeholder=\"开始日期\"\r\n        end-placeholder=\"结束日期\"\r\n        value-format=\"yyyy-MM-dd\"\r\n        @change=\"onDateChange\"\r\n      >\r\n      </el-date-picker>\r\n      <el-row :gutter=\"10\" style=\"margin-top: 10px;\">\r\n       \r\n        <el-col :span=\"1.5\">\r\n          <el-button\r\n            size=\"small\"\r\n            type=\"info\"\r\n            plain\r\n            icon=\"el-icon-link\"\r\n            @click=\"downloadTemplate\"\r\n            >数据下载</el-button\r\n          >\r\n        </el-col>\r\n         <!-- <el-col :span=\"1.5\" >\r\n          <el-upload\r\n            accept=\".xlsx, .xls\"\r\n            :headers=\"upload.headers\"\r\n            :disabled=\"upload.isUploading\"\r\n            :action=\"upload.url\"\r\n            :show-file-list=\"false\"\r\n            :multiple=\"false\"\r\n            :on-progress=\"handleFileUploadProgress\"\r\n            :on-success=\"handleFileSuccess\"\r\n          >\r\n            <el-button size=\"small\" type=\"warning\" plain icon=\"el-icon-download\"\r\n              >表格导入</el-button\r\n            >\r\n          </el-upload>\r\n        </el-col> -->\r\n      </el-row>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"importOpen = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <el-dialog\r\n      title=\"单周期报表导出\"\r\n      :visible.sync=\"SpecialImportOpen\"\r\n      width=\"400px\"\r\n      append-to-body\r\n      destroy-on-close\r\n    >\r\n      <span>选择导出时间：</span>\r\n      <el-date-picker\r\n            v-model=\"specialFcDate\"\r\n            value-format=\"yyyy-MM-dd\"\r\n            type=\"date\"\r\n            :default-value=\"new Date()\"\r\n            placeholder=\"选择时间\"\r\n          >\r\n      </el-date-picker>\r\n      <el-row :gutter=\"10\" style=\"margin-top: 10px;\">\r\n       \r\n        <el-col :span=\"1.5\">\r\n          <el-button\r\n            size=\"small\"\r\n            type=\"info\"\r\n            plain\r\n            icon=\"el-icon-link\"\r\n            @click=\"downloadTemplateSpecial\"\r\n            >数据下载</el-button\r\n          >\r\n        </el-col>\r\n         <!-- <el-col :span=\"1.5\" >\r\n          <el-upload\r\n            accept=\".xlsx, .xls\"\r\n            :headers=\"uploadSpecial.headers\"\r\n            :disabled=\"uploadSpecial.isUploading\"\r\n            :action=\"uploadSpecial.url\"\r\n            :show-file-list=\"false\"\r\n            :multiple=\"false\"\r\n            :on-progress=\"handleFileUploadProgress\"\r\n            :on-success=\"handleFileSuccess\"\r\n          >\r\n            <el-button size=\"small\" type=\"warning\" plain icon=\"el-icon-download\"\r\n              >表格导入</el-button\r\n            >\r\n          </el-upload>\r\n        </el-col> -->\r\n      </el-row>\r\n      <!-- <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"SpecialImportOpen = false\">取 消</el-button>\r\n      </div> -->\r\n    </el-dialog>\r\n\r\n    <el-dialog\r\n      title=\"选择导出范围\"\r\n      :visible.sync=\"connectOpen\"\r\n      width=\"400px\"\r\n      append-to-body\r\n      destroy-on-close\r\n    >\r\n      <span>数据日期范围：</span>\r\n      <el-date-picker\r\n        v-model=\"dateValue\"\r\n        type=\"daterange\"\r\n        range-separator=\"至\"\r\n        start-placeholder=\"开始日期\"\r\n        end-placeholder=\"结束日期\"\r\n        value-format=\"yyyy-MM-dd\"\r\n        @change=\"onDateChange\"\r\n      >\r\n      </el-date-picker>\r\n      <el-row :gutter=\"10\" style=\"margin-top: 10px;\">\r\n       \r\n        <el-col :span=\"1.5\">\r\n          <el-button\r\n            size=\"small\"\r\n            type=\"info\"\r\n            plain\r\n            icon=\"el-icon-link\"\r\n            @click=\"downloadConnectTemplate\"\r\n            >数据下载</el-button\r\n          >\r\n        </el-col>\r\n      </el-row>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"importOpen = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <el-dialog\r\n      title=\"选择预览时间范围\"\r\n      :visible.sync=\"preViewOpen\"\r\n      width=\"400px\"\r\n      append-to-body\r\n      destroy-on-close\r\n    >\r\n      <span>数据日期范围：</span>\r\n      <el-date-picker\r\n        v-model=\"dateValue\"\r\n        type=\"daterange\"\r\n        range-separator=\"至\"\r\n        start-placeholder=\"开始日期\"\r\n        end-placeholder=\"结束日期\"\r\n        value-format=\"yyyy-MM-dd\"\r\n        @change=\"onDateChange\"\r\n      >\r\n      </el-date-picker>\r\n      <el-row :gutter=\"10\" style=\"margin-top: 10px;\">\r\n       \r\n        <el-col :span=\"1.5\">\r\n          <el-button\r\n            size=\"small\"\r\n            type=\"info\"\r\n            plain\r\n            icon=\"el-icon-link\"\r\n            @click=\"handlePreview1\"\r\n            >数据预览</el-button\r\n          >\r\n        </el-col>\r\n      </el-row>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"importOpen = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <el-dialog  title=\"数据预览\"  :visible.sync=\"searchopen\" width=\"1800px\" >\r\n        <div class=\"test\">\r\n          <vue-office-excel\r\n              :src=\"customBlobContent\"\r\n              style=\"height: 100vh;\"\r\n          />\r\n        </div>\r\n    </el-dialog>\r\n\r\n  </div>\r\n</template>\r\n  \r\n  <script>\r\nimport { answerstatuslistAdmin ,formFrequency} from \"@/api/tYjy/form\";\r\nimport answerInput from \"./input\";\r\nimport { newAdd, addAlone } from \"@/api/tYjy/answer\";\r\nimport { getAllRootListForAnswer,getAllRootList } from \"@/api/tYjy/dimensionality\";\r\nimport { getToken } from \"@/utils/auth\";\r\nimport axios from \"axios\";\r\nimport * as xlsx from 'xlsx';\r\nexport default {\r\n  name: \"Answer\",\r\n  components: { answerInput },\r\n  data() {\r\n    return {\r\n      pickerOptions: {\r\n        disabledDate(time) {\r\n          return time.getTime() > Date.now();\r\n        },\r\n      },\r\n      frequencyOptions: [],\r\n\r\n      queryParams: {\r\n        formQuestion: undefined,\r\n        fcDate: undefined,\r\n        dimensionalityId: undefined,\r\n        formQuestion: undefined,\r\n      },\r\n\r\n      formType: null,\r\n      dimensionalityNames: null,\r\n      dimensionalityName: null,\r\n      specialFcDate:null,\r\n      drawerShow: false,\r\n      stickyTop: 0,\r\n      loading: false,\r\n      showSearch: true,\r\n      answerList: [],\r\n      formData: {},\r\n      row: {},\r\n      rootList: [],\r\n      userInfo: {},\r\n      datesave:{},\r\n      pathsave:{},\r\n      deptName:null,\r\n      dateValue: null,\r\n      queryImport: {\r\n        startDate: null,\r\n        endDate: null,\r\n        rootId: null,\r\n      },\r\n      count:1,\r\n      importOpen:false,\r\n      SpecialImportOpen:false,\r\n      connectOpen:false,\r\n      preViewOpen:false,\r\n      // 导入参数\r\n      upload: {\r\n        // 是否禁用上传\r\n        isUploading: false,\r\n        // 设置上传的请求头部\r\n        headers: { Authorization: \"Bearer \" + getToken() },\r\n        // 上传的地址\r\n        url:\r\n          process.env.VUE_APP_BASE_API +\r\n          \"/web/TYjy/answer/importData\",\r\n\r\n      },\r\n\r\n      uploadSpecial: {\r\n        // 是否禁用上传\r\n        isUploading: false,\r\n        // 设置上传的请求头部\r\n        headers: { Authorization: \"Bearer \" + getToken() },\r\n        // 上传的地址\r\n        url:\r\n          process.env.VUE_APP_BASE_API +\r\n          \"/web/TYjy/answer/importDataSpecial\",\r\n\r\n      },\r\n      excelHtml:\"\",\r\n      searchopen:false,\r\n      excelData: [], // 存储 Excel 数据\r\n      exceltitle: [],\r\n      customBlobContent:\"\"\r\n    };\r\n  },\r\n  mounted() {\r\n    this.userInfo = JSON.parse(JSON.stringify(this.$store.state.user));\r\n  },\r\n\r\n  created() {\r\n    const dimensionalityId = this.$route.query && this.$route.query.dimensionalityId;\r\n    const fcDate = this.$route.query && this.$route.query.fcDate;\r\n\r\n    this.dimensionalityName=this.$route.query && this.$route.query.dimensionalityName;\r\n    this.queryParams.dimensionalityId=dimensionalityId\r\n    this.queryParams.fcDate=fcDate\r\n    this.initData();\r\n\r\n\r\n    // if(this.$route.query)\r\n    // {\r\n    //   const dimensionalityId = this.$route.query && this.$route.query.dimensionalityId;\r\n    //   // const dimensionalityName = this.$route.query && this.$route.query.dimensionalityName;\r\n    //   const fcDate = this.$route.query && this.$route.query.fcDate;\r\n    //   this.queryParams.dimensionalityId=dimensionalityId\r\n    //   // this.queryParams.dimensionalityName=dimensionalityName\r\n    //   this.queryParams.fcDate=fcDate\r\n    //   this.initData1();\r\n    // }\r\n    // else\r\n    // {\r\n    //   this.initData();\r\n    // }\r\n  },\r\n  methods: {\r\n    onDateChange() {\r\n      console.log(this.dateValue);\r\n      if (this.dateValue != null && this.dateValue != \"\") {\r\n        this.queryImport.startDate = this.dateValue[0];\r\n        this.queryImport.endDate = this.dateValue[1];\r\n      } else {\r\n        this.queryImport.startDate = \"\";\r\n        this.queryImport.endDate = \"\";\r\n      }\r\n    },\r\n    clickNode($event, node) {\r\n      $event.target.parentElement.parentElement.firstElementChild.click();\r\n    },\r\n    changeEvent(params) {\r\n      const $form = this.$refs.formRef;\r\n      if ($form) {\r\n        $form.updateStatus(params);\r\n      }\r\n    },\r\n    disabledDate(time) {\r\n      return time.getTime() < Date.now() - 8.64e7; // 8.64e7 毫秒数代表一天\r\n    },\r\n    inputChange(val, row) {\r\n      row.formValue = val;\r\n    },\r\n    handleScroll() {\r\n      this.isSticky = window.scrollY >= this.stickyTop;\r\n    },\r\n    initData() {\r\n      // getAllRootListForAnswer().then((res) => {\r\n      //   this.rootList = res.data;\r\n      //   console.log(\"angry\",this.queryParams)\r\n        // if(this.queryParams.dimensionalityId==null)\r\n        // {\r\n        //   // this.queryParams.dimensionalityId = this.rootList[0].value;\r\n        //   // this.deptName= this.rootList[0].deptName;\r\n        //   // this.deptCode= this.rootList[0].deptCode;\r\n        // }\r\n        // else\r\n        // {\r\n        //   // this.queryParams.dimensionalityId = this.queryParams.dimensionalityId;\r\n        //   for(let i=0;i<this.rootList.length;i++)\r\n        //   {\r\n        //     if(this.queryParams.dimensionalityId == this.rootList[i].value)\r\n        //     {\r\n        //       this.queryParams.dimensionalityId = this.rootList[i].value;\r\n        //       this.deptName= this.rootList[i].deptName;\r\n        //       this.deptCode= this.rootList[i].deptCode;\r\n        //     }\r\n        //   }\r\n        // }\r\n      //   this.getList();\r\n      // });\r\n      getAllRootList().then((res) => {\r\n        this.rootList = res.data;\r\n        if(this.queryParams.dimensionalityId==null)\r\n        {\r\n          // this.queryParams.dimensionalityId = this.rootList[0].value;\r\n          // this.deptName= this.rootList[0].deptName;\r\n          // this.deptCode= this.rootList[0].deptCode;\r\n        }\r\n        else\r\n        {\r\n          // this.queryParams.dimensionalityId = this.queryParams.dimensionalityId;\r\n          for(let i=0;i<this.rootList.length;i++)\r\n          {\r\n            if(this.queryParams.dimensionalityId == this.rootList[i].value)\r\n            {\r\n              this.queryParams.dimensionalityId = this.rootList[i].value;\r\n              this.deptName= this.rootList[i].deptName;\r\n              this.deptCode= this.rootList[i].deptCode;\r\n            }\r\n          }\r\n        }\r\n        this.getList();\r\n      });\r\n    },\r\n    initData1() {\r\n      getAllRootListForAnswer().then((res) => {\r\n        this.rootList = res.data;\r\n        for(let i=0;i<this.rootList.length;i++)\r\n        {\r\n          if(this.queryParams.dimensionalityId == this.rootList[i].value)\r\n          {\r\n            this.deptName= this.rootList[0].deptName;\r\n          }\r\n        }\r\n        this.getList();\r\n      });\r\n    },\r\n\r\n    /** 查询TYjyAnswer列表 */\r\n    getList() {\r\n      formFrequency({dimensionalityId: this.queryParams.dimensionalityId}).then((res) => {\r\n           if(this.count!=res.data)\r\n           {\r\n            this.queryParams.fcDate=undefined\r\n           }\r\n           this.count=res.data\r\n      });\r\n      this.answerList = [];\r\n      answerstatuslistAdmin({\r\n        fcDate: this.queryParams.fcDate,\r\n        dimensionalityId: this.queryParams.dimensionalityId,\r\n        formQuestion: this.queryParams.formQuestion,\r\n      }).then((res) => {\r\n        let answerList = [];\r\n        let list = res.data;\r\n        for(let i=0;i<list.length;i++)\r\n        {\r\n            this.datesave[list[i].formId]=list[i].formValue\r\n            this.pathsave[list[i].dimensionalityName]=list[i].dimensionalityPath\r\n        }\r\n        // 使用 map 提取 dimensionalityName 属性到一个数组\r\n        let dimensionalityPaths = []\r\n        let Pathset=[]\r\n        list.forEach((x) => \r\n          {\r\n            if(!Pathset.includes(x.dimensionalityPath))\r\n            {\r\n              Pathset.push(x.dimensionalityPath)\r\n              dimensionalityPaths.push({ originalName: x.dimensionalityName, sortKey: x.dimensionalityPath})\r\n            }   \r\n          }\r\n        );\r\n        dimensionalityPaths.forEach((title) => {\r\n          let group = {\r\n            title: \"\",\r\n            list: [],\r\n          };\r\n          group.title = title.originalName;\r\n          group.list = list.filter((item) => item.dimensionalityPath === title.sortKey);\r\n          // 假设你有一个数组来存储所有的组\r\n          answerList.push(group); // 将生成的组添加到groups数组中\r\n        });\r\n        this.answerList = answerList;\r\n        console.log(\"angry\",this.answerList);\r\n\r\n\r\n\r\n        // // 使用 map 提取 dimensionalityName 属性到一个数组\r\n        // let dimensionalityNames = list.map((x) => x.dimensionalityName);\r\n\r\n        // // 提取 / 后的前三位字符，并与原字符串配对\r\n        // dimensionalityNames = dimensionalityNames.map((name) => {\r\n        //   // let key = name.includes(\"/\") ? name.split(\"/\")[1].slice(0, 3) : \"\";\r\n        //   let key = this.pathsave[name];\r\n        //   return { originalName: name, sortKey: key };\r\n        // });\r\n\r\n        // // 按照提取出的前三字符排序\r\n        // dimensionalityNames.sort((a, b) => a.sortKey.localeCompare(b.sortKey));\r\n        // // console.log(\"test0\",dimensionalityNames)\r\n        // // 如果需要，可以提取排序后的原始名字\r\n        // dimensionalityNames = dimensionalityNames.map(\r\n        //   (item) => item.originalName\r\n        // );\r\n\r\n        // // 使用 Set 去重\r\n        // let uniqueDimensionalityNames = [...new Set(dimensionalityNames)];\r\n\r\n        // uniqueDimensionalityNames.forEach((title) => {\r\n        //   let group = {\r\n        //     title: \"\",\r\n        //     list: [],\r\n        //   };\r\n        //   group.title = title;\r\n        //   group.list = list.filter((item) => item.dimensionalityName === title);\r\n        //   // 假设你有一个数组来存储所有的组\r\n        //   answerList.push(group); // 将生成的组添加到groups数组中\r\n        // });\r\n        // this.answerList = answerList;\r\n        // console.log(\"angry\",this.answerList);\r\n        this.$forceUpdate();\r\n      });\r\n    },\r\n    handleQuery() {\r\n      for(let i=0;i<this.rootList.length;i++)\r\n      {\r\n        if(this.queryParams.dimensionalityId == this.rootList[i].value)\r\n        {\r\n          this.deptName= this.rootList[0].deptName;\r\n          this.deptCode= this.rootList[i].deptCode;\r\n          this.dimensionalityName=this.rootList[i].label\r\n        }\r\n      }\r\n      this.getList();\r\n    },\r\n    handleDateChange() {\r\n      this.getList();\r\n    },\r\n    resetQuery() {\r\n      this.queryParams.fcDate = undefined;\r\n      this.queryParams.formQuestion = undefined;\r\n      this.queryParams.dimensionalityId = undefined;\r\n      this.getList();\r\n    },\r\n\r\n    handlePreview() {\r\n      let queryImport={}\r\n      queryImport.rootId = this.queryParams.dimensionalityId\r\n      queryImport.fcDate = this.queryParams.fcDate\r\n      queryImport.type=\"1\"\r\n      if(this.dimensionalityName=='研究院目标指标一览')\r\n      {\r\n          this.downloadXlsx(\r\n          \"/web/TYjy/answer/exportWithTemplate\",\r\n          {\r\n            ...queryImport,\r\n          },\r\n          this.dimensionalityName+\"(\" +this.specialFcDate+\r\n            \")\" +\r\n            `数据.xlsx`\r\n        ).then((blob) => {\r\n          let reader = new FileReader();\r\n          reader.readAsArrayBuffer(blob);\r\n          \r\n          reader.onload = (evt) => {\r\n            this.customBlobContent=reader.result;\r\n            let ints = new Uint8Array(evt.target.result); //要使用读取的内容，所以将读取内容转化成Uint8Array\r\n            ints = ints.slice(0, blob.size);\r\n            let workBook = xlsx.read(ints, { type: \"array\" });\r\n            let sheetNames = workBook.SheetNames;\r\n            let sheetName = sheetNames[0];\r\n            let workSheet = workBook.Sheets[sheetName];\r\n            //获取Excle内容，并将空内容用空值保存\r\n            let excelTable = xlsx.utils.sheet_to_json(workSheet);\r\n            // 获取Excel头部\r\n            let tableThead = Array.from(Object.keys(excelTable[0])).map(\r\n              (item) => {\r\n                return item\r\n              }\r\n            );\r\n            this.excelData = excelTable;\r\n            this.exceltitle=tableThead\r\n            this.excelHtml= excelTable\r\n            this.searchopen = true;\r\n          }\r\n        });\r\n      }\r\n      else\r\n      {\r\n        this.downloadXlsx(\r\n        \"/web/TYjy/answer/exportTemplateSpecial\",\r\n        {\r\n          ...queryImport,\r\n        },\r\n        this.dimensionalityName+\"(\" +this.specialFcDate+\r\n          \")\" +\r\n          `数据.xlsx`\r\n      ).then((blob) => {\r\n        let reader = new FileReader();\r\n        reader.readAsArrayBuffer(blob);\r\n\r\n        reader.onload = (evt) => {\r\n          this.customBlobContent=reader.result;\r\n          let ints = new Uint8Array(evt.target.result); //要使用读取的内容，所以将读取内容转化成Uint8Array\r\n          ints = ints.slice(0, blob.size);\r\n          let workBook = xlsx.read(ints, { type: \"array\" });\r\n          let sheetNames = workBook.SheetNames;\r\n          let sheetName = sheetNames[0];\r\n          let workSheet = workBook.Sheets[sheetName];\r\n          //获取Excle内容，并将空内容用空值保存\r\n          let excelTable = xlsx.utils.sheet_to_json(workSheet);\r\n          // 获取Excel头部\r\n          let tableThead = Array.from(Object.keys(excelTable[0])).map(\r\n            (item) => {\r\n              return item\r\n            }\r\n          );\r\n          this.excelData = excelTable;\r\n          this.exceltitle=tableThead\r\n          this.excelHtml= excelTable\r\n          this.searchopen = true;\r\n        }\r\n      });\r\n      }\r\n    },\r\n\r\n    handlePreview1() {\r\n      // let queryImport={}\r\n      // queryImport.rootId = this.queryParams.dimensionalityId\r\n      // queryImport.fcDate = this.queryParams.fcDate\r\n      // queryImport.type=\"1\"\r\n        if (\r\n        this.queryImport.startDate == null ||\r\n        this.queryImport.startDate == \"\"||\r\n        this.queryImport.endDate == null||\r\n        this.queryImport.endDate == \"\"\r\n      ) {\r\n        this.$notify.error({\r\n          title: \"错误\",\r\n          message: \"导出前请先输入开始结束时间\",\r\n        });\r\n        return;\r\n      }\r\n      this.queryImport.rootId = this.queryParams.dimensionalityId\r\n      this.queryImport.type=\"1\"\r\n      this.downloadXlsx(\r\n        \"/web/TYjy/answer/exportTemplateNomral\",\r\n        {\r\n          ...this.queryImport,\r\n        },\r\n        this.dimensionalityName+\"(\" +this.specialFcDate+\r\n          \")\" +\r\n          `数据.xlsx`\r\n      ).then((blob) => {\r\n        let reader = new FileReader();\r\n        reader.readAsArrayBuffer(blob);\r\n        \r\n        reader.onload = (evt) => {\r\n          this.customBlobContent=reader.result;\r\n          let ints = new Uint8Array(evt.target.result); //要使用读取的内容，所以将读取内容转化成Uint8Array\r\n          ints = ints.slice(0, blob.size);\r\n          let workBook = xlsx.read(ints, { type: \"array\" });\r\n          let sheetNames = workBook.SheetNames;\r\n          let sheetName = sheetNames[0];\r\n          let workSheet = workBook.Sheets[sheetName];\r\n          //获取Excle内容，并将空内容用空值保存\r\n          let excelTable = xlsx.utils.sheet_to_json(workSheet);\r\n          // 获取Excel头部\r\n          let tableThead = Array.from(Object.keys(excelTable[0])).map(\r\n            (item) => {\r\n              return item\r\n            }\r\n          );\r\n          this.excelData = excelTable;\r\n          this.exceltitle=tableThead\r\n          this.excelHtml= excelTable\r\n          this.searchopen = true;\r\n        }\r\n      });\r\n    },\r\n\r\n    handleUpload({ file }) {\r\n      const formData = new FormData();\r\n      formData.append(\"file\", file);\r\n      return axios\r\n        .post(\r\n          process.env.VUE_APP_BASE_API + \"/common/uploadMinioDataReport\",\r\n          formData\r\n        )\r\n        .then((res) => {\r\n          return {\r\n            ...res.data,\r\n          };\r\n        });\r\n    },\r\n    /** 提交按钮 */\r\n    handleSubmit() {\r\n      // 首先对 answerList 进行处理：合并、过滤和转换\r\n      let processedLists = this.answerList\r\n        .reduce((acc, current) => {\r\n          return acc.concat(current.list);\r\n        }, [])\r\n        .filter((x) => {\r\n          // 过滤条件\r\n          return (\r\n            x.formValue != null &&\r\n            x.formValue != \"\" &&\r\n            ((![\"0\", \"1\"].includes(x.status))&&\r\n            (\r\n              ([\"2\", \"3\"].includes(x.status) && this.datesave[x.formId]!=x.formValue))\r\n              ||([\"4\"].includes(x.status))\r\n            )\r\n          );\r\n        });\r\n\r\n      // 对符合条件的元素进行 formValue 的转换\r\n      processedLists.forEach((x) => {\r\n        if ([\"0\", \"1\"].includes(x.formType)) {\r\n          x.formValue = parseFloat(x.formValue);\r\n        }\r\n        x.fcDate = this.queryParams.fcDate;\r\n      });\r\n\r\n      // 最后进行深拷贝\r\n      let allLists = JSON.parse(JSON.stringify(processedLists));\r\n\r\n      console.log(\"allLists:\", allLists);\r\n      newAdd(allLists).then((res) => {\r\n        this.getList();\r\n        this.msgSuccess(\"保存成功\");\r\n      });\r\n    },\r\n    \r\n    handleFileUploadProgress() {\r\n      this.upload.isUploading = true;\r\n    },\r\n    handleFileSuccess(response) {\r\n      console.log(response)\r\n      if (response.code == 200) {\r\n        this.$modal.msgSuccess(\"上传成功\");\r\n        this.getList();\r\n        this.importOpen = false;\r\n        this.SpecialImportOpen = false;\r\n      }\r\n      else {\r\n        this.$modal.msgError(\"上传失败\")\r\n      }\r\n      this.upload.isUploading = false;\r\n    },\r\n    // 模板下载\r\n    downloadTemplate(){\r\n    \r\n      if (\r\n        this.queryImport.startDate == null ||\r\n        this.queryImport.startDate == \"\"||\r\n        this.queryImport.endDate == null||\r\n        this.queryImport.endDate == \"\"\r\n      ) {\r\n        this.$notify.error({\r\n          title: \"错误\",\r\n          message: \"导出前请先输入开始结束时间\",\r\n        });\r\n        return;\r\n      }\r\n      this.queryImport.rootId = this.queryParams.dimensionalityId\r\n      this.downloadFile(\r\n        \"/web/TYjy/answer/exportTemplate\",\r\n        {\r\n          ...this.queryImport,\r\n        },\r\n        \"(\" +\r\n          this.queryImport.startDate +\r\n          \"-\" +\r\n          this.queryImport.endDate +\r\n          \")\" +\r\n          `数据.xlsx`\r\n      );\r\n    \r\n    },\r\n    downloadTemplateSpecial(){\r\n      if (this.specialFcDate == null ) {\r\n        this.specialFcDate= this.queryParams.fcDate\r\n      }\r\n\r\n      // if (this.specialFcDate == null ) {\r\n      //   this.$notify.error({\r\n      //     title: \"错误\",\r\n      //     message: \"未选择时间\",\r\n      //   });\r\n      //   return;\r\n      // }\r\n      if(this.dimensionalityName=='研究院目标指标一览')\r\n      {\r\n        let queryImport={}\r\n        queryImport.rootId = this.queryParams.dimensionalityId\r\n        queryImport.fcDate = this.specialFcDate\r\n        queryImport.type=\"1\"\r\n        this.downloadFile(\r\n          \"/web/TYjy/answer/exportWithTemplate\",\r\n          {\r\n            ...queryImport,\r\n          },\r\n          this.dimensionalityName+\"(\" +this.specialFcDate+\r\n            \")\" +\r\n            `数据.xlsx`\r\n        );\r\n      }\r\n      else\r\n      {\r\n        let queryImport={}\r\n        queryImport.rootId = this.queryParams.dimensionalityId\r\n        queryImport.fcDate = this.specialFcDate\r\n        queryImport.type=\"1\"\r\n        this.downloadFile(\r\n          \"/web/TYjy/answer/exportTemplateSpecial\",\r\n          {\r\n            ...queryImport,\r\n          },\r\n          this.dimensionalityName+\"(\" +this.specialFcDate+\r\n            \")\" +\r\n            `数据.xlsx`\r\n        );\r\n      }\r\n\r\n    },\r\n        // 模板下载\r\n    downloadConnectTemplate(){\r\n    if (\r\n      this.queryImport.startDate == null ||\r\n      this.queryImport.startDate == \"\"||\r\n      this.queryImport.endDate == null||\r\n      this.queryImport.endDate == \"\"\r\n    ) {\r\n      this.$notify.error({\r\n        title: \"错误\",\r\n        message: \"导出前请先输入开始结束时间\",\r\n      });\r\n      return;\r\n    }\r\n    this.queryImport.rootId = this.queryParams.dimensionalityId\r\n    this.queryImport.type=\"1\"\r\n    this.downloadFile(\r\n      \"/web/TYjy/answer/exportTemplateNomral\",\r\n      {\r\n        ...this.queryImport,\r\n      },\r\n      \"(\" +\r\n        this.queryImport.startDate +\r\n        \"-\" +\r\n        this.queryImport.endDate +\r\n        \")\" +\r\n        `数据.xlsx`\r\n    );\r\n   },\r\n   containsSubstring(substring, string) {\r\n      return string.includes(substring);\r\n   },\r\n   aloneList(string) {\r\n      if(string== '气体结算月报')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '高炉、转炉煤气月报表')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '天然气消耗月报表')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '蒸汽消耗月报表')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '电量月报表')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '特板事业部2025年经济责任制奖罚汇总')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '研究院目标指标一览')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '安全责任工资考核表')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '安全责任工资考核汇总')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '水处理水量报表')\r\n      {\r\n        return true;\r\n      }\r\n      return false;\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style scoped lang=\"scss\">\r\n/* 设置滚动条的样式 */\r\n::-webkit-scrollbar {\r\n  width: 10px;\r\n  /* 竖向滚动条宽度 */\r\n  height: 15px;\r\n  /* 横向滚动条宽度 */\r\n  background-color: #ffffff;\r\n}\r\n\r\n/* 滚动槽 */\r\n::-webkit-scrollbar-track {\r\n  /* 其实直接在  ::-webkit-scrollbar 中设置也能达到同样的视觉效果*/\r\n  /* -webkit-box-shadow: inset 0 0 6px rgba(177, 223, 117, 0.7); */\r\n  background-color: #e4e4e4;\r\n  border-radius: 10px;\r\n}\r\n\r\n/* 滚动条滑块 */\r\n::-webkit-scrollbar-thumb {\r\n  border-radius: 5px;\r\n  -webkit-box-shadow: inset 0 0 6px rgba(158, 156, 156, 0.616);\r\n}\r\n\r\n::-webkit-scrollbar-thumb:hover {\r\n  background: rgba(139, 138, 138, 0.616);\r\n  -webkit-box-shadow: unset;\r\n}\r\n\r\n::-webkit-scrollbar-thumb:window-inactive {\r\n  /* 容器不被激活时的样式 */\r\n  background: #bdbdbd66;\r\n}\r\n\r\n::-webkit-scrollbar-corner {\r\n  /* 两个滚动条交汇处边角的样式 */\r\n  background-color: #cacaca66;\r\n}\r\n\r\n.sticky {\r\n  padding: 20px;\r\n  position: -webkit-sticky;\r\n  position: sticky;\r\n  top: 0; /* 粘性定位的起始位置 */\r\n  z-index: 100; /* 确保按钮在卡片之上 */\r\n}\r\n.el-tabs--card {\r\n  height: calc(100vh - 110px);\r\n}\r\n.el-tab-pane {\r\n  height: calc(100vh - 110px);\r\n  overflow-y: auto;\r\n}\r\n</style>\r\n  "], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgdA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,OAAA,GAAAH,OAAA;AACA,IAAAI,eAAA,GAAAJ,OAAA;AACA,IAAAK,KAAA,GAAAL,OAAA;AACA,IAAAM,MAAA,GAAAJ,sBAAA,CAAAF,OAAA;AACA,IAAAO,IAAA,GAAAC,uBAAA,CAAAR,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCACA;EACAS,IAAA;EACAC,UAAA;IAAAC,WAAA,EAAAA;EAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,aAAA;QACAC,YAAA,WAAAA,aAAAC,IAAA;UACA,OAAAA,IAAA,CAAAC,OAAA,KAAAC,IAAA,CAAAC,GAAA;QACA;MACA;MACAC,gBAAA;MAEAC,WAAA,MAAAC,gBAAA,CAAAC,OAAA;QACAC,YAAA,EAAAC,SAAA;QACAC,MAAA,EAAAD,SAAA;QACAE,gBAAA,EAAAF;MAAA,mBACAA,SAAA,CACA;MAEAG,QAAA;MACAC,mBAAA;MACAC,kBAAA;MACAC,aAAA;MACAC,UAAA;MACAC,SAAA;MACAC,OAAA;MACAC,UAAA;MACAC,UAAA;MACAC,QAAA;MACAC,GAAA;MACAC,QAAA;MACAC,QAAA;MACAC,QAAA;MACAC,QAAA;MACAC,QAAA;MACAC,SAAA;MACAC,WAAA;QACAC,SAAA;QACAC,OAAA;QACAC,MAAA;MACA;MACAC,KAAA;MACAC,UAAA;MACAC,iBAAA;MACAC,WAAA;MACAC,WAAA;MACA;MACAC,MAAA;QACA;QACAC,WAAA;QACA;QACAC,OAAA;UAAAC,aAAA,kBAAAC,cAAA;QAAA;QACA;QACAC,GAAA,EACAC,OAAA,CAAAC,GAAA,CAAAC,gBAAA,GACA;MAEA;MAEAC,aAAA;QACA;QACAR,WAAA;QACA;QACAC,OAAA;UAAAC,aAAA,kBAAAC,cAAA;QAAA;QACA;QACAC,GAAA,EACAC,OAAA,CAAAC,GAAA,CAAAC,gBAAA,GACA;MAEA;MACAE,SAAA;MACAC,UAAA;MACAC,SAAA;MAAA;MACAC,UAAA;MACAC,iBAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAA7B,QAAA,GAAA8B,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,MAAAC,MAAA,CAAAC,KAAA,CAAAC,IAAA;EACA;EAEAC,OAAA,WAAAA,QAAA;IACA,IAAAjD,gBAAA,QAAAkD,MAAA,CAAAC,KAAA,SAAAD,MAAA,CAAAC,KAAA,CAAAnD,gBAAA;IACA,IAAAD,MAAA,QAAAmD,MAAA,CAAAC,KAAA,SAAAD,MAAA,CAAAC,KAAA,CAAApD,MAAA;IAEA,KAAAI,kBAAA,QAAA+C,MAAA,CAAAC,KAAA,SAAAD,MAAA,CAAAC,KAAA,CAAAhD,kBAAA;IACA,KAAAT,WAAA,CAAAM,gBAAA,GAAAA,gBAAA;IACA,KAAAN,WAAA,CAAAK,MAAA,GAAAA,MAAA;IACA,KAAAqD,QAAA;;IAGA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACA;EACAC,OAAA;IACAC,YAAA,WAAAA,aAAA;MACAC,OAAA,CAAAC,GAAA,MAAAvC,SAAA;MACA,SAAAA,SAAA,iBAAAA,SAAA;QACA,KAAAC,WAAA,CAAAC,SAAA,QAAAF,SAAA;QACA,KAAAC,WAAA,CAAAE,OAAA,QAAAH,SAAA;MACA;QACA,KAAAC,WAAA,CAAAC,SAAA;QACA,KAAAD,WAAA,CAAAE,OAAA;MACA;IACA;IACAqC,SAAA,WAAAA,UAAAC,MAAA,EAAAC,IAAA;MACAD,MAAA,CAAAE,MAAA,CAAAC,aAAA,CAAAA,aAAA,CAAAC,iBAAA,CAAAC,KAAA;IACA;IACAC,WAAA,WAAAA,YAAAC,MAAA;MACA,IAAAC,KAAA,QAAAC,KAAA,CAAAC,OAAA;MACA,IAAAF,KAAA;QACAA,KAAA,CAAAG,YAAA,CAAAJ,MAAA;MACA;IACA;IACA7E,YAAA,WAAAA,aAAAC,IAAA;MACA,OAAAA,IAAA,CAAAC,OAAA,KAAAC,IAAA,CAAAC,GAAA;IACA;IACA8E,WAAA,WAAAA,YAAAC,GAAA,EAAA5D,GAAA;MACAA,GAAA,CAAA6D,SAAA,GAAAD,GAAA;IACA;IACAE,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,GAAAC,MAAA,CAAAC,OAAA,SAAAtE,SAAA;IACA;IACA8C,QAAA,WAAAA,SAAA;MAAA,IAAAyB,KAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAAC,8BAAA,IAAAC,IAAA,WAAAC,GAAA;QACAH,KAAA,CAAAjE,QAAA,GAAAoE,GAAA,CAAA9F,IAAA;QACA,IAAA2F,KAAA,CAAAnF,WAAA,CAAAM,gBAAA,UACA;UACA;UACA;UACA;QAAA,CACA,MAEA;UACA;UACA,SAAAiF,CAAA,MAAAA,CAAA,GAAAJ,KAAA,CAAAjE,QAAA,CAAAsE,MAAA,EAAAD,CAAA,IACA;YACA,IAAAJ,KAAA,CAAAnF,WAAA,CAAAM,gBAAA,IAAA6E,KAAA,CAAAjE,QAAA,CAAAqE,CAAA,EAAAE,KAAA,EACA;cACAN,KAAA,CAAAnF,WAAA,CAAAM,gBAAA,GAAA6E,KAAA,CAAAjE,QAAA,CAAAqE,CAAA,EAAAE,KAAA;cACAN,KAAA,CAAA7D,QAAA,GAAA6D,KAAA,CAAAjE,QAAA,CAAAqE,CAAA,EAAAjE,QAAA;cACA6D,KAAA,CAAAO,QAAA,GAAAP,KAAA,CAAAjE,QAAA,CAAAqE,CAAA,EAAAG,QAAA;YACA;UACA;QACA;QACAP,KAAA,CAAAQ,OAAA;MACA;IACA;IACAC,SAAA,WAAAA,UAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,uCAAA,IAAAT,IAAA,WAAAC,GAAA;QACAO,MAAA,CAAA3E,QAAA,GAAAoE,GAAA,CAAA9F,IAAA;QACA,SAAA+F,CAAA,MAAAA,CAAA,GAAAM,MAAA,CAAA3E,QAAA,CAAAsE,MAAA,EAAAD,CAAA,IACA;UACA,IAAAM,MAAA,CAAA7F,WAAA,CAAAM,gBAAA,IAAAuF,MAAA,CAAA3E,QAAA,CAAAqE,CAAA,EAAAE,KAAA,EACA;YACAI,MAAA,CAAAvE,QAAA,GAAAuE,MAAA,CAAA3E,QAAA,IAAAI,QAAA;UACA;QACA;QACAuE,MAAA,CAAAF,OAAA;MACA;IACA;IAEA,qBACAA,OAAA,WAAAA,QAAA;MAAA,IAAAI,MAAA;MACA,IAAAC,mBAAA;QAAA1F,gBAAA,OAAAN,WAAA,CAAAM;MAAA,GAAA+E,IAAA,WAAAC,GAAA;QACA,IAAAS,MAAA,CAAAnE,KAAA,IAAA0D,GAAA,CAAA9F,IAAA,EACA;UACAuG,MAAA,CAAA/F,WAAA,CAAAK,MAAA,GAAAD,SAAA;QACA;QACA2F,MAAA,CAAAnE,KAAA,GAAA0D,GAAA,CAAA9F,IAAA;MACA;MACA,KAAAuB,UAAA;MACA,IAAAkF,2BAAA;QACA5F,MAAA,OAAAL,WAAA,CAAAK,MAAA;QACAC,gBAAA,OAAAN,WAAA,CAAAM,gBAAA;QACAH,YAAA,OAAAH,WAAA,CAAAG;MACA,GAAAkF,IAAA,WAAAC,GAAA;QACA,IAAAvE,UAAA;QACA,IAAAmF,IAAA,GAAAZ,GAAA,CAAA9F,IAAA;QACA,SAAA+F,CAAA,MAAAA,CAAA,GAAAW,IAAA,CAAAV,MAAA,EAAAD,CAAA,IACA;UACAQ,MAAA,CAAA3E,QAAA,CAAA8E,IAAA,CAAAX,CAAA,EAAAY,MAAA,IAAAD,IAAA,CAAAX,CAAA,EAAAT,SAAA;UACAiB,MAAA,CAAA1E,QAAA,CAAA6E,IAAA,CAAAX,CAAA,EAAA9E,kBAAA,IAAAyF,IAAA,CAAAX,CAAA,EAAAa,kBAAA;QACA;QACA;QACA,IAAAC,mBAAA;QACA,IAAAC,OAAA;QACAJ,IAAA,CAAAK,OAAA,WAAAC,CAAA,EACA;UACA,KAAAF,OAAA,CAAAG,QAAA,CAAAD,CAAA,CAAAJ,kBAAA,GACA;YACAE,OAAA,CAAAI,IAAA,CAAAF,CAAA,CAAAJ,kBAAA;YACAC,mBAAA,CAAAK,IAAA;cAAAC,YAAA,EAAAH,CAAA,CAAA/F,kBAAA;cAAAmG,OAAA,EAAAJ,CAAA,CAAAJ;YAAA;UACA;QACA,CACA;QACAC,mBAAA,CAAAE,OAAA,WAAAM,KAAA;UACA,IAAAC,KAAA;YACAD,KAAA;YACAX,IAAA;UACA;UACAY,KAAA,CAAAD,KAAA,GAAAA,KAAA,CAAAF,YAAA;UACAG,KAAA,CAAAZ,IAAA,GAAAA,IAAA,CAAAa,MAAA,WAAAC,IAAA;YAAA,OAAAA,IAAA,CAAAZ,kBAAA,KAAAS,KAAA,CAAAD,OAAA;UAAA;UACA;UACA7F,UAAA,CAAA2F,IAAA,CAAAI,KAAA;QACA;QACAf,MAAA,CAAAhF,UAAA,GAAAA,UAAA;QACA8C,OAAA,CAAAC,GAAA,UAAAiC,MAAA,CAAAhF,UAAA;;QAIA;QACA;;QAEA;QACA;QACA;QACA;QACA;QACA;;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA;QACA;;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACAgF,MAAA,CAAAkB,YAAA;MACA;IACA;IACAC,WAAA,WAAAA,YAAA;MACA,SAAA3B,CAAA,MAAAA,CAAA,QAAArE,QAAA,CAAAsE,MAAA,EAAAD,CAAA,IACA;QACA,SAAAvF,WAAA,CAAAM,gBAAA,SAAAY,QAAA,CAAAqE,CAAA,EAAAE,KAAA,EACA;UACA,KAAAnE,QAAA,QAAAJ,QAAA,IAAAI,QAAA;UACA,KAAAoE,QAAA,QAAAxE,QAAA,CAAAqE,CAAA,EAAAG,QAAA;UACA,KAAAjF,kBAAA,QAAAS,QAAA,CAAAqE,CAAA,EAAA4B,KAAA;QACA;MACA;MACA,KAAAxB,OAAA;IACA;IACAyB,gBAAA,WAAAA,iBAAA;MACA,KAAAzB,OAAA;IACA;IACA0B,UAAA,WAAAA,WAAA;MACA,KAAArH,WAAA,CAAAK,MAAA,GAAAD,SAAA;MACA,KAAAJ,WAAA,CAAAG,YAAA,GAAAC,SAAA;MACA,KAAAJ,WAAA,CAAAM,gBAAA,GAAAF,SAAA;MACA,KAAAuF,OAAA;IACA;IAEA2B,aAAA,WAAAA,cAAA;MAAA,IAAAC,MAAA;MACA,IAAA/F,WAAA;MACAA,WAAA,CAAAG,MAAA,QAAA3B,WAAA,CAAAM,gBAAA;MACAkB,WAAA,CAAAnB,MAAA,QAAAL,WAAA,CAAAK,MAAA;MACAmB,WAAA,CAAAgG,IAAA;MACA,SAAA/G,kBAAA,iBACA;QACA,KAAAgH,YAAA,CACA,2CAAAC,cAAA,CAAAxH,OAAA,MAEAsB,WAAA,GAEA,KAAAf,kBAAA,cAAAC,aAAA,GACA,yBAEA,EAAA2E,IAAA,WAAAsC,IAAA;UACA,IAAAC,MAAA,OAAAC,UAAA;UACAD,MAAA,CAAAE,iBAAA,CAAAH,IAAA;UAEAC,MAAA,CAAAG,MAAA,aAAAC,GAAA;YACAT,MAAA,CAAAxE,iBAAA,GAAA6E,MAAA,CAAAK,MAAA;YACA,IAAAC,IAAA,OAAAC,UAAA,CAAAH,GAAA,CAAA9D,MAAA,CAAA+D,MAAA;YACAC,IAAA,GAAAA,IAAA,CAAAE,KAAA,IAAAT,IAAA,CAAAU,IAAA;YACA,IAAAC,QAAA,GAAAnJ,IAAA,CAAAoJ,IAAA,CAAAL,IAAA;cAAAV,IAAA;YAAA;YACA,IAAAgB,UAAA,GAAAF,QAAA,CAAAG,UAAA;YACA,IAAAC,SAAA,GAAAF,UAAA;YACA,IAAAG,SAAA,GAAAL,QAAA,CAAAM,MAAA,CAAAF,SAAA;YACA;YACA,IAAAG,UAAA,GAAA1J,IAAA,CAAA2J,KAAA,CAAAC,aAAA,CAAAJ,SAAA;YACA;YACA,IAAAK,UAAA,GAAAC,KAAA,CAAAC,IAAA,CAAAC,MAAA,CAAAC,IAAA,CAAAP,UAAA,MAAAQ,GAAA,CACA,UAAArC,IAAA;cACA,OAAAA,IAAA;YACA,CACA;YACAO,MAAA,CAAA1E,SAAA,GAAAgG,UAAA;YACAtB,MAAA,CAAAzE,UAAA,GAAAkG,UAAA;YACAzB,MAAA,CAAA5E,SAAA,GAAAkG,UAAA;YACAtB,MAAA,CAAA3E,UAAA;UACA;QACA;MACA,OAEA;QACA,KAAA6E,YAAA,CACA,8CAAAC,cAAA,CAAAxH,OAAA,MAEAsB,WAAA,GAEA,KAAAf,kBAAA,cAAAC,aAAA,GACA,yBAEA,EAAA2E,IAAA,WAAAsC,IAAA;UACA,IAAAC,MAAA,OAAAC,UAAA;UACAD,MAAA,CAAAE,iBAAA,CAAAH,IAAA;UAEAC,MAAA,CAAAG,MAAA,aAAAC,GAAA;YACAT,MAAA,CAAAxE,iBAAA,GAAA6E,MAAA,CAAAK,MAAA;YACA,IAAAC,IAAA,OAAAC,UAAA,CAAAH,GAAA,CAAA9D,MAAA,CAAA+D,MAAA;YACAC,IAAA,GAAAA,IAAA,CAAAE,KAAA,IAAAT,IAAA,CAAAU,IAAA;YACA,IAAAC,QAAA,GAAAnJ,IAAA,CAAAoJ,IAAA,CAAAL,IAAA;cAAAV,IAAA;YAAA;YACA,IAAAgB,UAAA,GAAAF,QAAA,CAAAG,UAAA;YACA,IAAAC,SAAA,GAAAF,UAAA;YACA,IAAAG,SAAA,GAAAL,QAAA,CAAAM,MAAA,CAAAF,SAAA;YACA;YACA,IAAAG,UAAA,GAAA1J,IAAA,CAAA2J,KAAA,CAAAC,aAAA,CAAAJ,SAAA;YACA;YACA,IAAAK,UAAA,GAAAC,KAAA,CAAAC,IAAA,CAAAC,MAAA,CAAAC,IAAA,CAAAP,UAAA,MAAAQ,GAAA,CACA,UAAArC,IAAA;cACA,OAAAA,IAAA;YACA,CACA;YACAO,MAAA,CAAA1E,SAAA,GAAAgG,UAAA;YACAtB,MAAA,CAAAzE,UAAA,GAAAkG,UAAA;YACAzB,MAAA,CAAA5E,SAAA,GAAAkG,UAAA;YACAtB,MAAA,CAAA3E,UAAA;UACA;QACA;MACA;IACA;IAEA0G,cAAA,WAAAA,eAAA;MAAA,IAAAC,MAAA;MACA;MACA;MACA;MACA;MACA,IACA,KAAA/H,WAAA,CAAAC,SAAA,YACA,KAAAD,WAAA,CAAAC,SAAA,UACA,KAAAD,WAAA,CAAAE,OAAA,YACA,KAAAF,WAAA,CAAAE,OAAA,QACA;QACA,KAAA8H,OAAA,CAAAC,KAAA;UACA5C,KAAA;UACA6C,OAAA;QACA;QACA;MACA;MACA,KAAAlI,WAAA,CAAAG,MAAA,QAAA3B,WAAA,CAAAM,gBAAA;MACA,KAAAkB,WAAA,CAAAgG,IAAA;MACA,KAAAC,YAAA,CACA,6CAAAC,cAAA,CAAAxH,OAAA,MAEA,KAAAsB,WAAA,GAEA,KAAAf,kBAAA,cAAAC,aAAA,GACA,yBAEA,EAAA2E,IAAA,WAAAsC,IAAA;QACA,IAAAC,MAAA,OAAAC,UAAA;QACAD,MAAA,CAAAE,iBAAA,CAAAH,IAAA;QAEAC,MAAA,CAAAG,MAAA,aAAAC,GAAA;UACAuB,MAAA,CAAAxG,iBAAA,GAAA6E,MAAA,CAAAK,MAAA;UACA,IAAAC,IAAA,OAAAC,UAAA,CAAAH,GAAA,CAAA9D,MAAA,CAAA+D,MAAA;UACAC,IAAA,GAAAA,IAAA,CAAAE,KAAA,IAAAT,IAAA,CAAAU,IAAA;UACA,IAAAC,QAAA,GAAAnJ,IAAA,CAAAoJ,IAAA,CAAAL,IAAA;YAAAV,IAAA;UAAA;UACA,IAAAgB,UAAA,GAAAF,QAAA,CAAAG,UAAA;UACA,IAAAC,SAAA,GAAAF,UAAA;UACA,IAAAG,SAAA,GAAAL,QAAA,CAAAM,MAAA,CAAAF,SAAA;UACA;UACA,IAAAG,UAAA,GAAA1J,IAAA,CAAA2J,KAAA,CAAAC,aAAA,CAAAJ,SAAA;UACA;UACA,IAAAK,UAAA,GAAAC,KAAA,CAAAC,IAAA,CAAAC,MAAA,CAAAC,IAAA,CAAAP,UAAA,MAAAQ,GAAA,CACA,UAAArC,IAAA;YACA,OAAAA,IAAA;UACA,CACA;UACAuC,MAAA,CAAA1G,SAAA,GAAAgG,UAAA;UACAU,MAAA,CAAAzG,UAAA,GAAAkG,UAAA;UACAO,MAAA,CAAA5G,SAAA,GAAAkG,UAAA;UACAU,MAAA,CAAA3G,UAAA;QACA;MACA;IACA;IAEA+G,YAAA,WAAAA,aAAAC,IAAA;MAAA,IAAAC,IAAA,GAAAD,IAAA,CAAAC,IAAA;MACA,IAAA7I,QAAA,OAAA8I,QAAA;MACA9I,QAAA,CAAA+I,MAAA,SAAAF,IAAA;MACA,OAAAG,cAAA,CACAC,IAAA,CACA1H,OAAA,CAAAC,GAAA,CAAAC,gBAAA,oCACAzB,QACA,EACAqE,IAAA,WAAAC,GAAA;QACA,WAAAoC,cAAA,CAAAxH,OAAA,MACAoF,GAAA,CAAA9F,IAAA;MAEA;IACA;IACA,WACA0K,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA;MACA,IAAAC,cAAA,QAAArJ,UAAA,CACAsJ,MAAA,WAAAC,GAAA,EAAAC,OAAA;QACA,OAAAD,GAAA,CAAAE,MAAA,CAAAD,OAAA,CAAArE,IAAA;MACA,OACAa,MAAA,WAAAP,CAAA;QACA;QACA,OACAA,CAAA,CAAA1B,SAAA,YACA0B,CAAA,CAAA1B,SAAA,WACA,YAAA2B,QAAA,CAAAD,CAAA,CAAAiE,MAAA,KAEA,WAAAhE,QAAA,CAAAD,CAAA,CAAAiE,MAAA,KAAAN,MAAA,CAAA/I,QAAA,CAAAoF,CAAA,CAAAL,MAAA,KAAAK,CAAA,CAAA1B,SAAA,IACA,MAAA2B,QAAA,CAAAD,CAAA,CAAAiE,MAAA,EACA;MAEA;;MAEA;MACAL,cAAA,CAAA7D,OAAA,WAAAC,CAAA;QACA,eAAAC,QAAA,CAAAD,CAAA,CAAAjG,QAAA;UACAiG,CAAA,CAAA1B,SAAA,GAAA4F,UAAA,CAAAlE,CAAA,CAAA1B,SAAA;QACA;QACA0B,CAAA,CAAAnG,MAAA,GAAA8J,MAAA,CAAAnK,WAAA,CAAAK,MAAA;MACA;;MAEA;MACA,IAAAsK,QAAA,GAAA1H,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,CAAAiH,cAAA;MAEAvG,OAAA,CAAAC,GAAA,cAAA6G,QAAA;MACA,IAAAC,cAAA,EAAAD,QAAA,EAAAtF,IAAA,WAAAC,GAAA;QACA6E,MAAA,CAAAxE,OAAA;QACAwE,MAAA,CAAAU,UAAA;MACA;IACA;IAEAC,wBAAA,WAAAA,yBAAA;MACA,KAAA7I,MAAA,CAAAC,WAAA;IACA;IACA6I,iBAAA,WAAAA,kBAAAC,QAAA;MACAnH,OAAA,CAAAC,GAAA,CAAAkH,QAAA;MACA,IAAAA,QAAA,CAAAC,IAAA;QACA,KAAAC,MAAA,CAAAL,UAAA;QACA,KAAAlF,OAAA;QACA,KAAA9D,UAAA;QACA,KAAAC,iBAAA;MACA,OACA;QACA,KAAAoJ,MAAA,CAAAC,QAAA;MACA;MACA,KAAAlJ,MAAA,CAAAC,WAAA;IACA;IACA;IACAkJ,gBAAA,WAAAA,iBAAA;MAEA,IACA,KAAA5J,WAAA,CAAAC,SAAA,YACA,KAAAD,WAAA,CAAAC,SAAA,UACA,KAAAD,WAAA,CAAAE,OAAA,YACA,KAAAF,WAAA,CAAAE,OAAA,QACA;QACA,KAAA8H,OAAA,CAAAC,KAAA;UACA5C,KAAA;UACA6C,OAAA;QACA;QACA;MACA;MACA,KAAAlI,WAAA,CAAAG,MAAA,QAAA3B,WAAA,CAAAM,gBAAA;MACA,KAAA+K,YAAA,CACA,uCAAA3D,cAAA,CAAAxH,OAAA,MAEA,KAAAsB,WAAA,GAEA,MACA,KAAAA,WAAA,CAAAC,SAAA,GACA,MACA,KAAAD,WAAA,CAAAE,OAAA,GACA,yBAEA;IAEA;IACA4J,uBAAA,WAAAA,wBAAA;MACA,SAAA5K,aAAA;QACA,KAAAA,aAAA,QAAAV,WAAA,CAAAK,MAAA;MACA;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,SAAAI,kBAAA,iBACA;QACA,IAAAe,WAAA;QACAA,WAAA,CAAAG,MAAA,QAAA3B,WAAA,CAAAM,gBAAA;QACAkB,WAAA,CAAAnB,MAAA,QAAAK,aAAA;QACAc,WAAA,CAAAgG,IAAA;QACA,KAAA6D,YAAA,CACA,2CAAA3D,cAAA,CAAAxH,OAAA,MAEAsB,WAAA,GAEA,KAAAf,kBAAA,cAAAC,aAAA,GACA,yBAEA;MACA,OAEA;QACA,IAAAc,YAAA;QACAA,YAAA,CAAAG,MAAA,QAAA3B,WAAA,CAAAM,gBAAA;QACAkB,YAAA,CAAAnB,MAAA,QAAAK,aAAA;QACAc,YAAA,CAAAgG,IAAA;QACA,KAAA6D,YAAA,CACA,8CAAA3D,cAAA,CAAAxH,OAAA,MAEAsB,YAAA,GAEA,KAAAf,kBAAA,cAAAC,aAAA,GACA,yBAEA;MACA;IAEA;IACA;IACA6K,uBAAA,WAAAA,wBAAA;MACA,IACA,KAAA/J,WAAA,CAAAC,SAAA,YACA,KAAAD,WAAA,CAAAC,SAAA,UACA,KAAAD,WAAA,CAAAE,OAAA,YACA,KAAAF,WAAA,CAAAE,OAAA,QACA;QACA,KAAA8H,OAAA,CAAAC,KAAA;UACA5C,KAAA;UACA6C,OAAA;QACA;QACA;MACA;MACA,KAAAlI,WAAA,CAAAG,MAAA,QAAA3B,WAAA,CAAAM,gBAAA;MACA,KAAAkB,WAAA,CAAAgG,IAAA;MACA,KAAA6D,YAAA,CACA,6CAAA3D,cAAA,CAAAxH,OAAA,MAEA,KAAAsB,WAAA,GAEA,MACA,KAAAA,WAAA,CAAAC,SAAA,GACA,MACA,KAAAD,WAAA,CAAAE,OAAA,GACA,yBAEA;IACA;IACA8J,iBAAA,WAAAA,kBAAAC,SAAA,EAAAC,MAAA;MACA,OAAAA,MAAA,CAAAjF,QAAA,CAAAgF,SAAA;IACA;IACAE,SAAA,WAAAA,UAAAD,MAAA;MACA,IAAAA,MAAA,cACA;QACA;MACA;MACA,IAAAA,MAAA,kBACA;QACA;MACA;MACA,IAAAA,MAAA,gBACA;QACA;MACA;MACA,IAAAA,MAAA,eACA;QACA;MACA;MACA,IAAAA,MAAA,aACA;QACA;MACA;MACA,IAAAA,MAAA,2BACA;QACA;MACA;MACA,IAAAA,MAAA,iBACA;QACA;MACA;MACA,IAAAA,MAAA,iBACA;QACA;MACA;MACA,IAAAA,MAAA,kBACA;QACA;MACA;MACA,IAAAA,MAAA,eACA;QACA;MACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}