{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\qualityCost\\companySummary2\\index.vue?vue&type=style&index=1&id=0d6db6c2&lang=css", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\qualityCost\\companySummary2\\index.vue", "mtime": 1756099891078}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\@vue\\cli-service\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQovKiDpnZ5zY29wZWTmoLflvI/vvIznoa7kv53og73lpJ/opobnm5Z2eGUtdGFibGXnmoTpu5jorqTmoLflvI8gKi8NCg0KLyog5by65Yi26KaG55uW5omA5pyJ5rua5Yqo5p2h5qC35byPIC0gMTVweOeyl+e7hu+8jOa3oeiTneiJsuS4u+mimCAqLw0KLnZ4ZS10YWJsZSA6Oi13ZWJraXQtc2Nyb2xsYmFyIHsNCiAgd2lkdGg6IDE1cHggIWltcG9ydGFudDsNCiAgaGVpZ2h0OiAxNXB4ICFpbXBvcnRhbnQ7DQp9DQoNCi52eGUtdGFibGUgOjotd2Via2l0LXNjcm9sbGJhci10cmFjayB7DQogIGJhY2tncm91bmQ6ICNlM2YyZmQgIWltcG9ydGFudDsNCiAgYm9yZGVyLXJhZGl1czogN3B4ICFpbXBvcnRhbnQ7DQp9DQoNCi52eGUtdGFibGUgOjotd2Via2l0LXNjcm9sbGJhci10aHVtYiB7DQogIGJhY2tncm91bmQ6ICM2NGI1ZjYgIWltcG9ydGFudDsNCiAgYm9yZGVyLXJhZGl1czogN3B4ICFpbXBvcnRhbnQ7DQogIGJvcmRlcjogbm9uZSAhaW1wb3J0YW50Ow0KfQ0KDQoudnhlLXRhYmxlIDo6LXdlYmtpdC1zY3JvbGxiYXItdGh1bWI6aG92ZXIgew0KICBiYWNrZ3JvdW5kOiAjNDJhNWY1ICFpbXBvcnRhbnQ7DQp9DQoNCi52eGUtdGFibGUgOjotd2Via2l0LXNjcm9sbGJhci10aHVtYjphY3RpdmUgew0KICBiYWNrZ3JvdW5kOiAjMjE5NmYzICFpbXBvcnRhbnQ7DQp9DQoNCi52eGUtdGFibGUgOjotd2Via2l0LXNjcm9sbGJhci1jb3JuZXIgew0KICBiYWNrZ3JvdW5kOiAjZTNmMmZkICFpbXBvcnRhbnQ7DQp9DQoNCi8qIHZ4ZS10YWJsZSDooajmoLzovrnmoYblkozmoLflvI8gKi8NCi52eGUtdGFibGUgOjp2LWRlZXAgLnZ4ZS10YWJsZSB7DQogIGJvcmRlcjogMXB4IHNvbGlkICNlYmVlZjU7DQp9DQoNCi52eGUtdGFibGUgOjp2LWRlZXAgLnZ4ZS10YWJsZS0tYm9yZGVyLWxpbmUgew0KICBib3JkZXItY29sb3I6ICNlYmVlZjU7DQp9DQoNCi8qIOihqOWktOagt+W8jyAqLw0KLnZ4ZS10YWJsZSA6OnYtZGVlcCAudnhlLXRhYmxlLS1oZWFkZXItd3JhcHBlciAudnhlLXRhYmxlLS1oZWFkZXIgew0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjVmN2ZhOw0KfQ0KDQoudnhlLXRhYmxlIDo6di1kZWVwIC52eGUtdGFibGUtLWhlYWRlciAudnhlLWhlYWRlci0tY29sdW1uIHsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2Y1ZjdmYTsNCiAgY29sb3I6ICMzMDMxMzM7DQogIGZvbnQtd2VpZ2h0OiA1MDA7DQp9DQoNCi8qIOihqOWktOWIhue7hOagt+W8jyAqLw0KLnZ4ZS10YWJsZSA6OnYtZGVlcCAudnhlLXRhYmxlLS1oZWFkZXIgLnZ4ZS1oZWFkZXItLWdyb3VwIHsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2U4ZjRmZCAhaW1wb3J0YW50Ow0KICBjb2xvcjogIzE4OTBmZiAhaW1wb3J0YW50Ow0KICBmb250LXdlaWdodDogNjAwICFpbXBvcnRhbnQ7DQogIGJvcmRlcjogMXB4IHNvbGlkICNkMWU3ZmYgIWltcG9ydGFudDsNCn0NCg0KLyog56Gu5L+d5YiG57uE6KGo5aS05pi+56S6ICovDQoudnhlLXRhYmxlIDo6di1kZWVwIC52eGUtdGFibGUtLWhlYWRlciAudnhlLWhlYWRlci0tZ3JvdXAgLnZ4ZS1oZWFkZXItLWdyb3VwLXRpdGxlIHsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2U4ZjRmZCAhaW1wb3J0YW50Ow0KICBjb2xvcjogIzE4OTBmZiAhaW1wb3J0YW50Ow0KICBmb250LXdlaWdodDogNjAwICFpbXBvcnRhbnQ7DQogIHBhZGRpbmc6IDhweCA0cHggIWltcG9ydGFudDsNCiAgdGV4dC1hbGlnbjogY2VudGVyICFpbXBvcnRhbnQ7DQp9DQoNCi8qIOWtkOWIl+ihqOWktOagt+W8jyAqLw0KLnZ4ZS10YWJsZSA6OnYtZGVlcCAudnhlLXRhYmxlLS1oZWFkZXIgLnZ4ZS1oZWFkZXItLWNvbHVtbiB7DQogIGJhY2tncm91bmQtY29sb3I6ICNmNWY3ZmEgIWltcG9ydGFudDsNCiAgY29sb3I6ICMzMDMxMzMgIWltcG9ydGFudDsNCiAgZm9udC13ZWlnaHQ6IDUwMCAhaW1wb3J0YW50Ow0KICBib3JkZXI6IDFweCBzb2xpZCAjZWJlZWY1ICFpbXBvcnRhbnQ7DQp9DQoNCi8qIOehruS/neihqOWktOWIhue7hOato+ehruaYvuekuiAqLw0KLnZ4ZS10YWJsZSA6OnYtZGVlcCAudnhlLXRhYmxlLS1oZWFkZXItd3JhcHBlciB7DQogIGJvcmRlcjogMXB4IHNvbGlkICNlYmVlZjUgIWltcG9ydGFudDsNCn0NCg0KLnZ4ZS10YWJsZSA6OnYtZGVlcCAudnhlLXRhYmxlLS1oZWFkZXIgew0KICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2ViZWVmNSAhaW1wb3J0YW50Ow0KfQ0KDQovKiDlvLrliLbmmL7npLrliIbnu4TooajlpLQgKi8NCi52eGUtdGFibGUgOjp2LWRlZXAgLnZ4ZS10YWJsZS0taGVhZGVyIC52eGUtaGVhZGVyLS1ncm91cCB7DQogIGRpc3BsYXk6IHRhYmxlLWNlbGwgIWltcG9ydGFudDsNCiAgdmVydGljYWwtYWxpZ246IG1pZGRsZSAhaW1wb3J0YW50Ow0KfQ0KDQovKiDliIbnu4TooajlpLTmloflrZfmoLflvI8gKi8NCi52eGUtdGFibGUgOjp2LWRlZXAgLnZ4ZS1oZWFkZXItLWdyb3VwLXRpdGxlIHsNCiAgZGlzcGxheTogYmxvY2sgIWltcG9ydGFudDsNCiAgd2lkdGg6IDEwMCUgIWltcG9ydGFudDsNCiAgdGV4dC1hbGlnbjogY2VudGVyICFpbXBvcnRhbnQ7DQogIGZvbnQtc2l6ZTogMTRweCAhaW1wb3J0YW50Ow0KICBsaW5lLWhlaWdodDogMS41ICFpbXBvcnRhbnQ7DQp9DQoNCi8qIOehruS/neihqOWktOWIhue7hOi+ueahhuaYvuekuiAqLw0KLnZ4ZS10YWJsZSA6OnYtZGVlcCAudnhlLXRhYmxlLS1oZWFkZXIgLnZ4ZS1oZWFkZXItLWdyb3VwIHsNCiAgYm9yZGVyLXJpZ2h0OiAxcHggc29saWQgI2QxZTdmZiAhaW1wb3J0YW50Ow0KICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2QxZTdmZiAhaW1wb3J0YW50Ow0KfQ0KDQovKiDmnIDlkI7kuIDkuKrliIbnu4TooajlpLTlj7PovrnmoYYgKi8NCi52eGUtdGFibGUgOjp2LWRlZXAgLnZ4ZS10YWJsZS0taGVhZGVyIC52eGUtaGVhZGVyLS1ncm91cDpsYXN0LWNoaWxkIHsNCiAgYm9yZGVyLXJpZ2h0OiAxcHggc29saWQgI2ViZWVmNSAhaW1wb3J0YW50Ow0KfQ0KDQovKiDnoa7kv53ooajlpLTmloflrZflsYXkuK0gKi8NCi52eGUtdGFibGUgOjp2LWRlZXAgLnZ4ZS10YWJsZS0taGVhZGVyIC52eGUtaGVhZGVyLS1jb2x1bW4gLnZ4ZS1jZWxsLS10aXRsZSB7DQogIHRleHQtYWxpZ246IGNlbnRlciAhaW1wb3J0YW50Ow0KICBkaXNwbGF5OiBmbGV4ICFpbXBvcnRhbnQ7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXIgIWltcG9ydGFudDsNCiAganVzdGlmeS1jb250ZW50OiBjZW50ZXIgIWltcG9ydGFudDsNCiAgaGVpZ2h0OiAxMDAlICFpbXBvcnRhbnQ7DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4oBA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/qualityCost/companySummary2", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <!-- 质量成本表格 -->\r\n    <div :style=\"containerStyle\">\r\n      <!-- 标题和产量信息 -->\r\n      <div style=\"position: relative; width: 100%; display: flex; justify-content: center; margin: 20px 0;\">\r\n        <h3 style=\"text-align: center; margin: 0;\">各分厂改判汇总表</h3>\r\n        <div style=\"position: absolute; right: 0; bottom: -5px; font-size: 16px; font-weight: bold; color: #333;\">\r\n          <!-- 类型选择框 -->\r\n          <!-- <el-select \r\n            v-model=\"selectedType\" \r\n            placeholder=\"选择类型\" \r\n            style=\"width: 100px; margin-right: 10px;\"\r\n            @change=\"handleTypeChange\">\r\n            <el-option\r\n              v-for=\"type in typeOptions\"\r\n              :key=\"type\"\r\n              :label=\"type\"\r\n              :value=\"type\">\r\n            </el-option>\r\n          </el-select> -->\r\n          <!-- 年份选择框 -->\r\n          <el-select \r\n            v-model=\"selectedYear\" \r\n            placeholder=\"选择年份\" \r\n            style=\"width: 100px; margin-right: 20px;\"\r\n            @change=\"handleYearChange\">\r\n            <el-option\r\n              v-for=\"year in yearOptions\"\r\n              :key=\"year\"\r\n              :label=\"year + '年'\"\r\n              :value=\"year\">\r\n            </el-option>\r\n          </el-select>\r\n         \r\n        </div>\r\n      </div>\r\n\r\n      <div :style=\"tableContainerStyle\">\r\n        <vxe-table\r\n          :loading=\"loading\"\r\n          :data=\"qualityCostList\"\r\n          border\r\n          v-bind=\"tableHeight ? { height: tableHeight } : {}\"\r\n          :class=\"{ 'no-scroll': !needScrollbar }\"\r\n          style=\"table-layout: fixed; width: 100%; margin: 0 auto; padding: 0;\"\r\n          :header-cell-style=\"{ backgroundColor: '#f5f7fa', color: '#303133' }\"\r\n          :merge-cells=\"mergeCells\"\r\n          stripe\r\n          :auto-resize=\"!tableHeight\"\r\n          :scroll-y=\"{enabled: needScrollbar}\"\r\n          :scroll-x=\"{enabled: true}\">\r\n          <vxe-column title=\"分厂\" align=\"center\" field=\"company\" width=\"10%\" fixed=\"left\">\r\n            <template #default=\"{ row }\">\r\n              <span :style=\"{ fontWeight: 'bold' }\">\r\n                {{ row.company }}\r\n              </span>\r\n            </template>\r\n          </vxe-column>\r\n\r\n          <!-- 一月分组 -->\r\n          <vxe-colgroup title=\"一月\" align=\"center\">\r\n            <vxe-column title=\"金额（元）\" align=\"center\" field=\"januaryAmount\" width=\"7%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatCurrency(row.januaryAmount) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n            <vxe-column title=\"吨位\" align=\"center\" field=\"januaryPerTon\" width=\"7%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatTon(row.januaryPerTon) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n          </vxe-colgroup>\r\n\r\n          <!-- 二月分组 -->\r\n          <vxe-colgroup title=\"二月\" align=\"center\">\r\n            <vxe-column title=\"金额（元）\" align=\"center\" field=\"februaryAmount\" width=\"7%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatCurrency(row.februaryAmount) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n            <vxe-column title=\"吨位\" align=\"center\" field=\"februaryPerTon\" width=\"7%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatTon(row.februaryPerTon) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n          </vxe-colgroup>\r\n\r\n          <!-- 三月分组 -->\r\n          <vxe-colgroup title=\"三月\" align=\"center\">\r\n            <vxe-column title=\"金额（元）\" align=\"center\" field=\"marchAmount\" width=\"7%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatCurrency(row.marchAmount) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n            <vxe-column title=\"吨位\" align=\"center\" field=\"marchPerTon\" width=\"7%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatTon(row.marchPerTon) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n          </vxe-colgroup>\r\n\r\n          <!-- 四月分组 -->\r\n          <vxe-colgroup title=\"四月\" align=\"center\">\r\n            <vxe-column title=\"金额（元）\" align=\"center\" field=\"aprilAmount\" width=\"7%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatCurrency(row.aprilAmount) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n            <vxe-column title=\"吨位\" align=\"center\" field=\"aprilPerTon\" width=\"7%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatTon(row.aprilPerTon) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n          </vxe-colgroup>\r\n\r\n          <!-- 五月分组 -->\r\n          <vxe-colgroup title=\"五月\" align=\"center\">\r\n            <vxe-column title=\"金额（元）\" align=\"center\" field=\"mayAmount\" width=\"7%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatCurrency(row.mayAmount) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n            <vxe-column title=\"吨位\" align=\"center\" field=\"mayPerTon\" width=\"7%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatTon(row.mayPerTon) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n          </vxe-colgroup>\r\n\r\n          <!-- 六月分组 -->\r\n          <vxe-colgroup title=\"六月\" align=\"center\">\r\n            <vxe-column title=\"金额（元）\" align=\"center\" field=\"juneAmount\" width=\"7%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatCurrency(row.juneAmount) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n            <vxe-column title=\"吨位\" align=\"center\" field=\"junePerTon\" width=\"7%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatTon(row.junePerTon) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n          </vxe-colgroup>\r\n\r\n          <!-- 七月分组 -->\r\n          <vxe-colgroup title=\"七月\" align=\"center\">\r\n            <vxe-column title=\"金额（元）\" align=\"center\" field=\"julyAmount\" width=\"7%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatCurrency(row.julyAmount) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n            <vxe-column title=\"吨位\" align=\"center\" field=\"julyPerTon\" width=\"7%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatTon(row.julyPerTon) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n          </vxe-colgroup>\r\n\r\n          <!-- 八月分组 -->\r\n          <vxe-colgroup title=\"八月\" align=\"center\">\r\n            <vxe-column title=\"金额（元）\" align=\"center\" field=\"augustAmount\" width=\"7%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatCurrency(row.augustAmount) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n            <vxe-column title=\"吨位\" align=\"center\" field=\"augustPerTon\" width=\"7%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatTon(row.augustPerTon) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n          </vxe-colgroup>\r\n\r\n          <!-- 九月分组 -->\r\n          <vxe-colgroup title=\"九月\" align=\"center\">\r\n            <vxe-column title=\"金额（元）\" align=\"center\" field=\"septemberAmount\" width=\"7%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatCurrency(row.septemberAmount) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n            <vxe-column title=\"吨位\" align=\"center\" field=\"septemberPerTon\" width=\"7%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatTon(row.septemberPerTon) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n          </vxe-colgroup>\r\n\r\n          <!-- 十月分组 -->\r\n          <vxe-colgroup title=\"十月\" align=\"center\">\r\n            <vxe-column title=\"金额（元）\" align=\"center\" field=\"octoberAmount\" width=\"7%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatCurrency(row.octoberAmount) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n            <vxe-column title=\"吨位\" align=\"center\" field=\"octoberPerTon\" width=\"7%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatTon(row.octoberPerTon) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n          </vxe-colgroup>\r\n\r\n          <!-- 十一月分组 -->\r\n          <vxe-colgroup title=\"十一月\" align=\"center\">\r\n            <vxe-column title=\"金额（元）\" align=\"center\" field=\"novemberAmount\" width=\"7%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatCurrency(row.novemberAmount) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n            <vxe-column title=\"吨位\" align=\"center\" field=\"novemberPerTon\" width=\"7%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatTon(row.novemberPerTon) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n          </vxe-colgroup>\r\n\r\n          <!-- 十二月分组 -->\r\n          <vxe-colgroup title=\"十二月\" align=\"center\">\r\n            <vxe-column title=\"金额（元）\" align=\"center\" field=\"decemberAmount\" width=\"7%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatCurrency(row.decemberAmount) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n            <vxe-column title=\"吨位\" align=\"center\" field=\"decemberPerTon\" width=\"7%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatTon(row.decemberPerTon) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n          </vxe-colgroup>\r\n\r\n          <!-- 年度累计分组 -->\r\n          <vxe-colgroup title=\"年度累计\" align=\"center\">\r\n            <vxe-column title=\"金额（元）\" align=\"center\" field=\"yearlyTotalAmount\" width=\"8%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'bold' }\">\r\n                  {{ formatCurrency(row.yearlyTotalAmount) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n            <vxe-column title=\"吨位\" align=\"center\" field=\"yearlyTotalPerTon\" width=\"8%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'bold' }\">\r\n                  {{ formatTon(row.yearlyTotalPerTon) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n          </vxe-colgroup>\r\n        </vxe-table>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listCompanySummaryData } from \"@/api/qualityCost/companySummary1\";\r\nimport { testFactoryData, testFactoryDataWithRealValues, testDataMapping } from \"./testData\";\r\n\r\n\r\nexport default {\r\n  name: \"CompanySummary1\",\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: false,\r\n      // 质量成本数据列表\r\n      qualityCostList: [],\r\n      // 合并单元格配置\r\n      mergeCells: [],\r\n      // 表格高度\r\n      tableHeight: null,\r\n      // 是否需要滚动条\r\n      needScrollbar: true,\r\n      // 年份选择\r\n      selectedYear: new Date().getFullYear(),\r\n      // 年份选项\r\n      yearOptions: [],\r\n      // 类型选择\r\n      selectedType: '报废',\r\n      // 类型选项\r\n      typeOptions: ['改判', '报废', '脱合同', '退货']\r\n    };\r\n  },\r\n  computed: {\r\n    /** 容器样式 */\r\n    containerStyle() {\r\n      return {\r\n        height: this.tableHeight ? `${this.tableHeight}px` : 'auto'\r\n      };\r\n    },\r\n    /** 表格容器样式 */\r\n    tableContainerStyle() {\r\n      return {\r\n        height: this.tableHeight ? `${this.tableHeight - 80}px` : 'auto'\r\n      };\r\n    }\r\n  },\r\n  mounted() {\r\n    this.calculateTableHeight();\r\n    window.addEventListener('resize', this.calculateTableHeight);\r\n    this.generateYearOptions();\r\n  },\r\n  beforeDestroy() {\r\n    window.removeEventListener('resize', this.calculateTableHeight);\r\n  },\r\n  methods: {\r\n    getCompanySummaryData() {\r\n      this.loading = true;\r\n      \r\n      const params = {\r\n        yearMonth: this.selectedYear,\r\n        companySummaryType: this.selectedType\r\n      };\r\n\r\n      listCompanySummaryData(params).then(response => {\r\n        console.log('listCompanySummaryData:', response);\r\n        if (response.data) {\r\n          // 处理分厂数据格式\r\n          this.processFactoryData(response.data);\r\n        } else {\r\n          this.qualityCostList = [];\r\n        }\r\n        this.loading = false;\r\n      }).catch(error => {\r\n        console.error('获取数据失败:', error);\r\n        this.$message.error('获取质量成本数据失败');\r\n        this.loading = false;\r\n      });\r\n    },\r\n\r\n    /** 处理分厂数据格式 */\r\n    processFactoryData(rawData) {\r\n      console.log('开始处理分厂数据:', rawData);\r\n      \r\n      // 检查是否为分厂数据格式\r\n      if (this.isFactoryDataFormat(rawData)) {\r\n        console.log('检测到分厂数据格式，开始处理...');\r\n        \r\n      // 转换为表格格式\r\n        const tableData = this.convertFactoryDataToTable(rawData);\r\n        \r\n        // 更新表格数据\r\n        this.updateTableWithFactoryData(tableData);\r\n      } else {\r\n        console.log('不是分厂数据格式，使用默认处理');\r\n        this.qualityCostList = [];\r\n      }\r\n    },\r\n\r\n    /** 检查是否为分厂数据格式 */\r\n    isFactoryDataFormat(data) {\r\n      if (!data || typeof data !== 'object') return false;\r\n      \r\n      const factoryKeys = Object.keys(data);\r\n      if (factoryKeys.length === 0) return false;\r\n      \r\n      const firstFactory = data[factoryKeys[0]];\r\n      if (!firstFactory || typeof firstFactory !== 'object') return false;\r\n      \r\n      const monthKeys = Object.keys(firstFactory);\r\n      if (monthKeys.length === 0) return false;\r\n      \r\n      const firstMonth = firstFactory[monthKeys[0]];\r\n      return firstMonth && \r\n             typeof firstMonth === 'object' && \r\n             'costEx' in firstMonth && \r\n             'costTon' in firstMonth;\r\n    },\r\n\r\n    /** 清理模拟数据 */\r\n    cleanMockData(data) {\r\n      const cleanedData = {};\r\n      \r\n      Object.keys(data).forEach(factoryName => {\r\n        const factoryData = data[factoryName];\r\n        const validMonths = {};\r\n        \r\n        Object.keys(factoryData).forEach(monthKey => {\r\n          const monthData = factoryData[monthKey];\r\n          \r\n          // 检查是否为模拟数据\r\n          if (!this.isMockData(monthData)) {\r\n            validMonths[monthKey] = monthData;\r\n          }\r\n        });\r\n        \r\n        // 只有当分厂有有效数据时才保留\r\n        if (Object.keys(validMonths).length > 0) {\r\n          cleanedData[factoryName] = validMonths;\r\n        }\r\n      });\r\n      \r\n      console.log('清理后的数据:', cleanedData);\r\n      return cleanedData;\r\n    },\r\n\r\n    /** 判断是否为模拟数据 */\r\n    isMockData(monthData) {\r\n      return (\r\n        monthData.costCenterName === null &&\r\n        monthData.costEx === 0 &&\r\n        monthData.costTon === 0 &&\r\n        monthData.yearMonth === null\r\n      );\r\n    },\r\n\r\n    /** 将分厂数据转换为表格格式 */\r\n    convertFactoryDataToTable(factoryData) {\r\n      const tableData = [];\r\n      \r\n      Object.keys(factoryData).forEach(factoryName => {\r\n        const factoryMonths = factoryData[factoryName];\r\n        \r\n        Object.keys(factoryMonths).forEach(monthKey => {\r\n          const monthData = factoryMonths[monthKey];\r\n          \r\n          // 创建表格行数据\r\n          const rowData = {\r\n            factoryName: factoryName,\r\n            month: monthKey,\r\n            costEx: monthData.costEx || 0,\r\n            costTon: monthData.costTon || 0,\r\n            yearMonth: monthData.yearMonth,\r\n            costCenterName: monthData.costCenterName\r\n          };\r\n          \r\n          tableData.push(rowData);\r\n        });\r\n      });\r\n      \r\n      console.log('转换后的表格数据:', tableData);\r\n      return tableData;\r\n    },\r\n\r\n    /** 更新表格显示分厂数据 */\r\n    updateTableWithFactoryData(tableData) {\r\n      if (tableData.length === 0) {\r\n        this.qualityCostList = [];\r\n        return;\r\n      }\r\n      \r\n      // 获取所有分厂名称\r\n      const factoryNames = [...new Set(tableData.map(row => row.factoryName))];\r\n      \r\n      // 创建表格行数据\r\n      this.qualityCostList = factoryNames.map(factoryName => {\r\n        const rowData = {\r\n          company: factoryName\r\n        };\r\n        \r\n        // 初始化所有月份的数据为0\r\n        const months = ['january', 'february', 'march', 'april', 'may', 'june',\r\n                       'july', 'august', 'september', 'october', 'november', 'december'];\r\n        \r\n        months.forEach((month, index) => {\r\n          const monthNumber = index + 1;\r\n          const monthData = tableData.find(row => \r\n            row.factoryName === factoryName && row.month === monthNumber.toString()\r\n          );\r\n          \r\n          if (monthData) {\r\n            rowData[`${month}Amount`] = monthData.costEx;\r\n            rowData[`${month}PerTon`] = monthData.costTon;\r\n          } else {\r\n            rowData[`${month}Amount`] = 0;\r\n            rowData[`${month}PerTon`] = 0;\r\n          }\r\n        });\r\n        \r\n        // 计算年度累计\r\n        rowData.yearlyTotalAmount = months.reduce((sum, month) => sum + (rowData[`${month}Amount`] || 0), 0);\r\n        rowData.yearlyTotalPerTon = months.reduce((sum, month) => sum + (rowData[`${month}PerTon`] || 0), 0);\r\n        \r\n        return rowData;\r\n      });\r\n      \r\n      console.log('更新后的表格数据:', this.qualityCostList);\r\n    },\r\n\r\n\r\n    /** 格式化货币 */\r\n    formatCurrency(value) {\r\n      if (value === null || value === undefined || value === '' || Number(value) === 0) {\r\n        return '-';\r\n      }\r\n      const num = Number(value);\r\n      // 金额显示为元，不转换为万\r\n      return num.toLocaleString('zh-CN', {\r\n        minimumFractionDigits: 2,\r\n        maximumFractionDigits: 2\r\n      });\r\n    },\r\n\r\n    /** 格式化吨位 */\r\n    formatTon(value) {\r\n      if (value === null || value === undefined || value === '' || Number(value) === 0) {\r\n        return '-';\r\n      }\r\n      const num = Number(value);\r\n      // 吨位显示为吨，保留2位小数\r\n      return num.toLocaleString('zh-CN', {\r\n        minimumFractionDigits: 2,\r\n        maximumFractionDigits: 2\r\n      });\r\n    },\r\n\r\n    /** 计算表格高度 */\r\n    calculateTableHeight() {\r\n      this.$nextTick(() => {\r\n        const windowHeight = window.innerHeight;\r\n        // 减去页面头部、标题等高度，大约100px\r\n        const availableHeight = windowHeight - 100;\r\n        // 设置表格最大高度，最小400px，最大不超过可用高度的90%\r\n        this.tableHeight = Math.max(400, Math.min(800, availableHeight * 0.9));\r\n        this.needScrollbar = this.qualityCostList.length > 8;\r\n      });\r\n    },\r\n\r\n    /** 生成年份选项 */\r\n    generateYearOptions() {\r\n      const currentYear = new Date().getFullYear();\r\n      for (let i = 2000; i <= currentYear; i++) {\r\n        this.yearOptions.push(i);\r\n      }\r\n      this.getCompanySummaryData()\r\n    },\r\n\r\n         /** 年份选择变化 */\r\n     handleYearChange(year) {\r\n       this.selectedYear = year;\r\n       // 重新获取数据\r\n       this.getCompanySummaryData();\r\n     },\r\n\r\n     /** 类型选择变化 */\r\n     handleTypeChange(type) {\r\n       this.selectedType = type;\r\n       // 重新获取数据\r\n       this.getCompanySummaryData();\r\n     },\r\n\r\n     /** 测试分厂数据处理 */\r\n     testFactoryDataProcessing() {\r\n       console.log('开始测试分厂数据处理...');\r\n       \r\n       // 测试1: 纯模拟数据\r\n       console.log('=== 测试1: 纯模拟数据 ===');\r\n       this.processFactoryData(testFactoryData);\r\n       \r\n       // 等待2秒后测试真实数据\r\n       setTimeout(() => {\r\n         console.log('=== 测试2: 包含真实数据 ===');\r\n         this.processFactoryData(testFactoryDataWithRealValues);\r\n       }, 2000);\r\n       \r\n       // 等待4秒后测试数据映射\r\n       setTimeout(() => {\r\n         console.log('=== 测试3: 数据映射验证 ===');\r\n         this.processFactoryData(testDataMapping);\r\n         this.verifyDataMapping();\r\n       }, 4000);\r\n     },\r\n\r\n     /** 验证数据映射 */\r\n     verifyDataMapping() {\r\n       console.log('验证数据映射...');\r\n       \r\n       if (this.qualityCostList.length > 0) {\r\n         this.qualityCostList.forEach((row, index) => {\r\n           console.log(`第${index + 1}行 - 分厂: ${row.company}`);\r\n           console.log(`  一月金额: ${row.januaryAmount}, 一月吨位: ${row.januaryPerTon}`);\r\n           console.log(`  二月金额: ${row.februaryAmount}, 二月吨位: ${row.februaryPerTon}`);\r\n           console.log(`  三月金额: ${row.marchAmount}, 三月吨位: ${row.marchPerTon}`);\r\n         });\r\n       }\r\n     }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n/* 页面容器样式 */\r\n.app-container {\r\n  position: relative;\r\n  padding: 20px;\r\n}\r\n\r\n/* 表格容器样式 */\r\n.table-container {\r\n  margin-top: 20px;\r\n}\r\n\r\n/* 表格滚动容器 */\r\n.table-scroll-container {\r\n  width: 100%;\r\n  position: relative;\r\n  border: 1px solid #ebeef5;\r\n  border-radius: 4px;\r\n}\r\n\r\n/* 表格单元格内容 */\r\n.table-cell-content {\r\n  padding: 10px 0;\r\n}\r\n</style>\r\n\r\n<style>\r\n/* 非scoped样式，确保能够覆盖vxe-table的默认样式 */\r\n\r\n/* 强制覆盖所有滚动条样式 - 15px粗细，淡蓝色主题 */\r\n.vxe-table ::-webkit-scrollbar {\r\n  width: 15px !important;\r\n  height: 15px !important;\r\n}\r\n\r\n.vxe-table ::-webkit-scrollbar-track {\r\n  background: #e3f2fd !important;\r\n  border-radius: 7px !important;\r\n}\r\n\r\n.vxe-table ::-webkit-scrollbar-thumb {\r\n  background: #64b5f6 !important;\r\n  border-radius: 7px !important;\r\n  border: none !important;\r\n}\r\n\r\n.vxe-table ::-webkit-scrollbar-thumb:hover {\r\n  background: #42a5f5 !important;\r\n}\r\n\r\n.vxe-table ::-webkit-scrollbar-thumb:active {\r\n  background: #2196f3 !important;\r\n}\r\n\r\n.vxe-table ::-webkit-scrollbar-corner {\r\n  background: #e3f2fd !important;\r\n}\r\n\r\n/* vxe-table 表格边框和样式 */\r\n.vxe-table ::v-deep .vxe-table {\r\n  border: 1px solid #ebeef5;\r\n}\r\n\r\n.vxe-table ::v-deep .vxe-table--border-line {\r\n  border-color: #ebeef5;\r\n}\r\n\r\n/* 表头样式 */\r\n.vxe-table ::v-deep .vxe-table--header-wrapper .vxe-table--header {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n.vxe-table ::v-deep .vxe-table--header .vxe-header--column {\r\n  background-color: #f5f7fa;\r\n  color: #303133;\r\n  font-weight: 500;\r\n}\r\n\r\n/* 表头分组样式 */\r\n.vxe-table ::v-deep .vxe-table--header .vxe-header--group {\r\n  background-color: #e8f4fd !important;\r\n  color: #1890ff !important;\r\n  font-weight: 600 !important;\r\n  border: 1px solid #d1e7ff !important;\r\n}\r\n\r\n/* 确保分组表头显示 */\r\n.vxe-table ::v-deep .vxe-table--header .vxe-header--group .vxe-header--group-title {\r\n  background-color: #e8f4fd !important;\r\n  color: #1890ff !important;\r\n  font-weight: 600 !important;\r\n  padding: 8px 4px !important;\r\n  text-align: center !important;\r\n}\r\n\r\n/* 子列表头样式 */\r\n.vxe-table ::v-deep .vxe-table--header .vxe-header--column {\r\n  background-color: #f5f7fa !important;\r\n  color: #303133 !important;\r\n  font-weight: 500 !important;\r\n  border: 1px solid #ebeef5 !important;\r\n}\r\n\r\n/* 确保表头分组正确显示 */\r\n.vxe-table ::v-deep .vxe-table--header-wrapper {\r\n  border: 1px solid #ebeef5 !important;\r\n}\r\n\r\n.vxe-table ::v-deep .vxe-table--header {\r\n  border-bottom: 1px solid #ebeef5 !important;\r\n}\r\n\r\n/* 强制显示分组表头 */\r\n.vxe-table ::v-deep .vxe-table--header .vxe-header--group {\r\n  display: table-cell !important;\r\n  vertical-align: middle !important;\r\n}\r\n\r\n/* 分组表头文字样式 */\r\n.vxe-table ::v-deep .vxe-header--group-title {\r\n  display: block !important;\r\n  width: 100% !important;\r\n  text-align: center !important;\r\n  font-size: 14px !important;\r\n  line-height: 1.5 !important;\r\n}\r\n\r\n/* 确保表头分组边框显示 */\r\n.vxe-table ::v-deep .vxe-table--header .vxe-header--group {\r\n  border-right: 1px solid #d1e7ff !important;\r\n  border-bottom: 1px solid #d1e7ff !important;\r\n}\r\n\r\n/* 最后一个分组表头右边框 */\r\n.vxe-table ::v-deep .vxe-table--header .vxe-header--group:last-child {\r\n  border-right: 1px solid #ebeef5 !important;\r\n}\r\n\r\n/* 确保表头文字居中 */\r\n.vxe-table ::v-deep .vxe-table--header .vxe-header--column .vxe-cell--title {\r\n  text-align: center !important;\r\n  display: flex !important;\r\n  align-items: center !important;\r\n  justify-content: center !important;\r\n  height: 100% !important;\r\n}\r\n</style> "]}]}