package com.ruoyi.app.leave.controller;

import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.app.leave.domain.LeaveTask;
import com.ruoyi.app.leave.service.ILeaveTaskService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 出门证任务Controller
 * 
 * <AUTHOR>
 * @date 2025-03-13
 */
@RestController
@RequestMapping("/leave/task")
public class LeaveTaskController extends BaseController
{
    @Autowired
    private ILeaveTaskService leaveTaskService;

    /**
     * 查询出门证任务列表
     */
    @PreAuthorize("@ss.hasPermi('leave:task:list')")
    @GetMapping("/list")
    public TableDataInfo list(LeaveTask leaveTask)
    {
        startPage();
        List<LeaveTask> list = leaveTaskService.selectLeaveTaskList(leaveTask);
        return getDataTable(list);
    }

    /**
     * 导出出门证任务列表
     */
    @PreAuthorize("@ss.hasPermi('leave:task:export')")
    @Log(title = "出门证任务", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(LeaveTask leaveTask)
    {
        List<LeaveTask> list = leaveTaskService.selectLeaveTaskList(leaveTask);
        ExcelUtil<LeaveTask> util = new ExcelUtil<LeaveTask>(LeaveTask.class);
        return util.exportExcel(list, "task");
    }

    /**
     * 获取出门证任务详细信息
     */
    @PreAuthorize("@ss.hasPermi('leave:task:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(leaveTaskService.selectLeaveTaskById(id));
    }

    /**
     * 新增出门证任务
     */
    @PreAuthorize("@ss.hasPermi('leave:task:add')")
    @Log(title = "出门证任务", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody LeaveTask leaveTask)
    {
        return toAjax(leaveTaskService.insertLeaveTask(leaveTask));
    }

    /**
     * 修改出门证任务
     */
    @PreAuthorize("@ss.hasPermi('leave:task:edit')")
    @Log(title = "出门证任务", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody LeaveTask leaveTask)
    {
        return toAjax(leaveTaskService.updateLeaveTask(leaveTask));
    }

    /**
     * 删除出门证任务
     */
    @PreAuthorize("@ss.hasPermi('leave:task:remove')")
    @Log(title = "出门证任务", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(leaveTaskService.deleteLeaveTaskByIds(ids));
    }
}
