package com.ruoyi.app.leave.service;

import java.util.List;
import com.ruoyi.app.leave.domain.LeaveCustomer;

/**
 * 出门证厂外客户Service接口
 * 
 * <AUTHOR>
 * @date 2025-03-13
 */
public interface ILeaveCustomerService 
{
    /**
     * 查询出门证厂外客户
     * 
     * @param id 出门证厂外客户ID
     * @return 出门证厂外客户
     */
    public LeaveCustomer selectLeaveCustomerById(Long id);

    /**
     * 查询出门证厂外客户列表
     * 
     * @param leaveCustomer 出门证厂外客户
     * @return 出门证厂外客户集合
     */
    public List<LeaveCustomer> selectLeaveCustomerList(LeaveCustomer leaveCustomer);

    /**
     * 新增出门证厂外客户
     * 
     * @param leaveCustomer 出门证厂外客户
     * @return 结果
     */
    public int insertLeaveCustomer(LeaveCustomer leaveCustomer);

    /**
     * 修改出门证厂外客户
     * 
     * @param leaveCustomer 出门证厂外客户
     * @return 结果
     */
    public int updateLeaveCustomer(LeaveCustomer leaveCustomer);

    /**
     * 批量删除出门证厂外客户
     * 
     * @param ids 需要删除的出门证厂外客户ID
     * @return 结果
     */
    public int deleteLeaveCustomerByIds(Long[] ids);

    /**
     * 删除出门证厂外客户信息
     * 
     * @param id 出门证厂外客户ID
     * @return 结果
     */
    public int deleteLeaveCustomerById(Long id);
}
