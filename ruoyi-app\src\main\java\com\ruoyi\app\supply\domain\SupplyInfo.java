package com.ruoyi.app.supply.domain;

import com.ruoyi.common.annotation.Excel;

public class SupplyInfo {
    /**
     * 供应商代码
     */
    @Excel(name = "供应商代码")
    private Integer supplyCode;

    /**
     * 供应商名称
     */
    @Excel(name = "供应商名称")
    private String supplyName;

    /**
     * 供应商税号
     */
    @Excel(name = "供应商税号")
    private String supplyTax;

    /**
     * 供应商地址
     */
    @Excel(name = "供应商地址")
    private String supplyAddr;
    /**
     * 负责人
     */
    @Excel(name = "负责人")
    private String supplyCharge;
    /**
     * 联系电话
     */
    @Excel(name = "联系电话")
    private String supplyTel;
    /**
     * 状态
     */
    @Excel(name = "状态")
    private String state;

    public Integer getSupplyCode() {
        return supplyCode;
    }

    public void setSupplyCode(Integer supplyCode) {
        this.supplyCode = supplyCode;
    }

    public String getSupplyName() {
        return supplyName;
    }

    public void setSupplyName(String supplyName) {
        this.supplyName = supplyName;
    }

    public String getSupplyTax() {
        return supplyTax;
    }

    public void setSupplyTax(String supplyTax) {
        this.supplyTax = supplyTax;
    }

    public String getSupplyAddr() {
        return supplyAddr;
    }

    public void setSupplyAddr(String supplyAddr) {
        this.supplyAddr = supplyAddr;
    }

    public String getSupplyCharge() {
        return supplyCharge;
    }

    public void setSupplyCharge(String supplyCharge) {
        this.supplyCharge = supplyCharge;
    }

    public String getSupplyTel() {
        return supplyTel;
    }

    public void setSupplyTel(String supplyTel) {
        this.supplyTel = supplyTel;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }
}
