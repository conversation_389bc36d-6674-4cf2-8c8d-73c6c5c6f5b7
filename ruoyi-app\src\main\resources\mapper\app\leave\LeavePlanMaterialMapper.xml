<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.app.leave.mapper.LeavePlanMaterialMapper">
    
    <resultMap type="LeavePlanMaterial" id="LeavePlanMaterialResult">
        <result property="id"    column="id"    />
        <result property="planId"    column="plan_id"    />
        <result property="materialId"    column="material_id"    />
        <result property="materialNo"    column="material_no"    />
        <result property="materialType"    column="material_type"    />
        <result property="materialName"    column="material_name"    />
        <result property="materialSpec"    column="material_spec"    />
        <result property="measureUnit"    column="measure_unit"    />
        <result property="measureFlag"    column="measure_flag"    />
        <result property="planNum"    column="plan_num"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="selectLeavePlanMaterialVo">
        select id, plan_id, material_id, material_no, material_type, material_name, material_spec, measure_unit, measure_flag, plan_num, create_time, create_by, update_time, update_by from leave_plan_material
    </sql>

    <select id="selectLeavePlanMaterialList" parameterType="LeavePlanMaterial" resultMap="LeavePlanMaterialResult">
        <include refid="selectLeavePlanMaterialVo"/>
        <where>  
            <if test="planId != null "> and plan_id = #{planId}</if>
            <if test="materialId != null "> and material_id = #{materialId}</if>
            <if test="materialNo != null  and materialNo != ''"> and material_no = #{materialNo}</if>
            <if test="materialType != null "> and material_type = #{materialType}</if>
            <if test="materialName != null  and materialName != ''"> and material_name like concat('%', #{materialName}, '%')</if>
            <if test="materialSpec != null  and materialSpec != ''"> and material_spec = #{materialSpec}</if>
            <if test="measureUnit != null  and measureUnit != ''"> and measure_unit = #{measureUnit}</if>
            <if test="measureFlag != null "> and measure_flag = #{measureFlag}</if>
            <if test="planNum != null "> and plan_num = #{planNum}</if>
        </where>
    </select>
    
    <select id="selectLeavePlanMaterialById" parameterType="Long" resultMap="LeavePlanMaterialResult">
        <include refid="selectLeavePlanMaterialVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertLeavePlanMaterial" parameterType="LeavePlanMaterial" useGeneratedKeys="true" keyProperty="id">
        insert into leave_plan_material
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="planId != null">plan_id,</if>
            <if test="materialId != null">material_id,</if>
            <if test="materialNo != null">material_no,</if>
            <if test="materialType != null">material_type,</if>
            <if test="materialName != null">material_name,</if>
            <if test="materialSpec != null">material_spec,</if>
            <if test="measureUnit != null">measure_unit,</if>
            <if test="measureFlag != null">measure_flag,</if>
            <if test="planNum != null">plan_num,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="planId != null">#{planId},</if>
            <if test="materialId != null">#{materialId},</if>
            <if test="materialNo != null">#{materialNo},</if>
            <if test="materialType != null">#{materialType},</if>
            <if test="materialName != null">#{materialName},</if>
            <if test="materialSpec != null">#{materialSpec},</if>
            <if test="measureUnit != null">#{measureUnit},</if>
            <if test="measureFlag != null">#{measureFlag},</if>
            <if test="planNum != null">#{planNum},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
         </trim>
    </insert>

    <update id="updateLeavePlanMaterial" parameterType="LeavePlanMaterial">
        update leave_plan_material
        <trim prefix="SET" suffixOverrides=",">
            <if test="planId != null">plan_id = #{planId},</if>
            <if test="materialId != null">material_id = #{materialId},</if>
            <if test="materialNo != null">material_no = #{materialNo},</if>
            <if test="materialType != null">material_type = #{materialType},</if>
            <if test="materialName != null">material_name = #{materialName},</if>
            <if test="materialSpec != null">material_spec = #{materialSpec},</if>
            <if test="measureUnit != null">measure_unit = #{measureUnit},</if>
            <if test="measureFlag != null">measure_flag = #{measureFlag},</if>
            <if test="planNum != null">plan_num = #{planNum},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteLeavePlanMaterialById" parameterType="Long">
        delete from leave_plan_material where id = #{id}
    </delete>

    <delete id="deleteLeavePlanMaterialByIds" parameterType="String">
        delete from leave_plan_material where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
</mapper>