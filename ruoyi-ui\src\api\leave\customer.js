import request from '@/utils/request'

// 查询出门证厂外客户列表
export function listCustomer(query) {
  return request({
    url: '/leave/customer/list',
    method: 'get',
    params: query
  })
}

// 查询出门证厂外客户详细
export function getCustomer(id) {
  return request({
    url: '/leave/customer/' + id,
    method: 'get'
  })
}

// 新增出门证厂外客户
export function addCustomer(data) {
  return request({
    url: '/leave/customer',
    method: 'post',
    data: data
  })
}

// 修改出门证厂外客户
export function updateCustomer(data) {
  return request({
    url: '/leave/customer',
    method: 'put',
    data: data
  })
}

// 删除出门证厂外客户
export function delCustomer(id) {
  return request({
    url: '/leave/customer/' + id,
    method: 'delete'
  })
}

// 导出出门证厂外客户
export function exportCustomer(query) {
  return request({
    url: '/leave/customer/export',
    method: 'get',
    params: query
  })
}