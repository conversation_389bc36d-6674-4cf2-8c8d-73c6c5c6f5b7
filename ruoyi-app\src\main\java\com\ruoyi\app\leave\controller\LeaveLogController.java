package com.ruoyi.app.leave.controller;

import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.app.leave.domain.LeaveLog;
import com.ruoyi.app.leave.service.ILeaveLogService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 出门证日志Controller
 * 
 * <AUTHOR>
 * @date 2025-03-13
 */
@RestController
@RequestMapping("/leave/log")
public class LeaveLogController extends BaseController
{
    @Autowired
    private ILeaveLogService leaveLogService;

    /**
     * 查询出门证日志列表
     */
    @PreAuthorize("@ss.hasPermi('leave:log:list')")
    @GetMapping("/list")
    public TableDataInfo list(LeaveLog leaveLog)
    {
        startPage();
        List<LeaveLog> list = leaveLogService.selectLeaveLogList(leaveLog);
        return getDataTable(list);
    }

    /**
     * 导出出门证日志列表
     */
    @PreAuthorize("@ss.hasPermi('leave:log:export')")
    @Log(title = "出门证日志", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(LeaveLog leaveLog)
    {
        List<LeaveLog> list = leaveLogService.selectLeaveLogList(leaveLog);
        ExcelUtil<LeaveLog> util = new ExcelUtil<LeaveLog>(LeaveLog.class);
        return util.exportExcel(list, "log");
    }

    /**
     * 获取出门证日志详细信息
     */
    @PreAuthorize("@ss.hasPermi('leave:log:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(leaveLogService.selectLeaveLogById(id));
    }

    /**
     * 新增出门证日志
     */
    @PreAuthorize("@ss.hasPermi('leave:log:add')")
    @Log(title = "出门证日志", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody LeaveLog leaveLog)
    {
        return toAjax(leaveLogService.insertLeaveLog(leaveLog));
    }

    /**
     * 修改出门证日志
     */
    @PreAuthorize("@ss.hasPermi('leave:log:edit')")
    @Log(title = "出门证日志", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody LeaveLog leaveLog)
    {
        return toAjax(leaveLogService.updateLeaveLog(leaveLog));
    }

    /**
     * 删除出门证日志
     */
    @PreAuthorize("@ss.hasPermi('leave:log:remove')")
    @Log(title = "出门证日志", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(leaveLogService.deleteLeaveLogByIds(ids));
    }
}
