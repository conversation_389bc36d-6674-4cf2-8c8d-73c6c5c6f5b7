<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.app.mapper.OutputFormMapper">
    
    <resultMap type="OutputForm" id="OutputFormResult">
        <result property="itemName"    column="ITEM_NAME"    />
        <result property="monthPlanProd"    column="MONTH_PLAN_PROD"    />
        <result property="dayWeight"    column="DAY_WEIGHT"    />
        <result property="totalWeight"    column="TOTAL_WEIGHT"    />
        <result property="dateCode"    column="DATE_CODE"    />
        <result property="finishPer"    column="FINISH_PER"    />
    </resultMap>

    <sql id="selectOutputFormVo">
        select ITEM_NAME, MONTH_PLAN_PROD, DAY_WEIGHT, TOTAL_WEIGHT, DATE_CODE, FINISH_PER from output_form
    </sql>

    <select id="selectOutputFormList" parameterType="OutputForm" resultMap="OutputFormResult">
        SELECT t.ITEM_NAME ,
       t.MONTH_PLAN_PROD ,
       t.DAY_WEIGHT ,
       t.TOTAL_WEIGHT ,
       t.DATE_CODE ,
       DECODE(t.MONTH_PLAN_PROD,null,0, 0,0,t.TOTAL_WEIGHT / t.MONTH_PLAN_PROD)*100 AS FINISH_PER
        FROM PCONM.VIEW_CL t
        WHERE T.DATE_CODE = #{dateCode}
        <if test="itemName != null  and itemName != ''">and t.ITEM_NAME = #{itemName}</if>
        ORDER BY SORT_NO ASC
    </select>
    
    <select id="selectOutputFormByItemName" parameterType="OutputForm" resultMap="OutputFormResult">
       SELECT t.ITEM_NAME ,
       t.MONTH_PLAN_PROD ,
       t.DAY_WEIGHT ,
       t.TOTAL_WEIGHT ,
       t.DATE_CODE ,
       DECODE(t.MONTH_PLAN_PROD,null,0, 0,0,t.TOTAL_WEIGHT / t.MONTH_PLAN_PROD)*100  AS FINISH_PER
  FROM PCONM.VIEW_CL t
 WHERE t.DATE_CODE >= #{starttime} and #{endtime} >= t.DATE_CODE  and t.ITEM_NAME = #{itemName}
 ORDER BY DATE_CODE desc ,SORT_NO ASC
    </select>


</mapper>