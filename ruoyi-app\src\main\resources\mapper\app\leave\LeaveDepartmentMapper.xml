<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.app.leave.mapper.LeaveDepartmentMapper">
    
    <resultMap type="LeaveDepartment" id="LeaveDepartmentResult">
        <result property="id"    column="id"    />
        <result property="validFlag"    column="valid_flag"    />
        <result property="storeCode"    column="store_code"    />
        <result property="storeName"    column="store_name"    />
        <result property="queryWord"    column="query_word"    />
        <result property="type"    column="type"    />
        <result property="unitName"    column="unit_name"    />
        <result property="position"    column="position"    />
        <result property="lowerLimit"    column="lower_limit"    />
        <result property="upLimit"    column="up_limit"    />
        <result property="memo"    column="memo"    />
        <result property="fStoreCode"    column="f_store_code"    />
        <result property="fStoreName"    column="f_store_name"    />
        <result property="levelName"    column="level_name"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="selectLeaveDepartmentVo">
        select id, valid_flag, store_code, store_name, query_word, type, unit_name, position, lower_limit, up_limit, memo, f_store_code, f_store_name, level_name, create_time, create_by, update_time, update_by from leave_department
    </sql>

    <select id="selectLeaveDepartmentList" parameterType="LeaveDepartment" resultMap="LeaveDepartmentResult">
        <include refid="selectLeaveDepartmentVo"/>
        <where>  
            <if test="validFlag != null "> and valid_flag = #{validFlag}</if>
            <if test="storeCode != null  and storeCode != ''"> and store_code = #{storeCode}</if>
            <if test="storeName != null  and storeName != ''"> and store_name like concat('%', #{storeName}, '%')</if>
            <if test="queryWord != null  and queryWord != ''"> and query_word = #{queryWord}</if>
            <if test="type != null "> and type = #{type}</if>
            <if test="unitName != null  and unitName != ''"> and unit_name like concat('%', #{unitName}, '%')</if>
            <if test="position != null  and position != ''"> and position = #{position}</if>
            <if test="lowerLimit != null "> and lower_limit = #{lowerLimit}</if>
            <if test="upLimit != null "> and up_limit = #{upLimit}</if>
            <if test="memo != null  and memo != ''"> and memo = #{memo}</if>
            <if test="fStoreCode != null  and fStoreCode != ''"> and f_store_code = #{fStoreCode}</if>
            <if test="fStoreName != null  and fStoreName != ''"> and f_store_name like concat('%', #{fStoreName}, '%')</if>
            <if test="levelName != null "> and level_name like concat('%', #{levelName}, '%')</if>
        </where>
    </select>
    
    <select id="selectLeaveDepartmentById" parameterType="Long" resultMap="LeaveDepartmentResult">
        <include refid="selectLeaveDepartmentVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertLeaveDepartment" parameterType="LeaveDepartment" useGeneratedKeys="true" keyProperty="id">
        insert into leave_department
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="validFlag != null">valid_flag,</if>
            <if test="storeCode != null">store_code,</if>
            <if test="storeName != null">store_name,</if>
            <if test="queryWord != null">query_word,</if>
            <if test="type != null">type,</if>
            <if test="unitName != null">unit_name,</if>
            <if test="position != null">position,</if>
            <if test="lowerLimit != null">lower_limit,</if>
            <if test="upLimit != null">up_limit,</if>
            <if test="memo != null">memo,</if>
            <if test="fStoreCode != null">f_store_code,</if>
            <if test="fStoreName != null">f_store_name,</if>
            <if test="levelName != null">level_name,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="validFlag != null">#{validFlag},</if>
            <if test="storeCode != null">#{storeCode},</if>
            <if test="storeName != null">#{storeName},</if>
            <if test="queryWord != null">#{queryWord},</if>
            <if test="type != null">#{type},</if>
            <if test="unitName != null">#{unitName},</if>
            <if test="position != null">#{position},</if>
            <if test="lowerLimit != null">#{lowerLimit},</if>
            <if test="upLimit != null">#{upLimit},</if>
            <if test="memo != null">#{memo},</if>
            <if test="fStoreCode != null">#{fStoreCode},</if>
            <if test="fStoreName != null">#{fStoreName},</if>
            <if test="levelName != null">#{levelName},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
         </trim>
    </insert>

    <update id="updateLeaveDepartment" parameterType="LeaveDepartment">
        update leave_department
        <trim prefix="SET" suffixOverrides=",">
            <if test="validFlag != null">valid_flag = #{validFlag},</if>
            <if test="storeCode != null">store_code = #{storeCode},</if>
            <if test="storeName != null">store_name = #{storeName},</if>
            <if test="queryWord != null">query_word = #{queryWord},</if>
            <if test="type != null">type = #{type},</if>
            <if test="unitName != null">unit_name = #{unitName},</if>
            <if test="position != null">position = #{position},</if>
            <if test="lowerLimit != null">lower_limit = #{lowerLimit},</if>
            <if test="upLimit != null">up_limit = #{upLimit},</if>
            <if test="memo != null">memo = #{memo},</if>
            <if test="fStoreCode != null">f_store_code = #{fStoreCode},</if>
            <if test="fStoreName != null">f_store_name = #{fStoreName},</if>
            <if test="levelName != null">level_name = #{levelName},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteLeaveDepartmentById" parameterType="Long">
        delete from leave_department where id = #{id}
    </delete>

    <delete id="deleteLeaveDepartmentByIds" parameterType="String">
        delete from leave_department where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
</mapper>