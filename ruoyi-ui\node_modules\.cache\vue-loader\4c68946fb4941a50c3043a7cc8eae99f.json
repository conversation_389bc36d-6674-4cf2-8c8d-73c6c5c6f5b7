{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\assess\\self\\check\\organizationCheck.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\assess\\self\\check\\organizationCheck.vue", "mtime": 1756099891054}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBsaXN0SW5mbywgbGlzdENoZWNrZWQsIGdldEluZm8sIGNoZWNrLCBsaXN0QmVBc3Nlc3NlZCwgcmVqZWN0SW5mbywgYmF0Y2hRdWlja1Njb3JlLCBiYXRjaFdpdGhCZW5lZml0QnlJZHN9IGZyb20gIkAvYXBpL2Fzc2Vzcy9zZWxmL2luZm8iOw0KaW1wb3J0IHsgZ2V0U2VsZkFzc2Vzc1VzZXJ9IGZyb20gIkAvYXBpL2Fzc2Vzcy9zZWxmL3VzZXIiOw0KaW1wb3J0IHsgbGlzdERlcHQgfSBmcm9tICJAL2FwaS9hc3Nlc3MvbGF0ZXJhbC9kZXB0IjsNCmltcG9ydCBUcmVlc2VsZWN0IGZyb20gIkByaW9waGFlL3Z1ZS10cmVlc2VsZWN0IjsNCmltcG9ydCAiQHJpb3BoYWUvdnVlLXRyZWVzZWxlY3QvZGlzdC92dWUtdHJlZXNlbGVjdC5jc3MiOw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJPcmdhbml6YXRpb25DaGVjayIsDQogIGNvbXBvbmVudHM6IHsNCiAgICBUcmVlc2VsZWN0DQogIH0sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIC8vIOmBrue9qeWxgg0KICAgICAgbG9hZGluZzogdHJ1ZSwNCiAgICAgIC8vIOaYvuekuuaQnOe0ouadoeS7tg0KICAgICAgc2hvd1NlYXJjaDogdHJ1ZSwNCiAgICAgIC8vIOmAgOWbnuWOn+WboOi+k+WFpeahhg0KICAgICAgcmVqZWN0T3BlbjpmYWxzZSwNCiAgICAgIC8vIOaAu+adoeaVsA0KICAgICAgdG90YWw6IDAsDQogICAgICBjaGVja2VkVG90YWw6IDAsDQogICAgICAvLyDnu6nmlYjogIPmoLgt5bmy6YOo6Ieq6K+E5Lq65ZGY6YWN572u6KGo5qC85pWw5o2uDQogICAgICBsaXN0VG9DaGVjazogW10sDQogICAgICBsaXN0Q2hlY2tlZDogW10sDQogICAgICAvLyDlvLnlh7rlsYLmoIfpopgNCiAgICAgIHRpdGxlOiAiIiwNCiAgICAgIC8vIOaYr+WQpuaYvuekuuW8ueWHuuWxgg0KICAgICAgb3BlbjogZmFsc2UsDQogICAgICAvLyDmn6Xor6Llj4LmlbANCiAgICAgIHF1ZXJ5UGFyYW1zOiB7DQogICAgICAgIHBhZ2VOdW06IDEsDQogICAgICAgIHBhZ2VTaXplOiAxMCwNCiAgICAgICAgd29ya05vOiBudWxsLA0KICAgICAgICBuYW1lOm51bGwsDQogICAgICAgIGRlcHRJZDpudWxsLA0KICAgICAgICBhc3Nlc3NEYXRlOm51bGwsDQogICAgICAgIHN0YXR1czoiMyIsDQogICAgICAgIHBvc3RUeXBlOm51bGwNCiAgICAgIH0sDQogICAgICAvLyDor4TliIborrDlvZXmn6Xor6Llj4LmlbANCiAgICAgIGNoZWNrZWRRdWVyeVBhcmFtczogew0KICAgICAgICBwYWdlTnVtOiAxLA0KICAgICAgICBwYWdlU2l6ZTogMTAsDQogICAgICAgIHdvcmtObzogbnVsbCwNCiAgICAgICAgbmFtZTpudWxsLA0KICAgICAgICBkZXB0SWQ6bnVsbCwNCiAgICAgICAgYXNzZXNzRGF0ZTpudWxsLA0KICAgICAgICBwb3N0VHlwZTpudWxsDQogICAgICB9LA0KICAgICAgLy8g6KGo5Y2V5Y+C5pWwDQogICAgICBmb3JtOiB7DQogICAgICAgIGlkOm51bGwsDQogICAgICAgIC8vIOmDqOmXqOmihuWvvOivhOWIhg0KICAgICAgICBkZXB0U2NvcmU6bnVsbCwNCiAgICAgICAgLy8g5LqL5Lia6YOo6K+E5YiGDQogICAgICAgIGJ1c2luZXNzU2NvcmU6bnVsbCwNCiAgICAgICAgLy8g5p2h57q/6aKG5a+86K+E5YiGDQogICAgICAgIGxlYWRlclNjb3JlOm51bGwsDQogICAgICAgIC8vIOi/kOaUuee7hOe7h+mDqOivhOWIhg0KICAgICAgICBvcmdhbml6YXRpb25TY29yZTpudWxsLA0KICAgICAgICAvLyDov5DmlLnnu4Tnu4fpg6jliqDlh4/liIbnkIbnlLENCiAgICAgICAgb3JnYW5pemF0aW9uU2NvcmVSZWFzb246bnVsbCwNCiAgICAgIH0sDQogICAgICAvLyDooajljZXmoKHpqowNCiAgICAgIHJ1bGVzOiB7DQogICAgICB9LA0KICAgICAgZGVwdE9wdGlvbnM6W10sDQogICAgICBvcGVuQ2hlY2s6ZmFsc2UsDQogICAgICBjaGVja0luZm86e30sDQogICAgICAvLyDlkIjlubbljZXlhYPmoLwNCiAgICAgIHNwYW5MaXN0OltdLA0KICAgICAgLy8g5b6F6K+E5YiG5qCH562+DQogICAgICB0b0NoZWNrTGFiZWw6IuW+heivhOWIhigwKSIsDQogICAgICAvLyDmqKrlkJHooqvogIPor4Tkv6Hmga8NCiAgICAgIGJlQXNzZXNzZWRMaXN0OltdLA0KICAgICAgYmVuZWZpdDpudWxsLCAgLy8g5YWs5Y+45pWI55uK5YiGDQogICAgICB1c2VySW5mbzp7DQogICAgICAgIGJlbmVmaXRMaW5rRmxhZzpudWxsDQogICAgICB9LA0KICAgICAgYmVuZWZpdERldGFpbDoiIiwgIC8vIOaViOebiuivpue7hg0KICAgICAgLy8g6YCJ5Lit5pWw57uEDQogICAgICBtdWx0aXBsZVNlbGVjdGlvbjogW10sDQogICAgICAvLyDpnZ7ljZXkuKrnpoHnlKgNCiAgICAgIHNpbmdsZTogdHJ1ZSwNCiAgICAgIC8vIOmdnuWkmuS4quemgeeUqA0KICAgICAgbXVsdGlwbGU6IHRydWUsDQogICAgICAvLyDmibnph4/lv6vpgJ/or4TliIblr7nor53moYbmmL7npLrnirbmgIENCiAgICAgIHF1aWNrU2NvcmVEaWFsb2dWaXNpYmxlOiBmYWxzZSwNCiAgICAgIC8vIOaJuemHj+W/q+mAn+ivhOWIhuihqOWNleWPguaVsA0KICAgICAgYmF0Y2hRdWlja1Njb3JlRm9ybTogew0KICAgICAgICBzY29yZTogdW5kZWZpbmVkLA0KICAgICAgICBpZHM6IFtdDQogICAgICB9LA0KICAgICAgLy8g5om56YeP5b+r6YCf6K+E5YiG6KGo5Y2V6aqM6K+B6KeE5YiZDQogICAgICBiYXRjaFF1aWNrU2NvcmVSdWxlczogew0KICAgICAgICBzY29yZTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLor4TliIbkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfSwNCiAgICAgICAgICB7IHR5cGU6ICdudW1iZXInLCBtZXNzYWdlOiAi6K+E5YiG5b+F6aG75Li65pWw5a2XIiwgdHJpZ2dlcjogImJsdXIiIH0NCiAgICAgICAgXQ0KICAgICAgfSwNCiAgICAgIC8vIOaJuemHj+W/q+mAn+ivhOWIhuWvueivneahhg0KICAgICAgYmF0Y2hRdWlja1Njb3JlT3BlbjogZmFsc2UsDQogICAgICAvLyDpgInkuK3mlbDnu4QNCiAgICAgIGlkczogW10sDQogICAgICAvLyDpgInkuK3nmoTooYzmlbDmja4NCiAgICAgIHNlbGVjdGVkUm93czogW10sDQogICAgfTsNCiAgfSwNCiAgY29tcHV0ZWQ6IHsNCiAgICAvLyDmmK/lkKblj6/ku6Xmj5DkuqTmibnph4/or4TliIYNCiAgICBjYW5TdWJtaXRCYXRjaFNjb3JlKCkgew0KICAgICAgaWYgKHRoaXMuc2VsZWN0ZWRSb3dzLmxlbmd0aCA9PT0gMCkgcmV0dXJuIGZhbHNlOw0KICAgICAgDQogICAgICAvLyDnroDljZXmo4Dmn6XmmK/lkKbmiYDmnInooYzpg73loavlhpnkuobor4TliIYNCiAgICAgIGZvciAobGV0IHJvdyBvZiB0aGlzLnNlbGVjdGVkUm93cykgew0KICAgICAgICBpZiAoIXJvdy5xdWlja1Njb3JlICYmIHJvdy5xdWlja1Njb3JlICE9PSAwKSB7DQogICAgICAgICAgcmV0dXJuIGZhbHNlOw0KICAgICAgICB9DQogICAgICB9DQogICAgICANCiAgICAgIHJldHVybiB0cnVlOw0KICAgIH0NCiAgfSwNCiAgY3JlYXRlZCgpIHsNCiAgICB0aGlzLnF1ZXJ5UGFyYW1zLmFzc2Vzc0RhdGUgPSB0aGlzLmdldERlZmF1bHRBc3Nlc3NEYXRlKCkNCiAgICB0aGlzLmNoZWNrZWRRdWVyeVBhcmFtcy5hc3Nlc3NEYXRlID0gdGhpcy5nZXREZWZhdWx0QXNzZXNzRGF0ZSgpDQogICAgLy8gdGhpcy5nZXRTZWxmQXNzZXNzVXNlcigpOw0KICAgIC8vIHRoaXMuZ2V0Q2hlY2tEZXB0TGlzdCgpOw0KICAgIHRoaXMuZ2V0VHJlZXNlbGVjdCgpOw0KICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgIHRoaXMuZ2V0Q2hlY2tlZExpc3QoKTsNCiAgfSwNCiAgbWV0aG9kczogew0KDQogICAgLy8g6I635Y+W6buY6K6k6ICD5qC45pel5pyfDQogICAgZ2V0RGVmYXVsdEFzc2Vzc0RhdGUoKSB7DQogICAgICBjb25zdCBub3cgPSBuZXcgRGF0ZSgpOw0KICAgICAgY29uc3QgY3VycmVudERheSA9IG5vdy5nZXREYXRlKCk7DQoNCiAgICAgIGxldCB0YXJnZXREYXRlOw0KICAgICAgaWYgKGN1cnJlbnREYXkgPCAxMCkgew0KICAgICAgICAvLyDlvZPliY3ml6XmnJ/lsI/kuo4xMOaXpe+8jOm7mOiupOS4uuS4iuS4quaciA0KICAgICAgICB0YXJnZXREYXRlID0gbmV3IERhdGUobm93LmdldEZ1bGxZZWFyKCksIG5vdy5nZXRNb250aCgpIC0gMSwgMSk7DQogICAgICB9IGVsc2Ugew0KICAgICAgICAvLyDlvZPliY3ml6XmnJ/lpKfkuo7nrYnkuo4xMOaXpe+8jOm7mOiupOS4uuW9k+aciA0KICAgICAgICB0YXJnZXREYXRlID0gbmV3IERhdGUobm93LmdldEZ1bGxZZWFyKCksIG5vdy5nZXRNb250aCgpLCAxKTsNCiAgICAgIH0NCg0KICAgICAgLy8g5qC85byP5YyW5Li6IFlZWVktTSDmoLzlvI8NCiAgICAgIGNvbnN0IHllYXIgPSB0YXJnZXREYXRlLmdldEZ1bGxZZWFyKCk7DQogICAgICBjb25zdCBtb250aCA9IHRhcmdldERhdGUuZ2V0TW9udGgoKSArIDE7DQogICAgICByZXR1cm4gYCR7eWVhcn0tJHttb250aH1gOw0KICAgIH0sDQoNCiAgICAvLyDojrflj5booqvogIPmoLjkv6Hmga8NCiAgICBnZXRCZUFzc2Vzc2VkTGlzdChwYXJhbSl7DQogICAgICBsaXN0QmVBc3Nlc3NlZChwYXJhbSkudGhlbihyZXMgPT57DQogICAgICAgIGxldCBiZUFzc2Vzc2VkTGlzdCA9IFtdOw0KICAgICAgICBpZihyZXMuY29kZSA9PSAyMDApew0KICAgICAgICAgIGlmKHJlcy5kYXRhLmxlbmd0aCA+IDApew0KICAgICAgICAgICAgcmVzLmRhdGEuZm9yRWFjaChpdGVtID0+IHsNCiAgICAgICAgICAgICAgYmVBc3Nlc3NlZExpc3QgPSBbLi4uYmVBc3Nlc3NlZExpc3QsLi4uaXRlbS5ockxhdGVyYWxBc3Nlc3NJbmZvTGlzdF0NCiAgICAgICAgICAgIH0pDQogICAgICAgICAgICB0aGlzLmJlQXNzZXNzZWRMaXN0ID0gYmVBc3Nlc3NlZExpc3Q7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICAgIGNvbnNvbGUubG9nKGJlQXNzZXNzZWRMaXN0KQ0KICAgICAgfSkNCiAgICB9LA0KICAgIC8vIOiOt+WPluiiq+ivhOWIhuS6uuWRmOS/oeaBrw0KICAgIGdldFNlbGZBc3Nlc3NVc2VyKHBhcmFtKXsNCiAgICAgIGdldFNlbGZBc3Nlc3NVc2VyKHBhcmFtKS50aGVuKHJlcyA9PnsNCiAgICAgICAgaWYocmVzLmNvZGUgPT0gMjAwKXsNCiAgICAgICAgICB0aGlzLnVzZXJJbmZvID0gcmVzLmRhdGE7DQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCiAgICAvKiog6L2s5o2i5qiq5ZCR6K+E5Lu36YOo6Zeo5pWw5o2u57uT5p6EICovDQogICAgbm9ybWFsaXplcihub2RlKSB7DQogICAgICBpZiAobm9kZS5jaGlsZHJlbiAmJiAhbm9kZS5jaGlsZHJlbi5sZW5ndGgpIHsNCiAgICAgICAgZGVsZXRlIG5vZGUuY2hpbGRyZW47DQogICAgICB9DQogICAgICByZXR1cm4gew0KICAgICAgICBpZDogbm9kZS5kZXB0SWQsDQogICAgICAgIGxhYmVsOiBub2RlLmRlcHROYW1lLA0KICAgICAgICBjaGlsZHJlbjogbm9kZS5jaGlsZHJlbg0KICAgICAgfTsNCiAgICB9LA0KCSAgLyoqIOafpeivouaoquWQkeivhOS7t+mDqOmXqOS4i+aLieagkee7k+aehCAqLw0KICAgIGdldFRyZWVzZWxlY3QoKSB7DQogICAgICBsaXN0RGVwdCgpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLmRlcHRPcHRpb25zID0gdGhpcy5oYW5kbGVUcmVlKHJlc3BvbnNlLmRhdGEsICJkZXB0SWQiLCAicGFyZW50SWQiKTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOafpeivoue7qeaViOiAg+aguC3lubLpg6joh6ror4TlvoXlrqHmoLjliJfooaggKi8NCiAgICBnZXRMaXN0KCkgew0KICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsNCiAgICAgIGxpc3RJbmZvKHRoaXMucXVlcnlQYXJhbXMpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLmxpc3RUb0NoZWNrID0gcmVzcG9uc2Uucm93czsNCiAgICAgICAgdGhpcy50b3RhbCA9IHJlc3BvbnNlLnRvdGFsOw0KICAgICAgICB0aGlzLnRvQ2hlY2tMYWJlbCA9IGDlvoXor4TliIYoJHtyZXNwb25zZS50b3RhbH0pYA0KICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOiOt+WPluW3suWuoeaguOWIl+ihqCAqLw0KICAgIGdldENoZWNrZWRMaXN0KCl7DQogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOw0KICAgICAgbGlzdENoZWNrZWQodGhpcy5jaGVja2VkUXVlcnlQYXJhbXMpLnRoZW4ocmVzID0+IHsNCiAgICAgICAgdGhpcy5saXN0Q2hlY2tlZCA9IHJlcy5yb3dzOw0KICAgICAgICB0aGlzLmNoZWNrZWRUb3RhbCA9IHJlcy50b3RhbDsNCiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7DQogICAgICB9KQ0KICAgIH0sDQoNCiAgICAvLyDlj5bmtojmjInpkq4NCiAgICBjYW5jZWwoKSB7DQogICAgICB0aGlzLm9wZW4gPSBmYWxzZTsNCiAgICAgIHRoaXMucmVzZXQoKTsNCiAgICB9LA0KICAgIC8vIOihqOWNlemHjee9rg0KICAgIHJlc2V0KCkgew0KICAgICAgdGhpcy5mb3JtID0gew0KICAgICAgICBpZDogbnVsbCwNCiAgICAgICAgb3JnYW5pemF0aW9uU2NvcmU6IG51bGwsDQogICAgICAgIG9yZ2FuaXphdGlvblNjb3JlUmVhc29uOiBudWxsLA0KICAgICAgfTsNCiAgICAgIHRoaXMuYmVuZWZpdCA9IG51bGw7DQogICAgICB0aGlzLmJlbmVmaXREZXRhaWwgPSAiIjsNCiAgICAgIHRoaXMudXNlckluZm8gPSB7DQogICAgICAgIGJlbmVmaXRMaW5rRmxhZzpudWxsDQogICAgICB9DQogICAgICAvLyB0aGlzLnJlc2V0Rm9ybSgiZm9ybSIpOw0KICAgIH0sDQogICAgLyoqIOaQnOe0ouaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZVF1ZXJ5KCkgew0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMTsNCiAgICAgIHRoaXMuY2hlY2tlZFF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxOw0KICAgICAgLy8g5ZCM5q2l5pCc57Si5p2h5Lu2DQogICAgICB0aGlzLmNoZWNrZWRRdWVyeVBhcmFtcy5uYW1lID0gdGhpcy5xdWVyeVBhcmFtcy5uYW1lOw0KICAgICAgdGhpcy5jaGVja2VkUXVlcnlQYXJhbXMuZGVwdElkID0gdGhpcy5xdWVyeVBhcmFtcy5kZXB0SWQ7DQogICAgICB0aGlzLmNoZWNrZWRRdWVyeVBhcmFtcy5hc3Nlc3NEYXRlID0gdGhpcy5xdWVyeVBhcmFtcy5hc3Nlc3NEYXRlOw0KICAgICAgdGhpcy5jaGVja2VkUXVlcnlQYXJhbXMucG9zdFR5cGUgPSB0aGlzLnF1ZXJ5UGFyYW1zLnBvc3RUeXBlOw0KICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICB0aGlzLmdldENoZWNrZWRMaXN0KCk7DQogICAgfSwNCiAgICAvKiog6YeN572u5oyJ6ZKu5pON5L2cICovDQogICAgcmVzZXRRdWVyeSgpIHsNCiAgICAgIHRoaXMucmVzZXRGb3JtKCJxdWVyeUZvcm0iKTsNCiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKTsNCiAgICB9LA0KDQogICAgLy8g5a6h5om56K+m5oOFDQogICAgaGFuZGxlQ2hlY2tEZXRhaWwocm93KXsNCiAgICAgIGdldEluZm8oe2lkOnJvdy5pZH0pLnRoZW4ocmVzID0+IHsNCiAgICAgICAgY29uc29sZS5sb2cocmVzKTsNCiAgICAgICAgdGhpcy5yZXNldCgpOw0KICAgICAgICBpZihyZXMuY29kZSA9PSAyMDApew0KICAgICAgICAgIHRoaXMuY2hlY2tJbmZvID0gcmVzLmRhdGE7DQogICAgICAgICAgbGV0IGxpc3QgPSBKU09OLnBhcnNlKHJlcy5kYXRhLmNvbnRlbnQpOw0KICAgICAgICAgIHRoaXMuaGFuZGxlU3Bhbkxpc3QobGlzdCk7DQogICAgICAgICAgbGV0IHBhcmFtID17DQogICAgICAgICAgICBkZXB0SWQ6cmVzLmRhdGEuZGVwdElkLA0KICAgICAgICAgICAgYXNzZXNzRGF0ZTpyZXMuZGF0YS5hc3Nlc3NEYXRlDQogICAgICAgICAgfQ0KICAgICAgICAgIHRoaXMuZ2V0QmVBc3Nlc3NlZExpc3QocGFyYW0pOyAgLy8g6I635Y+W5qiq5ZCR6K+E5YiG6KKr6ICD5qC45pWw5o2uDQogICAgICAgICAgdGhpcy5nZXRTZWxmQXNzZXNzVXNlcih7aWQ6cmVzLmRhdGEudXNlcklkfSk7ICAvLyDojrflj5bnlKjmiLfkv6Hmga8NCiAgICAgICAgICB0aGlzLmNoZWNrSW5mby5saXN0ID0gbGlzdDsNCg0KICAgICAgICAgIC8vIOWIneWni+WMluihqOWNleaVsOaNrg0KICAgICAgICAgIHRoaXMuZm9ybS5vcmdhbml6YXRpb25TY29yZSA9IHJlcy5kYXRhLm9yZ2FuaXphdGlvblNjb3JlOw0KICAgICAgICAgIHRoaXMuZm9ybS5vcmdhbml6YXRpb25TY29yZVJlYXNvbiA9IHJlcy5kYXRhLm9yZ2FuaXphdGlvblNjb3JlUmVhc29uOw0KICAgICAgICB9DQogICAgICAgIHRoaXMub3BlbiA9IHRydWUNCiAgICAgIH0pDQogICAgfSwNCg0KICAgIC8vIOWuoeaJueaPkOS6pA0KICAgIGNoZWNrU3VibWl0KCl7DQogICAgICBpZih0aGlzLnZlcmlmeSgpKXsNCiAgICAgICAgdGhpcy5mb3JtLmlkID0gdGhpcy5jaGVja0luZm8uaWQ7DQogICAgICAgIHRoaXMuZm9ybS5zdGF0dXMgPSB0aGlzLmNoZWNrSW5mby5zdGF0dXM7DQogICAgICAgIGNoZWNrKHRoaXMuZm9ybSkudGhlbihyZXMgPT4gew0KICAgICAgICAgIGNvbnNvbGUubG9nKHJlcykNCiAgICAgICAgICBpZihyZXMuY29kZSA9PSAyMDApew0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICAgIHR5cGU6ICdzdWNjZXNzJywNCiAgICAgICAgICAgICAgbWVzc2FnZTogJ+aPkOS6pOaIkOWKnyEnDQogICAgICAgICAgICB9KTsNCiAgICAgICAgICAgIHRoaXMucmVzZXQoKTsNCiAgICAgICAgICAgIHRoaXMub3BlbiA9IGZhbHNlOw0KICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgICAgICB0aGlzLmdldENoZWNrZWRMaXN0KCk7DQogICAgICAgICAgfWVsc2V7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgICAgdHlwZTogJ3dhcm5pbmcnLA0KICAgICAgICAgICAgICBtZXNzYWdlOiAn5pON5L2c5aSx6LSl77yM5peg5p2D6ZmQ5oiW5b2T5YmN5a6h5om554q25oCB5LiN5Yy56YWNJw0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgfQ0KICAgICAgICB9KQ0KICAgICAgfWVsc2V7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJywNCiAgICAgICAgICBtZXNzYWdlOiAn6K+35aGr5YaZ6K+E5YiGJw0KICAgICAgICB9KTsNCiAgICAgICAgcmV0dXJuIGZhbHNlOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvLyDpgIDlm57ngrnlh7vkuovku7YNCiAgICByZWplY3RDbGljaygpew0KICAgICAgdGhpcy5yZWplY3RPcGVuID0gdHJ1ZTsNCiAgICB9LA0KDQogICAgcmVqZWN0Q2FuY2VsKCl7DQogICAgICB0aGlzLmNoZWNrSW5mby5yZWplY3RSZWFzb24gPSBudWxsOw0KICAgICAgdGhpcy5yZWplY3RPcGVuID0gZmFsc2U7DQogICAgfSwNCg0KICAgIC8vIOmAgOWbng0KICAgIHJlamVjdFN1Ym1pdCgpew0KICAgICAgaWYoIXRoaXMuY2hlY2tJbmZvLnJlamVjdFJlYXNvbil7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJywNCiAgICAgICAgICBtZXNzYWdlOiAn6K+35aGr5YaZ6YCA5Zue5Y6f5ZugJw0KICAgICAgICB9KTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KICAgICAgdGhpcy4kY29uZmlybSgn56Gu6K6k5ZCOLCDmmK/lkKbnu6fnu60/JywgJ+aPkOekuicsIHsNCiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLA0KICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywNCiAgICAgICAgdHlwZTogJ3dhcm5pbmcnDQogICAgICB9KS50aGVuKCgpID0+IHsNCiAgICAgICAgdGhpcy5yZWplY3QoKTsNCiAgICAgIH0pLmNhdGNoKCgpID0+IHsNCiAgICAgICAgICANCiAgICAgIH0pOw0KICAgIH0sDQoNCiAgICByZWplY3QoKXsNCiAgICAgIHJlamVjdEluZm8odGhpcy5jaGVja0luZm8pLnRoZW4ocmVzID0+IHsNCiAgICAgICAgICB0aGlzLnJlc2V0KCk7DQogICAgICAgICAgdGhpcy5jaGVja0luZm8ucmVqZWN0UmVhc29uID0gbnVsbDsNCiAgICAgICAgICB0aGlzLnJlamVjdE9wZW4gPSBmYWxzZTsNCiAgICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZTsNCiAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgICB0aGlzLmdldENoZWNrZWRMaXN0KCk7DQogICAgICB9KQ0KICAgIH0sDQoNCg0KICAgIC8vIOaVsOaNrumqjOivgQ0KICAgIHZlcmlmeSgpew0KICAgICAgaWYoIXRoaXMuZm9ybS5vcmdhbml6YXRpb25TY29yZSkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+ivt+i+k+WFpei/kOaUuee7hOe7h+mDqOivhOWIhicpOw0KICAgICAgICByZXR1cm4gZmFsc2U7DQogICAgICB9DQoNCiAgICAgIC8vIOajgOafpeaYr+WQpumcgOimgeWhq+WGmeWKoOWHj+WIhueQhueUsQ0KICAgICAgbGV0IHByZXZpb3VzU2NvcmUgPSBudWxsOw0KICAgICAgbGV0IHByZXZpb3VzU2NvcmVOYW1lID0gJyc7DQoNCiAgICAgIGlmICh0aGlzLmNoZWNrSW5mby5idXNpbmVzc1Njb3JlKSB7DQogICAgICAgIHByZXZpb3VzU2NvcmUgPSBwYXJzZUZsb2F0KHRoaXMuY2hlY2tJbmZvLmJ1c2luZXNzU2NvcmUpOw0KICAgICAgICBwcmV2aW91c1Njb3JlTmFtZSA9ICfkuovkuJrpg6jor4TliIYnOw0KICAgICAgfSBlbHNlIGlmICh0aGlzLmNoZWNrSW5mby5kZXB0U2NvcmUpIHsNCiAgICAgICAgcHJldmlvdXNTY29yZSA9IHBhcnNlRmxvYXQodGhpcy5jaGVja0luZm8uZGVwdFNjb3JlKTsNCiAgICAgICAgcHJldmlvdXNTY29yZU5hbWUgPSAn6YOo6Zeo6aKG5a+86K+E5YiGJzsNCiAgICAgIH0gZWxzZSBpZiAodGhpcy5jaGVja0luZm8uc2VsZlNjb3JlKSB7DQogICAgICAgIHByZXZpb3VzU2NvcmUgPSBwYXJzZUZsb2F0KHRoaXMuY2hlY2tJbmZvLnNlbGZTY29yZSk7DQogICAgICAgIHByZXZpb3VzU2NvcmVOYW1lID0gJ+iHquivhOWIhic7DQogICAgICB9DQoNCiAgICAgIC8vIOi/kOaUuee7hOe7h+mDqOivhOWIhuS4juS4iuS4gOeOr+iKguivhOWIhuS4jeS4gOiHtOaXtu+8jOWKoOWHj+WIhueQhueUseW/heWhqw0KICAgICAgaWYgKHByZXZpb3VzU2NvcmUgIT09IG51bGwgJiYgcGFyc2VGbG9hdCh0aGlzLmZvcm0ub3JnYW5pemF0aW9uU2NvcmUpICE9PSBwcmV2aW91c1Njb3JlICYmICF0aGlzLmZvcm0ub3JnYW5pemF0aW9uU2NvcmVSZWFzb24pIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKGDov5DmlLnnu4Tnu4fpg6jor4TliIYoJHt0aGlzLmZvcm0ub3JnYW5pemF0aW9uU2NvcmV95YiGKeS4jiR7cHJldmlvdXNTY29yZU5hbWV9KCR7cHJldmlvdXNTY29yZX3liIYp5LiN5LiA6Ie077yM6K+35aGr5YaZ5Yqg5YeP5YiG55CG55SxYCk7DQogICAgICAgIHJldHVybiBmYWxzZTsNCiAgICAgIH0NCg0KICAgICAgcmV0dXJuIHRydWU7DQogICAgfSwNCg0KICAgIGhhbmRsZUxpc3RDaGFuZ2UodHlwZSl7DQogICAgICBjb25zb2xlLmxvZyh0eXBlKQ0KICAgIH0sDQogICAgLy8g5aSE55CG5YiX6KGoDQogICAgaGFuZGxlU3Bhbkxpc3QoZGF0YSl7DQogICAgICBsZXQgc3Bhbkxpc3QgPSBbXTsNCiAgICAgIGxldCBmbGFnID0gMDsNCiAgICAgIGZvcihsZXQgaSA9IDA7IGkgPCBkYXRhLmxlbmd0aDsgaSsrKXsNCiAgICAgICAgLy8g55u45ZCM6ICD5qC46aG55ZCI5bm2DQogICAgICAgIGlmKGkgPT0gMCl7DQogICAgICAgICAgc3Bhbkxpc3QucHVzaCh7DQogICAgICAgICAgICByb3dzcGFuOiAxLA0KICAgICAgICAgICAgY29sc3BhbjogMQ0KICAgICAgICAgIH0pDQogICAgICAgIH1lbHNlew0KICAgICAgICAgIGlmKGRhdGFbaSAtIDFdLml0ZW0gPT0gZGF0YVtpXS5pdGVtKXsNCiAgICAgICAgICAgIHNwYW5MaXN0LnB1c2goew0KICAgICAgICAgICAgICByb3dzcGFuOiAwLA0KICAgICAgICAgICAgICBjb2xzcGFuOiAwDQogICAgICAgICAgICB9KQ0KICAgICAgICAgICAgc3Bhbkxpc3RbZmxhZ10ucm93c3BhbiArPSAxOw0KICAgICAgICAgIH1lbHNlew0KICAgICAgICAgICAgc3Bhbkxpc3QucHVzaCh7DQogICAgICAgICAgICAgIHJvd3NwYW46IDEsDQogICAgICAgICAgICAgIGNvbHNwYW46IDENCiAgICAgICAgICAgIH0pDQogICAgICAgICAgICBmbGFnID0gaTsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIHRoaXMuc3Bhbkxpc3QgPSBzcGFuTGlzdDsNCiAgICB9LA0KDQogICAgLy8g5ZCI5bm25Y2V5YWD5qC85pa55rOVDQogICAgb2JqZWN0U3Bhbk1ldGhvZCh7IHJvdywgY29sdW1uLCByb3dJbmRleCwgY29sdW1uSW5kZXggfSkgew0KICAgICAgLy8g56ys5LiA5YiX55u45ZCM6aG55ZCI5bm2DQogICAgICBpZiAoY29sdW1uSW5kZXggPT09IDApIHsNCiAgICAgICAgcmV0dXJuIHRoaXMuc3Bhbkxpc3Rbcm93SW5kZXhdOw0KICAgICAgfQ0KICAgICAgLy8g57G75Yir5peg5YaF5a65IOWQiOW5tg0KICAgICAgaWYoY29sdW1uSW5kZXggPT09IDEpew0KICAgICAgICBpZighcm93LmNhdGVnb3J5KXsNCiAgICAgICAgICByZXR1cm4gew0KICAgICAgICAgICAgcm93c3BhbjogMCwNCiAgICAgICAgICAgIGNvbHNwYW46IDANCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIGlmKGNvbHVtbkluZGV4ID09PSAyKXsNCiAgICAgICAgaWYoIXJvdy5jYXRlZ29yeSl7DQogICAgICAgICAgcmV0dXJuIHsNCiAgICAgICAgICAgIHJvd3NwYW46IDEsDQogICAgICAgICAgICBjb2xzcGFuOiAyDQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9DQogICAgfSwNCiAgICAvLyDorqHnrpfmlYjnm4oNCiAgICBjYWxTY29yZSgpew0KICAgICAgaWYodGhpcy5iZW5lZml0IHx8IHRoaXMuYmVuZWZpdCA9PSAwKXsNCiAgICAgICAgbGV0IGJlbmVmaXQgPSBOdW1iZXIodGhpcy5iZW5lZml0KTsNCg0KICAgICAgICAvLyDnoa7lrprliY3kuIDmraXor4TliIbvvJrkvJjlhYjkuovkuJrpg6jor4TliIbvvIzlhbbmrKHpg6jpl6jor4TliIbvvIzmnIDlkI7oh6ror4TliIYNCiAgICAgICAgbGV0IHByZXZpb3VzU2NvcmUgPSAwOw0KICAgICAgICBsZXQgcHJldmlvdXNTY29yZU5hbWUgPSAnJzsNCiAgICAgICAgaWYgKHRoaXMuY2hlY2tJbmZvLmJ1c2luZXNzU2NvcmUgIT09IG51bGwgJiYgdGhpcy5jaGVja0luZm8uYnVzaW5lc3NTY29yZSAhPT0gdW5kZWZpbmVkICYmIHRoaXMuY2hlY2tJbmZvLmJ1c2luZXNzU2NvcmUgIT09ICcnKSB7DQogICAgICAgICAgLy8g5pyJ5LqL5Lia6YOo6K+E5YiG77yM5Lul5LqL5Lia6YOo6K+E5YiG5Li65Z+656GADQogICAgICAgICAgcHJldmlvdXNTY29yZSA9IE51bWJlcih0aGlzLmNoZWNrSW5mby5idXNpbmVzc1Njb3JlKTsNCiAgICAgICAgICBwcmV2aW91c1Njb3JlTmFtZSA9ICfkuovkuJrpg6jor4TliIYnOw0KICAgICAgICB9IGVsc2UgaWYgKHRoaXMuY2hlY2tJbmZvLmRlcHRTY29yZSAhPT0gbnVsbCAmJiB0aGlzLmNoZWNrSW5mby5kZXB0U2NvcmUgIT09IHVuZGVmaW5lZCAmJiB0aGlzLmNoZWNrSW5mby5kZXB0U2NvcmUgIT09ICcnKSB7DQogICAgICAgICAgLy8g5rKh5pyJ5LqL5Lia6YOo6K+E5YiG77yM5Lul6YOo6Zeo6K+E5YiG5Li65Z+656GADQogICAgICAgICAgcHJldmlvdXNTY29yZSA9IE51bWJlcih0aGlzLmNoZWNrSW5mby5kZXB0U2NvcmUpOw0KICAgICAgICAgIHByZXZpb3VzU2NvcmVOYW1lID0gJ+mDqOmXqOivhOWIhic7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgLy8g6YO95rKh5pyJ77yM5Lul6Ieq6K+E5YiG5Li65Z+656GADQogICAgICAgICAgcHJldmlvdXNTY29yZSA9IE51bWJlcih0aGlzLmNoZWNrSW5mby5zZWxmU2NvcmUpOw0KICAgICAgICAgIHByZXZpb3VzU2NvcmVOYW1lID0gJ+iHquivhOWIhic7DQogICAgICAgIH0NCg0KICAgICAgICBsZXQgY2F0ZWdvcnlTY29yZSA9IDA7DQogICAgICAgIGlmKHRoaXMudXNlckluZm8uYmVuZWZpdExpbmtGbGFnID09ICJOIil7DQogICAgICAgICAgdGhpcy5jaGVja0luZm8ubGlzdC5mb3JFYWNoKHJvdyA9PiB7DQogICAgICAgICAgICBjb25zb2xlLmxvZyhyb3cpDQogICAgICAgICAgICBpZihyb3cuY2F0ZWdvcnkgPT0gIuaViOebiiIpew0KICAgICAgICAgICAgICBjYXRlZ29yeVNjb3JlICs9IE51bWJlcihyb3cuZGVQb2ludHMpOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pDQogICAgICAgICAgdGhpcy5mb3JtLm9yZ2FuaXphdGlvblNjb3JlID0gcGFyc2VGbG9hdCgocHJldmlvdXNTY29yZSArIChiZW5lZml0IC8gMikgLSAoY2F0ZWdvcnlTY29yZSAvIDIpKS50b0ZpeGVkKDEpKTsNCiAgICAgICAgICB0aGlzLmJlbmVmaXREZXRhaWwgPSBg6K6h566X77yaJHtwcmV2aW91c1Njb3JlTmFtZX0oJHtwcmV2aW91c1Njb3JlfSkgJHtiZW5lZml0ID4gMCA/ICIrIiA6ICItIn0gJHtNYXRoLmFicyhiZW5lZml0IC8gMil9ICR7Y2F0ZWdvcnlTY29yZSA+IDAgPyAiLSIgOiAiKyJ9ICR7TWF0aC5hYnMoY2F0ZWdvcnlTY29yZSAvIDIpfSA9ICR7dGhpcy5mb3JtLm9yZ2FuaXphdGlvblNjb3JlfWANCiAgICAgICAgfWVsc2V7DQogICAgICAgICAgdGhpcy5mb3JtLm9yZ2FuaXphdGlvblNjb3JlID0gcGFyc2VGbG9hdCgocHJldmlvdXNTY29yZSArIGJlbmVmaXQpLnRvRml4ZWQoMSkpOw0KICAgICAgICAgIHRoaXMuYmVuZWZpdERldGFpbCA9IGDorqHnrpfvvJoke3ByZXZpb3VzU2NvcmVOYW1lfSgke3ByZXZpb3VzU2NvcmV9KSAke2JlbmVmaXQgPiAwID8gIisiIDogIi0ifSAke01hdGguYWJzKGJlbmVmaXQpfSA9ICR7dGhpcy5mb3JtLm9yZ2FuaXphdGlvblNjb3JlfWANCiAgICAgICAgfQ0KICAgICAgfWVsc2V7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJywNCiAgICAgICAgICBtZXNzYWdlOiAn6K+35aGr5YaZ5YWs5Y+45pWI55uK5Yqg5YeP5YiGJw0KICAgICAgICB9KTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KICAgIH0sDQogICAgLy8g5om56YeP6K6h566XDQogICAgYmF0Y2hDYWxTY29yZSgpew0KICAgICAgaWYodGhpcy5iZW5lZml0IHx8IHRoaXMuYmVuZWZpdCA9PSAwKXsNCiAgICAgICAgbGV0IGJlbmVmaXQgPSBOdW1iZXIodGhpcy5iZW5lZml0KTsNCiAgICAgICAgdGhpcy5zZWxlY3RlZFJvd3MuZm9yRWFjaChyb3cgPT4gew0KICAgICAgICAgIC8vIOehruWumuWJjeS4gOatpeivhOWIhu+8muS8mOWFiOS6i+S4mumDqOivhOWIhu+8jOWFtuasoemDqOmXqOivhOWIhu+8jOacgOWQjuiHquivhOWIhg0KICAgICAgICAgIGxldCBwcmV2aW91c1Njb3JlID0gMDsNCiAgICAgICAgICBpZiAocm93LmJ1c2luZXNzU2NvcmUgIT09IG51bGwgJiYgcm93LmJ1c2luZXNzU2NvcmUgIT09IHVuZGVmaW5lZCAmJiByb3cuYnVzaW5lc3NTY29yZSAhPT0gJycpIHsNCiAgICAgICAgICAgIC8vIOacieS6i+S4mumDqOivhOWIhu+8jOS7peS6i+S4mumDqOivhOWIhuS4uuWfuuehgA0KICAgICAgICAgICAgcHJldmlvdXNTY29yZSA9IE51bWJlcihyb3cuYnVzaW5lc3NTY29yZSk7DQogICAgICAgICAgfSBlbHNlIGlmIChyb3cuZGVwdFNjb3JlICE9PSBudWxsICYmIHJvdy5kZXB0U2NvcmUgIT09IHVuZGVmaW5lZCAmJiByb3cuZGVwdFNjb3JlICE9PSAnJykgew0KICAgICAgICAgICAgLy8g5rKh5pyJ5LqL5Lia6YOo6K+E5YiG77yM5Lul6YOo6Zeo6K+E5YiG5Li65Z+656GADQogICAgICAgICAgICBwcmV2aW91c1Njb3JlID0gTnVtYmVyKHJvdy5kZXB0U2NvcmUpOw0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAvLyDpg73msqHmnInvvIzku6Xoh6ror4TliIbkuLrln7rnoYANCiAgICAgICAgICAgIHByZXZpb3VzU2NvcmUgPSBOdW1iZXIocm93LnNlbGZTY29yZSk7DQogICAgICAgICAgfQ0KICAgICAgICAgIA0KICAgICAgICAgIGlmKHJvdy5iZW5lZml0TGlua0ZsYWcgPT0gIk4iKXsNCiAgICAgICAgICAgIHJvdy5xdWlja1Njb3JlID0gcGFyc2VGbG9hdCgocHJldmlvdXNTY29yZSArIChiZW5lZml0IC8gMikgLSAoTnVtYmVyKHJvdy5iZW5lZml0U2NvcmUpIC8gMikpLnRvRml4ZWQoMSkpOw0KICAgICAgICAgIH1lbHNlew0KICAgICAgICAgICAgcm93LnF1aWNrU2NvcmUgPSBwYXJzZUZsb2F0KChwcmV2aW91c1Njb3JlICsgYmVuZWZpdCkudG9GaXhlZCgxKSk7DQogICAgICAgICAgfQ0KICAgICAgICB9KQ0KICAgICAgfWVsc2V7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJywNCiAgICAgICAgICBtZXNzYWdlOiAn6K+35aGr5YaZ5YWs5Y+45pWI55uK5Yqg5YeP5YiGJw0KICAgICAgICB9KTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvKiog6YCJ5oup5p2h5pWw5pS55Y+YICovDQogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgew0KICAgICAgdGhpcy5pZHMgPSBzZWxlY3Rpb24ubWFwKGl0ZW0gPT4gaXRlbS5pZCkNCiAgICAgIHRoaXMuc2VsZWN0ZWRSb3dzID0gc2VsZWN0aW9uDQogICAgICB0aGlzLnNpbmdsZSA9IHNlbGVjdGlvbi5sZW5ndGggIT09IDENCiAgICAgIHRoaXMubXVsdGlwbGUgPSAhc2VsZWN0aW9uLmxlbmd0aA0KICAgIH0sDQoNCiAgICAvKiog5om56YeP5b+r6YCf6K+E5YiG5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlQmF0Y2hRdWlja1Njb3JlKCkgew0KICAgICAgaWYgKHRoaXMuaWRzLmxlbmd0aCA9PT0gMCkgew0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcigi6K+36YCJ5oup6ZyA6KaB6K+E5YiG55qE5pWw5o2uIik7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCiAgICAgIGJhdGNoV2l0aEJlbmVmaXRCeUlkcyh0aGlzLmlkcykudGhlbihyZXMgPT4gew0KICAgICAgICBpZihyZXMuY29kZSA9PSAyMDApew0KICAgICAgICAgIHRoaXMuc2VsZWN0ZWRSb3dzID0gcmVzLmRhdGE7DQogICAgICAgICAgdGhpcy5iYXRjaFF1aWNrU2NvcmVPcGVuID0gdHJ1ZTsNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICAgIC8vIC8vIOajgOafpeaYr+WQpuacieacquWhq+WGmeivhOWIhueahOiusOW9lQ0KICAgICAgLy8gY29uc3QgZW1wdHlTY29yZXMgPSB0aGlzLnNlbGVjdGVkUm93cy5maWx0ZXIocm93ID0+ICFyb3cucXVpY2tTY29yZSk7DQogICAgICAvLyBpZiAoZW1wdHlTY29yZXMubGVuZ3RoID4gMCkgew0KICAgICAgLy8gICB0aGlzLiRtb2RhbC5tc2dFcnJvcihg5pyJJHtlbXB0eVNjb3Jlcy5sZW5ndGh95p2h6K6w5b2V5pyq5aGr5YaZ5b+r6YCf6K+E5YiG77yM6K+35YWI5aGr5YaZ6K+E5YiGYCk7DQogICAgICAvLyAgIHJldHVybjsNCiAgICAgIC8vIH0NCiAgICB9LA0KDQogICAgLyoqIOWPlua2iOaJuemHj+W/q+mAn+ivhOWIhuaTjeS9nCAqLw0KICAgIGNhbmNlbEJhdGNoUXVpY2tTY29yZSgpIHsNCiAgICAgIHRoaXMuYmF0Y2hRdWlja1Njb3JlT3BlbiA9IGZhbHNlOw0KICAgIH0sDQoNCiAgICAvKiog5o+Q5Lqk5om56YeP5b+r6YCf6K+E5YiGICovDQogICAgc3VibWl0QmF0Y2hRdWlja1Njb3JlKCkgew0KICAgICAgLy8g6aqM6K+B6K+E5YiG5LiA6Ie05oCn5ZKM55CG55Sx5b+F5aGrDQogICAgICBjb25zdCB2YWxpZGF0aW9uUmVzdWx0ID0gdGhpcy52YWxpZGF0ZUJhdGNoUXVpY2tTY29yZSgpOw0KICAgICAgaWYgKCF2YWxpZGF0aW9uUmVzdWx0LmlzVmFsaWQpIHsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IodmFsaWRhdGlvblJlc3VsdC5tZXNzYWdlKTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KDQogICAgICAvLyDlh4blpIfmj5DkuqTmlbDmja4NCiAgICAgIGNvbnN0IHN1Ym1pdERhdGEgPSB0aGlzLnNlbGVjdGVkUm93cy5tYXAocm93ID0+ICh7DQogICAgICAgIGlkOiByb3cuaWQsDQogICAgICAgIHF1aWNrU2NvcmU6IHJvdy5xdWlja1Njb3JlLA0KICAgICAgICBxdWlja1JlYXNvbjogcm93LnF1aWNrUmVhc29uDQogICAgICB9KSk7DQoNCiAgICAgIHRoaXMuJG1vZGFsLmNvbmZpcm0oJ+aYr+WQpuehruiupOaPkOS6pOmAieS4reS6uuWRmOeahOW/q+mAn+ivhOWIhu+8nycpLnRoZW4oKCkgPT4gew0KICAgICAgICByZXR1cm4gYmF0Y2hRdWlja1Njb3JlKHN1Ym1pdERhdGEpOw0KICAgICAgfSkudGhlbigoKSA9PiB7DQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuaJuemHj+ivhOWIhuaIkOWKnyIpOw0KICAgICAgICB0aGlzLmJhdGNoUXVpY2tTY29yZU9wZW4gPSBmYWxzZTsNCiAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgIHRoaXMuZ2V0Q2hlY2tlZExpc3QoKTsNCiAgICAgIH0pLmNhdGNoKCgpID0+IHt9KTsNCiAgICB9LA0KDQogICAgLyoqIOmqjOivgeaJuemHj+W/q+mAn+ivhOWIhiAqLw0KICAgIHZhbGlkYXRlQmF0Y2hRdWlja1Njb3JlKCkgew0KICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCB0aGlzLnNlbGVjdGVkUm93cy5sZW5ndGg7IGkrKykgew0KICAgICAgICBjb25zdCByb3cgPSB0aGlzLnNlbGVjdGVkUm93c1tpXTsNCiAgICAgICAgDQogICAgICAgIC8vIOajgOafpeaYr+WQpuWhq+WGmeS6huivhOWIhg0KICAgICAgICBpZiAoIXJvdy5xdWlja1Njb3JlICYmIHJvdy5xdWlja1Njb3JlICE9PSAwKSB7DQogICAgICAgICAgcmV0dXJuIHsNCiAgICAgICAgICAgIGlzVmFsaWQ6IGZhbHNlLA0KICAgICAgICAgICAgbWVzc2FnZTogYOesrCR7aSArIDF96KGMICR7cm93Lm5hbWV9IOacquWhq+WGmeivhOWIhu+8jOivt+WFiOWhq+WGmeivhOWIhmANCiAgICAgICAgICB9Ow0KICAgICAgICB9DQoNCiAgICAgICAgLy8g6L+Q5pS557uE57uH6YOo6K+E5YiG5pe255qE6aqM6K+BDQogICAgICAgIGlmIChyb3cuc3RhdHVzID09ICczJykgew0KICAgICAgICAgIGxldCBwcmV2aW91c1Njb3JlID0gbnVsbDsNCiAgICAgICAgICBsZXQgcHJldmlvdXNTY29yZU5hbWUgPSAnJzsNCiAgICAgICAgICANCiAgICAgICAgICAvLyDliKTmlq3kuIrkuIDnjq/oioLor4TliIYNCiAgICAgICAgICBpZiAocm93LmJ1c2luZXNzU2NvcmUgIT09IG51bGwgJiYgcm93LmJ1c2luZXNzU2NvcmUgIT09IHVuZGVmaW5lZCAmJiByb3cuYnVzaW5lc3NTY29yZSAhPT0gJycpIHsNCiAgICAgICAgICAgIC8vIOacieS6i+S4mumDqOivhOWIhu+8jOS7peS6i+S4mumDqOivhOWIhuS4uuWHhg0KICAgICAgICAgICAgcHJldmlvdXNTY29yZSA9IHBhcnNlRmxvYXQocm93LmJ1c2luZXNzU2NvcmUpOw0KICAgICAgICAgICAgcHJldmlvdXNTY29yZU5hbWUgPSAn5LqL5Lia6YOo6aKG5a+86K+E5YiGJzsNCiAgICAgICAgICB9IGVsc2UgaWYgKHJvdy5kZXB0U2NvcmUgIT09IG51bGwgJiYgcm93LmRlcHRTY29yZSAhPT0gdW5kZWZpbmVkICYmIHJvdy5kZXB0U2NvcmUgIT09ICcnKSB7DQogICAgICAgICAgICAvLyDmsqHmnInkuovkuJrpg6jor4TliIbvvIzku6Xpg6jpl6jor4TliIbkuLrlh4YNCiAgICAgICAgICAgIHByZXZpb3VzU2NvcmUgPSBwYXJzZUZsb2F0KHJvdy5kZXB0U2NvcmUpOw0KICAgICAgICAgICAgcHJldmlvdXNTY29yZU5hbWUgPSAn6YOo6Zeo6aKG5a+86K+E5YiGJzsNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgLy8g6YO95rKh5pyJ77yM5Lul6Ieq6K+E5YiG5Li65YeGDQogICAgICAgICAgICBwcmV2aW91c1Njb3JlID0gcGFyc2VGbG9hdChyb3cuc2VsZlNjb3JlKTsNCiAgICAgICAgICAgIHByZXZpb3VzU2NvcmVOYW1lID0gJ+iHquivhOWIhic7DQogICAgICAgICAgfQ0KICAgICAgICAgIA0KICAgICAgICAgIC8vIOi/kOaUuee7hOe7h+mDqOivhOWIhuS4juS4iuS4gOeOr+iKguivhOWIhuS4jeS4gOiHtOaXtu+8jOWKoOWHj+WIhueQhueUseW/heWhqw0KICAgICAgICAgIGlmIChwYXJzZUZsb2F0KHJvdy5xdWlja1Njb3JlKSAhPT0gcHJldmlvdXNTY29yZSAmJiAhcm93LnF1aWNrUmVhc29uKSB7DQogICAgICAgICAgICByZXR1cm4gew0KICAgICAgICAgICAgICBpc1ZhbGlkOiBmYWxzZSwNCiAgICAgICAgICAgICAgbWVzc2FnZTogYOesrCR7aSArIDF96KGMICR7cm93Lm5hbWV9IOi/kOaUuee7hOe7h+mDqOivhOWIhigke3Jvdy5xdWlja1Njb3JlfeWIhinkuI4ke3ByZXZpb3VzU2NvcmVOYW1lfSgke3ByZXZpb3VzU2NvcmV95YiGKeS4jeS4gOiHtO+8jOivt+Whq+WGmeWKoOWHj+WIhueQhueUsWANCiAgICAgICAgICAgIH07DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9DQoNCiAgICAgIHJldHVybiB7IGlzVmFsaWQ6IHRydWUgfTsNCiAgICB9LA0KDQogICAgLyoqIOafpeeci+ivhOWIhuiusOW9leivpuaDhSAqLw0KICAgIGhhbmRsZUNoZWNrZWREZXRhaWwocm93KSB7DQogICAgICBnZXRJbmZvKHtpZDogcm93LmluZm9JZH0pLnRoZW4ocmVzID0+IHsNCiAgICAgICAgY29uc29sZS5sb2cocmVzKTsNCiAgICAgICAgdGhpcy5yZXNldCgpOw0KICAgICAgICBpZihyZXMuY29kZSA9PSAyMDApew0KICAgICAgICAgIHRoaXMuY2hlY2tJbmZvID0gcmVzLmRhdGE7DQogICAgICAgICAgbGV0IGxpc3QgPSBKU09OLnBhcnNlKHJlcy5kYXRhLmNvbnRlbnQpOw0KICAgICAgICAgIHRoaXMuaGFuZGxlU3Bhbkxpc3QobGlzdCk7DQogICAgICAgICAgbGV0IHBhcmFtID17DQogICAgICAgICAgICBkZXB0SWQ6cmVzLmRhdGEuZGVwdElkLA0KICAgICAgICAgICAgYXNzZXNzRGF0ZTpyZXMuZGF0YS5hc3Nlc3NEYXRlDQogICAgICAgICAgfQ0KICAgICAgICAgIHRoaXMuZ2V0QmVBc3Nlc3NlZExpc3QocGFyYW0pOyAgLy8g6I635Y+W5qiq5ZCR6K+E5YiG6KKr6ICD5qC45pWw5o2uDQogICAgICAgICAgdGhpcy5nZXRTZWxmQXNzZXNzVXNlcih7aWQ6cmVzLmRhdGEudXNlcklkfSk7ICAvLyDojrflj5bnlKjmiLfkv6Hmga8NCiAgICAgICAgICB0aGlzLmNoZWNrSW5mby5saXN0ID0gbGlzdDsNCiAgICAgICAgICB0aGlzLm9wZW4gPSB0cnVlOw0KICAgICAgICB9DQogICAgICB9KS5jYXRjaChlcnJvciA9PiB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+iOt+WPluivpuaDheWksei0pScpOw0KICAgICAgfSk7DQogICAgfSwNCg0KICAgIC8vIOiOt+WPluWOhuWPsuWkh+azqO+8iOWQhOatpemqpOWKoOWHj+WIhuWOn+WboOaLvOaOpe+8iQ0KICAgIGdldEhpc3RvcnlSZW1hcmtzKHJvdykgew0KICAgICAgY29uc3QgcmVtYXJrcyA9IFtdOw0KDQogICAgICAvLyDpg6jpl6jpooblr7zliqDlh4/liIbnkIbnlLENCiAgICAgIGlmIChyb3cuZGVwdFNjb3JlUmVhc29uICYmIHJvdy5kZXB0U2NvcmVSZWFzb24udHJpbSgpKSB7DQogICAgICAgIHJlbWFya3MucHVzaChg6YOo6Zeo6aKG5a+877yaJHtyb3cuZGVwdFNjb3JlUmVhc29uLnRyaW0oKX1gKTsNCiAgICAgIH0NCg0KICAgICAgLy8g5LqL5Lia6YOo5Yqg5YeP5YiG55CG55SxDQogICAgICBpZiAocm93LmJ1c2luZXNzU2NvcmVSZWFzb24gJiYgcm93LmJ1c2luZXNzU2NvcmVSZWFzb24udHJpbSgpKSB7DQogICAgICAgIHJlbWFya3MucHVzaChg5LqL5Lia6YOo77yaJHtyb3cuYnVzaW5lc3NTY29yZVJlYXNvbi50cmltKCl9YCk7DQogICAgICB9DQoNCiAgICAgIC8vIOi/kOaUuemDqC/nu4Tnu4fpg6jliqDlh4/liIbnkIbnlLENCiAgICAgIGlmIChyb3cub3JnYW5pemF0aW9uU2NvcmVSZWFzb24gJiYgcm93Lm9yZ2FuaXphdGlvblNjb3JlUmVhc29uLnRyaW0oKSkgew0KICAgICAgICByZW1hcmtzLnB1c2goYOi/kOaUuemDqC/nu4Tnu4fpg6jvvJoke3Jvdy5vcmdhbml6YXRpb25TY29yZVJlYXNvbi50cmltKCl9YCk7DQogICAgICB9DQoNCiAgICAgIHJldHVybiByZW1hcmtzLmpvaW4oJ1xuJyk7DQogICAgfQ0KICB9DQp9Ow0K"}, {"version": 3, "sources": ["organizationCheck.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkg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file": "organizationCheck.vue", "sourceRoot": "src/views/assess/self/check", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"80px\">\r\n      <el-row>\r\n        <el-form-item label=\"考核年月\" prop=\"assessDate\">\r\n          <el-date-picker\r\n            v-model=\"queryParams.assessDate\"\r\n            type=\"month\"\r\n            value-format=\"yyyy-M\"\r\n            format=\"yyyy 年 M 月\"\r\n            placeholder=\"选择考核年月\"\r\n            :clearable=\"false\">\r\n          </el-date-picker>\r\n        </el-form-item>\r\n        <el-form-item label=\"姓名\" prop=\"name\">\r\n          <el-input\r\n            v-model=\"queryParams.name\"\r\n            placeholder=\"请输入姓名\"\r\n            clearable\r\n            @keyup.enter.native=\"handleQuery\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"部门\" prop=\"deptId\">\r\n          <treeselect style=\"width: 200px;\" v-model=\"queryParams.deptId\" :multiple=\"false\" :options=\"deptOptions\" :normalizer=\"normalizer\" :disable-branch-nodes=\"true\" placeholder=\"请选择部门\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"岗位类型\" prop=\"postType\">\r\n          <el-select v-model=\"queryParams.postType\" placeholder=\"请选择岗位类型\" clearable style=\"width: 150px;\">\r\n            <el-option label=\"技术\" value=\"0\"></el-option>\r\n            <el-option label=\"行政\" value=\"1\"></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n          <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n        </el-form-item>\r\n      </el-row>\r\n    </el-form>\r\n\r\n    \r\n    <!-- 待评分列表 -->\r\n    <el-card class=\"box-card\" style=\"margin-bottom: 20px;\">\r\n      <div slot=\"header\" class=\"clearfix\">\r\n        <span style=\"font-size: 16px; font-weight: bold; color: #409EFF;\">\r\n          <i class=\"el-icon-s-order\"></i>\r\n          {{ toCheckLabel }}\r\n        </span>\r\n      </div>\r\n      \r\n      <el-row :gutter=\"10\" class=\"mb8\">\r\n        <el-col :span=\"1.5\">\r\n          <el-button\r\n            type=\"success\"\r\n            plain\r\n            icon=\"el-icon-edit\"\r\n            size=\"mini\"\r\n            @click=\"handleBatchQuickScore\"\r\n          >批量快速评分</el-button>\r\n        </el-col>\r\n        <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n      </el-row>\r\n        <el-table v-loading=\"loading\" :data=\"listToCheck\" @selection-change=\"handleSelectionChange\">\r\n          <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n          <el-table-column label=\"工号\" align=\"center\" prop=\"workNo\" width=\"120\"/>\r\n          <el-table-column label=\"姓名\" align=\"center\" prop=\"name\" width=\"120\"/>\r\n          <el-table-column label=\"部门\" align=\"center\" prop=\"deptName\" ></el-table-column>\r\n          <el-table-column label=\"岗位类型\" align=\"center\" prop=\"postType\" width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              <el-tag :type=\"scope.row.postType == '0' ? 'primary' : 'success'\" size=\"small\">\r\n                {{ scope.row.postType == '0' ? '技术' : '行政' }}\r\n              </el-tag>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"自评分\" align=\"center\" prop=\"selfScore\"></el-table-column>\r\n          <el-table-column label=\"部门领导评分\" align=\"center\" prop=\"deptScore\"></el-table-column>\r\n          <el-table-column label=\"事业部评分\" align=\"center\" prop=\"businessScore\">\r\n            <template slot-scope=\"scope\">\r\n              <span v-if=\"scope.row.businessScore\">{{ scope.row.businessScore }}</span>\r\n              <span v-else>无</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"是否100%挂钩公司效益\" align=\"center\" prop=\"benefitLinkFlag\">\r\n            <template slot-scope=\"scope\">\r\n              <el-tag :type=\"scope.row.benefitLinkFlag == 'Y' ? 'success' : 'info'\">{{ scope.row.benefitLinkFlag == \"Y\" ? \"是\" : \" 否\" }}</el-tag>\r\n            </template>\r\n          </el-table-column>\r\n          <!-- <el-table-column label=\"运改组织部评分\" align=\"center\" prop=\"organizationScore\">\r\n            <template slot-scope=\"scope\">\r\n              <el-input-number\r\n                v-model=\"scope.row.quickScore\"\r\n                :min=\"0\"\r\n                :max=\"100\"\r\n                size=\"mini\"\r\n                style=\"width: 120px\"\r\n                placeholder=\"请输入分数\">\r\n              </el-input-number>\r\n            </template>\r\n          </el-table-column> -->\r\n          <!-- <el-table-column label=\"加减分原因\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <el-input\r\n                v-model=\"scope.row.quickReason\"\r\n                type=\"textarea\"\r\n                :autosize=\"{ minRows: 1, maxRows: 4}\"\r\n                size=\"mini\"\r\n                style=\"width: 150px\"\r\n                placeholder=\"请输入加减分原因\">\r\n              </el-input>\r\n            </template>\r\n          </el-table-column> -->\r\n          <el-table-column label=\"备注\" align=\"center\" width=\"200\">\r\n            <template slot-scope=\"scope\">\r\n              <el-tooltip effect=\"dark\" placement=\"top\" :disabled=\"!getHistoryRemarks(scope.row)\">\r\n                <div slot=\"content\" style=\"max-width: 300px; white-space: pre-wrap;\">\r\n                  {{ getHistoryRemarks(scope.row) }}\r\n                </div>\r\n                <div class=\"history-remarks-cell\">\r\n                  {{ getHistoryRemarks(scope.row) || '暂无备注' }}\r\n                </div>\r\n              </el-tooltip>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"150\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                icon=\"el-icon-edit\"\r\n                @click=\"handleCheckDetail(scope.row)\"\r\n              >详细评分</el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      <pagination\r\n        v-show=\"total>0\"\r\n        :total=\"total\"\r\n        :page.sync=\"queryParams.pageNum\"\r\n        :limit.sync=\"queryParams.pageSize\"\r\n        @pagination=\"getList\"\r\n      />\r\n    </el-card>\r\n\r\n    <!-- 评分记录 -->\r\n    <el-card class=\"box-card\">\r\n      <div slot=\"header\" class=\"clearfix\">\r\n        <span style=\"font-size: 16px; font-weight: bold; color: #67C23A;\">\r\n          <i class=\"el-icon-document\"></i>\r\n          评分记录({{ checkedTotal }})\r\n        </span>\r\n      </div>\r\n      \r\n      <el-table v-loading=\"loading\" :data=\"listChecked\">\r\n        <el-table-column label=\"工号\" align=\"center\" prop=\"workNo\" width=\"120\"/>\r\n        <el-table-column label=\"姓名\" align=\"center\" prop=\"name\" width=\"120\"/>\r\n        <el-table-column label=\"部门\" align=\"center\" prop=\"deptName\" />\r\n        <el-table-column label=\"职务\" align=\"center\" prop=\"job\" width=\"150\"/>\r\n        <el-table-column label=\"岗位类型\" align=\"center\" prop=\"postType\" width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag :type=\"scope.row.postType == '0' ? 'primary' : 'success'\" size=\"small\">\r\n              {{ scope.row.postType == '0' ? '技术' : '行政' }}\r\n            </el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"评分类型\" align=\"center\" prop=\"type\" >\r\n          <template slot-scope=\"scope\">\r\n            <el-tag v-if=\"scope.row.type == '1'\" type=\"primary\" size=\"small\">部门领导评分</el-tag>\r\n            <el-tag v-if=\"scope.row.type == '2'\" type=\"warning\" size=\"small\">事业部领导评分</el-tag>\r\n            <el-tag v-if=\"scope.row.type == '3'\" type=\"success\" size=\"small\">运改组织部审核</el-tag>\r\n            <el-tag v-if=\"scope.row.type == '4'\" type=\"info\" size=\"small\">条线领导评分</el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"评分时间\" align=\"center\" prop=\"createTime\" width=\"160\"/>\r\n        <el-table-column label=\"评分\" align=\"center\" prop=\"score\" width=\"100\"/>\r\n        <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              icon=\"el-icon-view\"\r\n              @click=\"handleCheckedDetail(scope.row)\"\r\n            >查看详情</el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      \r\n      <pagination\r\n        v-show=\"checkedTotal>0\"\r\n        :total=\"checkedTotal\"\r\n        :page.sync=\"checkedQueryParams.pageNum\"\r\n        :limit.sync=\"checkedQueryParams.pageSize\"\r\n        @pagination=\"getCheckedList\"\r\n        style=\"margin-top: 20px;\"\r\n      />\r\n    </el-card>\r\n\r\n    <el-dialog\r\n      :visible.sync=\"open\"\r\n      fullscreen\r\n      class=\"assessment-detail-dialog\">\r\n      <div class=\"detail-container\">\r\n        <div class=\"detail-header\">\r\n          <h2 style=\"text-align: center; color: #303133; margin-bottom: 20px;\">\r\n            <i class=\"el-icon-document\"></i>\r\n            月度业绩考核表\r\n          </h2>\r\n          <el-card shadow=\"never\" style=\"margin-bottom: 20px;\">\r\n            <el-descriptions class=\"margin-top\" :column=\"3\" border>\r\n              <el-descriptions-item>\r\n                <template slot=\"label\">\r\n                  <i class=\"el-icon-user\"></i> 姓名\r\n                </template>\r\n                {{ checkInfo.name }}\r\n              </el-descriptions-item>\r\n              <el-descriptions-item>\r\n                <template slot=\"label\">\r\n                  <i class=\"el-icon-office-building\"></i> 部门\r\n                </template>\r\n                {{ checkInfo.deptName }}\r\n              </el-descriptions-item>\r\n              <el-descriptions-item>\r\n                <template slot=\"label\">\r\n                  <i class=\"el-icon-date\"></i> 考核年月\r\n                </template>\r\n                {{ checkInfo.assessDate }}\r\n              </el-descriptions-item>\r\n            </el-descriptions>\r\n          </el-card>\r\n        </div>\r\n        \r\n        <el-card shadow=\"never\" class=\"assessment-table-card\">\r\n          <div slot=\"header\" class=\"clearfix\">\r\n            <span style=\"font-size: 16px; font-weight: bold; color: #409EFF;\">\r\n              <i class=\"el-icon-s-data\"></i>\r\n              考核详情\r\n            </span>\r\n          </div>\r\n          <el-table v-loading=\"loading\" :data=\"checkInfo.list\"\r\n            :span-method=\"objectSpanMethod\" border stripe>\r\n            <el-table-column label=\"类型\" align=\"center\" prop=\"item\" width=\"120\"/>\r\n            <el-table-column label=\"指标\" align=\"center\" prop=\"category\" width=\"150\"/>\r\n            <el-table-column label=\"目标\" align=\"center\" prop=\"target\" width=\"180\"/>\r\n            <el-table-column label=\"评分标准\" align=\"center\" prop=\"standard\" />\r\n            <el-table-column label=\"完成实绩（若扣分，写明原因）\" align=\"center\" prop=\"performance\" >\r\n              <template slot-scope=\"scope\">\r\n                  <div style=\"display: flex\">\r\n                    <el-popover\r\n                      placement=\"left\"\r\n                      width=\"636\"\r\n                      trigger=\"click\"\r\n                      :ref=\"'popover' + scope.$index\">\r\n                      <el-table :data=\"beAssessedList\">\r\n                        <el-table-column width=\"150\" property=\"assessDeptName\" label=\"提出考核单位\"></el-table-column>\r\n                        <el-table-column width=\"300\" property=\"assessContent\" label=\"事项\"></el-table-column>\r\n                        <el-table-column width=\"80\" property=\"deductionOfPoint\" label=\"加减分\"></el-table-column>\r\n                      </el-table>\r\n                      <el-button slot=\"reference\" icon=\"el-icon-search\" size=\"small\"></el-button>\r\n                    </el-popover>\r\n                    <span style=\"margin-left: 10px;\">{{ scope.row.performance }}</span>\r\n                  </div>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"加减分\" align=\"center\" prop=\"dePoints\" width=\"150\" />\r\n            <el-table-column label=\"加减分理由\" align=\"center\" prop=\"pointsReason\" width=\"180\" />\r\n          </el-table>\r\n        </el-card>\r\n        \r\n        <el-card shadow=\"never\" class=\"signature-card\" style=\"margin-top: 20px;\">\r\n          <div slot=\"header\" class=\"clearfix\">\r\n            <span style=\"font-size: 16px; font-weight: bold; color: #67C23A;\">\r\n              <i class=\"el-icon-edit-outline\"></i>\r\n              评分记录\r\n            </span>\r\n          </div>\r\n          <el-form size=\"small\" :inline=\"false\" label-width=\"200px\" label-position=\"left\">\r\n            <!-- 自评分 -->\r\n            <el-form-item>\r\n              <template slot=\"label\">\r\n                <span style=\"color: #606266;\">\r\n                  自评分数 / 签名：\r\n                </span>\r\n              </template>\r\n              <div class=\"signature-content\">\r\n                <span class=\"score-text\">{{ checkInfo.selfScore }} 分</span>\r\n                <span class=\"separator\">/</span>\r\n                <span class=\"signature-name\">{{ checkInfo.name }}</span>\r\n              </div>\r\n            </el-form-item>\r\n            \r\n            <!-- 部门领导评分 -->\r\n            <el-form-item v-if=\"checkInfo.deptScore && checkInfo.deptUserName\">\r\n              <template slot=\"label\">\r\n                <span style=\"color: #606266;\">\r\n                  部门领导评分 / 签名：\r\n                </span>\r\n              </template>\r\n              <div class=\"signature-content\">\r\n                <span class=\"score-text\">{{ checkInfo.deptScore }} 分</span>\r\n                <span class=\"separator\">/</span>\r\n                <span class=\"signature-name\">{{ checkInfo.deptUserName }}</span>\r\n                <div v-if=\"checkInfo.deptScoreReason\" class=\"reason-text\">\r\n                  <span class=\"reason-label\">加减分理由：</span>\r\n                  <span class=\"reason-content\">{{ checkInfo.deptScoreReason }}</span>\r\n                </div>\r\n              </div>\r\n            </el-form-item>\r\n            \r\n            <!-- 事业部领导评分 -->\r\n            <el-form-item v-if=\"checkInfo.businessUserName && checkInfo.businessScore\">\r\n              <template slot=\"label\">\r\n                <span style=\"color: #606266;\">\r\n                  事业部领导评分 / 签名：\r\n                </span>\r\n              </template>\r\n              <div class=\"signature-content\">\r\n                <span class=\"score-text\">{{ checkInfo.businessScore }} 分</span>\r\n                <span class=\"separator\">/</span>\r\n                <span class=\"signature-name\">{{ checkInfo.businessUserName }}</span>\r\n                <div v-if=\"checkInfo.businessScoreReason\" class=\"reason-text\">\r\n                  <span class=\"reason-label\">加减分理由：</span>\r\n                  <span class=\"reason-content\">{{ checkInfo.businessScoreReason }}</span>\r\n                </div>\r\n              </div>\r\n            </el-form-item>\r\n            \r\n            <!-- 运改组织部评分 -->\r\n            <el-form-item v-if=\"checkInfo.organizationScore && checkInfo.organizationUserName\">\r\n              <template slot=\"label\">\r\n                <span style=\"color: #606266;\">\r\n                  运改组织部评分 / 签名：\r\n                </span>\r\n              </template>\r\n              <div class=\"signature-content\">\r\n                <span class=\"score-text\">{{ checkInfo.organizationScore }} 分</span>\r\n                <span class=\"separator\">/</span>\r\n                <span class=\"signature-name\">{{ checkInfo.organizationUserName }}</span>\r\n                <div v-if=\"checkInfo.organizationScoreReason\" class=\"reason-text\">\r\n                  <span class=\"reason-label\">加减分理由：</span>\r\n                  <span class=\"reason-content\">{{ checkInfo.organizationScoreReason }}</span>\r\n                </div>\r\n              </div>\r\n            </el-form-item>\r\n            \r\n            <!-- 公司效益信息 -->\r\n            <el-form-item label=\"是否100%挂钩公司效益：\">\r\n              <el-tag :type=\"userInfo.benefitLinkFlag == 'Y' ? 'success' : 'info'\">{{ userInfo.benefitLinkFlag == \"Y\" ? \"是\" : \" 否\" }}</el-tag>\r\n            </el-form-item>\r\n            \r\n            <!-- 当前状态评分输入 -->\r\n            <el-form-item v-if=\"checkInfo.status == '3'\" label=\"公司效益加减分：\">\r\n              <div style=\"display: flex; align-items: center; gap: 10px; margin-bottom: 10px;\">\r\n                <el-input-number v-model=\"benefit\" placeholder=\"请输入公司效益加减\" style=\"width: 200px;\" />\r\n                <span>分</span>\r\n                <el-button type=\"primary\" size=\"mini\" @click=\"calScore\">计 算</el-button>\r\n              </div>\r\n              <div v-if=\"benefitDetail\" class=\"benefit-detail\">\r\n                <i class=\"el-icon-info\"></i>\r\n                {{ benefitDetail }}\r\n              </div>\r\n            </el-form-item>\r\n            <el-form-item v-if=\"checkInfo.status == '3'\" label=\"运改部/组织部审核：\">\r\n              <div style=\"display: flex; align-items: center; gap: 10px;\">\r\n                <el-input-number v-model=\"form.organizationScore\" :min=\"0\" :max=\"100\" placeholder=\"请输入评分\" style=\"width: 150px;\" />\r\n                <span>分</span>\r\n              </div>\r\n            </el-form-item>\r\n            <el-form-item v-if=\"checkInfo.status == '3'\" label=\"运改组织部加减分理由：\">\r\n              <div style=\"display: flex;width: 500px;\">\r\n                <el-input\r\n                  v-model=\"form.organizationScoreReason\"\r\n                  type=\"textarea\"\r\n                  :autosize=\"{ minRows: 3, maxRows: 6}\"\r\n                  placeholder=\"请输入运改组织部加减分理由\"\r\n                  maxlength=\"500\"\r\n                  show-word-limit>\r\n                </el-input>\r\n              </div>\r\n            </el-form-item>\r\n          </el-form>\r\n        </el-card>\r\n        \r\n        <div class=\"dialog-footer\" style=\"text-align: center; margin-top: 30px; padding: 20px;\">\r\n          <el-button type=\"primary\" size=\"medium\" @click=\"checkSubmit\">\r\n            <i class=\"el-icon-check\"></i> 提 交\r\n          </el-button>\r\n          <el-button plain type=\"danger\" size=\"medium\" @click=\"rejectClick\">\r\n            <i class=\"el-icon-close\"></i> 退 回\r\n          </el-button>\r\n          <el-button plain type=\"info\" size=\"medium\" @click=\"cancel\">\r\n            <i class=\"el-icon-back\"></i> 取 消\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 驳回弹出框 -->\r\n    <el-dialog title=\"退回\" :visible.sync=\"rejectOpen\" append-to-body center width=\"40%\">\r\n        <el-form label-width=\"150px\">\r\n            <el-form-item label=\"退回原因:\">\r\n                <el-input type=\"textarea\"\r\n                :autosize=\"{ minRows: 5}\" \r\n                v-model=\"checkInfo.rejectReason\" \r\n                placeholder=\"请输入内容\"></el-input>\r\n            </el-form-item>\r\n        </el-form>\r\n        <div slot=\"footer\" class=\"dialog-footer\" style=\"text-align: center;\">\r\n            <el-button type=\"success\" plain @click=\"rejectSubmit\">提 交</el-button>\r\n            <el-button @click=\"rejectCancel\">取 消</el-button>\r\n        </div>\r\n    </el-dialog>\r\n\r\n      <!-- 批量快速评分对话框 -->\r\n      <el-dialog :title=\"'批量快速评分确认'\" :visible.sync=\"batchQuickScoreOpen\" width=\"1400px\" append-to-body>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <div class=\"benefit-score-container\" style=\"display: flex; align-items: center; justify-content: flex-start; margin-bottom: 8px;\">\r\n              <div class=\"benefit-input-group\" style=\"display: flex; align-items: center; height: 32px;\">\r\n                <span style=\"margin-right: 10px; white-space: nowrap; line-height: 32px;\">公司效益加减分:</span>\r\n                <el-input \r\n                  type=\"number\" \r\n                  v-model=\"benefit\" \r\n                  placeholder=\"请输入公司效益加减\" \r\n                  style=\"width: 180px; margin-right: 5px;\"\r\n                />\r\n                <span style=\"margin-right: 15px; line-height: 32px;\">分</span>\r\n                <el-button type=\"primary\" size=\"mini\" @click=\"batchCalScore\" style=\"height: 28px;\">计 算</el-button>\r\n              </div>\r\n            </div>\r\n          </el-col>\r\n        </el-row>\r\n        <el-alert\r\n          title=\"请确认以下人员的评分信息\"\r\n          type=\"warning\"\r\n          :closable=\"false\"\r\n          show-icon\r\n          class=\"mb20\"\r\n        />\r\n        <el-table :data=\"selectedRows\" size=\"small\" border>\r\n          <el-table-column label=\"工号\" align=\"center\" prop=\"workNo\" />\r\n          <el-table-column label=\"姓名\" align=\"center\" prop=\"name\" />\r\n          <el-table-column label=\"部门\" align=\"center\" prop=\"deptName\" />\r\n          <el-table-column label=\"岗位\" align=\"center\" prop=\"job\" />\r\n          <el-table-column label=\"岗位类型\" align=\"center\" prop=\"postType\" width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              <el-tag :type=\"scope.row.postType == '0' ? 'primary' : 'success'\" size=\"small\">\r\n                {{ scope.row.postType == '0' ? '技术' : '行政' }}\r\n              </el-tag>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"是否100%挂钩公司效益\" align=\"center\" prop=\"benefitLinkFlag\">\r\n            <template slot-scope=\"scope\">\r\n              <el-tag :type=\"scope.row.benefitLinkFlag == 'Y' ? 'success' : 'info'\">{{ scope.row.benefitLinkFlag == \"Y\" ? \"是\" : \" 否\" }}</el-tag>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"自评分\" align=\"center\" prop=\"selfScore\" width=\"90px\"/>\r\n          <el-table-column label=\"部门评分\" align=\"center\" prop=\"deptScore\" width=\"90px\">\r\n            <template slot-scope=\"scope\">\r\n              <span v-if=\"scope.row.deptScore\">{{ scope.row.deptScore }}</span>\r\n              <span v-else>-</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"事业部评分\" align=\"center\" prop=\"businessScore\" width=\"90px\">\r\n            <template slot-scope=\"scope\">\r\n              <span v-if=\"scope.row.businessScore\">{{ scope.row.businessScore }}</span>\r\n              <span v-else>-</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"效益加减分\" align=\"center\" prop=\"benefitScore\" width=\"90px\"/>\r\n          <el-table-column label=\"运改/组织部评分\" align=\"center\" prop=\"quickScore\" width=\"160px\">\r\n            <template slot-scope=\"scope\">\r\n              <!-- <span :class=\"{'text-red': !scope.row.quickScore}\">{{ scope.row.quickScore || '未计算' }}</span> -->\r\n               <el-input-number \r\n                v-model=\"scope.row.quickScore\" \r\n                :min=\"0\" \r\n                :max=\"100\" \r\n                size=\"mini\"\r\n                style=\"width: 120px\"\r\n                placeholder=\"请输入分数\">\r\n              </el-input-number>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"加减分理由\" align=\"center\" prop=\"quickReason\" width=\"180px\">\r\n            <template slot-scope=\"scope\">\r\n              <el-input\r\n                v-model=\"scope.row.quickReason\"\r\n                type=\"textarea\"\r\n                :autosize=\"{ minRows: 1, maxRows: 4}\"\r\n                size=\"mini\"\r\n                style=\"width: 150px\"\r\n                placeholder=\"请输入加减分原因\">\r\n              </el-input>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"备注\" align=\"center\" width=\"200\">\r\n            <template slot-scope=\"scope\">\r\n              <el-tooltip effect=\"dark\" placement=\"top\" :disabled=\"!getHistoryRemarks(scope.row)\">\r\n                <div slot=\"content\" style=\"max-width: 300px; white-space: pre-wrap;\">\r\n                  {{ getHistoryRemarks(scope.row) }}\r\n                </div>\r\n                <div class=\"batch-history-remarks-cell\">\r\n                  {{ getHistoryRemarks(scope.row) || '暂无备注' }}\r\n                </div>\r\n              </el-tooltip>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n        <div slot=\"footer\" class=\"dialog-footer\">\r\n          <el-button type=\"primary\" @click=\"submitBatchQuickScore\" :disabled=\"!canSubmitBatchScore\">确 定</el-button>\r\n          <el-button @click=\"cancelBatchQuickScore\">取 消</el-button>\r\n          </div>\r\n      </el-dialog>\r\n\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listInfo, listChecked, getInfo, check, listBeAssessed, rejectInfo, batchQuickScore, batchWithBenefitByIds} from \"@/api/assess/self/info\";\r\nimport { getSelfAssessUser} from \"@/api/assess/self/user\";\r\nimport { listDept } from \"@/api/assess/lateral/dept\";\r\nimport Treeselect from \"@riophae/vue-treeselect\";\r\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.css\";\r\n\r\nexport default {\r\n  name: \"OrganizationCheck\",\r\n  components: {\r\n    Treeselect\r\n  },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 退回原因输入框\r\n      rejectOpen:false,\r\n      // 总条数\r\n      total: 0,\r\n      checkedTotal: 0,\r\n      // 绩效考核-干部自评人员配置表格数据\r\n      listToCheck: [],\r\n      listChecked: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        workNo: null,\r\n        name:null,\r\n        deptId:null,\r\n        assessDate:null,\r\n        status:\"3\",\r\n        postType:null\r\n      },\r\n      // 评分记录查询参数\r\n      checkedQueryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        workNo: null,\r\n        name:null,\r\n        deptId:null,\r\n        assessDate:null,\r\n        postType:null\r\n      },\r\n      // 表单参数\r\n      form: {\r\n        id:null,\r\n        // 部门领导评分\r\n        deptScore:null,\r\n        // 事业部评分\r\n        businessScore:null,\r\n        // 条线领导评分\r\n        leaderScore:null,\r\n        // 运改组织部评分\r\n        organizationScore:null,\r\n        // 运改组织部加减分理由\r\n        organizationScoreReason:null,\r\n      },\r\n      // 表单校验\r\n      rules: {\r\n      },\r\n      deptOptions:[],\r\n      openCheck:false,\r\n      checkInfo:{},\r\n      // 合并单元格\r\n      spanList:[],\r\n      // 待评分标签\r\n      toCheckLabel:\"待评分(0)\",\r\n      // 横向被考评信息\r\n      beAssessedList:[],\r\n      benefit:null,  // 公司效益分\r\n      userInfo:{\r\n        benefitLinkFlag:null\r\n      },\r\n      benefitDetail:\"\",  // 效益详细\r\n      // 选中数组\r\n      multipleSelection: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 批量快速评分对话框显示状态\r\n      quickScoreDialogVisible: false,\r\n      // 批量快速评分表单参数\r\n      batchQuickScoreForm: {\r\n        score: undefined,\r\n        ids: []\r\n      },\r\n      // 批量快速评分表单验证规则\r\n      batchQuickScoreRules: {\r\n        score: [\r\n          { required: true, message: \"评分不能为空\", trigger: \"blur\" },\r\n          { type: 'number', message: \"评分必须为数字\", trigger: \"blur\" }\r\n        ]\r\n      },\r\n      // 批量快速评分对话框\r\n      batchQuickScoreOpen: false,\r\n      // 选中数组\r\n      ids: [],\r\n      // 选中的行数据\r\n      selectedRows: [],\r\n    };\r\n  },\r\n  computed: {\r\n    // 是否可以提交批量评分\r\n    canSubmitBatchScore() {\r\n      if (this.selectedRows.length === 0) return false;\r\n      \r\n      // 简单检查是否所有行都填写了评分\r\n      for (let row of this.selectedRows) {\r\n        if (!row.quickScore && row.quickScore !== 0) {\r\n          return false;\r\n        }\r\n      }\r\n      \r\n      return true;\r\n    }\r\n  },\r\n  created() {\r\n    this.queryParams.assessDate = this.getDefaultAssessDate()\r\n    this.checkedQueryParams.assessDate = this.getDefaultAssessDate()\r\n    // this.getSelfAssessUser();\r\n    // this.getCheckDeptList();\r\n    this.getTreeselect();\r\n    this.getList();\r\n    this.getCheckedList();\r\n  },\r\n  methods: {\r\n\r\n    // 获取默认考核日期\r\n    getDefaultAssessDate() {\r\n      const now = new Date();\r\n      const currentDay = now.getDate();\r\n\r\n      let targetDate;\r\n      if (currentDay < 10) {\r\n        // 当前日期小于10日，默认为上个月\r\n        targetDate = new Date(now.getFullYear(), now.getMonth() - 1, 1);\r\n      } else {\r\n        // 当前日期大于等于10日，默认为当月\r\n        targetDate = new Date(now.getFullYear(), now.getMonth(), 1);\r\n      }\r\n\r\n      // 格式化为 YYYY-M 格式\r\n      const year = targetDate.getFullYear();\r\n      const month = targetDate.getMonth() + 1;\r\n      return `${year}-${month}`;\r\n    },\r\n\r\n    // 获取被考核信息\r\n    getBeAssessedList(param){\r\n      listBeAssessed(param).then(res =>{\r\n        let beAssessedList = [];\r\n        if(res.code == 200){\r\n          if(res.data.length > 0){\r\n            res.data.forEach(item => {\r\n              beAssessedList = [...beAssessedList,...item.hrLateralAssessInfoList]\r\n            })\r\n            this.beAssessedList = beAssessedList;\r\n          }\r\n        }\r\n        console.log(beAssessedList)\r\n      })\r\n    },\r\n    // 获取被评分人员信息\r\n    getSelfAssessUser(param){\r\n      getSelfAssessUser(param).then(res =>{\r\n        if(res.code == 200){\r\n          this.userInfo = res.data;\r\n        }\r\n      })\r\n    },\r\n    /** 转换横向评价部门数据结构 */\r\n    normalizer(node) {\r\n      if (node.children && !node.children.length) {\r\n        delete node.children;\r\n      }\r\n      return {\r\n        id: node.deptId,\r\n        label: node.deptName,\r\n        children: node.children\r\n      };\r\n    },\r\n\t  /** 查询横向评价部门下拉树结构 */\r\n    getTreeselect() {\r\n      listDept().then(response => {\r\n        this.deptOptions = this.handleTree(response.data, \"deptId\", \"parentId\");\r\n      });\r\n    },\r\n    /** 查询绩效考核-干部自评待审核列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listInfo(this.queryParams).then(response => {\r\n        this.listToCheck = response.rows;\r\n        this.total = response.total;\r\n        this.toCheckLabel = `待评分(${response.total})`\r\n        this.loading = false;\r\n      });\r\n    },\r\n    /** 获取已审核列表 */\r\n    getCheckedList(){\r\n      this.loading = true;\r\n      listChecked(this.checkedQueryParams).then(res => {\r\n        this.listChecked = res.rows;\r\n        this.checkedTotal = res.total;\r\n        this.loading = false;\r\n      })\r\n    },\r\n\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        id: null,\r\n        organizationScore: null,\r\n        organizationScoreReason: null,\r\n      };\r\n      this.benefit = null;\r\n      this.benefitDetail = \"\";\r\n      this.userInfo = {\r\n        benefitLinkFlag:null\r\n      }\r\n      // this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.checkedQueryParams.pageNum = 1;\r\n      // 同步搜索条件\r\n      this.checkedQueryParams.name = this.queryParams.name;\r\n      this.checkedQueryParams.deptId = this.queryParams.deptId;\r\n      this.checkedQueryParams.assessDate = this.queryParams.assessDate;\r\n      this.checkedQueryParams.postType = this.queryParams.postType;\r\n      this.getList();\r\n      this.getCheckedList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n\r\n    // 审批详情\r\n    handleCheckDetail(row){\r\n      getInfo({id:row.id}).then(res => {\r\n        console.log(res);\r\n        this.reset();\r\n        if(res.code == 200){\r\n          this.checkInfo = res.data;\r\n          let list = JSON.parse(res.data.content);\r\n          this.handleSpanList(list);\r\n          let param ={\r\n            deptId:res.data.deptId,\r\n            assessDate:res.data.assessDate\r\n          }\r\n          this.getBeAssessedList(param);  // 获取横向评分被考核数据\r\n          this.getSelfAssessUser({id:res.data.userId});  // 获取用户信息\r\n          this.checkInfo.list = list;\r\n\r\n          // 初始化表单数据\r\n          this.form.organizationScore = res.data.organizationScore;\r\n          this.form.organizationScoreReason = res.data.organizationScoreReason;\r\n        }\r\n        this.open = true\r\n      })\r\n    },\r\n\r\n    // 审批提交\r\n    checkSubmit(){\r\n      if(this.verify()){\r\n        this.form.id = this.checkInfo.id;\r\n        this.form.status = this.checkInfo.status;\r\n        check(this.form).then(res => {\r\n          console.log(res)\r\n          if(res.code == 200){\r\n            this.$message({\r\n              type: 'success',\r\n              message: '提交成功!'\r\n            });\r\n            this.reset();\r\n            this.open = false;\r\n            this.getList();\r\n            this.getCheckedList();\r\n          }else{\r\n            this.$message({\r\n              type: 'warning',\r\n              message: '操作失败，无权限或当前审批状态不匹配'\r\n            });\r\n          }\r\n        })\r\n      }else{\r\n        this.$message({\r\n          type: 'warning',\r\n          message: '请填写评分'\r\n        });\r\n        return false;\r\n      }\r\n    },\r\n\r\n    // 退回点击事件\r\n    rejectClick(){\r\n      this.rejectOpen = true;\r\n    },\r\n\r\n    rejectCancel(){\r\n      this.checkInfo.rejectReason = null;\r\n      this.rejectOpen = false;\r\n    },\r\n\r\n    // 退回\r\n    rejectSubmit(){\r\n      if(!this.checkInfo.rejectReason){\r\n        this.$message({\r\n          type: 'warning',\r\n          message: '请填写退回原因'\r\n        });\r\n        return;\r\n      }\r\n      this.$confirm('确认后, 是否继续?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.reject();\r\n      }).catch(() => {\r\n          \r\n      });\r\n    },\r\n\r\n    reject(){\r\n      rejectInfo(this.checkInfo).then(res => {\r\n          this.reset();\r\n          this.checkInfo.rejectReason = null;\r\n          this.rejectOpen = false;\r\n          this.open = false;\r\n          this.getList();\r\n          this.getCheckedList();\r\n      })\r\n    },\r\n\r\n\r\n    // 数据验证\r\n    verify(){\r\n      if(!this.form.organizationScore) {\r\n        this.$message.warning('请输入运改组织部评分');\r\n        return false;\r\n      }\r\n\r\n      // 检查是否需要填写加减分理由\r\n      let previousScore = null;\r\n      let previousScoreName = '';\r\n\r\n      if (this.checkInfo.businessScore) {\r\n        previousScore = parseFloat(this.checkInfo.businessScore);\r\n        previousScoreName = '事业部评分';\r\n      } else if (this.checkInfo.deptScore) {\r\n        previousScore = parseFloat(this.checkInfo.deptScore);\r\n        previousScoreName = '部门领导评分';\r\n      } else if (this.checkInfo.selfScore) {\r\n        previousScore = parseFloat(this.checkInfo.selfScore);\r\n        previousScoreName = '自评分';\r\n      }\r\n\r\n      // 运改组织部评分与上一环节评分不一致时，加减分理由必填\r\n      if (previousScore !== null && parseFloat(this.form.organizationScore) !== previousScore && !this.form.organizationScoreReason) {\r\n        this.$message.warning(`运改组织部评分(${this.form.organizationScore}分)与${previousScoreName}(${previousScore}分)不一致，请填写加减分理由`);\r\n        return false;\r\n      }\r\n\r\n      return true;\r\n    },\r\n\r\n    handleListChange(type){\r\n      console.log(type)\r\n    },\r\n    // 处理列表\r\n    handleSpanList(data){\r\n      let spanList = [];\r\n      let flag = 0;\r\n      for(let i = 0; i < data.length; i++){\r\n        // 相同考核项合并\r\n        if(i == 0){\r\n          spanList.push({\r\n            rowspan: 1,\r\n            colspan: 1\r\n          })\r\n        }else{\r\n          if(data[i - 1].item == data[i].item){\r\n            spanList.push({\r\n              rowspan: 0,\r\n              colspan: 0\r\n            })\r\n            spanList[flag].rowspan += 1;\r\n          }else{\r\n            spanList.push({\r\n              rowspan: 1,\r\n              colspan: 1\r\n            })\r\n            flag = i;\r\n          }\r\n        }\r\n      }\r\n      this.spanList = spanList;\r\n    },\r\n\r\n    // 合并单元格方法\r\n    objectSpanMethod({ row, column, rowIndex, columnIndex }) {\r\n      // 第一列相同项合并\r\n      if (columnIndex === 0) {\r\n        return this.spanList[rowIndex];\r\n      }\r\n      // 类别无内容 合并\r\n      if(columnIndex === 1){\r\n        if(!row.category){\r\n          return {\r\n            rowspan: 0,\r\n            colspan: 0\r\n          }\r\n        }\r\n      }\r\n      if(columnIndex === 2){\r\n        if(!row.category){\r\n          return {\r\n            rowspan: 1,\r\n            colspan: 2\r\n          }\r\n        }\r\n      }\r\n    },\r\n    // 计算效益\r\n    calScore(){\r\n      if(this.benefit || this.benefit == 0){\r\n        let benefit = Number(this.benefit);\r\n\r\n        // 确定前一步评分：优先事业部评分，其次部门评分，最后自评分\r\n        let previousScore = 0;\r\n        let previousScoreName = '';\r\n        if (this.checkInfo.businessScore !== null && this.checkInfo.businessScore !== undefined && this.checkInfo.businessScore !== '') {\r\n          // 有事业部评分，以事业部评分为基础\r\n          previousScore = Number(this.checkInfo.businessScore);\r\n          previousScoreName = '事业部评分';\r\n        } else if (this.checkInfo.deptScore !== null && this.checkInfo.deptScore !== undefined && this.checkInfo.deptScore !== '') {\r\n          // 没有事业部评分，以部门评分为基础\r\n          previousScore = Number(this.checkInfo.deptScore);\r\n          previousScoreName = '部门评分';\r\n        } else {\r\n          // 都没有，以自评分为基础\r\n          previousScore = Number(this.checkInfo.selfScore);\r\n          previousScoreName = '自评分';\r\n        }\r\n\r\n        let categoryScore = 0;\r\n        if(this.userInfo.benefitLinkFlag == \"N\"){\r\n          this.checkInfo.list.forEach(row => {\r\n            console.log(row)\r\n            if(row.category == \"效益\"){\r\n              categoryScore += Number(row.dePoints);\r\n            }\r\n          })\r\n          this.form.organizationScore = parseFloat((previousScore + (benefit / 2) - (categoryScore / 2)).toFixed(1));\r\n          this.benefitDetail = `计算：${previousScoreName}(${previousScore}) ${benefit > 0 ? \"+\" : \"-\"} ${Math.abs(benefit / 2)} ${categoryScore > 0 ? \"-\" : \"+\"} ${Math.abs(categoryScore / 2)} = ${this.form.organizationScore}`\r\n        }else{\r\n          this.form.organizationScore = parseFloat((previousScore + benefit).toFixed(1));\r\n          this.benefitDetail = `计算：${previousScoreName}(${previousScore}) ${benefit > 0 ? \"+\" : \"-\"} ${Math.abs(benefit)} = ${this.form.organizationScore}`\r\n        }\r\n      }else{\r\n        this.$message({\r\n          type: 'warning',\r\n          message: '请填写公司效益加减分'\r\n        });\r\n        return;\r\n      }\r\n    },\r\n    // 批量计算\r\n    batchCalScore(){\r\n      if(this.benefit || this.benefit == 0){\r\n        let benefit = Number(this.benefit);\r\n        this.selectedRows.forEach(row => {\r\n          // 确定前一步评分：优先事业部评分，其次部门评分，最后自评分\r\n          let previousScore = 0;\r\n          if (row.businessScore !== null && row.businessScore !== undefined && row.businessScore !== '') {\r\n            // 有事业部评分，以事业部评分为基础\r\n            previousScore = Number(row.businessScore);\r\n          } else if (row.deptScore !== null && row.deptScore !== undefined && row.deptScore !== '') {\r\n            // 没有事业部评分，以部门评分为基础\r\n            previousScore = Number(row.deptScore);\r\n          } else {\r\n            // 都没有，以自评分为基础\r\n            previousScore = Number(row.selfScore);\r\n          }\r\n          \r\n          if(row.benefitLinkFlag == \"N\"){\r\n            row.quickScore = parseFloat((previousScore + (benefit / 2) - (Number(row.benefitScore) / 2)).toFixed(1));\r\n          }else{\r\n            row.quickScore = parseFloat((previousScore + benefit).toFixed(1));\r\n          }\r\n        })\r\n      }else{\r\n        this.$message({\r\n          type: 'warning',\r\n          message: '请填写公司效益加减分'\r\n        });\r\n        return;\r\n      }\r\n    },\r\n\r\n    /** 选择条数改变 */\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.id)\r\n      this.selectedRows = selection\r\n      this.single = selection.length !== 1\r\n      this.multiple = !selection.length\r\n    },\r\n\r\n    /** 批量快速评分按钮操作 */\r\n    handleBatchQuickScore() {\r\n      if (this.ids.length === 0) {\r\n        this.$modal.msgError(\"请选择需要评分的数据\");\r\n        return;\r\n      }\r\n      batchWithBenefitByIds(this.ids).then(res => {\r\n        if(res.code == 200){\r\n          this.selectedRows = res.data;\r\n          this.batchQuickScoreOpen = true;\r\n        }\r\n      })\r\n      // // 检查是否有未填写评分的记录\r\n      // const emptyScores = this.selectedRows.filter(row => !row.quickScore);\r\n      // if (emptyScores.length > 0) {\r\n      //   this.$modal.msgError(`有${emptyScores.length}条记录未填写快速评分，请先填写评分`);\r\n      //   return;\r\n      // }\r\n    },\r\n\r\n    /** 取消批量快速评分操作 */\r\n    cancelBatchQuickScore() {\r\n      this.batchQuickScoreOpen = false;\r\n    },\r\n\r\n    /** 提交批量快速评分 */\r\n    submitBatchQuickScore() {\r\n      // 验证评分一致性和理由必填\r\n      const validationResult = this.validateBatchQuickScore();\r\n      if (!validationResult.isValid) {\r\n        this.$modal.msgError(validationResult.message);\r\n        return;\r\n      }\r\n\r\n      // 准备提交数据\r\n      const submitData = this.selectedRows.map(row => ({\r\n        id: row.id,\r\n        quickScore: row.quickScore,\r\n        quickReason: row.quickReason\r\n      }));\r\n\r\n      this.$modal.confirm('是否确认提交选中人员的快速评分？').then(() => {\r\n        return batchQuickScore(submitData);\r\n      }).then(() => {\r\n        this.$modal.msgSuccess(\"批量评分成功\");\r\n        this.batchQuickScoreOpen = false;\r\n        this.getList();\r\n        this.getCheckedList();\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 验证批量快速评分 */\r\n    validateBatchQuickScore() {\r\n      for (let i = 0; i < this.selectedRows.length; i++) {\r\n        const row = this.selectedRows[i];\r\n        \r\n        // 检查是否填写了评分\r\n        if (!row.quickScore && row.quickScore !== 0) {\r\n          return {\r\n            isValid: false,\r\n            message: `第${i + 1}行 ${row.name} 未填写评分，请先填写评分`\r\n          };\r\n        }\r\n\r\n        // 运改组织部评分时的验证\r\n        if (row.status == '3') {\r\n          let previousScore = null;\r\n          let previousScoreName = '';\r\n          \r\n          // 判断上一环节评分\r\n          if (row.businessScore !== null && row.businessScore !== undefined && row.businessScore !== '') {\r\n            // 有事业部评分，以事业部评分为准\r\n            previousScore = parseFloat(row.businessScore);\r\n            previousScoreName = '事业部领导评分';\r\n          } else if (row.deptScore !== null && row.deptScore !== undefined && row.deptScore !== '') {\r\n            // 没有事业部评分，以部门评分为准\r\n            previousScore = parseFloat(row.deptScore);\r\n            previousScoreName = '部门领导评分';\r\n          } else {\r\n            // 都没有，以自评分为准\r\n            previousScore = parseFloat(row.selfScore);\r\n            previousScoreName = '自评分';\r\n          }\r\n          \r\n          // 运改组织部评分与上一环节评分不一致时，加减分理由必填\r\n          if (parseFloat(row.quickScore) !== previousScore && !row.quickReason) {\r\n            return {\r\n              isValid: false,\r\n              message: `第${i + 1}行 ${row.name} 运改组织部评分(${row.quickScore}分)与${previousScoreName}(${previousScore}分)不一致，请填写加减分理由`\r\n            };\r\n          }\r\n        }\r\n      }\r\n\r\n      return { isValid: true };\r\n    },\r\n\r\n    /** 查看评分记录详情 */\r\n    handleCheckedDetail(row) {\r\n      getInfo({id: row.infoId}).then(res => {\r\n        console.log(res);\r\n        this.reset();\r\n        if(res.code == 200){\r\n          this.checkInfo = res.data;\r\n          let list = JSON.parse(res.data.content);\r\n          this.handleSpanList(list);\r\n          let param ={\r\n            deptId:res.data.deptId,\r\n            assessDate:res.data.assessDate\r\n          }\r\n          this.getBeAssessedList(param);  // 获取横向评分被考核数据\r\n          this.getSelfAssessUser({id:res.data.userId});  // 获取用户信息\r\n          this.checkInfo.list = list;\r\n          this.open = true;\r\n        }\r\n      }).catch(error => {\r\n        this.$message.error('获取详情失败');\r\n      });\r\n    },\r\n\r\n    // 获取历史备注（各步骤加减分原因拼接）\r\n    getHistoryRemarks(row) {\r\n      const remarks = [];\r\n\r\n      // 部门领导加减分理由\r\n      if (row.deptScoreReason && row.deptScoreReason.trim()) {\r\n        remarks.push(`部门领导：${row.deptScoreReason.trim()}`);\r\n      }\r\n\r\n      // 事业部加减分理由\r\n      if (row.businessScoreReason && row.businessScoreReason.trim()) {\r\n        remarks.push(`事业部：${row.businessScoreReason.trim()}`);\r\n      }\r\n\r\n      // 运改部/组织部加减分理由\r\n      if (row.organizationScoreReason && row.organizationScoreReason.trim()) {\r\n        remarks.push(`运改部/组织部：${row.organizationScoreReason.trim()}`);\r\n      }\r\n\r\n      return remarks.join('\\n');\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.assessment-detail-dialog .detail-container {\r\n  padding: 20px;\r\n  background-color: #f5f7fa;\r\n  min-height: 100vh;\r\n}\r\n\r\n.assessment-detail-dialog .detail-header h2 {\r\n  background: linear-gradient(135deg, #409EFF, #67C23A);\r\n  background-clip: text;\r\n  -webkit-background-clip: text;\r\n  color: transparent;\r\n  font-weight: bold;\r\n}\r\n\r\n.assessment-detail-dialog .assessment-table-card {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.assessment-detail-dialog .signature-card {\r\n  background: #ffffff;\r\n}\r\n\r\n.signature-content {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.score-text {\r\n  font-weight: 500;\r\n  color: #303133;\r\n}\r\n\r\n.separator {\r\n  color: #909399;\r\n  margin: 0 4px;\r\n}\r\n\r\n.signature-name {\r\n  color: #303133;\r\n}\r\n\r\n.reason-text {\r\n  width: 100%;\r\n  margin-top: 8px;\r\n  padding: 8px 12px;\r\n  background-color: #f8f9fa;\r\n  border-left: 3px solid #409EFF;\r\n  border-radius: 4px;\r\n}\r\n\r\n.reason-label {\r\n  font-weight: 500;\r\n  color: #606266;\r\n  margin-right: 8px;\r\n}\r\n\r\n.reason-content {\r\n  color: #303133;\r\n  line-height: 1.6;\r\n}\r\n\r\n.benefit-detail {\r\n  color: #909399;\r\n  font-size: 13px;\r\n  padding: 8px 12px;\r\n  background-color: #f4f4f5;\r\n  border-radius: 4px;\r\n  border-left: 3px solid #e6a23c;\r\n}\r\n\r\n.dialog-footer {\r\n  border-top: 1px solid #e4e7ed;\r\n  background-color: #ffffff;\r\n  border-radius: 0 0 6px 6px;\r\n}\r\n\r\n.assessment-detail-dialog .el-card {\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.assessment-detail-dialog .el-descriptions {\r\n  background-color: #ffffff;\r\n}\r\n\r\n.assessment-detail-dialog .el-table {\r\n  border-radius: 6px;\r\n  overflow: hidden;\r\n}\r\n\r\n.text-red {\r\n  color: #F56C6C;\r\n}\r\n\r\n.history-remarks-cell {\r\n  max-width: 180px;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n  font-size: 12px;\r\n  color: #606266;\r\n  line-height: 1.4;\r\n  cursor: pointer;\r\n}\r\n\r\n.history-remarks-cell:hover {\r\n  color: #409EFF;\r\n}\r\n\r\n.batch-history-remarks-cell {\r\n  max-width: 180px;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n  font-size: 12px;\r\n  color: #606266;\r\n  line-height: 1.4;\r\n  cursor: pointer;\r\n}\r\n\r\n.batch-history-remarks-cell:hover {\r\n  color: #409EFF;\r\n}\r\n</style>\r\n"]}]}