{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\qualityCost\\dashboard\\index.vue?vue&type=style&index=0&id=06fad4ca&scoped=true&lang=css", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\qualityCost\\dashboard\\index.vue", "mtime": 1755748204382}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\@vue\\cli-service\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi5xdWFsaXR5LWNvc3QtZGFzaGJvYXJkIHsNCiAgZm9udC1mYW1pbHk6IC1hcHBsZS1zeXN0ZW0sIEJsaW5rTWFjU3lzdGVtRm9udCwgJ1NlZ29lIFVJJywgUm9ib3RvLCAnSGVsdmV0aWNhIE5ldWUnLCBBcmlhbCwgJ05vdG8gU2FucycsIHNhbnMtc2VyaWYsICdBcHBsZSBDb2xvciBFbW9qaScsICdTZWdvZSBVSSBFbW9qaScsICdTZWdvZSBVSSBTeW1ib2wnLCAnTm90byBDb2xvciBFbW9qaSc7DQogIGJhY2tncm91bmQtY29sb3I6ICMxMTE4Mjc7DQogIC8qIOa3seiJsuiDjOaZryAqLw0KICBjb2xvcjogI2QxZDVkYjsNCiAgLyog5rWF6Imy5paH5a2XICovDQogIG1hcmdpbjogMDsNCiAgcGFkZGluZzogMjRweDsNCiAgbWluLWhlaWdodDogMTAwdmg7DQp9DQoNCi5oZWFkZXIgew0KICBtYXJnaW4tYm90dG9tOiAyNHB4Ow0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogIHBvc2l0aW9uOiByZWxhdGl2ZTsNCn0NCg0KLmhlYWRlci13cmFwcGVyIHsNCiAgZGlzcGxheTogaW5saW5lLWJsb2NrOw0KICBwb3NpdGlvbjogcmVsYXRpdmU7DQp9DQoNCi5oZWFkZXIgaDEgew0KICBmb250LXNpemU6IDI4cHg7DQogIGNvbG9yOiAjZjlmYWZiOw0KICAvKiDnmb3oibLmoIfpopggKi8NCiAgZm9udC13ZWlnaHQ6IDYwMDsNCiAgbWFyZ2luOiAwOw0KICBtYXJnaW4tYm90dG9tOiA4cHg7DQp9DQoNCi5oZWFkZXIgcCB7DQogIGZvbnQtc2l6ZTogMTZweDsNCiAgY29sb3I6ICM5Y2EzYWY7DQogIC8qIOS4reeBsOiJsuaWh+WtlyAqLw0KICBtYXJnaW46IDhweCAwIDAgMDsNCn0NCg0KLmhlYWRlci1maWx0ZXJzIHsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgZ2FwOiAxMnB4Ow0KICBmbGV4LXdyYXA6IG5vd3JhcDsNCiAganVzdGlmeS1jb250ZW50OiBmbGV4LXN0YXJ0Ow0KICBtYXJnaW4tdG9wOiAxMnB4Ow0KICBtYXJnaW4tbGVmdDogOTUwcHg7DQogIC8qIOWQkeW3puWvuem9kCAqLw0KfQ0KDQouZmlsdGVyLWl0ZW0gew0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBnYXA6IDhweDsNCn0NCg0KLmZpbHRlci1pdGVtIC5sYWJlbCB7DQogIGNvbG9yOiAjZDFkNWRiOw0KICAvKiDmtYXoibLmoIfnrb7mloflrZcgKi8NCiAgZm9udC1zaXplOiAxNHB4Ow0KICB3aGl0ZS1zcGFjZTogbm93cmFwOw0KfQ0KDQovKiDlj7PkuIrop5LnrZvpgInljLrln5/nmoTmoLflvI8gKi8NCi5oZWFkZXItZmlsdGVycyAuZWwtaW5wdXRfX2lubmVyLA0KLmhlYWRlci1maWx0ZXJzIC5lbC1zZWxlY3QgLmVsLWlucHV0X19pbm5lciB7DQogIGJhY2tncm91bmQtY29sb3I6ICMxMTE4Mjc7IC8qIOS4jumhtemdouiDjOaZr+S4gOiHtOeahOa3seiJsiAqLw0KICBib3JkZXItY29sb3I6ICMzNzQxNTE7DQogIGNvbG9yOiAjZmZmZmZmOyAvKiDnmb3oibLlrZfkvZMgKi8NCn0NCg0KLmhlYWRlci1maWx0ZXJzIC5lbC1pbnB1dF9faW5uZXI6Zm9jdXMsDQouaGVhZGVyLWZpbHRlcnMgLmVsLXNlbGVjdCAuZWwtaW5wdXRfX2lubmVyOmZvY3VzIHsNCiAgYm9yZGVyLWNvbG9yOiAjOTNjNWZkOw0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjMTExODI3OyAvKiDogZrnhKbml7bkv53mjIHog4zmma/oibIgKi8NCn0NCg0KLmhlYWRlci1maWx0ZXJzIC5lbC1zZWxlY3QtZHJvcGRvd24gew0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjMTExODI3OyAvKiDkuIvmi4noj5zljZXog4zmma/kuI7pobXpnaLkuIDoh7QgKi8NCiAgYm9yZGVyLWNvbG9yOiAjMzc0MTUxOw0KfQ0KDQouaGVhZGVyLWZpbHRlcnMgLmVsLXNlbGVjdC1kcm9wZG93biAuZWwtc2VsZWN0LWRyb3Bkb3duX19pdGVtIHsNCiAgY29sb3I6ICNmZmZmZmY7IC8qIOS4i+aLiemAiemhueeZveiJsuWtl+S9kyAqLw0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjMTExODI3Ow0KfQ0KDQouaGVhZGVyLWZpbHRlcnMgLmVsLXNlbGVjdC1kcm9wZG93biAuZWwtc2VsZWN0LWRyb3Bkb3duX19pdGVtOmhvdmVyIHsNCiAgYmFja2dyb3VuZC1jb2xvcjogIzFmMjkzNzsgLyog5oKs5rWu5pe256iN5b6u5Lqu5LiA54K5ICovDQogIGNvbG9yOiAjZmZmZmZmOw0KfQ0KDQouaGVhZGVyLWZpbHRlcnMgLmVsLXNlbGVjdC1kcm9wZG93biAuZWwtc2VsZWN0LWRyb3Bkb3duX19pdGVtLnNlbGVjdGVkIHsNCiAgYmFja2dyb3VuZC1jb2xvcjogIzM3NDE1MTsgLyog6YCJ5Lit6aG56IOM5pmvICovDQogIGNvbG9yOiAjZmZmZmZmOw0KfQ0KDQovKiDkuIvmi4nmoYbnrq3lpLTpopzoibIgKi8NCi5oZWFkZXItZmlsdGVycyAuZWwtc2VsZWN0IC5lbC1pbnB1dF9fc3VmZml4IHsNCiAgY29sb3I6ICNmZmZmZmY7DQp9DQoNCi5oZWFkZXItZmlsdGVycyAuZWwtc2VsZWN0IC5lbC1zZWxlY3RfX2NhcmV0IHsNCiAgY29sb3I6ICNmZmZmZmY7DQp9DQoNCi8qIOWNoOS9jeespuaWh+Wtl+minOiJsiAqLw0KLmhlYWRlci1maWx0ZXJzIC5lbC1pbnB1dF9faW5uZXI6OnBsYWNlaG9sZGVyIHsNCiAgY29sb3I6ICM5Y2EzYWY7DQp9DQoNCi8qIOa4hemZpOaMiemSruminOiJsiAqLw0KLmhlYWRlci1maWx0ZXJzIC5lbC1zZWxlY3QgLmVsLXNlbGVjdF9fY2xlYXIgew0KICBjb2xvcjogIzljYTNhZjsNCn0NCg0KLmhlYWRlci1maWx0ZXJzIC5lbC1zZWxlY3QgLmVsLXNlbGVjdF9fY2xlYXI6aG92ZXIgew0KICBjb2xvcjogI2ZmZmZmZjsNCn0NCg0KLyog5ZON5bqU5byP6K6+6K6hICovDQpAbWVkaWEgKG1heC13aWR0aDogMTAyNHB4KSB7DQogIC5oZWFkZXItZmlsdGVycyB7DQogICAgZmxleC13cmFwOiB3cmFwOw0KICAgIGdhcDogOHB4Ow0KICAgIG1hcmdpbi1sZWZ0OiAwOw0KICAgIGp1c3RpZnktY29udGVudDogZmxleC1lbmQ7DQogIH0NCn0NCg0KQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7DQogIC5oZWFkZXIgew0KICAgIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgfQ0KDQogIC5oZWFkZXItZmlsdGVycyB7DQogICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCiAgICBnYXA6IDEycHg7DQogICAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgICBtYXJnaW4tbGVmdDogMDsNCiAgICAvKiDlnKjlsI/lsY/luZXkuIrlj5bmtojlt6bovrnot50gKi8NCiAgfQ0KDQogIC5maWx0ZXItaXRlbSB7DQogICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7DQogIH0NCn0NCg0KLmRhc2hib2FyZC1ncmlkIHsNCiAgZGlzcGxheTogZ3JpZDsNCiAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiByZXBlYXQoNCwgMWZyKTsgLyog5pS55Li65Zub5YiX5biD5bGA77yM5pSv5oyBMS805ZKMMy805YiG6YWNICovDQogIGdhcDogMjRweDsNCn0NCg0KLyog5ZON5bqU5byP6K6+6K6h77ya5Zyo5bCP5bGP5bmV5LiK5pS55Li65Y2V5YiXICovDQpAbWVkaWEgKG1heC13aWR0aDogMTIwMHB4KSB7DQogIC5kYXNoYm9hcmQtZ3JpZCB7DQogICAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiAxZnI7IC8qIOWwj+Wxj+W5leaXtuaUueS4uuWNleWIlyAqLw0KICB9DQoNCiAgLyog5bCP5bGP5bmV5pe26YeN572u5omA5pyJZ3JpZC1jb2x1bW7moLflvI8gKi8NCiAgLmRhc2hib2FyZC1ncmlkIC5jaGFydC1jb250YWluZXIgew0KICAgIGdyaWQtY29sdW1uOiAxICFpbXBvcnRhbnQ7DQogIH0NCn0NCg0KLmNoYXJ0LWNvbnRhaW5lciwNCi5rcGktY2FyZCB7DQogIGJhY2tncm91bmQtY29sb3I6ICMxZjI5Mzc7DQogIC8qIOa3seiJsuWNoeeJh+iDjOaZryAqLw0KICBib3JkZXItcmFkaXVzOiA4cHg7DQogIGJvcmRlcjogMXB4IHNvbGlkICMzNzQxNTE7DQogIC8qIOi+ueahhiAqLw0KICBib3gtc2hhZG93OiBub25lOw0KICBwYWRkaW5nOiAyNHB4Ow0KICBkaXNwbGF5OiBmbGV4Ow0KICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KfQ0KDQouY2hhcnQtY29udGFpbmVyIHsNCiAgaGVpZ2h0OiA0MDBweDsNCn0NCg0KLyog5aSn5Zu+6KGo5qC35byPIC0g55So5LqO5Lik5Lik5o6S5YiX55qE5Zu+6KGoICovDQouY2hhcnQtY29udGFpbmVyLmxhcmdlLWNoYXJ0IHsNCiAgaGVpZ2h0OiA1MDBweDsgLyog5aKe5Yqg6auY5bqmICovDQogIG1pbi1oZWlnaHQ6IDUwMHB4Ow0KfQ0KDQouY2hhcnQtY29udGFpbmVyIGgzIHsNCiAgbWFyZ2luLXRvcDogMDsNCiAgbWFyZ2luLWJvdHRvbTogMTZweDsNCiAgZm9udC1zaXplOiAxOHB4Ow0KICBmb250LXdlaWdodDogNjAwOw0KICBjb2xvcjogI2Y5ZmFmYjsNCiAgLyog55m96Imy5Y2h54mH5qCH6aKYICovDQp9DQoNCi5jaGFydCB7DQogIHdpZHRoOiAxMDAlOw0KICBmbGV4LWdyb3c6IDE7DQp9DQoNCi5rcGktZ3JpZCB7DQogIGdyaWQtY29sdW1uOiAxIC8gLTE7DQogIGRpc3BsYXk6IGdyaWQ7DQogIGdyaWQtdGVtcGxhdGUtY29sdW1uczogcmVwZWF0KGF1dG8tZml0LCBtaW5tYXgoMjUwcHgsIDFmcikpOw0KICBnYXA6IDI0cHg7DQp9DQoNCi5rcGktY2FyZCB7DQogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCn0NCg0KLmtwaS1jYXJkIC50aXRsZSB7DQogIGZvbnQtc2l6ZTogMTRweDsNCiAgY29sb3I6ICM5Y2EzYWY7DQogIG1hcmdpbi1ib3R0b206IDhweDsNCn0NCg0KLmtwaS1jYXJkIC52YWx1ZSB7DQogIGZvbnQtc2l6ZTogMzJweDsNCiAgZm9udC13ZWlnaHQ6IDcwMDsNCiAgY29sb3I6ICNmOWZhZmI7DQp9DQoNCi5rcGktY2FyZCAuY29tcGFyaXNvbiB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGZvbnQtc2l6ZTogMTRweDsNCiAgbWFyZ2luLXRvcDogOHB4Ow0KfQ0KDQoua3BpLWNhcmQgLmNvbXBhcmlzb24gLmFycm93IHsNCiAgd2lkdGg6IDIwcHg7DQogIGhlaWdodDogMjBweDsNCiAgbWFyZ2luLXJpZ2h0OiA0cHg7DQogIHN0cm9rZS13aWR0aDogMi41cHg7DQp9DQoNCi5rcGktY2FyZCAuY29tcGFyaXNvbiAucG9zaXRpdmUgew0KICBjb2xvcjogIzM0ZDM5OTsNCiAgLyog5Lqu57u/6ImyICovDQp9DQoNCi5rcGktY2FyZCAuY29tcGFyaXNvbiAubmVnYXRpdmUgew0KICBjb2xvcjogI2Y4NzE3MTsNCiAgLyog5Lqu57qi6ImyICovDQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAk5DA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/qualityCost/dashboard", "sourcesContent": ["<template>\r\n  <div class=\"quality-cost-dashboard\">\r\n    <header class=\"header\">\r\n      <div class=\"header-wrapper\">\r\n        <h1>兴澄特钢质量成本看板</h1>\r\n        <!-- 标题右下角筛选区域 -->\r\n        <div class=\"header-filters\">\r\n          <div class=\"filter-item\">\r\n            <span class=\"label\">成本中心：</span>\r\n            <el-select v-model=\"costCenter\" placeholder=\"请选择成本中心\" style=\"width: 160px;\" :loading=\"costCenterLoading\"\r\n              size=\"small\">\r\n              <el-option v-for=\"item in costCenterOptions\" :key=\"item.key\" :label=\"item.label\" :value=\"item.key\">\r\n              </el-option>\r\n            </el-select>\r\n          </div>\r\n          <div class=\"filter-item\">\r\n            <span class=\"label\">会计期：</span>\r\n            <el-date-picker v-model=\"accountingPeriod\" type=\"month\" placeholder=\"请选择年月\" format=\"yyyy-MM\"\r\n              value-format=\"yyyy-MM\" style=\"width: 130px;\" size=\"small\">\r\n            </el-date-picker>\r\n          </div>\r\n          <div class=\"filter-item\">\r\n            <span class=\"label\">质量成本类型：</span>\r\n            <el-select v-model=\"containType\" placeholder=\"请选择质量成本类型\" style=\"width: 130px;\" size=\"small\">\r\n              <el-option label=\"含不列入项\" :value=\"2\"></el-option>\r\n              <el-option label=\"不含列入项\" :value=\"1\"></el-option>\r\n            </el-select>\r\n          </div>\r\n        </div> \r\n      </div>\r\n      <!-- <p>数据更新时间: {{ updateTime }}</p> -->\r\n    </header>\r\n\r\n    <div class=\"dashboard-grid\">\r\n      <!-- 第四类：核心绩效指标（KPI）看板 -->\r\n      <div class=\"kpi-grid\">\r\n        <div class=\"kpi-card\">\r\n          <div class=\"title\">{{ costCenter === 'JYXCTZG' ? '销量' : '产量' }}</div>\r\n          <div class=\"value\">{{ formatTonnage(containType === 2 ? qualityCostData.allcTon : qualityCostData.costTon) }}</div>\r\n          <div class=\"comparison\">\r\n            <svg :class=\"['arrow', getPercentageClass(qualityCostDetail.costTonUpPercent)]\" fill=\"none\"\r\n              stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\r\n              <path v-if=\"isNegativePercentage(qualityCostDetail.costTonUpPercent)\" stroke-linecap=\"round\"\r\n                stroke-linejoin=\"round\" d=\"M17 13l-5 5m0 0l-5-5m5 5V6\"></path>\r\n              <path v-else stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M7 11l5-5m0 0l5 5m-5-5v12\"></path>\r\n            </svg>\r\n            <span :class=\"getPercentageClass(qualityCostDetail.costTonUpPercent)\">{{ qualityCostDetail.costTonUpPercent\r\n            }}\r\n              vs 上期</span>\r\n          </div>\r\n        </div>\r\n        <div class=\"kpi-card\">\r\n          <div class=\"title\">总金额</div>\r\n          <div class=\"value\">{{ formatAmount(containType === 2 ? qualityCostData.allcEx : qualityCostData.costEx) }}</div>\r\n          <div class=\"comparison\">\r\n            <svg :class=\"['arrow', getPercentageClass(qualityCostDetail.costExPercent)]\" fill=\"none\"\r\n              stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\r\n              <path v-if=\"isNegativePercentage(qualityCostDetail.costExPercent)\" stroke-linecap=\"round\"\r\n                stroke-linejoin=\"round\" d=\"M17 13l-5 5m0 0l-5-5m5 5V6\"></path>\r\n              <path v-else stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M7 11l5-5m0 0l5 5m-5-5v12\"></path>\r\n            </svg>\r\n            <span :class=\"getPercentageClass(qualityCostDetail.costExPercent)\">{{ qualityCostDetail.costExPercent }} vs\r\n              上期</span>\r\n          </div>\r\n        </div>\r\n        <div class=\"kpi-card\">\r\n          <div class=\"title\">吨钢成本</div>\r\n          <div class=\"value\">{{ formatUnitCost(containType === 2 ? qualityCostData.allcPerEx : qualityCostData.costPerEx) }}</div>\r\n          <div class=\"comparison\">\r\n            <svg :class=\"['arrow', getPercentageClass(qualityCostDetail.costPerExPercent)]\" fill=\"none\"\r\n              stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\r\n              <path v-if=\"isNegativePercentage(qualityCostDetail.costPerExPercent)\" stroke-linecap=\"round\"\r\n                stroke-linejoin=\"round\" d=\"M17 13l-5 5m0 0l-5-5m5 5V6\"></path>\r\n              <path v-else stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M7 11l5-5m0 0l5 5m-5-5v12\"></path>\r\n            </svg>\r\n            <span :class=\"getPercentageClass(qualityCostDetail.costPerExPercent)\">{{ qualityCostDetail.costPerExPercent\r\n            }}\r\n              vs 上期</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 第2行：质量成本四大类别占比（占1/2宽度）+ 四大质量成本趋势（占1/2宽度） -->\r\n      <div class=\"chart-container\" style=\"grid-column: span 2;\">\r\n        <h3>1. 质量成本四大类别占比</h3>\r\n        <div ref=\"pieChart\" class=\"chart\"></div>\r\n      </div>\r\n      <div class=\"chart-container\" style=\"grid-column: span 2;\">\r\n        <h3>2. 四大质量成本趋势</h3>\r\n        <div ref=\"multiLineChart\" class=\"chart\"></div>\r\n      </div>\r\n\r\n      <!-- 第3行：外部损失成本构成图表 + 内部损失成本构成图表 - 每个占据行宽的50% -->\r\n      <div class=\"chart-container large-chart\" style=\"grid-column: span 2;\">\r\n        <h3>3. 外部损失成本构成</h3>\r\n        <div ref=\"externalCostDetailChart\" class=\"chart\"></div>\r\n      </div>\r\n      <div class=\"chart-container large-chart\" style=\"grid-column: span 2;\">\r\n        <h3>4. 内部损失成本构成</h3>\r\n        <div ref=\"internalCostDetailChart\" class=\"chart\"></div>\r\n      </div>\r\n\r\n      <!-- 第3行：产品挽救处理成本分析图表 - 占据整行宽度 -->\r\n      <div class=\"chart-container\" style=\"grid-column: 1 / -1;\">\r\n        <h3>5. 产品挽救处理成本分析</h3>\r\n        <div ref=\"waterfallChart\" class=\"chart\"></div>\r\n      </div>\r\n\r\n      <!-- 第4行：产品报废损失明细图表 - 占据整行宽度 -->\r\n      <div class=\"chart-container\" style=\"grid-column: 1 / -1;\">\r\n        <h3>6. 产品报废损失明细</h3>\r\n        <div ref=\"scrapLossChart\" class=\"chart\"></div>\r\n      </div>\r\n\r\n      <!-- 第5行：产品质量异议损失明细图表 - 占据整行宽度 -->\r\n      <div class=\"chart-container\" style=\"grid-column: 1 / -1;\">\r\n        <h3>7. 产品质量异议损失明细</h3>\r\n        <div ref=\"qualityObjectionChart\" class=\"chart\"></div>\r\n      </div>\r\n\r\n      <!-- 第7行：控制成本 vs 失败成本对比（占满整行） -->\r\n      <div class=\"chart-container\" style=\"grid-column: 1 / -1;\">\r\n        <h3>8. \"控制成本\" vs \"失败成本\" 对比</h3>\r\n        <div ref=\"comboChart\" class=\"chart\"></div>\r\n      </div>\r\n\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from 'echarts'\r\nimport { costCenterlist } from \"@/api/qualityCost/qualityCostDetail\";\r\nimport { getPieChartData, getMultiLineChartData, getQualityCostDetail, getExternalCostDetail, getInternalCostDetail, getComboChartDetail,getWaterfallChartDetail,getScrapLossChartDetailsDetail,getQualityObjectionLossDetail } from \"@/api/qualityCost/dashboard\";\r\n\r\nexport default {\r\n  name: 'QualityCostDashboard',\r\n  data() {\r\n    // 获取默认会计期（上个月）\r\n    const getDefaultYearMonth = () => {\r\n      const now = new Date();\r\n      const year = now.getFullYear();\r\n      const month = now.getMonth() + 1; // 1-12\r\n      const day = now.getDate();\r\n      const hour = now.getHours();\r\n\r\n      // 如果今天是本月25号8点前（含25号7:59），则用上个月\r\n      if (day < 28 || (day === 28 && hour < 1)) {\r\n        // 处理1月时的跨年\r\n        const prevMonth = month === 1 ? 12 : month - 1;\r\n        const prevYear = month === 1 ? year - 1 : year;\r\n        return `${prevYear}-${String(prevMonth).padStart(2, '0')}`;\r\n      } else {\r\n        return `${year}-${String(month).padStart(2, '0')}`;\r\n      }\r\n    };\r\n\r\n    return {\r\n      updateTime: '2023-10-27 10:00',\r\n      charts: {},\r\n      // 成本中心和会计期\r\n      costCenter: '',\r\n      accountingPeriod: getDefaultYearMonth(),\r\n      // 质量成本类型，默认值为1（不含列入项）\r\n      containType: 1,\r\n      // 成本中心选项\r\n      costCenterOptions: [],\r\n      costCenterLoading: false,\r\n      qualityCostDetail: {},\r\n      qualityCostData: {}\r\n    }\r\n  },\r\n  watch: {\r\n    // 监听成本中心变化\r\n    costCenter: {\r\n      handler() {\r\n        console.log('成本中心变化:', this.costCenter);\r\n        this.refreshChartData();\r\n      }\r\n    },\r\n    // 监听会计期变化\r\n    accountingPeriod: {\r\n      handler() {\r\n        console.log('会计期变化:', this.accountingPeriod);\r\n        this.refreshChartData();\r\n      }\r\n    },\r\n    // 监听质量成本类型变化\r\n    containType: {\r\n      handler() {\r\n        console.log('质量成本类型变化:', this.containType);\r\n        this.refreshChartData();\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    this.getCostCenterList();\r\n    //质量成本四大类别占比\r\n\r\n    this.initCharts();\r\n    this.resizeObserver = new ResizeObserver(() => {\r\n      this.resizeCharts()\r\n    })\r\n    this.resizeObserver.observe(this.$el)\r\n    window.addEventListener('resize', this.resizeCharts)\r\n  },\r\n  beforeDestroy() {\r\n    // 销毁所有图表实例\r\n    Object.values(this.charts).forEach(chart => {\r\n      if (chart) {\r\n        chart.dispose()\r\n      }\r\n    })\r\n    if (this.resizeObserver) {\r\n      this.resizeObserver.disconnect()\r\n    }\r\n    window.removeEventListener('resize', this.resizeCharts)\r\n  },\r\n  methods: {\r\n    // 判断百分比是否为负数\r\n    isNegativePercentage(percentage) {\r\n      if (!percentage) return false;\r\n      return percentage.toString().startsWith('-');\r\n    },\r\n\r\n    // 根据百分比正负值返回对应的CSS类\r\n    getPercentageClass(percentage) {\r\n      if (!percentage) return 'neutral';\r\n      return this.isNegativePercentage(percentage) ? 'negative' : 'positive';\r\n    },\r\n\r\n    // 格式化数字，最多保留两位小数\r\n    formatNumber(num) {\r\n      if (num === null || num === undefined || num === '') {\r\n        return '0';\r\n      }\r\n      const number = Number(num);\r\n      if (isNaN(number)) {\r\n        return '0';\r\n      }\r\n      // 使用toFixed(2)保留两位小数，然后用parseFloat去掉末尾的0\r\n      return parseFloat(number.toFixed(2)).toString();\r\n    },\r\n\r\n    // 添加千分位分隔符\r\n    addThousandSeparator(num) {\r\n      return num.toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\r\n    },\r\n\r\n    // 格式化产量/销量为万吨单位\r\n    formatTonnage(num) {\r\n      if (num === null || num === undefined || num === '') {\r\n        return '0万吨';\r\n      }\r\n      const number = Number(num);\r\n      if (isNaN(number)) {\r\n        return '0万吨';\r\n      }\r\n      // 转换为万吨并保留两位小数，添加千分位分隔符\r\n      const result = (number / 10000).toFixed(2);\r\n      return `${this.addThousandSeparator(result)}万吨`;\r\n    },\r\n\r\n    // 格式化总金额为万元单位\r\n    formatAmount(num) {\r\n      if (num === null || num === undefined || num === '') {\r\n        return '0万元';\r\n      }\r\n      const number = Number(num);\r\n      if (isNaN(number)) {\r\n        return '0万元';\r\n      }\r\n      // 转换为万元并保留两位小数，添加千分位分隔符\r\n      const result = (number / 10000).toFixed(2);\r\n      return `${this.addThousandSeparator(result)}万元`;\r\n    },\r\n\r\n    // 格式化吨钢成本为元/吨单位\r\n    formatUnitCost(num) {\r\n      if (num === null || num === undefined || num === '') {\r\n        return '0元/吨';\r\n      }\r\n      const number = Number(num);\r\n      if (isNaN(number)) {\r\n        return '0元/吨';\r\n      }\r\n      // 保留两位小数并添加单位，添加千分位分隔符\r\n      const result = number.toFixed(2);\r\n      return `${this.addThousandSeparator(result)}元/吨`;\r\n    },\r\n\r\n\r\n    getWaterfallChartDetail() {\r\n      // 只有当成本中心和会计期都有值时才请求\r\n      if (!this.costCenter || !this.accountingPeriod || (this.containType !== 2 && this.containType !== 1)) {\r\n        console.log('成本中心、会计期或质量成本类型为空，跳过数据请求');\r\n        return;\r\n      }\r\n\r\n      const params = {\r\n        costCenter: this.costCenter,\r\n        yearMonth: this.accountingPeriod.replace('-', ''), // 将 2025-06 转换为 202506\r\n        containType: this.containType\r\n      };\r\n\r\n      getWaterfallChartDetail(params).then(response => {\r\n        console.log('getWaterfallChartDetail:', response);\r\n        if (response.data) {\r\n          // 更新WaterfallChart柱状图\r\n          this.updateWaterfallChart(response.data);\r\n        }\r\n      }).catch(error => {\r\n        console.error('获取WaterfallChart数据失败:', error);\r\n        this.$message.error('获取WaterfallChart数据失败');\r\n      });\r\n    },\r\n\r\n    // 更新WaterfallChart柱状图\r\n    updateWaterfallChart(data) {\r\n      if (this.charts.waterfallChart && data) {\r\n        console.log('接收到的WaterfallChart数据:', data);\r\n\r\n        // 处理rescueProject数据\r\n        const xAxisData = [];      // x轴维度数据\r\n        const seriesData = [];     // 柱状图数据\r\n        const colors = ['#93C5FD', '#86EFAC', '#FDE68A', '#FCA5A5', '#C4B5FD', '#F3E8FF'];\r\n\r\n        let dataItems = [];\r\n\r\n        if (data.rescueProject) {\r\n          // 将rescueProject对象转换为数组，转换为万元\r\n          dataItems = Object.entries(data.rescueProject).map(([key, value]) => ({\r\n            name: key,    // 第一项为维度名称\r\n            value: ((Number(value) || 0) / 10000).toFixed(2)  // 第二项为对应维度的值，转换为万元\r\n          }));\r\n        }\r\n\r\n        console.log('处理后的数据项:', dataItems);\r\n\r\n        if (dataItems.length > 0) {\r\n          // 按数值从高到低排序\r\n          dataItems.sort((a, b) => b.value - a.value);\r\n\r\n          // 只取前十个最大的数据\r\n          const topTenItems = dataItems.slice(0, 10);\r\n          console.log('取前十个最大数据:', topTenItems);\r\n\r\n          // 分离排序后的数据\r\n          topTenItems.forEach((item, index) => {\r\n            xAxisData.push(item.name);\r\n            seriesData.push({\r\n              value: item.value,\r\n              itemStyle: { color: colors[index % colors.length] }\r\n            });\r\n          });\r\n        } else {\r\n          console.warn('没有找到有效的数据项');\r\n          // 添加默认数据以便测试\r\n          xAxisData.push('无数据');\r\n          seriesData.push({\r\n            value: 0,\r\n            itemStyle: { color: colors[0] }\r\n          });\r\n        }\r\n\r\n        console.log('x轴维度数据:', xAxisData);\r\n        console.log('柱状图数据:', seriesData);\r\n\r\n        // 更新图表配置\r\n        const option = {\r\n          grid: { left: '8%', right: '4%', bottom: '3%', containLabel: true },\r\n          tooltip: {\r\n            trigger: 'axis',\r\n            axisPointer: { type: 'shadow' },\r\n            backgroundColor: 'rgba(30, 41, 59, 0.9)',\r\n            borderColor: '#93C5FD',\r\n            textStyle: { color: '#fff' },\r\n            formatter: function (params) {\r\n              let result = params[0].name + '<br/>';\r\n              params.forEach(function (item) {\r\n                const formattedValue = parseFloat(item.value).toFixed(2).toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\r\n                result += item.marker + ' ' + item.seriesName + ': ' + formattedValue + '万元<br/>';\r\n              });\r\n              return result;\r\n            }\r\n          },\r\n          xAxis: {\r\n            type: 'category',\r\n            data: xAxisData,\r\n            axisLabel: {\r\n              color: '#9CA3AF',\r\n              interval: 0, // 显示所有标签\r\n              rotate: 0, // 水平显示标签\r\n              align: 'center' // 居中对齐\r\n            },\r\n            axisLine: { lineStyle: { color: '#4B5563' } }\r\n          },\r\n          yAxis: {\r\n            type: 'value',\r\n            name: '金额 (万元)',\r\n            nameTextStyle: { color: '#9CA3AF' },\r\n            axisLabel: { color: '#9CA3AF' },\r\n            axisLine: { lineStyle: { color: '#4B5563' } },\r\n            splitLine: { lineStyle: { color: '#374151' } }\r\n          },\r\n          series: [{\r\n            name: '挽救处理成本',\r\n            type: 'bar',\r\n            data: seriesData\r\n          }]\r\n        };\r\n\r\n        this.charts.waterfallChart.setOption(option, true); // 使用true强制刷新\r\n        console.log('WaterfallChart柱状图数据已更新');\r\n      } else {\r\n        console.error('WaterfallChart实例不存在或数据为空');\r\n      }\r\n    },\r\n\r\n    // 更新ScrapLossChart柱状图\r\n    updateScrapLossChart(data) {\r\n      if (this.charts.scrapLossChart && data) {\r\n        console.log('接收到的ScrapLossChart数据:', data);\r\n        console.log('数据类型:', typeof data);\r\n        console.log('数据键:', Object.keys(data));\r\n\r\n        // 处理报废损失数据，尝试多种可能的数据结构\r\n        const xAxisData = [];      // x轴维度数据\r\n        const seriesData = [];     // 柱状图数据\r\n        const colors = ['#93C5FD', '#86EFAC', '#FDE68A', '#FCA5A5', '#C4B5FD', '#F3E8FF'];\r\n        let dataItems = [];\r\n\r\n        // 尝试不同的数据结构，转换为万元\r\n        if (data.scrapLossMap) {\r\n          // 情况1: 使用scrapLossMap数据（根据实际API返回的数据结构）\r\n          console.log('使用scrapLossMap数据');\r\n          dataItems = Object.entries(data.scrapLossMap).map(([key, value]) => ({\r\n            name: key,\r\n            value: ((Number(value) || 0) / 10000).toFixed(2)\r\n          }));\r\n        } else if (data.rescueProject) {\r\n          // 情况2: 使用rescueProject数据（与WaterfallChart相同）\r\n          console.log('使用rescueProject数据');\r\n          dataItems = Object.entries(data.rescueProject).map(([key, value]) => ({\r\n            name: key,\r\n            value: ((Number(value) || 0) / 10000).toFixed(2)\r\n          }));\r\n        } else if (data.scrapLoss) {\r\n          // 情况3: 使用scrapLoss数据\r\n          console.log('使用scrapLoss数据');\r\n          dataItems = Object.entries(data.scrapLoss).map(([key, value]) => ({\r\n            name: key,\r\n            value: ((Number(value) || 0) / 10000).toFixed(2)\r\n          }));\r\n        } else if (data.scrapLossProject) {\r\n          // 情况4: 使用scrapLossProject数据\r\n          console.log('使用scrapLossProject数据');\r\n          dataItems = Object.entries(data.scrapLossProject).map(([key, value]) => ({\r\n            name: key,\r\n            value: ((Number(value) || 0) / 10000).toFixed(2)\r\n          }));\r\n        } else {\r\n          // 情况5: 直接使用data作为对象\r\n          console.log('直接使用data对象');\r\n          dataItems = Object.entries(data).map(([key, value]) => ({\r\n            name: key,\r\n            value: ((Number(value) || 0) / 10000).toFixed(2)\r\n          }));\r\n        }\r\n\r\n        console.log('处理后的数据项:', dataItems);\r\n\r\n        if (dataItems.length > 0) {\r\n          // 按数值从高到低排序\r\n          dataItems.sort((a, b) => b.value - a.value);\r\n\r\n          // 只取前十个最大的数据\r\n          const topTenItems = dataItems.slice(0, 10);\r\n          console.log('取前十个最大数据:', topTenItems);\r\n\r\n          // 分离排序后的数据\r\n          topTenItems.forEach((item, index) => {\r\n            xAxisData.push(item.name);\r\n            seriesData.push({\r\n              value: item.value,\r\n              itemStyle: { color: colors[index % colors.length] }\r\n            });\r\n          });\r\n        } else {\r\n          console.warn('没有找到有效的数据项');\r\n          // 添加默认数据以便测试\r\n          xAxisData.push('无数据');\r\n          seriesData.push({\r\n            value: 0,\r\n            itemStyle: { color: colors[0] }\r\n          });\r\n        }\r\n\r\n        console.log('x轴维度数据:', xAxisData);\r\n        console.log('柱状图数据:', seriesData);\r\n\r\n        // 更新图表配置\r\n        const option = {\r\n          grid: { left: '8%', right: '4%', bottom: '3%', containLabel: true },\r\n          tooltip: {\r\n            trigger: 'axis',\r\n            axisPointer: { type: 'shadow' },\r\n            backgroundColor: 'rgba(30, 41, 59, 0.9)',\r\n            borderColor: '#93C5FD',\r\n            textStyle: { color: '#fff' },\r\n            formatter: function (params) {\r\n              let result = params[0].name + '<br/>';\r\n              params.forEach(function (item) {\r\n                const formattedValue = parseFloat(item.value).toFixed(2).toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\r\n                result += item.marker + ' ' + item.seriesName + ': ' + formattedValue + '万元<br/>';\r\n              });\r\n              return result;\r\n            }\r\n          },\r\n          xAxis: {\r\n            type: 'category',\r\n            data: xAxisData,\r\n            axisLabel: {\r\n              color: '#9CA3AF',\r\n              interval: 0, // 显示所有标签\r\n              rotate: 0, // 水平显示标签\r\n              align: 'center' // 居中对齐\r\n            },\r\n            axisLine: { lineStyle: { color: '#4B5563' } }\r\n          },\r\n          yAxis: {\r\n            type: 'value',\r\n            name: '金额 (万元)',\r\n            nameTextStyle: { color: '#9CA3AF' },\r\n            axisLabel: { color: '#9CA3AF' },\r\n            axisLine: { lineStyle: { color: '#4B5563' } },\r\n            splitLine: { lineStyle: { color: '#374151' } }\r\n          },\r\n          series: [{\r\n            name: '报废损失成本',\r\n            type: 'bar',\r\n            data: seriesData\r\n          }]\r\n        };\r\n\r\n        this.charts.scrapLossChart.setOption(option, true); // 使用true强制刷新\r\n        console.log('ScrapLossChart柱状图数据已更新');\r\n      } else {\r\n        console.error('ScrapLossChart实例不存在或数据为空');\r\n      }\r\n    },\r\n\r\n    // 更新QualityObjectionChart柱状图\r\n    updateQualityObjectionChart(data) {\r\n      if (this.charts.qualityObjectionChart && data) {\r\n        console.log('接收到的QualityObjectionChart数据:', data);\r\n        console.log('数据类型:', typeof data);\r\n        console.log('数据键:', Object.keys(data));\r\n\r\n        // 处理质量异议损失数据，尝试多种可能的数据结构\r\n        const xAxisData = [];      // x轴维度数据\r\n        const seriesData = [];     // 柱状图数据\r\n        const colors = ['#93C5FD', '#86EFAC', '#FDE68A', '#FCA5A5', '#C4B5FD', '#F3E8FF'];\r\n        let dataItems = [];\r\n\r\n        // 尝试不同的数据结构，转换为万元\r\n        if (data.qualityObjectionLossMap) {\r\n          // 情况1: 使用qualityObjectionLossMap数据\r\n          console.log('使用qualityObjectionLossMap数据');\r\n          dataItems = Object.entries(data.qualityObjectionLossMap).map(([key, value]) => ({\r\n            name: key,\r\n            value: ((Number(value) || 0) / 10000).toFixed(2)\r\n          }));\r\n        } else if (data.rescueProject) {\r\n          // 情况2: 使用rescueProject数据（与WaterfallChart相同）\r\n          console.log('使用rescueProject数据');\r\n          dataItems = Object.entries(data.rescueProject).map(([key, value]) => ({\r\n            name: key,\r\n            value: ((Number(value) || 0) / 10000).toFixed(2)\r\n          }));\r\n        } else {\r\n          // 情况3: 直接使用data作为对象\r\n          console.log('直接使用data对象');\r\n          dataItems = Object.entries(data).map(([key, value]) => ({\r\n            name: key,\r\n            value: ((Number(value) || 0) / 10000).toFixed(2)\r\n          }));\r\n        }\r\n\r\n        console.log('处理后的数据项:', dataItems);\r\n\r\n        if (dataItems.length > 0) {\r\n          // 按数值从高到低排序\r\n          dataItems.sort((a, b) => b.value - a.value);\r\n\r\n          // 只取前十个最大的数据\r\n          const topTenItems = dataItems.slice(0, 10);\r\n          console.log('取前十个最大数据:', topTenItems);\r\n\r\n          // 分离排序后的数据\r\n          topTenItems.forEach((item, index) => {\r\n            xAxisData.push(item.name);\r\n            seriesData.push({\r\n              value: item.value,\r\n              itemStyle: { color: colors[index % colors.length] }\r\n            });\r\n          });\r\n        } else {\r\n          console.warn('没有找到有效的数据项');\r\n          // 添加默认数据以便测试\r\n          xAxisData.push('无数据');\r\n          seriesData.push({\r\n            value: 0,\r\n            itemStyle: { color: colors[0] }\r\n          });\r\n        }\r\n\r\n        console.log('x轴维度数据:', xAxisData);\r\n        console.log('柱状图数据:', seriesData);\r\n\r\n        // 更新图表配置\r\n        const option = {\r\n          grid: { left: '8%', right: '4%', bottom: '3%', containLabel: true },\r\n          tooltip: {\r\n            trigger: 'axis',\r\n            axisPointer: { type: 'shadow' },\r\n            backgroundColor: 'rgba(30, 41, 59, 0.9)',\r\n            borderColor: '#93C5FD',\r\n            textStyle: { color: '#fff' },\r\n            formatter: function (params) {\r\n              let result = params[0].name + '<br/>';\r\n              params.forEach(function (item) {\r\n                const formattedValue = parseFloat(item.value).toFixed(2).toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\r\n                result += item.marker + ' ' + item.seriesName + ': ' + formattedValue + '万元<br/>';\r\n              });\r\n              return result;\r\n            }\r\n          },\r\n          xAxis: {\r\n            type: 'category',\r\n            data: xAxisData,\r\n            axisLabel: {\r\n              color: '#9CA3AF',\r\n              interval: 0, // 显示所有标签\r\n              rotate: 0, // 水平显示标签\r\n              align: 'center' // 居中对齐\r\n            },\r\n            axisLine: { lineStyle: { color: '#4B5563' } }\r\n          },\r\n          yAxis: {\r\n            type: 'value',\r\n            name: '金额 (万元)',\r\n            nameTextStyle: { color: '#9CA3AF' },\r\n            axisLabel: { color: '#9CA3AF' },\r\n            axisLine: { lineStyle: { color: '#4B5563' } },\r\n            splitLine: { lineStyle: { color: '#374151' } }\r\n          },\r\n          series: [{\r\n            name: '质量异议损失成本',\r\n            type: 'bar',\r\n            data: seriesData\r\n          }]\r\n        };\r\n\r\n        this.charts.qualityObjectionChart.setOption(option, true); // 使用true强制刷新\r\n        console.log('QualityObjectionChart柱状图数据已更新');\r\n      } else {\r\n        console.error('QualityObjectionChart实例不存在或数据为空');\r\n      }\r\n    },\r\n\r\n    getQualityObjectionLossDetail() {\r\n      // 只有当成本中心和会计期都有值时才请求\r\n      if (!this.costCenter || !this.accountingPeriod || (this.containType !== 2 && this.containType !== 1)) {\r\n        console.log('成本中心、会计期或质量成本类型为空，跳过数据请求');\r\n        return;\r\n      }\r\n\r\n      const params = {\r\n        costCenter: this.costCenter,\r\n        yearMonth: this.accountingPeriod.replace('-', ''), // 将 2025-06 转换为 202506\r\n        containType: this.containType\r\n      };\r\n\r\n      getQualityObjectionLossDetail(params).then(response => {\r\n        console.log('getQualityObjectionLossDetail:', response);\r\n        if (response.data) {\r\n          // 更新QualityObjectionChart柱状图\r\n          this.updateQualityObjectionChart(response.data);\r\n        }\r\n      }).catch(error => {\r\n        console.error('获取QualityObjectionChart数据失败:', error);\r\n        this.$message.error('获取产品质量异议损失明细数据失败');\r\n      });\r\n    },\r\n\r\n    getScrapLossChartDetailsDetail() {\r\n      // 只有当成本中心和会计期都有值时才请求\r\n      if (!this.costCenter || !this.accountingPeriod || (this.containType !== 2 && this.containType !== 1)) {\r\n        console.log('成本中心、会计期或质量成本类型为空，跳过数据请求');\r\n        return;\r\n      }\r\n\r\n      const params = {\r\n        costCenter: this.costCenter,\r\n        yearMonth: this.accountingPeriod.replace('-', ''), // 将 2025-06 转换为 202506\r\n        containType: this.containType\r\n      };\r\n\r\n      getScrapLossChartDetailsDetail(params).then(response => {\r\n        console.log('getScrapLossChartDetailsDetail:', response);\r\n        if (response.data) {\r\n          // 更新ScrapLossChart柱状图\r\n          this.updateScrapLossChart(response.data);\r\n        }\r\n      }).catch(error => {\r\n        console.error('获取ScrapLossChart数据失败:', error);\r\n        this.$message.error('获取产品报废损失明细数据失败');\r\n      });\r\n    },\r\n\r\n    getExternalCostDetail() {\r\n      // 只有当成本中心和会计期都有值时才请求\r\n      if (!this.costCenter || !this.accountingPeriod || (this.containType !== 2 && this.containType !== 1)) {\r\n        console.log('成本中心、会计期或质量成本类型为空，跳过数据请求');\r\n        return;\r\n      }\r\n\r\n      const params = {\r\n        costCenter: this.costCenter,\r\n        yearMonth: this.accountingPeriod.replace('-', ''), // 将 2025-06 转换为 202506\r\n        containType: this.containType\r\n      };\r\n\r\n      getExternalCostDetail(params).then(response => {\r\n        console.log('getExternalCostDetail:', response);\r\n        if (response.data) {\r\n          // 更新外部损失成本构成图表\r\n          this.updateExternalCostDetailChart(response.data);\r\n        }\r\n      }).catch(error => {\r\n        // console.error('获取外部损失成本数据失败:', error);\r\n        this.$message.error('获取外部损失成本数据失败');\r\n      });\r\n    },\r\n\r\n    getInternalCostDetail() {\r\n      // 只有当成本中心和会计期都有值时才请求\r\n      if (!this.costCenter || !this.accountingPeriod || (this.containType !== 2 && this.containType !== 1)) {\r\n        console.log('成本中心、会计期或质量成本类型为空，跳过数据请求');\r\n        return;\r\n      }\r\n\r\n      const params = {\r\n        costCenter: this.costCenter,\r\n        yearMonth: this.accountingPeriod.replace('-', ''), // 将 2025-06 转换为 202506\r\n        containType: this.containType\r\n      };\r\n\r\n      getInternalCostDetail(params).then(response => {\r\n        console.log('getInternalCostDetail:', response);\r\n        if (response.data) {\r\n          // 更新内部损失成本构成图表\r\n          this.updateInternalCostDetailChart(response.data);\r\n        }\r\n      }).catch(error => {\r\n        this.$message.error('获取内部损失成本数据失败');\r\n      });\r\n    },\r\n\r\n    // 更新内部损失成本构成图表\r\n    updateInternalCostDetailChart(data) {\r\n      if (this.charts.internalCostDetailChart && data) {\r\n        console.log('接收到的内部损失成本数据:', data);\r\n\r\n        // 收集所有数据项\r\n        const allDataItems = [];\r\n        const colors = ['#93C5FD', '#86EFAC', '#FDE68A', '#FCA5A5', '#C4B5FD'];\r\n\r\n        // 处理各个成本项，收集到统一数组中，转换为万元\r\n        if (data.contractionLoss) {\r\n          Object.entries(data.contractionLoss).forEach(([key, value]) => {\r\n            // 确保数值转换，包括0值也要显示，转换为万元\r\n            const numValue = ((Number(value) || 0) / 10000).toFixed(2);\r\n            allDataItems.push({ name: key, value: numValue });\r\n          });\r\n        }\r\n\r\n        if (data.rescueCost) {\r\n          Object.entries(data.rescueCost).forEach(([key, value]) => {\r\n            // 确保数值转换，包括0值也要显示，转换为万元\r\n            const numValue = ((Number(value) || 0) / 10000).toFixed(2);\r\n            allDataItems.push({ name: key, value: numValue });\r\n          });\r\n        }\r\n\r\n        if (data.revisionLoss) {\r\n          Object.entries(data.revisionLoss).forEach(([key, value]) => {\r\n            // 确保数值转换，包括0值也要显示，转换为万元\r\n            const numValue = ((Number(value) || 0) / 10000).toFixed(2);\r\n            allDataItems.push({ name: key, value: numValue });\r\n          });\r\n        }\r\n\r\n        if (data.scrapLoss) {\r\n          Object.entries(data.scrapLoss).forEach(([key, value]) => {\r\n            // 确保数值转换，包括0值也要显示，转换为万元\r\n            const numValue = ((Number(value) || 0) / 10000).toFixed(2);\r\n            allDataItems.push({ name: key, value: numValue });\r\n          });\r\n        }\r\n\r\n        console.log('收集到的所有数据项（包含0值）:', allDataItems);\r\n\r\n        // 按数值从高到低排序（0值会排在负值之前，正值之后）\r\n        allDataItems.sort((a, b) => b.value - a.value);\r\n\r\n        console.log('排序后的数据（包含0值）:', allDataItems);\r\n\r\n        // 分离排序后的数据，反转顺序使金额大的显示在上面\r\n        const yAxisData = [];\r\n        const seriesData = [];\r\n\r\n        // 反转数组，使金额大的显示在图表上方\r\n        allDataItems.reverse().forEach((item, index) => {\r\n          yAxisData.push(item.name);\r\n          seriesData.push({\r\n            value: item.value,\r\n            itemStyle: { color: colors[index % colors.length] }\r\n          });\r\n        });\r\n\r\n        console.log('y轴数据:', yAxisData);\r\n        console.log('系列数据（包含0值）:', seriesData);\r\n\r\n        // 更新图表配置\r\n        const option = {\r\n          yAxis: {\r\n            type: 'category',\r\n            data: yAxisData,\r\n            axisLabel: { color: '#9CA3AF' },\r\n            axisLine: { lineStyle: { color: '#4B5563' } }\r\n          },\r\n          series: [{\r\n            name: '金额 (万元)',\r\n            type: 'bar',\r\n            data: seriesData\r\n          }]\r\n        };\r\n\r\n        this.charts.internalCostDetailChart.setOption(option);\r\n        console.log('内部损失成本构成图表数据已更新（包含0值，按数值从高到低排序）');\r\n      }\r\n    },\r\n\r\n    // 更新外部损失成本构成图表\r\n    updateExternalCostDetailChart(data) {\r\n      if (this.charts.externalCostDetailChart && data) {\r\n        // 收集所有数据项\r\n        const allDataItems = [];\r\n        const colors = ['#FCA5A5', '#FDE68A', '#86EFAC', '#93C5FD', '#C4B5FD'];\r\n\r\n        // 处理各个成本项，收集到统一数组中，转换为万元\r\n        if (data.customerClaimCost) {\r\n          Object.entries(data.customerClaimCost).forEach(([key, value]) => {\r\n            allDataItems.push({ name: key, value: ((Number(value) || 0) / 10000).toFixed(2) });\r\n          });\r\n        }\r\n\r\n        if (data.qualityObjectionFeeCost) {\r\n          Object.entries(data.qualityObjectionFeeCost).forEach(([key, value]) => {\r\n            allDataItems.push({ name: key, value: ((Number(value) || 0) / 10000).toFixed(2) });\r\n          });\r\n        }\r\n\r\n        if (data.qualityObjectionTravelCost) {\r\n          Object.entries(data.qualityObjectionTravelCost).forEach(([key, value]) => {\r\n            allDataItems.push({ name: key, value: ((Number(value) || 0) / 10000).toFixed(2) });\r\n          });\r\n        }\r\n\r\n        if (data.returnLoss) {\r\n          Object.entries(data.returnLoss).forEach(([key, value]) => {\r\n            allDataItems.push({ name: key, value: ((Number(value) || 0) / 10000).toFixed(2) });\r\n          });\r\n        }\r\n\r\n        // 按数值从高到低排序\r\n        allDataItems.sort((a, b) => b.value - a.value);\r\n\r\n        // 分离排序后的数据，反转顺序使金额大的显示在上面\r\n        const yAxisData = [];\r\n        const seriesData = [];\r\n\r\n        // 反转数组，使金额大的显示在图表上方\r\n        allDataItems.reverse().forEach((item, index) => {\r\n          yAxisData.push(item.name);\r\n          seriesData.push({\r\n            value: item.value,\r\n            itemStyle: { color: colors[index % colors.length] }\r\n          });\r\n        });\r\n\r\n        // 更新图表配置\r\n        const option = {\r\n          yAxis: {\r\n            type: 'category',\r\n            data: yAxisData,\r\n            axisLabel: { color: '#9CA3AF' },\r\n            axisLine: { lineStyle: { color: '#4B5563' } }\r\n          },\r\n          series: [{\r\n            name: '金额 (万元)',\r\n            type: 'bar',\r\n            data: seriesData\r\n          }]\r\n        };\r\n\r\n        this.charts.externalCostDetailChart.setOption(option);\r\n        console.log('外部损失成本构成图表数据已更新（已按数值从高到低排序）');\r\n      }\r\n    },\r\n\r\n    getQualityCostDetail() {\r\n      // 只有当成本中心和会计期都有值时才请求\r\n      if (!this.costCenter || !this.accountingPeriod || (this.containType !== 2 && this.containType !== 1)) {\r\n        console.log('成本中心、会计期或质量成本类型为空，跳过数据请求');\r\n        return;\r\n      }\r\n\r\n      const params = {\r\n        costCenter: this.costCenter,\r\n        yearMonth: this.accountingPeriod.replace('-', ''), // 将 2025-06 转换为 202506\r\n        containType: this.containType\r\n      };\r\n\r\n      getQualityCostDetail(params).then(response => {\r\n        console.log('getQualityCostDetail:', response);\r\n        if (response.data) {\r\n          this.qualityCostData = response.data.qualityCostData;\r\n          this.qualityCostDetail = response.data;\r\n        }\r\n      }).catch(error => {\r\n        // console.error('获取饼图数据失败:', error);\r\n        this.$message.error('获取质量成本数据失败');\r\n      });\r\n    },\r\n\r\n    //质量成本四大类别占比\r\n    getMultiLineChartData() {\r\n      // 只有当成本中心和会计期都有值时才请求\r\n      if (!this.costCenter || !this.accountingPeriod || (this.containType !== 2 && this.containType !== 1)) {\r\n        console.log('成本中心、会计期或质量成本类型为空，跳过数据请求');\r\n        return;\r\n      }\r\n\r\n      const params = {\r\n        costCenter: this.costCenter,\r\n        yearMonth: this.accountingPeriod.replace('-', ''), // 将 2025-06 转换为 202506\r\n        containType: this.containType\r\n      };\r\n\r\n      getMultiLineChartData(params).then(response => {\r\n        console.log('getMultiLineChartData:', response);\r\n        if (response.data) {\r\n          this.updateMultiLineChart(response.data);\r\n        }\r\n      }).catch(error => {\r\n        // console.error('获取饼图数据失败:', error);\r\n        this.$message.error('获取质量成本数据失败');\r\n      });\r\n    },\r\n\r\n    getComboChartDetail() {\r\n      // 只有当成本中心和会计期都有值时才请求\r\n      if (!this.costCenter || !this.accountingPeriod || (this.containType !== 2 && this.containType !== 1)) {\r\n        console.log('成本中心、会计期或质量成本类型为空，跳过数据请求');\r\n        return;\r\n      }\r\n\r\n      const params = {\r\n        costCenter: this.costCenter,\r\n        yearMonth: this.accountingPeriod.replace('-', ''), // 将 2025-06 转换为 202506\r\n        containType: this.containType\r\n      };\r\n\r\n      getComboChartDetail(params).then(response => {\r\n        console.log('getComboChartDetail:', response);\r\n        if (response.data) {\r\n          this.updateComboChart(response.data);\r\n        }\r\n      }).catch(error => {\r\n        this.$message.error('获取ComboChart数据失败');\r\n      });\r\n    },\r\n\r\n    // 更新ComboChart图表\r\n    updateComboChart(data) {\r\n      if (this.charts.comboChart && data) {\r\n        console.log('接收到的ComboChart数据:', data);\r\n\r\n        // 基于会计期生成近6个月的月份标签作为x轴数据\r\n        const months = this.generateComboChartMonthsByAccountingPeriod();\r\n        console.log('生成的月份标签:', months);\r\n\r\n        // 生成对应的年月格式用于数据匹配\r\n        const yearMonths = this.generateYearMonthsByAccountingPeriod();\r\n        console.log('生成的年月格式:', yearMonths);\r\n\r\n        const failureCostData = [];     // 失败成本数据\r\n        const controllingCostData = []; // 控制成本数据\r\n\r\n        // 为每个月份提取对应的数值，转换为万元\r\n        yearMonths.forEach(yearMonth => {\r\n          // 获取失败成本数据，转换为万元\r\n          const failureValue = data.failureCostMap && data.failureCostMap[yearMonth]\r\n            ? ((Number(data.failureCostMap[yearMonth]) || 0) / 10000).toFixed(2)\r\n            : 0;\r\n          failureCostData.push(failureValue);\r\n\r\n          // 获取控制成本数据，转换为万元\r\n          const controllingValue = data.controllingCostMap && data.controllingCostMap[yearMonth]\r\n            ? ((Number(data.controllingCostMap[yearMonth]) || 0) / 10000).toFixed(2)\r\n            : 0;\r\n          controllingCostData.push(controllingValue);\r\n        });\r\n\r\n        console.log('x轴月份数据:', months.map(month => `${month}月`));\r\n        console.log('失败成本数据:', failureCostData);\r\n        console.log('控制成本数据:', controllingCostData);\r\n\r\n        // 更新图表配置\r\n        const option = {\r\n          // 图例配置 - 标注颜色对应的维度\r\n          legend: {\r\n            data: ['失败成本', '控制成本'], // 失败成本(红色#FCA5A5)，控制成本(绿色#86EFAC)\r\n            textStyle: { color: '#E5E7EB' }\r\n          },\r\n          grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },\r\n          xAxis: {\r\n            type: 'category',\r\n            boundaryGap: false,\r\n            data: months.map(month => `${month}月`), // 近6个月的月份\r\n            axisLabel: {\r\n              color: '#9CA3AF',\r\n              rotate: 0, // 水平显示标签\r\n              align: 'center' // 居中对齐\r\n            },\r\n            axisLine: { lineStyle: { color: '#4B5563' } }\r\n          },\r\n          yAxis: {\r\n            type: 'value',\r\n            name: '成本 (万元)',\r\n            nameTextStyle: { color: '#9CA3AF' },\r\n            axisLabel: { color: '#9CA3AF' },\r\n            axisLine: { lineStyle: { color: '#4B5563' } },\r\n            splitLine: { lineStyle: { color: '#374151' } }\r\n          },\r\n\r\n          series: [\r\n            {\r\n              name: '失败成本', // 红色曲线 #FCA5A5\r\n              type: 'line',\r\n              data: failureCostData,\r\n              smooth: true, // 启用平滑曲线\r\n              lineStyle: { color: '#FCA5A5', width: 3 },\r\n              itemStyle: { color: '#FCA5A5' },\r\n              symbol: 'circle',\r\n              symbolSize: 6\r\n            },\r\n            {\r\n              name: '控制成本', // 绿色曲线 #86EFAC\r\n              type: 'line',\r\n              data: controllingCostData,\r\n              smooth: true, // 启用平滑曲线\r\n              lineStyle: { color: '#86EFAC', width: 3 },\r\n              itemStyle: { color: '#86EFAC' },\r\n              symbol: 'circle',\r\n              symbolSize: 6\r\n            }\r\n          ]\r\n        };\r\n\r\n        this.charts.comboChart.setOption(option);\r\n        console.log('ComboChart图表数据已更新');\r\n      }\r\n    },\r\n\r\n    // 生成ComboChart的月份标签（当前月份和之前的5个月）\r\n    generateComboChartMonths() {\r\n      const months = [];\r\n      const currentDate = new Date();\r\n\r\n      for (let i = 5; i >= 0; i--) {\r\n        const date = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1);\r\n        const month = date.getMonth() + 1;\r\n        months.push(month);\r\n      }\r\n\r\n      return months;\r\n    },\r\n\r\n    // 生成对应的年月格式（当前月份和之前的5个月，如202501, 202502等）\r\n    generateYearMonths() {\r\n      const yearMonths = [];\r\n      const currentDate = new Date();\r\n\r\n      for (let i = 5; i >= 0; i--) {\r\n        const date = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1);\r\n        const year = date.getFullYear();\r\n        const month = date.getMonth() + 1;\r\n        const yearMonth = `${year}${String(month).padStart(2, '0')}`;\r\n        yearMonths.push(yearMonth);\r\n      }\r\n\r\n      return yearMonths;\r\n    },\r\n\r\n    // 基于会计期生成ComboChart的月份标签（会计期当前月份和之前的5个月）\r\n    generateComboChartMonthsByAccountingPeriod() {\r\n      const months = [];\r\n\r\n      if (!this.accountingPeriod) {\r\n        console.warn('会计期为空，使用系统当前时间');\r\n        return this.generateComboChartMonths();\r\n      }\r\n\r\n      // 解析会计期 (格式: 2025-06)\r\n      const [year, month] = this.accountingPeriod.split('-').map(Number);\r\n      const accountingDate = new Date(year, month - 1, 1); // month-1 因为Date的月份从0开始\r\n\r\n      for (let i = 5; i >= 0; i--) {\r\n        const date = new Date(accountingDate.getFullYear(), accountingDate.getMonth() - i, 1);\r\n        const monthNum = date.getMonth() + 1;\r\n        months.push(monthNum);\r\n      }\r\n\r\n      return months;\r\n    },\r\n\r\n    // 基于会计期生成对应的年月格式（会计期当前月份和之前的5个月）\r\n    generateYearMonthsByAccountingPeriod() {\r\n      const yearMonths = [];\r\n\r\n      if (!this.accountingPeriod) {\r\n        console.warn('会计期为空，使用系统当前时间');\r\n        return this.generateYearMonths();\r\n      }\r\n\r\n      // 解析会计期 (格式: 2025-06)\r\n      const [year, month] = this.accountingPeriod.split('-').map(Number);\r\n      const accountingDate = new Date(year, month - 1, 1); // month-1 因为Date的月份从0开始\r\n\r\n      for (let i = 5; i >= 0; i--) {\r\n        const date = new Date(accountingDate.getFullYear(), accountingDate.getMonth() - i, 1);\r\n        const yearNum = date.getFullYear();\r\n        const monthNum = date.getMonth() + 1;\r\n        const yearMonth = `${yearNum}${String(monthNum).padStart(2, '0')}`;\r\n        yearMonths.push(yearMonth);\r\n      }\r\n\r\n      return yearMonths;\r\n    },\r\n\r\n    //质量成本四大类别占比\r\n    getPieChartData() {\r\n      // 只有当成本中心和会计期都有值时才请求\r\n      if (!this.costCenter || !this.accountingPeriod || (this.containType !== 2 && this.containType !== 1)) {\r\n        console.log('成本中心、会计期或质量成本类型为空，跳过数据请求');\r\n        return;\r\n      }\r\n\r\n      const params = {\r\n        costCenter: this.costCenter,\r\n        yearMonth: this.accountingPeriod.replace('-', ''), // 将 2025-06 转换为 202506\r\n        containType: this.containType\r\n      };\r\n\r\n\r\n      getPieChartData(params).then(response => {\r\n        console.log('getPieChartData:', response);\r\n        if (response.data) {\r\n          this.updatePieChart(response.data);\r\n        }\r\n      }).catch(error => {\r\n        console.error('获取饼图数据失败:', error);\r\n        this.$message.error('获取质量成本数据失败');\r\n      });\r\n    },\r\n    // 获取成本中心列表\r\n    getCostCenterList() {\r\n      this.costCenterLoading = true;\r\n      costCenterlist().then(response => {\r\n        this.costCenterOptions = response.data || [];\r\n        // 如果有数据，设置默认选中第一个\r\n        if (this.costCenterOptions.length > 0) {\r\n          console.log('获取成本中心列表:', this.costCenterOptions);\r\n          this.costCenter = this.costCenterOptions[0].key;\r\n          // 设置默认值后，主动触发一次数据刷新\r\n          this.$nextTick(() => {\r\n            this.refreshChartData();\r\n          });\r\n        }\r\n      }).catch(error => {\r\n        console.error('获取成本中心列表失败:', error);\r\n        this.$message.error('获取成本中心列表失败');\r\n      }).finally(() => {\r\n        this.costCenterLoading = false;\r\n      });\r\n    },\r\n\r\n    // 更新饼图数据\r\n    updatePieChart(data) {\r\n      if (this.charts.pieChart && data) {\r\n        // 更新饼图的数据，转换为万元\r\n        const option = this.charts.pieChart.getOption();\r\n        if (option && option.series && option.series[0]) {\r\n          option.series[0].data = [\r\n            { value: (data.preventionCost / 10000).toFixed(2), name: '预防成本', itemStyle: { color: '#93C5FD' } },\r\n            { value: (data.appraisalCost / 10000).toFixed(2), name: '鉴定成本', itemStyle: { color: '#86EFAC' } },\r\n            { value: (data.internalCost / 10000).toFixed(2), name: '内部损失成本', itemStyle: { color: '#FDE68A' } },\r\n            { value: (data.externalCost / 10000).toFixed(2), name: '外部损失成本', itemStyle: { color: '#FCA5A5' } },\r\n          ],\r\n            this.charts.pieChart.setOption(option);\r\n          // console.log('饼图数据已更新');\r\n        }\r\n      }\r\n    },\r\n\r\n    // 更新多线图数据\r\n    updateMultiLineChart(data) {\r\n      if (this.charts.multiLineChart && data) {\r\n        // 基于会计期生成月份标签和对应的年月数字\r\n        const months = this.generateComboChartMonthsByAccountingPeriod();\r\n        const yearMonths = this.generateYearMonthsByAccountingPeriod();\r\n\r\n        // 处理各种成本数据，转换为万元\r\n        const preventionData = this.processMapData(data.preventionCostMap, yearMonths, true);\r\n        const appraisalData = this.processMapData(data.appraisalCostMap, yearMonths, true);\r\n        const internalData = this.processMapData(data.internalCostMap, yearMonths, true);\r\n        const externalData = this.processMapData(data.externalCostMap, yearMonths, true);\r\n\r\n        // 更新多线图的配置\r\n        const option = {\r\n          xAxis: {\r\n            type: 'category',\r\n            boundaryGap: false,\r\n            data: months.map(month => `${month}月`), // 格式化为\"X月\"\r\n            axisLabel: {\r\n              color: '#9CA3AF',\r\n              rotate: 0, // 水平显示标签\r\n              align: 'center' // 居中对齐\r\n            },\r\n            axisLine: { lineStyle: { color: '#4B5563' } }\r\n          },\r\n          series: [\r\n            {\r\n              name: '预防成本',\r\n              type: 'line',\r\n              data: preventionData,\r\n              smooth: true, // 启用平滑曲线\r\n              lineStyle: { color: '#93C5FD', width: 3 },\r\n              itemStyle: { color: '#93C5FD' },\r\n              symbol: 'circle',\r\n              symbolSize: 6\r\n            },\r\n            {\r\n              name: '鉴定成本',\r\n              type: 'line',\r\n              data: appraisalData,\r\n              smooth: true, // 启用平滑曲线\r\n              lineStyle: { color: '#86EFAC', width: 3 },\r\n              itemStyle: { color: '#86EFAC' },\r\n              symbol: 'circle',\r\n              symbolSize: 6\r\n            },\r\n            {\r\n              name: '内部损失成本',\r\n              type: 'line',\r\n              data: internalData,\r\n              smooth: true, // 启用平滑曲线\r\n              lineStyle: { color: '#FDE68A', width: 3 },\r\n              itemStyle: { color: '#FDE68A' },\r\n              symbol: 'circle',\r\n              symbolSize: 6\r\n            },\r\n            {\r\n              name: '外部损失成本',\r\n              type: 'line',\r\n              data: externalData,\r\n              smooth: true, // 启用平滑曲线\r\n              lineStyle: { color: '#FCA5A5', width: 3 },\r\n              itemStyle: { color: '#FCA5A5' },\r\n              symbol: 'circle',\r\n              symbolSize: 6\r\n            }\r\n          ]\r\n        };\r\n\r\n        this.charts.multiLineChart.setOption(option);\r\n        console.log('多线图数据已更新');\r\n      }\r\n    },\r\n\r\n    // 处理Map数据，根据年月匹配对应的值\r\n    processMapData(costMap, yearMonths, convertToWanYuan = false) {\r\n      if (!costMap) return new Array(yearMonths.length).fill(0);\r\n\r\n      return yearMonths.map(yearMonth => {\r\n        const value = costMap[yearMonth] || 0;\r\n        return convertToWanYuan ? (value / 10000).toFixed(2) : value;\r\n      });\r\n    },\r\n\r\n    // 生成月份标签（当前月份及前五个月份）\r\n    generateMonthLabels() {\r\n      const months = [];\r\n      const yearMonths = [];\r\n      const currentDate = new Date();\r\n\r\n      for (let i = 5; i >= 0; i--) {\r\n        const date = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1);\r\n        const year = date.getFullYear();\r\n        const month = date.getMonth() + 1;\r\n\r\n        months.push(`${month}月`);\r\n        yearMonths.push(parseInt(`${year}${String(month).padStart(2, '0')}`));\r\n      }\r\n\r\n      return { months, yearMonths };\r\n    },\r\n\r\n    // 刷新图表数据\r\n    refreshChartData() {\r\n      // 只有当成本中心和会计期都有值时才刷新\r\n      if (!this.costCenter || !this.accountingPeriod || (this.containType !== 2 && this.containType !== 1)) {\r\n        return;\r\n      }\r\n\r\n      this.getQualityCostDetail();\r\n      this.getPieChartData();\r\n      this.getMultiLineChartData();\r\n      this.getExternalCostDetail();\r\n      this.getInternalCostDetail();\r\n      this.getComboChartDetail();\r\n      this.getWaterfallChartDetail();\r\n      this.getScrapLossChartDetailsDetail();\r\n      this.getQualityObjectionLossDetail();\r\n\r\n      // 这里可以添加其他图表的数据刷新\r\n      // this.$message.success(`已切换到成本中心: ${this.costCenter}, 会计期: ${this.accountingPeriod}`);\r\n    },\r\n\r\n    /** 查询按钮操作 */\r\n    handleQuery() {\r\n      this.refreshChartData();\r\n    },\r\n\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      // 重置为默认值\r\n      if (this.costCenterOptions.length > 0) {\r\n        this.costCenter = this.costCenterOptions[0].key;\r\n      }\r\n\r\n      // 获取默认会计期\r\n      const now = new Date();\r\n      const year = now.getFullYear();\r\n      const month = now.getMonth();\r\n      const prevMonth = month === 0 ? 12 : month;\r\n      const prevYear = month === 0 ? year - 1 : year;\r\n      this.accountingPeriod = `${prevYear}-${String(prevMonth).padStart(2, '0')}`;\r\n\r\n      this.$message.success('查询条件已重置');\r\n    },\r\n\r\n    initCharts() {\r\n      const THEME = 'dark'\r\n\r\n      // 定义商务风淡色系色彩方案\r\n      this.businessColors = {\r\n        light: ['#93C5FD', '#86EFAC', '#FDE68A', '#FCA5A5', '#C4B5FD', '#7DD3FC', '#F9A8D4', '#BEF264'],\r\n        gradient: [\r\n          { offset: 0, color: '#3B82F6' },\r\n          { offset: 1, color: '#1E40AF' }\r\n        ]\r\n      }\r\n\r\n      // 初始化所有图表\r\n      this.charts.pieChart = echarts.init(this.$refs.pieChart, THEME)\r\n      this.charts.multiLineChart = echarts.init(this.$refs.multiLineChart, THEME)\r\n      this.charts.externalCostDetailChart = echarts.init(this.$refs.externalCostDetailChart, THEME)\r\n      this.charts.internalCostDetailChart = echarts.init(this.$refs.internalCostDetailChart, THEME)\r\n      this.charts.waterfallChart = echarts.init(this.$refs.waterfallChart, THEME)\r\n      this.charts.comboChart = echarts.init(this.$refs.comboChart, THEME)\r\n      this.charts.scrapLossChart = echarts.init(this.$refs.scrapLossChart, THEME)\r\n      this.charts.qualityObjectionChart = echarts.init(this.$refs.qualityObjectionChart, THEME)\r\n\r\n      // 配置所有图表\r\n      this.setPieChartOption()\r\n      this.setMultiLineChartOption()\r\n      this.setExternalCostDetailChartOption()\r\n      this.setInternalCostDetailChartOption()\r\n      this.setWaterfallChartOption()\r\n      this.setComboChartOption()\r\n      this.setScrapLossChartOption()\r\n      this.setQualityObjectionChartOption()\r\n    },\r\n\r\n    setPieChartOption() {\r\n      this.charts.pieChart.setOption({\r\n        color: ['#93C5FD', '#86EFAC', '#FDE68A', '#FCA5A5'],\r\n        tooltip: {\r\n          trigger: 'item',\r\n          formatter: (params) => {\r\n            const value = parseFloat(params.value).toFixed(2);\r\n            const formattedValue = value.toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\r\n            return `${params.seriesName} <br/>${params.name}: ${formattedValue}万元 (${params.percent}%)`;\r\n          },\r\n          backgroundColor: 'rgba(30, 41, 59, 0.9)',\r\n          borderColor: '#93C5FD',\r\n          textStyle: { color: '#fff' }\r\n        },\r\n        legend: {\r\n          top: 'bottom',\r\n          left: 'center',\r\n          textStyle: { color: '#E5E7EB', fontSize: 12 }\r\n        },\r\n        series: [{\r\n          name: '成本类别',\r\n          type: 'pie',\r\n          radius: '65%',\r\n          data: [],\r\n          emphasis: {\r\n            itemStyle: {\r\n              shadowBlur: 15,\r\n              shadowOffsetX: 0,\r\n              shadowColor: 'rgba(147, 197, 253, 0.6)'\r\n            }\r\n          },\r\n          labelLine: { lineStyle: { color: '#9CA3AF' } },\r\n          label: {\r\n            color: '#E5E7EB',\r\n            formatter: (params) => {\r\n              const value = parseFloat(params.value).toFixed(2);\r\n              const formattedValue = value.toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\r\n              return `${params.name}(${formattedValue}万元, ${params.percent}%)`;\r\n            }\r\n          }\r\n        }]\r\n      })\r\n    },\r\n\r\n\r\n\r\n    setComboChartOption() {\r\n      this.charts.comboChart.setOption({\r\n        color: ['#FCA5A5', '#86EFAC'],\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: { type: 'cross' },\r\n          backgroundColor: 'rgba(30, 41, 59, 0.9)',\r\n          borderColor: '#93C5FD',\r\n          textStyle: { color: '#fff' }\r\n        },\r\n        legend: {\r\n          data: ['失败成本', '控制成本'],\r\n          textStyle: { color: '#E5E7EB' }\r\n        },\r\n        grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },\r\n        xAxis: {\r\n          type: 'category',\r\n          boundaryGap: false,\r\n          data: ['1月', '2月', '3月', '4月', '5月', '6月'],\r\n          axisLabel: {\r\n            color: '#9CA3AF',\r\n            rotate: 0, // 水平显示标签\r\n            align: 'center' // 居中对齐\r\n          },\r\n          axisLine: { lineStyle: { color: '#4B5563' } }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          name: '成本 (万元)',\r\n          nameTextStyle: { color: '#9CA3AF' },\r\n          axisLabel: { color: '#9CA3AF' },\r\n          axisLine: { lineStyle: { color: '#4B5563' } },\r\n          splitLine: { lineStyle: { color: '#374151' } }\r\n        },\r\n        series: [\r\n          {\r\n            name: '失败成本', // 红色曲线 #FCA5A5\r\n            type: 'line',\r\n            data: [280, 260, 240, 220, 200, 180],\r\n            smooth: true, // 启用平滑曲线\r\n            lineStyle: { color: '#FCA5A5', width: 3 },\r\n            itemStyle: { color: '#FCA5A5' },\r\n            symbol: 'circle',\r\n            symbolSize: 6\r\n          },\r\n          {\r\n            name: '控制成本', // 绿色曲线 #86EFAC\r\n            type: 'line',\r\n            data: [120, 125, 130, 135, 140, 145],\r\n            smooth: true, // 启用平滑曲线\r\n            lineStyle: { color: '#86EFAC', width: 3 },\r\n            itemStyle: { color: '#86EFAC' },\r\n            symbol: 'circle',\r\n            symbolSize: 6\r\n          }\r\n        ]\r\n      })\r\n    },\r\n\r\n    setMultiLineChartOption() {\r\n      this.charts.multiLineChart.setOption({\r\n        color: ['#93C5FD', '#86EFAC', '#FDE68A', '#FCA5A5'],\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          backgroundColor: 'rgba(30, 41, 59, 0.9)',\r\n          borderColor: '#93C5FD',\r\n          textStyle: { color: '#fff' }\r\n        },\r\n        legend: {\r\n          data: ['预防成本', '鉴定成本', '内部损失成本', '外部损失成本'],\r\n          textStyle: { color: '#E5E7EB' }\r\n        },\r\n        grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },\r\n        xAxis: {\r\n          type: 'category',\r\n          boundaryGap: false,\r\n          data: ['1月', '2月', '3月', '4月', '5月', '6月'],\r\n          axisLabel: {\r\n            color: '#9CA3AF',\r\n            rotate: 0, // 水平显示标签\r\n            align: 'center' // 居中对齐\r\n          },\r\n          axisLine: { lineStyle: { color: '#4B5563' } }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          name: '成本 (万元)',\r\n          nameTextStyle: { color: '#9CA3AF' },\r\n          axisLabel: { color: '#9CA3AF' },\r\n          axisLine: { lineStyle: { color: '#4B5563' } },\r\n          splitLine: { lineStyle: { color: '#374151' } }\r\n        },\r\n        series: [\r\n          {\r\n            name: '预防成本',\r\n            type: 'line',\r\n            data: [80, 82, 85, 88, 90, 95],\r\n            smooth: true, // 启用平滑曲线\r\n            lineStyle: { color: '#93C5FD', width: 3 },\r\n            itemStyle: { color: '#93C5FD' },\r\n            symbol: 'circle',\r\n            symbolSize: 6\r\n          },\r\n          {\r\n            name: '鉴定成本',\r\n            type: 'line',\r\n            data: [120, 122, 125, 128, 130, 135],\r\n            smooth: true, // 启用平滑曲线\r\n            lineStyle: { color: '#86EFAC', width: 3 },\r\n            itemStyle: { color: '#86EFAC' },\r\n            symbol: 'circle',\r\n            symbolSize: 6\r\n          },\r\n          {\r\n            name: '内部损失成本',\r\n            type: 'line',\r\n            data: [450, 430, 410, 380, 350, 320],\r\n            smooth: true, // 启用平滑曲线\r\n            lineStyle: { color: '#FDE68A', width: 3 },\r\n            itemStyle: { color: '#FDE68A' },\r\n            symbol: 'circle',\r\n            symbolSize: 6\r\n          },\r\n          {\r\n            name: '外部损失成本',\r\n            type: 'line',\r\n            data: [350, 340, 310, 290, 260, 230],\r\n            smooth: true, // 启用平滑曲线\r\n            lineStyle: { color: '#FCA5A5', width: 3 },\r\n            itemStyle: { color: '#FCA5A5' },\r\n            symbol: 'circle',\r\n            symbolSize: 6\r\n          }\r\n        ]\r\n      })\r\n    },\r\n\r\n\r\n\r\n\r\n\r\n    setParetoChartOption() {\r\n      this.charts.paretoChart.setOption({\r\n        color: ['#93C5FD', '#FDE68A'],\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: { type: 'cross' },\r\n          backgroundColor: 'rgba(30, 41, 59, 0.9)',\r\n          borderColor: '#93C5FD',\r\n          textStyle: { color: '#fff' }\r\n        },\r\n        grid: { right: '20%' },\r\n        xAxis: [{\r\n          type: 'category',\r\n          data: ['产品报废', '产品改判', '设备故障', '工艺废料', '其他'],\r\n          axisLabel: {\r\n            interval: 0,\r\n            rotate: 0, // 水平显示标签\r\n            align: 'center', // 居中对齐\r\n            color: '#9CA3AF'\r\n          },\r\n          axisLine: { lineStyle: { color: '#4B5563' } }\r\n        }],\r\n        yAxis: [\r\n          {\r\n            type: 'value',\r\n            name: '损失金额(元)',\r\n            nameTextStyle: { color: '#9CA3AF' },\r\n            axisLabel: { color: '#9CA3AF' },\r\n            axisLine: { lineStyle: { color: '#4B5563' } },\r\n            splitLine: { lineStyle: { color: '#374151' } }\r\n          },\r\n          {\r\n            type: 'value',\r\n            name: '累计占比',\r\n            min: 0,\r\n            max: 100,\r\n            axisLabel: {\r\n              formatter: '{value} %',\r\n              color: '#9CA3AF'\r\n            },\r\n            nameTextStyle: { color: '#9CA3AF' },\r\n            axisLine: { lineStyle: { color: '#4B5563' } }\r\n          }\r\n        ],\r\n        series: [\r\n          {\r\n            name: '损失金额',\r\n            type: 'bar',\r\n            data: [280, 110, 35, 20, 5],\r\n            itemStyle: { color: '#93C5FD' }\r\n          },\r\n          {\r\n            name: '累计占比',\r\n            type: 'line',\r\n            yAxisIndex: 1,\r\n            data: [62.2, 86.7, 94.4, 98.9, 100],\r\n            lineStyle: { color: '#FDE68A', width: 3 },\r\n            itemStyle: { color: '#FDE68A' },\r\n            symbol: 'circle',\r\n            symbolSize: 8\r\n          }\r\n        ]\r\n      })\r\n    },\r\n\r\n    setExternalCostDetailChartOption() {\r\n      this.charts.externalCostDetailChart.setOption({\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: { type: 'shadow' },\r\n          backgroundColor: 'rgba(30, 41, 59, 0.9)',\r\n          borderColor: '#93C5FD',\r\n          textStyle: { color: '#fff' },\r\n          formatter: function (params) {\r\n            let result = params[0].name + '<br/>';\r\n            params.forEach(function (item) {\r\n              const formattedValue = parseFloat(item.value).toFixed(2).toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\r\n              result += item.marker + ' 金额: ' + formattedValue + '万元<br/>';\r\n            });\r\n            return result;\r\n          }\r\n        },\r\n        grid: { left: '5%', right: '15%', top: '12%', bottom: '12%', containLabel: true },\r\n        xAxis: {\r\n          type: 'value',\r\n          name: '金额（万元）',\r\n          nameTextStyle: { color: '#9CA3AF' },\r\n          axisLabel: { color: '#9CA3AF' },\r\n          axisLine: { lineStyle: { color: '#4B5563' } },\r\n          splitLine: { lineStyle: { color: '#374151' } }\r\n        },\r\n        yAxis: {\r\n          type: 'category',\r\n\r\n          data: [],\r\n          axisLabel: { color: '#9CA3AF' },\r\n          axisLine: { lineStyle: { color: '#4B5563' } }\r\n        },\r\n        series: [{\r\n          name: '金额 (元)',\r\n          type: 'bar',\r\n          data: []\r\n        }]\r\n      })\r\n    },\r\n\r\n    setInternalCostDetailChartOption() {\r\n      this.charts.internalCostDetailChart.setOption({\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: { type: 'shadow' },\r\n          backgroundColor: 'rgba(30, 41, 59, 0.9)',\r\n          borderColor: '#93C5FD',\r\n          textStyle: { color: '#fff' },\r\n          formatter: function (params) {\r\n            let result = params[0].name + '<br/>';\r\n            params.forEach(function (item) {\r\n              const formattedValue = parseFloat(item.value).toFixed(2).toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\r\n              result += item.marker + ' 金额: ' + formattedValue + '万元<br/>';\r\n            });\r\n            return result;\r\n          }\r\n        },\r\n        grid: { left: '5%', right: '15%', top: '12%', bottom: '12%', containLabel: true },\r\n        xAxis: {\r\n          type: 'value',\r\n          name: '金额（万元）',\r\n          nameTextStyle: { color: '#9CA3AF' },\r\n          axisLabel: { color: '#9CA3AF' },\r\n          axisLine: { lineStyle: { color: '#4B5563' } },\r\n          splitLine: { lineStyle: { color: '#374151' } }\r\n        },\r\n        yAxis: {\r\n          type: 'category',\r\n          data: [],\r\n          axisLabel: { color: '#9CA3AF' },\r\n          axisLine: { lineStyle: { color: '#4B5563' } }\r\n        },\r\n        series: [{\r\n          name: '金额 (元)',\r\n          type: 'bar',\r\n          data: []\r\n        }]\r\n      })\r\n    },\r\n\r\n    setWaterfallChartOption() {\r\n      this.charts.waterfallChart.setOption({\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: { type: 'shadow' },\r\n          backgroundColor: 'rgba(30, 41, 59, 0.9)',\r\n          borderColor: '#93C5FD',\r\n          textStyle: { color: '#fff' },\r\n        },\r\n        grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: ['初始成本', '修磨', '矫直', '探伤', '热处理', '总成本'],\r\n          axisLabel: {\r\n            interval: 0,\r\n            rotate: 0, // 水平显示标签\r\n            align: 'center', // 居中对齐\r\n            color: '#9CA3AF'\r\n          },\r\n          axisLine: { lineStyle: { color: '#4B5563' } }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          name: '金额 (元)',\r\n          nameTextStyle: { color: '#9CA3AF' },\r\n          axisLabel: { color: '#9CA3AF' },\r\n          axisLine: { lineStyle: { color: '#4B5563' } },\r\n          splitLine: { lineStyle: { color: '#374151' } }\r\n        },\r\n        series: [\r\n          {\r\n            name: '辅助',\r\n            type: 'bar',\r\n            stack: '总量',\r\n            itemStyle: {\r\n              color: 'rgba(0,0,0,0)',\r\n              borderColor: 'rgba(0,0,0,0)',\r\n              borderWidth: 0\r\n            },\r\n            emphasis: {\r\n              itemStyle: {\r\n                color: 'rgba(0,0,0,0)'\r\n              }\r\n            },\r\n            data: [0, 0, 50, 80, 105, 0]\r\n          },\r\n        ]\r\n      })\r\n    },\r\n\r\n    setScrapLossChartOption() {\r\n      this.charts.scrapLossChart.setOption({\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: { type: 'shadow' },\r\n          backgroundColor: 'rgba(30, 41, 59, 0.9)',\r\n          borderColor: '#93C5FD',\r\n          textStyle: { color: '#fff' },\r\n        },\r\n        grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: [],\r\n          axisLabel: {\r\n            color: '#9CA3AF',\r\n            interval: 0, // 显示所有标签\r\n            rotate: 0, // 水平显示标签\r\n            align: 'center' // 居中对齐\r\n          },\r\n          axisLine: { lineStyle: { color: '#4B5563' } }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          name: '金额 (元)',\r\n          axisLabel: { color: '#9CA3AF' },\r\n          axisLine: { lineStyle: { color: '#4B5563' } },\r\n          splitLine: { lineStyle: { color: '#374151' } }\r\n        },\r\n        series: [{\r\n          name: '报废损失成本',\r\n          type: 'bar',\r\n          data: []\r\n        }]\r\n      })\r\n    },\r\n\r\n    setQualityObjectionChartOption() {\r\n      this.charts.qualityObjectionChart.setOption({\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: { type: 'shadow' },\r\n          backgroundColor: 'rgba(30, 41, 59, 0.9)',\r\n          borderColor: '#93C5FD',\r\n          textStyle: { color: '#fff' },\r\n        },\r\n        grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: [],\r\n          axisLabel: {\r\n            color: '#9CA3AF',\r\n            interval: 0, // 显示所有标签\r\n            rotate: 0, // 水平显示标签\r\n            align: 'center' // 居中对齐\r\n          },\r\n          axisLine: { lineStyle: { color: '#4B5563' } }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          name: '金额 (元)',\r\n          axisLabel: { color: '#9CA3AF' },\r\n          axisLine: { lineStyle: { color: '#4B5563' } },\r\n          splitLine: { lineStyle: { color: '#374151' } }\r\n        },\r\n        series: [{\r\n          name: '质量异议损失成本',\r\n          type: 'bar',\r\n          data: []\r\n        }]\r\n      })\r\n    },\r\n\r\n    setDualYChartOption() {\r\n      this.charts.dualYChart.setOption({\r\n        color: ['#93C5FD', '#FDE68A'],\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: { type: 'cross' },\r\n          backgroundColor: 'rgba(30, 41, 59, 0.9)',\r\n          borderColor: '#93C5FD',\r\n          textStyle: { color: '#fff' }\r\n        },\r\n        legend: {\r\n          data: ['产量(吨)', '吨钢成本(元)'],\r\n          textStyle: { color: '#E5E7EB' }\r\n        },\r\n        xAxis: [{\r\n          type: 'category',\r\n          data: ['1月', '2月', '3月', '4月', '5月', '6月'],\r\n          axisLabel: {\r\n            color: '#9CA3AF',\r\n            rotate: 0, // 水平显示标签\r\n            align: 'center' // 居中对齐\r\n          },\r\n          axisLine: { lineStyle: { color: '#4B5563' } }\r\n        }],\r\n        yAxis: [\r\n          {\r\n            type: 'value',\r\n            name: '产量(吨)',\r\n            nameTextStyle: { color: '#9CA3AF' },\r\n            axisLabel: { color: '#9CA3AF' },\r\n            axisLine: { lineStyle: { color: '#4B5563' } },\r\n            splitLine: { lineStyle: { color: '#374151' } }\r\n          },\r\n          {\r\n            type: 'value',\r\n            name: '吨钢成本(元)',\r\n            nameTextStyle: { color: '#9CA3AF' },\r\n            axisLabel: { color: '#9CA3AF' },\r\n            axisLine: { lineStyle: { color: '#4B5563' } }\r\n          }\r\n        ],\r\n        series: [\r\n          {\r\n            name: '产量(吨)',\r\n            type: 'bar',\r\n            data: [80000, 82000, 85000, 83000, 88000, 90000],\r\n            itemStyle: { color: '#93C5FD' }\r\n          },\r\n          {\r\n            name: '吨钢成本(元)',\r\n            type: 'line',\r\n            yAxisIndex: 1,\r\n            data: [13.1, 12.8, 12.5, 12.6, 12.2, 12.0],\r\n            lineStyle: { color: '#FDE68A', width: 3 },\r\n            itemStyle: { color: '#FDE68A' },\r\n            symbol: 'circle',\r\n            symbolSize: 8\r\n          }\r\n        ]\r\n      })\r\n    },\r\n\r\n    resizeCharts() {\r\n      Object.values(this.charts).forEach(chart => {\r\n        if (chart) {\r\n          chart.resize()\r\n        }\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.quality-cost-dashboard {\r\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';\r\n  background-color: #111827;\r\n  /* 深色背景 */\r\n  color: #d1d5db;\r\n  /* 浅色文字 */\r\n  margin: 0;\r\n  padding: 24px;\r\n  min-height: 100vh;\r\n}\r\n\r\n.header {\r\n  margin-bottom: 24px;\r\n  text-align: center;\r\n  position: relative;\r\n}\r\n\r\n.header-wrapper {\r\n  display: inline-block;\r\n  position: relative;\r\n}\r\n\r\n.header h1 {\r\n  font-size: 28px;\r\n  color: #f9fafb;\r\n  /* 白色标题 */\r\n  font-weight: 600;\r\n  margin: 0;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.header p {\r\n  font-size: 16px;\r\n  color: #9ca3af;\r\n  /* 中灰色文字 */\r\n  margin: 8px 0 0 0;\r\n}\r\n\r\n.header-filters {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  flex-wrap: nowrap;\r\n  justify-content: flex-start;\r\n  margin-top: 12px;\r\n  margin-left: 950px;\r\n  /* 向左对齐 */\r\n}\r\n\r\n.filter-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.filter-item .label {\r\n  color: #d1d5db;\r\n  /* 浅色标签文字 */\r\n  font-size: 14px;\r\n  white-space: nowrap;\r\n}\r\n\r\n/* 右上角筛选区域的样式 */\r\n.header-filters .el-input__inner,\r\n.header-filters .el-select .el-input__inner {\r\n  background-color: #111827; /* 与页面背景一致的深色 */\r\n  border-color: #374151;\r\n  color: #ffffff; /* 白色字体 */\r\n}\r\n\r\n.header-filters .el-input__inner:focus,\r\n.header-filters .el-select .el-input__inner:focus {\r\n  border-color: #93c5fd;\r\n  background-color: #111827; /* 聚焦时保持背景色 */\r\n}\r\n\r\n.header-filters .el-select-dropdown {\r\n  background-color: #111827; /* 下拉菜单背景与页面一致 */\r\n  border-color: #374151;\r\n}\r\n\r\n.header-filters .el-select-dropdown .el-select-dropdown__item {\r\n  color: #ffffff; /* 下拉选项白色字体 */\r\n  background-color: #111827;\r\n}\r\n\r\n.header-filters .el-select-dropdown .el-select-dropdown__item:hover {\r\n  background-color: #1f2937; /* 悬浮时稍微亮一点 */\r\n  color: #ffffff;\r\n}\r\n\r\n.header-filters .el-select-dropdown .el-select-dropdown__item.selected {\r\n  background-color: #374151; /* 选中项背景 */\r\n  color: #ffffff;\r\n}\r\n\r\n/* 下拉框箭头颜色 */\r\n.header-filters .el-select .el-input__suffix {\r\n  color: #ffffff;\r\n}\r\n\r\n.header-filters .el-select .el-select__caret {\r\n  color: #ffffff;\r\n}\r\n\r\n/* 占位符文字颜色 */\r\n.header-filters .el-input__inner::placeholder {\r\n  color: #9ca3af;\r\n}\r\n\r\n/* 清除按钮颜色 */\r\n.header-filters .el-select .el-select__clear {\r\n  color: #9ca3af;\r\n}\r\n\r\n.header-filters .el-select .el-select__clear:hover {\r\n  color: #ffffff;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 1024px) {\r\n  .header-filters {\r\n    flex-wrap: wrap;\r\n    gap: 8px;\r\n    margin-left: 0;\r\n    justify-content: flex-end;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .header {\r\n    text-align: center;\r\n  }\r\n\r\n  .header-filters {\r\n    flex-direction: column;\r\n    gap: 12px;\r\n    align-items: center;\r\n    margin-left: 0;\r\n    /* 在小屏幕上取消左边距 */\r\n  }\r\n\r\n  .filter-item {\r\n    justify-content: center;\r\n  }\r\n}\r\n\r\n.dashboard-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(4, 1fr); /* 改为四列布局，支持1/4和3/4分配 */\r\n  gap: 24px;\r\n}\r\n\r\n/* 响应式设计：在小屏幕上改为单列 */\r\n@media (max-width: 1200px) {\r\n  .dashboard-grid {\r\n    grid-template-columns: 1fr; /* 小屏幕时改为单列 */\r\n  }\r\n\r\n  /* 小屏幕时重置所有grid-column样式 */\r\n  .dashboard-grid .chart-container {\r\n    grid-column: 1 !important;\r\n  }\r\n}\r\n\r\n.chart-container,\r\n.kpi-card {\r\n  background-color: #1f2937;\r\n  /* 深色卡片背景 */\r\n  border-radius: 8px;\r\n  border: 1px solid #374151;\r\n  /* 边框 */\r\n  box-shadow: none;\r\n  padding: 24px;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.chart-container {\r\n  height: 400px;\r\n}\r\n\r\n/* 大图表样式 - 用于两两排列的图表 */\r\n.chart-container.large-chart {\r\n  height: 500px; /* 增加高度 */\r\n  min-height: 500px;\r\n}\r\n\r\n.chart-container h3 {\r\n  margin-top: 0;\r\n  margin-bottom: 16px;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #f9fafb;\r\n  /* 白色卡片标题 */\r\n}\r\n\r\n.chart {\r\n  width: 100%;\r\n  flex-grow: 1;\r\n}\r\n\r\n.kpi-grid {\r\n  grid-column: 1 / -1;\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\r\n  gap: 24px;\r\n}\r\n\r\n.kpi-card {\r\n  justify-content: space-between;\r\n}\r\n\r\n.kpi-card .title {\r\n  font-size: 14px;\r\n  color: #9ca3af;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.kpi-card .value {\r\n  font-size: 32px;\r\n  font-weight: 700;\r\n  color: #f9fafb;\r\n}\r\n\r\n.kpi-card .comparison {\r\n  display: flex;\r\n  align-items: center;\r\n  font-size: 14px;\r\n  margin-top: 8px;\r\n}\r\n\r\n.kpi-card .comparison .arrow {\r\n  width: 20px;\r\n  height: 20px;\r\n  margin-right: 4px;\r\n  stroke-width: 2.5px;\r\n}\r\n\r\n.kpi-card .comparison .positive {\r\n  color: #34d399;\r\n  /* 亮绿色 */\r\n}\r\n\r\n.kpi-card .comparison .negative {\r\n  color: #f87171;\r\n  /* 亮红色 */\r\n}\r\n</style>\r\n"]}]}