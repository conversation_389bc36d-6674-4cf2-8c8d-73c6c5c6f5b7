<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SysUserMapper">

	<resultMap type="SysUser" id="SysUserResult">
		<id     property="userId"       column="user_id"      />
		<result property="deptId"       column="dept_id"      />
		<result property="rsDeptCode"       column="rs_dept_code"      />
		<result property="rsDeptName"       column="rs_dept_name"      />
		<result property="userName"     column="user_name"    />
		<result property="nickName"     column="nick_name"    />
		<result property="email"        column="email"        />
		<result property="phonenumber"  column="phonenumber"  />
		<result property="sex"          column="sex"          />
		<result property="avatar"       column="avatar"       />
		<result property="idCard"       column="id_card"       />
		<result property="password"     column="password"     />
		<result property="status"       column="status"       />
		<result property="delFlag"      column="del_flag"     />
		<result property="loginIp"      column="login_ip"     />
		<result property="loginDate"    column="login_date"   />
		<result property="createBy"     column="create_by"    />
		<result property="createTime"   column="create_time"  />
		<result property="updateBy"     column="update_by"    />
		<result property="updateTime"   column="update_time"  />
		<result property="remark"       column="remark"       />
		<association property="dept"    column="dept_id" javaType="SysDept" resultMap="deptResult" />
		<collection  property="roles"   javaType="java.util.List"        resultMap="RoleResult" />
	</resultMap>

	<resultMap id="deptResult" type="SysDept">
		<id     property="deptId"   column="dept_id"     />
		<result property="parentId" column="parent_id"   />
		<result property="deptName" column="dept_name"   />
		<result property="orderNum" column="order_num"   />
		<result property="leader"   column="leader"      />
		<result property="status"   column="dept_status" />
	</resultMap>

	<resultMap id="RoleResult" type="SysRole">
		<id     property="roleId"       column="role_id"        />
		<result property="roleName"     column="role_name"      />
		<result property="roleKey"      column="role_key"       />
		<result property="roleSort"     column="role_sort"      />
		<result property="dataScope"     column="data_scope"    />
		<result property="status"       column="role_status"    />
	</resultMap>

	<sql id="selectUserVo">
        select u.user_id, u.dept_id,u.rs_dept_code,u.rs_dept_name, u.user_name, u.nick_name, u.email, u.avatar, u.id_card, u.phonenumber, u.password, u.sex, u.status, u.del_flag, u.login_ip, u.login_date, u.create_by, u.create_time, u.remark,
        d.dept_id, d.parent_id, d.dept_name, d.order_num, d.leader, d.status as dept_status,
        r.role_id, r.role_name, r.role_key, r.role_sort, r.data_scope, r.status as role_status
        from sys_user u
		    left join sys_dept d on u.dept_id = d.dept_id
		    left join sys_user_role ur on u.user_id = ur.user_id
		    left join sys_role r on r.role_id = ur.role_id
    </sql>


    <select id="selectUserList" parameterType="SysUser" resultMap="SysUserResult">
		select u.user_id, u.dept_id,u.rs_dept_code,u.rs_dept_name, u.nick_name, u.user_name, u.email, u.avatar, u.id_card, u.phonenumber, u.password, u.sex, u.status, u.del_flag, u.login_ip, u.login_date, u.create_by, u.create_time, u.remark, d.dept_name, d.leader from sys_user u
		left join sys_dept d on u.dept_id = d.dept_id
		where u.del_flag = '0'
		<if test="userName != null and userName != ''">
			AND u.user_name like concat('%', #{userName}, '%')
		</if>
		<if test="nickName != null and nickName != ''">
			AND u.nick_name like concat('%', #{nickName}, '%')
		</if>
		<if test="status != null and status != ''">
			AND u.status = #{status}
		</if>
		<if test="phonenumber != null and phonenumber != ''">
			AND u.phonenumber like concat('%', #{phonenumber}, '%')
		</if>
		<if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
			AND date_format(u.create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
		</if>
		<if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
			AND date_format(u.create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
		</if>
		<if test="deptId != null and deptId != 0">
			AND (u.dept_id = #{deptId} OR u.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE find_in_set(#{deptId}, ancestors) ))
		</if>
		<!-- 数据范围过滤 -->
		${params.dataScope}
	</select>

	<select id="selectVoteUserList" parameterType="String" resultType="Map">
		select u.dept_id as deptId, u.nick_name as nickName, u.user_name as userName, d.dept_name as deptName from sys_user u
		left join sys_dept d on u.dept_id = d.dept_id
		where u.del_flag = '0'
		<if test="remark != null and remark != ''">
			AND (u.user_name like concat('%', #{remark}, '%') or u.nick_name like concat('%', #{remark}, '%') or d.dept_name like concat('%', #{remark}, '%') )
		</if>
		order by u.nick_name

	</select>

	<select id="selectVoterList" parameterType="String" resultType="String">
		select  concat(u.nick_name,' ', u.user_name) as voter from sys_user u
		where 1=1
		and u.user_name in
		<foreach collection="array" item="item"  open="(" separator="," close=")">
		#{item}
		</foreach>

	</select>


	<select id="selectUserIdsByUserNames" parameterType="String" resultType="Long">
		select  u.user_id from sys_user u
		where 1=1
		and u.user_name in
		<foreach collection="array" item="item"  open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>


	<select id="selectUserByUserName" parameterType="String" resultMap="SysUserResult">
		<include refid="selectUserVo"/>
		where u.user_name = #{userName} and  u.del_flag = '0'
	</select>

	<select id="selectUserByUserNameIncludeDel" parameterType="String" resultMap="SysUserResult">
		<include refid="selectUserVo"/>
		where u.user_name = #{userName}
	</select>

	<select id="selectUserByUserNameList" parameterType="String" resultMap="SysUserResult">
		<include refid="selectUserVo"/>
		where  u.del_flag = '0' and u.user_name in
		<foreach item="userName" collection="userNameList" open="(" separator="," close=")">
			#{userName}
		</foreach>
	</select>

	<select id="selectUserByLikeUserName" parameterType="String" resultMap="SysUserResult">
		<include refid="selectUserVo"/>
		where u.user_name like  concat(concat('%',#{userName}), '%') and  u.del_flag = '0'
	</select>

	<select id="selectUserListByUserName" parameterType="String" resultMap="SysUserResult">
		select u.user_id, u.dept_id,u.rs_dept_code,u.rs_dept_name, u.user_name, u.nick_name, u.email, u.avatar, u.id_card, u.phonenumber, u.password, u.sex, u.status, u.del_flag, u.login_ip, u.login_date, u.create_by, u.create_time, u.remark
        from sys_user u
		where u.user_name like  concat(concat('%',#{userName}), '%') and  u.del_flag = '0'
	</select>

	<select id="selectUserByUserNamePass" parameterType="String" resultMap="SysUserResult">
		select u.user_id, u.dept_id, u.user_name, u.nick_name, u.email, u.avatar, u.phonenumber, u.password, u.sex, u.status, u.del_flag, u.login_ip, u.login_date, u.create_by, u.create_time, u.remark,
        d.dept_id, d.parent_id, d.dept_name, d.order_num, d.leader, d.status as dept_status,
        r.role_id, r.role_name, r.role_key, r.role_sort, r.data_scope, r.status as role_status
        from sys_user u
		    left join sys_dept d on u.dept_id = d.dept_id
		    left join sys_user_role ur on u.user_id = ur.user_id
		    left join sys_role r on r.role_id = ur.role_id
		where u.user_name = #{userName} and  u.del_flag = '0'
	</select>

	<select id="selectUserById" parameterType="Long" resultMap="SysUserResult">
		<include refid="selectUserVo"/>
		where u.user_id = #{userId} and  u.del_flag = '0'
	</select>




	<select id="checkUserNameUnique" parameterType="String" resultType="int">
		select count(1) from sys_user where user_name = #{userName} limit 1
	</select>

	<select id="checkPhoneUnique" parameterType="String" resultMap="SysUserResult">
		select user_id, phonenumber from sys_user where phonenumber = #{phonenumber} limit 1
	</select>

	<select id="checkEmailUnique" parameterType="String" resultMap="SysUserResult">
		select user_id, email from sys_user where email = #{email} limit 1
	</select>


	<insert id="insertUser" parameterType="SysUser" useGeneratedKeys="true" keyProperty="userId">
 		insert into sys_user(
 			<if test="userId != null and userId != 0">user_id,</if>
 			<if test="deptId != null and deptId != 0">dept_id,</if>
			<if test="rsDeptCode != null and rsDeptCode != ''">rs_dept_code,</if>
			<if test="rsDeptName != null and rsDeptName != ''">rs_dept_name,</if>
 			<if test="userName != null and userName != ''">user_name,</if>
 			<if test="nickName != null and nickName != ''">nick_name,</if>
 			<if test="email != null and email != ''">email,</if>
 			<if test="avatar != null and avatar != ''">avatar,</if>
			<if test="idCard != null and idCard != ''">id_card,</if>
 			<if test="phonenumber != null and phonenumber != ''">phonenumber,</if>
 			<if test="sex != null and sex != ''">sex,</if>
 			<if test="password != null and password != ''">password,</if>
 			<if test="status != null and status != ''">status,</if>
 			<if test="createBy != null and createBy != ''">create_by,</if>
 			<if test="remark != null and remark != ''">remark,</if>
 			create_time
 		)values(

 			<if test="userId != null and userId != ''">#{userId},</if>
 			<if test="deptId != null and deptId != ''">#{deptId},</if>
			<if test="rsDeptCode != null and rsDeptCode != ''">#{rsDeptCode},</if>
			<if test="rsDeptName != null and rsDeptName != ''">#{rsDeptName},</if>
 			<if test="userName != null and userName != ''">#{userName},</if>
 			<if test="nickName != null and nickName != ''">#{nickName},</if>
 			<if test="email != null and email != ''">#{email},</if>
 			<if test="avatar != null and avatar != ''">#{avatar},</if>
			<if test="idCard != null and idCard != ''">#{idCard},</if>
 			<if test="phonenumber != null and phonenumber != ''">#{phonenumber},</if>
 			<if test="sex != null and sex != ''">#{sex},</if>
 			<if test="password != null and password != ''">#{password},</if>
 			<if test="status != null and status != ''">#{status},</if>
 			<if test="createBy != null and createBy != ''">#{createBy},</if>
 			<if test="remark != null and remark != ''">#{remark},</if>
 			sysdate()
 		)
	</insert>

	<update id="updateUser" parameterType="SysUser">
 		update sys_user
 		<set>
 			<if test="deptId != null and deptId != 0">dept_id = #{deptId},</if>
			<if test="rsDeptCode != null and rsDeptCode != ''">rs_dept_code = #{rsDeptCode},</if>
			<if test="rsDeptName != null and rsDeptName != ''">rs_dept_name = #{rsDeptName},</if>
 			<if test="userName != null and userName != ''">user_name = #{userName},</if>
 			<if test="nickName != null and nickName != ''">nick_name = #{nickName},</if>
			<if test="signUrl != null and signUrl != ''">sign_url = #{signUrl},</if>
 			<if test="email != null and email != ''">email = #{email},</if>
 			<if test="phonenumber != null and phonenumber != ''">phonenumber = #{phonenumber},</if>
 			<if test="sex != null and sex != ''">sex = #{sex},</if>
 			<if test="avatar != null and avatar != ''">avatar = #{avatar},</if>
			<if test="idCard != null and idCard != ''">id_card = #{idCard},</if>
 			<if test="password != null and password != ''">password = #{password},</if>
 			<if test="status != null and status != ''">status = #{status},</if>
 			<if test="loginIp != null and loginIp != ''">login_ip = #{loginIp},</if>
 			<if test="loginDate != null">login_date = #{loginDate},</if>
 			<if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
 			<if test="remark != null">remark = #{remark},</if>
 			update_time = sysdate()
 		</set>
 		where user_id = #{userId}
	</update>

	<update id="updateUserStatus" parameterType="SysUser">
 		update sys_user set status = #{status} where user_id = #{userId}
	</update>

	<update id="updateUserAvatar" parameterType="SysUser">
 		update sys_user set avatar = #{avatar} where user_name = #{userName}
	</update>

	<update id="resetUserPwd" parameterType="SysUser">
 		update sys_user set password = #{password} where user_name = #{userName}
	</update>

	<delete id="deleteUserById" parameterType="Long">
 		delete from sys_user where user_id = #{userId}
 	</delete>

	<delete id="deleteUser" parameterType="SysUser">
 		update sys_user set del_flag = #{delFlag},remark = #{remark} where user_id = #{userId}
 	</delete>


 	<delete id="deleteUserByIds" parameterType="Long">
 		update sys_user set del_flag = '2' where user_id in
 		<foreach collection="array" item="userId" open="(" separator="," close=")">
 			#{userId}
        </foreach>
 	</delete>


	<select id="selectUserPasInMes" parameterType="String" resultType="String">
       select  t.keyvalue  from  es.tesuserinfo t where  t.ename =  #{username}

    </select>

	<select id="checkInMes" parameterType="Map" resultType="String">
       select xcc1.GET_USER_PASS(#{username}, #{pas}) from dual
    </select>

	<update id="resetLastTime" parameterType="String">
 		update sys_user set last_time = sysdate() where user_name = #{workNo}
	</update>

	<select id="selectApprovalUserList"  parameterType="SysUser" resultMap="SysUserResult">
		SELECT t1.user_name ,t1.nick_name
		from sys_user t1
		where 1=1
		and substring(t1.user_name,1,1)='X'
		and t1.dept_id!='100'
		<if test="remark != null and remark != ''">and (t1.user_name like concat('%',#{remark},'%') or t1.nick_name like concat('%',#{remark},'%'))</if>
		and t1.user_name in(select distinct u.user_name
		from sys_menu m
		left join sys_role_menu rm on m.menu_id = rm.menu_id
		left join sys_user_role ur on rm.role_id = ur.role_id
		left join sys_role r on r.role_id = ur.role_id
		left join sys_user u on u.user_id = ur.user_id
		where m.status = '0' and r.status = '0' and m.is_frame = '1' and m.path = 'fileExamine')

		and t1.user_name != #{userName}
		order by nick_name desc
	</select>

	<select id="selectAllocatedList" parameterType="SysUser" resultMap="SysUserResult">
		select distinct u.user_id, u.dept_id, u.user_name, u.nick_name, u.email, u.phonenumber, u.status, u.create_time
		from sys_user u
		left join sys_dept d on u.dept_id = d.dept_id
		left join sys_user_role ur on u.user_id = ur.user_id
		left join sys_role r on r.role_id = ur.role_id
		where u.del_flag = '0' and r.role_id = #{roleId}
		<if test="userName != null and userName != ''">
			AND u.user_name like concat('%', #{userName}, '%')
		</if>
		<if test="nickName != null and nickName != ''">
			AND u.nick_name like concat('%', #{nickName}, '%')
		</if>
		<if test="phonenumber != null and phonenumber != ''">
			AND u.phonenumber like concat('%', #{phonenumber}, '%')
		</if>
		<!-- 数据范围过滤 -->
		${params.dataScope}
	</select>

	<select id="getListByRoleKey" parameterType="SysUser" resultMap="SysUserResult">
		select distinct u.user_id, u.dept_id, u.user_name, u.nick_name, u.email, u.phonenumber, u.status, u.create_time
		from sys_user u
		left join sys_user_role ur on u.user_id = ur.user_id
		left join sys_role r on r.role_id = ur.role_id
		where u.del_flag = '0'
		<if test="roleKey != null and roleKey != ''">
			AND r.role_key = #{roleKey}
		</if>
		<if test="userName != null and userName != ''">
			AND u.user_name like concat('%', #{userName}, '%')
		</if>
		<if test="nickName != null and nickName != ''">
			AND u.nick_name like concat('%', #{nickName}, '%')
		</if>
		<!-- 数据范围过滤 -->
		${params.dataScope}
	</select>

	<select id="selectUnallocatedList" parameterType="SysUser" resultMap="SysUserResult">
		select distinct u.user_id, u.dept_id, u.user_name, u.nick_name, u.email, u.phonenumber, u.status, u.create_time
		from sys_user u
		left join sys_dept d on u.dept_id = d.dept_id
		left join sys_user_role ur on u.user_id = ur.user_id
		left join sys_role r on r.role_id = ur.role_id
		where u.del_flag = '0' and (r.role_id != #{roleId} or r.role_id IS NULL)
		and u.user_id not in (select u.user_id from sys_user u inner join sys_user_role ur on u.user_id = ur.user_id and ur.role_id = #{roleId})
		<if test="userName != null and userName != ''">
			AND u.user_name like concat('%', #{userName}, '%')
		</if>
		<if test="nickName != null and nickName != ''">
			AND u.nick_name like concat('%', #{nickName}, '%')
		</if>
		<if test="phonenumber != null and phonenumber != ''">
			AND u.phonenumber like concat('%', #{phonenumber}, '%')
		</if>
		<!-- 数据范围过滤 -->
		${params.dataScope}
	</select>
	<select id="selectByRoleKey" parameterType="String" resultMap="SysUserResult">
		select a.* from sys_user a
							inner join sys_user_role b on a.user_id = b.user_id
							inner join sys_role c on c.role_id = b.role_id
		where c.role_key = #{roleKey}
	</select>

	<select id="selectAllUsers" parameterType="String" resultType="Map">
		select a.user_id as userId,a.user_name as userName,a.nick_name as nickName from sys_user a
		where a.del_flag = '0' and a.status != '1' and a.status != '2'
		<if test="info != null and info != ''">
			and (a.nick_name like concat('%', #{info}, '%') or a.user_name like concat('%', #{info}, '%'))
		</if>
		order by a.create_time desc
		limit 50
	</select>

	<select id="selectAllUserNames" resultType="Map">
		select a.user_id as userId,a.user_name as userName,a.rs_dept_code as rsDeptCodeOld,a.rs_dept_name as rsDeptNameOld,
		a.del_flag as delFlag,a.status as status
		from sys_user a
	</select>

	<select id="selectUsersCommon" parameterType="SysUser" resultMap="SysUserResult">
		select u.user_id, u.dept_id,u.rs_dept_code,u.rs_dept_name, u.user_name, u.nick_name, u.create_time
		from sys_user u
		where u.del_flag = '0'
		and u.user_name != 'admin'
		and u.user_name != 'xctg'
		and (u.user_name like 'X%' or u.user_name like 'Y%' or u.user_name like 'Q%' or u.user_name like 'O%' or u.user_name like 'S%' or u.user_name like 'G%'
		or u.user_name like 'T%' or u.user_name like 'J%' or u.user_name like 'L%' or u.user_name like 'H%')
		<if test="userName != null and userName != ''">
			AND (u.user_name like concat('%', #{userName}, '%') or u.nick_name like concat('%', #{userName}, '%'))
		</if>
	</select>

	<select id="selectUsersExcludeSpecial" parameterType="SysUser" resultMap="SysUserResult">
		select u.user_id, u.dept_id,u.rs_dept_code,u.rs_dept_name, u.user_name, u.nick_name, u.create_time
		from sys_user u
		where u.del_flag = '0'
		and u.user_name != 'admin'
		and u.user_name != 'xctg'
		<if test="userName != null and userName != ''">
			AND u.user_name like concat('%', #{userName}, '%')
		</if>
		<if test="nickName != null and nickName != ''">
			AND u.nick_name like concat('%', #{nickName}, '%')
		</if>
	</select>

	<select id="getUsersByRoleKey" parameterType="SysUser" resultMap="SysUserResult">
		select distinct u.user_id, u.dept_id, u.user_name, u.nick_name, u.email, u.phonenumber, u.status, u.create_time
		from sys_user u
		left join sys_user_role ur on u.user_id = ur.user_id
		left join sys_role r on r.role_id = ur.role_id
		where u.del_flag = '0'
		<if test="roleKey != null and roleKey != ''">
			AND r.role_key = #{roleKey}
		</if>
		<if test="searchValue != null and searchValue != ''">
			AND (u.user_name like concat('%', #{searchValue}, '%') or u.nick_name like concat('%', #{searchValue}, '%'))
		</if>
	</select>
	<select id="selectSignUrl" parameterType="String" resultType="String">
		select  t.sign_url  from  sys_user t where  t.user_name =  #{userName}
	</select>
</mapper>
