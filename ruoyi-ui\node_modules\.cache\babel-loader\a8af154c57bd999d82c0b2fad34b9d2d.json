{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\leave\\plan\\edit.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\leave\\plan\\edit.vue", "mtime": 1756084866763}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\babel.config.js", "mtime": 1688548084091}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_department", "require", "_customer", "_material", "_plan", "_auth", "name", "data", "importVisible", "upload", "isUploading", "headers", "Authorization", "getToken", "url", "process", "env", "VUE_APP_BASE_API", "isEdit", "form", "planType", "businessCategory", "measureFlag", "plannedAmount", "sourceCompanyCode", "sourceCompany", "receiveCompanyCode", "receiveCompany", "targetCompanyCode", "targetCompany", "refundCompanyCode", "refundCompany", "contractNo", "plannedReturnTime", "startTime", "endTime", "expireTime", "monitor", "specialManager", "itemType", "reason", "secApproveFlag", "applyImgUrl", "applyFileUrl", "materials", "rules", "required", "message", "trigger", "applyCompany", "materialType", "leaveReason", "departmentOptions", "customerOptions", "departmentLoading", "customerLoading", "materialOptions", "materialLoading", "uploadUrl", "uploadParams", "uploadType", "imgFileList", "fileList", "imgPreviewVisible", "imgPreviewUrl", "departmentMap", "Map", "customerMap", "datePickerOptions", "disabledDate", "time", "currentDate", "Date", "year", "getFullYear", "month", "getMonth", "monthLimit", "getTime", "computed", "isSpecialCondition", "watch", "val", "handler", "syncExpireTime", "created", "applyNo", "$route", "params", "getDetail", "now", "setDate", "getDate", "yyyy", "mm", "String", "padStart", "dd", "concat", "handleAddMaterial", "handlePlanTypeChange", "updateFormRules", "methods", "_this", "detailPlan", "then", "response", "code", "detail", "id", "refundDepartmentCode", "refundDepartment", "addToDepartmentOptions", "addToCustomerOptions", "planReturnTime", "initFileList", "length", "map", "item", "materialId", "materialName", "materialSpec", "planNum", "measureUnit", "remark", "for<PERSON>ach", "addToMaterialOptions", "remoteSearchDepartment", "query", "_this2", "listDepartment", "storeName", "pageNum", "pageSize", "rows", "finally", "remoteSearchCustomer", "_this3", "listCustomer", "customerName", "handleBusinessCategoryChange", "tempRules", "push", "handleDeleteMaterial", "index", "splice", "remoteSearchMaterial", "_this4", "listMaterial", "handleMaterialSelect", "value", "row", "selectedMaterial", "find", "imgFiles", "JSON", "parse", "e", "console", "log", "files", "handleImgSuccess", "file", "imgData", "originalFileName", "stringify", "$message", "error", "handleAnnexFileSuccess", "annexFileData", "handleMaterialFileSuccess", "_this5", "$refs", "clearFiles", "success", "importedMaterials", "$nextTick", "_toConsumableArray2", "default", "msg", "queryMaterialIdByName", "rowIndex", "handleImgPreview", "handleFilePreview", "window", "open", "handleImgRemove", "filter", "handleFileRemove", "handleExceed", "warning", "submitForm", "_this6", "validate", "valid", "apiMethod", "updatePlan", "addPlan", "successMsg", "$tab", "closeOpenPage", "$router", "path", "t", "refresh", "catch", "cancel", "beforeImgUpload", "isImage", "type", "indexOf", "isLt10M", "size", "beforeFileUpload", "isLt20M", "handleUploadError", "err", "status", "getDepartmentName", "dept", "getCustomerName", "customer", "some", "spec", "unit", "handleExportMaterialTemplate", "_this7", "exportMaterialTemplate", "download", "handleImportMaterial", "handleFileUploadProgress", "submitFileForm", "submit"], "sources": ["src/views/leave/plan/edit.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-card class=\"box-card\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <h3>{{ isEdit ? '修改申请' : '新增申请' }}</h3>\r\n      </div>\r\n\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"120px\">\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"计划类型\" prop=\"planType\">\r\n              <el-radio-group v-model=\"form.planType\" @change=\"handlePlanTypeChange\">\r\n                <el-radio :label=\"1\">出厂不返回</el-radio>\r\n                <el-radio :label=\"2\">出厂返回</el-radio>\r\n                <el-radio :label=\"3\">跨区调拨</el-radio>\r\n                <el-radio :label=\"4\">退货申请</el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n          </el-col>\r\n\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"业务类型\" prop=\"businessCategory\">\r\n              <el-radio-group v-model=\"form.businessCategory\" @change=\"handleBusinessCategoryChange\">\r\n                <el-radio v-if=\"form.planType === 1\" :label=\"1\">通用</el-radio>\r\n                <el-radio v-if=\"form.planType === 2\" :label=\"11\">通用</el-radio>\r\n                <el-radio v-if=\"form.planType === 2\" :label=\"12\">委外加工</el-radio>\r\n                <el-radio v-if=\"form.planType === 3\" :label=\"21\">有计划量计量</el-radio>\r\n                <el-radio v-if=\"form.planType === 3\" :label=\"22\">短期</el-radio>\r\n                <el-radio v-if=\"form.planType === 3\" :label=\"23\">钢板（圆钢）</el-radio>\r\n                <el-radio v-if=\"form.planType === 4\" :label=\"31\">通用</el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"是否计量\" prop=\"measureFlag\">\r\n              <el-select v-model=\"form.measureFlag\" placeholder=\"请选择是否计量\" :disabled=\"isSpecialCondition\">\r\n                <el-option label=\"计量\" :value=\"1\"></el-option>\r\n                <el-option label=\"不计量\" :value=\"0\"></el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n\r\n          <el-col :span=\"12\" v-if=\"form.planType === 3 && form.businessCategory === 21\">\r\n            <el-form-item label=\"计划量（吨）\" prop=\"plannedAmount\">\r\n              <el-input-number v-model=\"form.plannedAmount\" :min=\"0\" controls-position=\"right\" placeholder=\"请输入计划量\"></el-input-number>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-row :gutter=\"20\" v-if=\"form.planType === 3 && form.businessCategory === 21\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"开始时间\" prop=\"startTime\">\r\n              <el-tooltip class=\"item\" effect=\"dark\" content=\"开始时间默认为该日期的0时0分0秒\" placement=\"top\">\r\n                <i class=\"el-icon-question\" style=\"margin-left: 2px;\"></i>\r\n              </el-tooltip>\r\n              <el-date-picker\r\n                v-model=\"form.startTime\"\r\n                type=\"date\"\r\n                placeholder=\"选择开始时间\"\r\n                value-format=\"yyyy-MM-dd\"\r\n                :picker-options=\"form.planType === 3 && form.businessCategory === 21 ? datePickerOptions : {}\">\r\n              </el-date-picker>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"结束时间\" prop=\"endTime\">\r\n              <el-tooltip class=\"item\" effect=\"dark\" content=\"结束时间默认为该日期的23时59分59秒\" placement=\"top\">\r\n                <i class=\"el-icon-question\" style=\"margin-left: 2px;\"></i>\r\n              </el-tooltip>\r\n              <el-date-picker\r\n                v-model=\"form.endTime\"\r\n                type=\"date\"\r\n                placeholder=\"选择结束时间\"\r\n                value-format=\"yyyy-MM-dd\"\r\n                :picker-options=\"form.planType === 3 && form.businessCategory === 21 ? datePickerOptions : {}\">\r\n              </el-date-picker>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-row :gutter=\"20\" v-if=\"!(form.planType === 3 && form.businessCategory === 21)\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"有效期\" prop=\"expireTime\">\r\n              <el-tooltip class=\"item\" effect=\"dark\" content=\"有效期默认为该日期的23时59分59秒\" placement=\"top\">\r\n                <i class=\"el-icon-question\" style=\"margin-left: 2px;\"></i>\r\n              </el-tooltip>\r\n              <el-date-picker\r\n                v-model=\"form.expireTime\"\r\n                type=\"date\"\r\n                placeholder=\"选择有效期\"\r\n                value-format=\"yyyy-MM-dd\">\r\n              </el-date-picker>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"申请单位\" prop=\"sourceCompanyCode\">\r\n              <el-select\r\n                v-model=\"form.sourceCompanyCode\"\r\n                placeholder=\"请选择申请单位\"\r\n                filterable\r\n                remote\r\n                reserve-keyword\r\n                :remote-method=\"remoteSearchDepartment\"\r\n                :loading=\"departmentLoading\">\r\n                <template slot=\"selected\">\r\n                  <span>{{ getDepartmentName(form.sourceCompanyCode) }}</span>\r\n                </template>\r\n                <el-option\r\n                  v-for=\"item in departmentOptions\"\r\n                  :key=\"item.id\"\r\n                  :label=\"item.storeName\"\r\n                  :value=\"item.id\">\r\n                </el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"收货单位\" prop=\"receiveCompanyCode\">\r\n              <el-select\r\n                v-model=\"form.receiveCompanyCode\"\r\n                placeholder=\"请选择收货单位\"\r\n                filterable\r\n                remote\r\n                reserve-keyword\r\n                :remote-method=\"form.planType === 3 ? remoteSearchDepartment : remoteSearchCustomer\"\r\n                :loading=\"form.planType === 3 ? departmentLoading : customerLoading\">\r\n                <template slot=\"selected\">\r\n                  <span>{{ form.planType === 3 ? getDepartmentName(form.receiveCompanyCode) : getCustomerName(form.receiveCompanyCode) }}</span>\r\n                </template>\r\n                <el-option\r\n                  v-for=\"item in form.planType === 3 ? departmentOptions : customerOptions\"\r\n                  :key=\"item.id\"\r\n                  :label=\"form.planType === 3 ? item.storeName : item.customerName\"\r\n                  :value=\"item.id\">\r\n                </el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\" v-if=\"form.planType === 2\">\r\n            <el-form-item label=\"返回单位\" prop=\"targetCompanyCode\">\r\n              <el-select\r\n                v-model=\"form.targetCompanyCode\"\r\n                placeholder=\"请选择返回单位\"\r\n                filterable\r\n                remote\r\n                reserve-keyword\r\n                :remote-method=\"remoteSearchDepartment\"\r\n                :loading=\"departmentLoading\">\r\n                <template slot=\"selected\">\r\n                  <span>{{ getDepartmentName(form.targetCompanyCode) }}</span>\r\n                </template>\r\n                <el-option\r\n                  v-for=\"item in departmentOptions\"\r\n                  :key=\"item.id\"\r\n                  :label=\"item.storeName\"\r\n                  :value=\"item.id\">\r\n                </el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n\r\n          <el-col :span=\"12\" v-if=\"form.planType === 4\">\r\n            <el-form-item label=\"退货单位\" prop=\"refundCompanyCode\">\r\n              <el-select\r\n                v-model=\"form.refundCompanyCode\"\r\n                placeholder=\"请选择退货单位\"\r\n                filterable\r\n                remote\r\n                reserve-keyword\r\n                :remote-method=\"remoteSearchDepartment\"\r\n                :loading=\"departmentLoading\">\r\n                <template slot=\"selected\">\r\n                  <span>{{ getDepartmentName(form.refundCompanyCode) }}</span>\r\n                </template>\r\n                <el-option\r\n                  v-for=\"item in departmentOptions\"\r\n                  :key=\"item.id\"\r\n                  :label=\"item.storeName\"\r\n                  :value=\"item.id\">\r\n                </el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\" v-if=\"form.planType === 2\">\r\n            <el-form-item label=\"计划返回时间\" prop=\"plannedReturnTime\">\r\n              <el-date-picker\r\n                v-model=\"form.plannedReturnTime\"\r\n                type=\"date\"\r\n                placeholder=\"选择计划返回时间\"\r\n                value-format=\"yyyy-MM-dd\"\r\n                :picker-options=\"{\r\n                  disabledDate(time) {\r\n                    return time.getTime() < Date.now() - 8.64e7;\r\n                  }\r\n                }\">\r\n              </el-date-picker>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"监装人\" prop=\"monitor\">\r\n              <el-input v-model=\"form.monitor\" placeholder=\"请输入监装人\"></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"物资专管员\" prop=\"specialManager\">\r\n              <el-input v-model=\"form.specialManager\" placeholder=\"请输入物资专管员\"></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"物资类型\" prop=\"itemType\">\r\n              <el-select v-model=\"form.itemType\" placeholder=\"请选择物资类型\">\r\n                <el-option label=\"钢材\" :value=\"1\"></el-option>\r\n                <el-option label=\"钢板\" :value=\"2\"></el-option>\r\n                <el-option label=\"其他\" :value=\"3\"></el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"是否复审\" prop=\"secApproveFlag\">\r\n              <el-select v-model=\"form.secApproveFlag\" placeholder=\"请选择是否复审\">\r\n                <el-option label=\"是\" :value=\"1\"></el-option>\r\n                <el-option label=\"否\" :value=\"0\"></el-option>\r\n              </el-select>\r\n              <div class=\"tip-text\">在写申请单时，一般是不需要复审的，但一些物资比较特殊或者贵重等原因，必须经过更高领导审核(签字)，因此请申请人注意选择</div>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"出厂原因\" prop=\"reason\">\r\n              <el-input v-model=\"form.reason\" placeholder=\"请输入出厂原因\"></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"合同号\" prop=\"contractNo\">\r\n              <el-input v-model=\"form.contractNo\" placeholder=\"请输入合同号\"></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <!-- 图片上传 -->\r\n        <el-form-item label=\"申请图片\">\r\n          <el-upload\r\n            class=\"upload-demo\"\r\n            :action=\"uploadUrl\"\r\n            list-type=\"picture-card\"\r\n            :file-list=\"imgFileList\"\r\n            :limit=\"3\"\r\n            :on-preview=\"handleImgPreview\"\r\n            :on-remove=\"handleImgRemove\"\r\n            :on-success=\"handleImgSuccess\"\r\n            :on-error=\"handleUploadError\"\r\n            :before-upload=\"beforeImgUpload\"\r\n            :on-exceed=\"handleExceed\"\r\n            :data=\"uploadParams\"\r\n            multiple>\r\n            <i class=\"el-icon-plus\"></i>\r\n            <div slot=\"tip\" class=\"el-upload__tip\">只能上传jpg/png文件，且不超过10MB</div>\r\n          </el-upload>\r\n          <el-dialog :visible.sync=\"imgPreviewVisible\">\r\n            <img width=\"100%\" :src=\"imgPreviewUrl\" alt=\"\">\r\n          </el-dialog>\r\n        </el-form-item>\r\n\r\n        <!-- 附件上传 -->\r\n        <el-form-item label=\"申请附件\">\r\n          <el-upload\r\n            class=\"upload-demo\"\r\n            :action=\"uploadUrl\"\r\n            :file-list=\"fileList\"\r\n            :limit=\"3\"\r\n            :on-preview=\"handleFilePreview\"\r\n            :on-remove=\"handleFileRemove\"\r\n            :on-success=\"handleAnnexFileSuccess\"\r\n            :on-error=\"handleUploadError\"\r\n            :before-upload=\"beforeFileUpload\"\r\n            :on-exceed=\"handleExceed\"\r\n            :data=\"uploadParams\">\r\n            <el-button size=\"small\" type=\"primary\">点击上传</el-button>\r\n            <div slot=\"tip\" class=\"el-upload__tip\">可上传任意类型文件，且不超过20MB</div>\r\n          </el-upload>\r\n        </el-form-item>\r\n\r\n        <!-- 物资列表 -->\r\n        <el-card class=\"material-card\" shadow=\"hover\">\r\n          <div slot=\"header\" class=\"clearfix\">\r\n            <span>物资列表</span>\r\n            <div class=\"material-btn-group\">\r\n              <el-button size=\"small\" type=\"primary\" plain icon=\"el-icon-download\" @click=\"handleExportMaterialTemplate\">导出物资模板</el-button>\r\n              <el-button size=\"small\" type=\"success\" plain icon=\"el-icon-upload2\" @click=\"handleImportMaterial\">导入物资</el-button>\r\n              <el-button size=\"small\" type=\"warning\" plain icon=\"el-icon-plus\" @click=\"handleAddMaterial\">添加物资</el-button>\r\n            </div>\r\n          </div>\r\n\r\n          <el-table\r\n            :data=\"form.materials\"\r\n            style=\"width: 100%\"\r\n            border>\r\n            <el-table-column\r\n              type=\"index\"\r\n              width=\"50\"\r\n              label=\"序号\">\r\n            </el-table-column>\r\n            \r\n            <el-table-column\r\n              prop=\"materialName\"\r\n              label=\"物资名称\"\r\n              width=\"180\">\r\n              <template slot-scope=\"scope\">\r\n                <el-select\r\n                  v-model=\"scope.row.materialId\"\r\n                  placeholder=\"请输入物资名称\"\r\n                  filterable\r\n                  remote\r\n                  reserve-keyword\r\n                  :remote-method=\"remoteSearchMaterial\"\r\n                  :loading=\"materialLoading\"\r\n                  @change=\"(value) => handleMaterialSelect(value, scope.row)\">\r\n                  <el-option\r\n                    v-for=\"item in materialOptions\"\r\n                    :key=\"item.id\"\r\n                    :label=\"item.materialName\"\r\n                    :value=\"item.id\">\r\n                  </el-option>\r\n                </el-select>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column\r\n              prop=\"materialSpec\"\r\n              label=\"物资型号规格\"\r\n              width=\"180\">\r\n              <template slot-scope=\"scope\">\r\n                <el-input v-model=\"scope.row.materialSpec\" placeholder=\"请输入物资型号规格\"></el-input>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column\r\n              prop=\"planNum\"\r\n              label=\"计划数量\"\r\n              width=\"180\">\r\n              <template slot-scope=\"scope\">\r\n                <el-input-number v-model=\"scope.row.planNum\" :min=\"0\" controls-position=\"right\" placeholder=\"请输入计划数量\"></el-input-number>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column\r\n              prop=\"measureUnit\"\r\n              label=\"计量单位\"\r\n              width=\"120\">\r\n              <template slot-scope=\"scope\">\r\n                <el-input v-model=\"scope.row.measureUnit\" placeholder=\"请输入计量单位\"></el-input>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column\r\n              prop=\"remark\"\r\n              label=\"备注\">\r\n              <template slot-scope=\"scope\">\r\n                <el-input v-model=\"scope.row.remark\" placeholder=\"请输入备注\"></el-input>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"操作\"\r\n              width=\"120\">\r\n              <template slot-scope=\"scope\">\r\n                <el-button\r\n                  size=\"mini\"\r\n                  type=\"danger\"\r\n                  icon=\"el-icon-delete\"\r\n                  @click=\"handleDeleteMaterial(scope.$index)\">删除</el-button>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </el-card>\r\n\r\n        <div class=\"form-footer\">\r\n          <el-button type=\"primary\" @click=\"submitForm\">{{ isEdit ? '修 改' : '确 定' }}</el-button>\r\n          <el-button @click=\"cancel\">取 消</el-button>\r\n        </div>\r\n      </el-form>\r\n    </el-card>\r\n    \r\n    <!-- 导入物资对话框 -->\r\n    <el-dialog :title=\"'导入物资'\" :visible.sync=\"importVisible\" width=\"400px\" append-to-body>\r\n      <el-upload\r\n        ref=\"upload\"\r\n        :limit=\"1\"\r\n        accept=\".xlsx, .xls\"\r\n        :headers=\"upload.headers\"\r\n        :action=\"upload.url\"\r\n        :disabled=\"upload.isUploading\"\r\n        :on-progress=\"handleFileUploadProgress\"\r\n        :on-success=\"handleMaterialFileSuccess\"\r\n        :auto-upload=\"false\"\r\n        drag>\r\n        <i class=\"el-icon-upload\"></i>\r\n        <div class=\"el-upload__text\">将物资Excel文件拖到此处，或<em>点击上传</em></div>\r\n        <div class=\"el-upload__tip\" slot=\"tip\">只能上传xlsx/xls文件，且不超过5MB</div>\r\n      </el-upload>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitFileForm\">确 定</el-button>\r\n        <el-button @click=\"importVisible = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listDepartment } from \"@/api/leave/department\";\r\nimport { listCustomer } from \"@/api/leave/customer\";\r\nimport { listMaterial } from \"@/api/leave/material\";\r\nimport { addPlan, updatePlan, detailPlan, exportMaterialTemplate, importMaterialList } from \"@/api/leave/plan\";\r\nimport { getToken } from \"@/utils/auth\";\r\n\r\nexport default {\r\n  name: \"EditLeavePlan\",\r\n  data() {\r\n    return {\r\n      // 导入参数\r\n      importVisible: false,\r\n      // 上传参数\r\n      upload: {\r\n        // 是否禁用上传\r\n        isUploading: false,\r\n        // 设置上传的请求头部\r\n        headers: { Authorization: \"Bearer \" + getToken() },\r\n        // 上传的地址\r\n        url: process.env.VUE_APP_BASE_API + \"/web/leave/plan/importMaterialList\"\r\n      },\r\n      // 是否为编辑模式\r\n      isEdit: false,\r\n      // 表单参数\r\n      form: {\r\n        planType: 1,\r\n        businessCategory: 1,\r\n        measureFlag: 1,\r\n        plannedAmount: 0,\r\n        sourceCompanyCode: \"\",\r\n        sourceCompany: \"\",\r\n        receiveCompanyCode: \"\",\r\n        receiveCompany: \"\",\r\n        targetCompanyCode: \"\",\r\n        targetCompany: \"\",\r\n        refundCompanyCode: \"\",\r\n        refundCompany: \"\",\r\n        contractNo: \"\",\r\n        plannedReturnTime: \"\",\r\n        startTime: \"\",\r\n        endTime: \"\",\r\n        expireTime: \"\", // 有效期\r\n        monitor: \"\",\r\n        specialManager: \"\",\r\n        itemType: 1,\r\n        reason: \"\",\r\n        secApproveFlag: 0,\r\n        applyImgUrl: \"\", // 申请图片URL\r\n        applyFileUrl: \"\", // 申请文件URL\r\n        materials: []\r\n      },\r\n      // 表单校验规则\r\n      rules: {\r\n        planType: [\r\n          { required: true, message: \"请选择计划类型\", trigger: \"change\" }\r\n        ],\r\n        businessCategory: [\r\n          { required: true, message: \"请选择业务类型\", trigger: \"change\" }\r\n        ],\r\n        measureFlag: [\r\n          { required: true, message: \"请选择是否计量\", trigger: \"change\" }\r\n        ],\r\n        applyCompany: [\r\n          { required: true, message: \"请选择申请单位\", trigger: \"change\" }\r\n        ],\r\n        receiveCompany: [\r\n          { required: true, message: \"请选择收货单位\", trigger: \"change\" }\r\n        ],\r\n        targetCompany: [\r\n          { required: true, message: \"请选择返回单位\", trigger: \"change\" }\r\n        ],\r\n        monitor: [\r\n          { required: true, message: \"请输入监装人\", trigger: \"blur\" }\r\n        ],\r\n        specialManager: [\r\n          { required: true, message: \"请输入物资专管员\", trigger: \"blur\" }\r\n        ],\r\n        materialType: [\r\n          { required: true, message: \"请选择物资类型\", trigger: \"change\" }\r\n        ],\r\n        leaveReason: [\r\n          { required: true, message: \"请输入出厂原因\", trigger: \"blur\" }\r\n        ],\r\n        plannedReturnTime: [\r\n          { required: true, message: \"请选择计划返回时间\", trigger: \"change\" }\r\n        ]\r\n      },\r\n      // 单位下拉选项\r\n      departmentOptions: [],\r\n      customerOptions: [],\r\n      departmentLoading: false,\r\n      customerLoading: false,\r\n      // 物资下拉选项\r\n      materialOptions: [],\r\n      materialLoading: false,\r\n      \r\n      // 上传相关\r\n      uploadUrl: process.env.VUE_APP_BASE_API + \"/common/uploadMinio\",\r\n      uploadParams: {\r\n        // 上传时可能需要的额外参数\r\n        uploadType: 'leavePlan'\r\n      },\r\n      imgFileList: [], // 图片文件列表\r\n      fileList: [], // 文件列表\r\n      imgPreviewVisible: false, // 图片预览对话框可见性\r\n      imgPreviewUrl: \"\", // 图片预览URL\r\n      \r\n      // 部门和客户选项关联映射\r\n      departmentMap: new Map(), // 部门id到名称的映射\r\n      customerMap: new Map(), // 客户id到名称的映射\r\n      // 添加日期限制对象\r\n      datePickerOptions: {\r\n        disabledDate(time) {\r\n          // 获取当前月份的25号日期\r\n          const currentDate = new Date();\r\n          const year = currentDate.getFullYear();\r\n          const month = currentDate.getMonth();\r\n          const monthLimit = new Date(year, month, 25, 23, 59, 59);\r\n          \r\n          // 禁用超过当月25号的日期\r\n          return time.getTime() > monthLimit.getTime();\r\n        }\r\n      },\r\n    };\r\n  },\r\n  computed: {\r\n    // 特殊条件：跨区调拨且有计划量计量时\r\n    isSpecialCondition() {\r\n      return this.form.planType === 3 && this.form.businessCategory === 21;\r\n    }\r\n  },\r\n  watch: {\r\n    // 监控特殊条件变化，自动设置是否计量为\"计量\"\r\n    isSpecialCondition(val) {\r\n      if (val) {\r\n        this.form.measureFlag = 1;\r\n      }\r\n    },\r\n    // 计划类型、业务类型、计划结束时间变化时，同步有效期\r\n    'form.planType': {\r\n    handler(val) {\r\n      this.syncExpireTime();\r\n    }\r\n  },\r\n  'form.businessCategory': {\r\n    handler(val) {\r\n      this.syncExpireTime();\r\n    }\r\n  },\r\n  'form.endTime': {\r\n    handler(val) {\r\n      this.syncExpireTime();\r\n    }\r\n  }\r\n  },\r\n  created() {\r\n    // 获取路由参数中的applyNo，判断是新增还是编辑\r\n    const applyNo = this.$route.params.applyNo;\r\n    if (applyNo) {\r\n      this.isEdit = true;\r\n      this.getDetail(applyNo);\r\n    } else {\r\n       // 新增时，设置有效期为当前日期+3天 23:59:59\r\n      const now = new Date();\r\n      now.setDate(now.getDate() + 3);\r\n      const yyyy = now.getFullYear();\r\n      const mm = String(now.getMonth() + 1).padStart(2, '0');\r\n      const dd = String(now.getDate()).padStart(2, '0');\r\n      this.form.expireTime = `${yyyy}-${mm}-${dd}`; \r\n      // 初始化一行物资数据\r\n      this.handleAddMaterial();\r\n      // 根据选中的计划类型设置默认业务类型\r\n      this.handlePlanTypeChange(this.form.planType);\r\n      // 初始化表单校验规则\r\n      this.updateFormRules();\r\n    }\r\n  },\r\n  methods: {\r\n    // 同步有效期\r\n    syncExpireTime() {\r\n    if (this.form.planType === 3 && this.form.businessCategory === 21) {\r\n      this.form.expireTime = this.form.endTime;\r\n      }else if (!this.form.expireTime) {\r\n      // 只在有效期为空时设置默认值（3天）\r\n      const now = new Date();\r\n      now.setDate(now.getDate() + 3);\r\n      const yyyy = now.getFullYear();\r\n      const mm = String(now.getMonth() + 1).padStart(2, '0');\r\n      const dd = String(now.getDate()).padStart(2, '0');\r\n      this.form.expireTime = `${yyyy}-${mm}-${dd}`;\r\n    }\r\n    },    \r\n    // 获取申请详情\r\n    getDetail(applyNo) {\r\n      detailPlan(applyNo).then(response => {\r\n        if (response.code === 200) {\r\n          const detail = response.data;\r\n          \r\n          // 填充基本信息\r\n          this.form.id = detail.id;\r\n          this.form.applyNo = detail.applyNo;\r\n          this.form.planType = detail.planType;\r\n          this.form.businessCategory = detail.businessCategory;\r\n          this.form.measureFlag = detail.measureFlag;\r\n          this.form.plannedAmount = detail.plannedAmount || 0;\r\n          \r\n          // 保存单位代码和名称\r\n          this.form.sourceCompanyCode = detail.sourceCompanyCode;\r\n          this.form.sourceCompany = detail.sourceCompany;\r\n          this.form.receiveCompanyCode = detail.receiveCompanyCode;\r\n          this.form.receiveCompany = detail.receiveCompany;\r\n          this.form.targetCompanyCode = detail.targetCompanyCode;\r\n          this.form.targetCompany = detail.targetCompany;\r\n          this.form.refundCompanyCode = detail.refundDepartmentCode;\r\n          this.form.refundCompany = detail.refundDepartment;\r\n          \r\n          // 手动添加已存在的部门和客户到选项数组，以便正确显示选中的值\r\n          // 添加申请单位到部门选项\r\n          if (detail.sourceCompanyCode && detail.sourceCompany) {\r\n            this.addToDepartmentOptions(detail.sourceCompanyCode, detail.sourceCompany);\r\n          }\r\n          \r\n          // 添加收货单位（可能是部门或客户）\r\n          if (detail.receiveCompanyCode && detail.receiveCompany) {\r\n            if (detail.planType === 3) {\r\n              // 跨区调拨时，收货单位是部门\r\n              this.addToDepartmentOptions(detail.receiveCompanyCode, detail.receiveCompany);\r\n            } else {\r\n              // 其他情况，收货单位是客户\r\n              this.addToCustomerOptions(detail.receiveCompanyCode, detail.receiveCompany);\r\n            }\r\n          }\r\n          \r\n          // 添加返回单位到部门选项\r\n          if (detail.targetCompanyCode && detail.targetCompany) {\r\n            this.addToDepartmentOptions(detail.targetCompanyCode, detail.targetCompany);\r\n          }\r\n          \r\n          // 添加退货单位到部门选项\r\n          if (detail.refundDepartmentCode && detail.refundDepartment) {\r\n            this.addToDepartmentOptions(detail.refundDepartmentCode, detail.refundDepartment);\r\n          }\r\n          \r\n          this.form.plannedReturnTime = detail.planReturnTime;\r\n          this.form.startTime = detail.startTime;\r\n          this.form.endTime = detail.endTime;\r\n          this.form.monitor = detail.monitor;\r\n          this.form.specialManager = detail.specialManager;\r\n          this.form.itemType = detail.itemType;\r\n          this.form.reason = detail.reason;\r\n          this.form.contractNo = detail.contractNo;\r\n          this.form.secApproveFlag = detail.secApproveFlag;\r\n          this.form.applyImgUrl = detail.applyImgUrl;\r\n          this.form.applyFileUrl = detail.applyFileUrl;\r\n          \r\n          // 处理图片和文件列表\r\n          this.initFileList();\r\n          \r\n          // 填充物资列表\r\n          if (detail.materials && detail.materials.length > 0) {\r\n            this.form.materials = detail.materials.map(item => ({\r\n              id: item.id,\r\n              materialId: item.materialId,\r\n              materialName: item.materialName,\r\n              materialSpec: item.materialSpec,\r\n              planNum: item.planNum,\r\n              measureUnit: item.measureUnit,\r\n              remark: item.remark\r\n            }));\r\n            \r\n            // 添加物资到选项数组\r\n            detail.materials.forEach(item => {\r\n              if (item.materialId && item.materialName) {\r\n                this.addToMaterialOptions(item.materialId, item.materialName, item.materialSpec, item.measureUnit);\r\n              }\r\n            });\r\n          } else {\r\n            this.handleAddMaterial();\r\n          }\r\n          \r\n          // 更新表单校验规则\r\n          this.updateFormRules();\r\n        }\r\n      });\r\n    },\r\n    // 远程搜索部门\r\n    remoteSearchDepartment(query) {\r\n      if (query !== '') {\r\n        this.departmentLoading = true;\r\n        listDepartment({\r\n          storeName: query,\r\n          pageNum: 1,\r\n          pageSize: 20\r\n        }).then(response => {\r\n          this.departmentLoading = false;\r\n          if (response.code === 200) {\r\n            this.departmentOptions = response.rows;\r\n          }\r\n        }).finally(() => {\r\n          this.departmentLoading = false;\r\n        });\r\n      } else {\r\n        this.departmentOptions = [];\r\n      }\r\n    },\r\n\r\n    // 远程搜索客户\r\n    remoteSearchCustomer(query) {\r\n      if (query !== '') {\r\n        this.customerLoading = true;\r\n        listCustomer({\r\n          customerName: query,\r\n          pageNum: 1,\r\n          pageSize: 20\r\n        }).then(response => {\r\n          this.customerLoading = false;\r\n          if (response.code === 200) {\r\n            this.customerOptions = response.rows;\r\n          }\r\n        }).finally(() => {\r\n          this.customerLoading = false;\r\n        });\r\n      } else {\r\n        this.customerOptions = [];\r\n      }\r\n    },\r\n\r\n    // 业务类型变更时触发\r\n    handleBusinessCategoryChange() {\r\n      // 更新表单校验规则\r\n      this.updateFormRules();\r\n    },\r\n\r\n    // 计划类型变更时触发\r\n    handlePlanTypeChange(val) {\r\n      // 根据计划类型设置默认业务类型\r\n      switch (val) {\r\n        case 1:\r\n          this.form.businessCategory = 1;\r\n          break;\r\n        case 2:\r\n          this.form.businessCategory = 11;\r\n          break;\r\n        case 3:\r\n          this.form.businessCategory = 21;\r\n          break;\r\n        case 4:\r\n          this.form.businessCategory = 31;\r\n          break;\r\n        default:\r\n          break;\r\n      }\r\n\r\n      // 重置不需要的字段\r\n      if (val !== 2) {\r\n        this.form.targetCompany = \"\";\r\n        this.form.plannedReturnTime = \"\";\r\n      }\r\n\r\n      if (val !== 4) {\r\n        this.form.refundCompany = \"\";\r\n      }\r\n\r\n      if (val !== 3 || this.form.businessCategory !== 21) {\r\n        this.form.startTime = \"\";\r\n        this.form.endTime = \"\";\r\n      }\r\n\r\n      // 更新表单校验规则\r\n      this.updateFormRules();\r\n    },\r\n\r\n    // 更新表单校验规则\r\n    updateFormRules() {\r\n      const tempRules = {\r\n        planType: [\r\n          { required: true, message: \"请选择计划类型\", trigger: \"change\" }\r\n        ],\r\n        businessCategory: [\r\n          { required: true, message: \"请选择业务类型\", trigger: \"change\" }\r\n        ],\r\n        measureFlag: [\r\n          { required: true, message: \"请选择是否计量\", trigger: \"change\" }\r\n        ],\r\n        applyCompany: [\r\n          { required: true, message: \"请选择申请单位\", trigger: \"change\" }\r\n        ],\r\n        monitor: [\r\n          { required: true, message: \"请输入监装人\", trigger: \"blur\" }\r\n        ],\r\n        specialManager: [\r\n          { required: true, message: \"请输入物资专管员\", trigger: \"blur\" }\r\n        ],\r\n        materialType: [\r\n          { required: true, message: \"请选择物资类型\", trigger: \"change\" }\r\n        ]\r\n      };\r\n\r\n      // 根据条件添加校验规则\r\n      if (this.form.planType !== 1) {\r\n        tempRules.receiveCompany = [\r\n          { required: true, message: \"请选择收货单位\", trigger: \"change\" }\r\n        ];\r\n      }\r\n\r\n      if (this.form.planType === 2) {\r\n        tempRules.targetCompany = [\r\n          { required: true, message: \"请选择返回单位\", trigger: \"change\" }\r\n        ];\r\n        tempRules.plannedReturnTime = [\r\n          { required: true, message: \"请选择计划返回时间\", trigger: \"change\" }\r\n        ];\r\n      }\r\n\r\n      if (this.form.planType === 4) {\r\n        tempRules.refundCompany = [\r\n          { required: true, message: \"请选择退货单位\", trigger: \"change\" }\r\n        ];\r\n      }\r\n\r\n      if (this.form.planType === 3 && this.form.businessCategory === 21) {\r\n        tempRules.plannedAmount = [\r\n          { required: true, message: \"请输入计划量\", trigger: \"blur\" }\r\n        ];\r\n        tempRules.startTime = [\r\n          { required: true, message: \"请选择开始时间\", trigger: \"change\" }\r\n        ];\r\n        tempRules.endTime = [\r\n          { required: true, message: \"请选择结束时间\", trigger: \"change\" }\r\n        ];\r\n      }\r\n\r\n      // 更新规则\r\n      this.rules = tempRules;\r\n    },\r\n\r\n    // 添加物资\r\n    handleAddMaterial() {\r\n      this.form.materials.push({\r\n        materialId: \"\",\r\n        materialName: \"\",\r\n        materialSpec: \"\",\r\n        planNum: 1,\r\n        measureUnit: \"\",\r\n        remark: \"\"\r\n      });\r\n    },\r\n    // 删除物资\r\n    handleDeleteMaterial(index) {\r\n      this.form.materials.splice(index, 1);\r\n      // 如果删完了，至少保留一行\r\n      if (this.form.materials.length === 0) {\r\n        this.handleAddMaterial();\r\n      }\r\n    },\r\n    // 远程搜索物资\r\n    remoteSearchMaterial(query) {\r\n      if (query !== '') {\r\n        this.materialLoading = true;\r\n        listMaterial({\r\n          materialName: query,\r\n          pageNum: 1,\r\n          pageSize: 20\r\n        }).then(response => {\r\n          this.materialLoading = false;\r\n          if (response.code === 200) {\r\n            this.materialOptions = response.rows;\r\n          }\r\n        }).finally(() => {\r\n          this.materialLoading = false;\r\n        });\r\n      } else {\r\n        this.materialOptions = [];\r\n      }\r\n    },\r\n    // 处理物资选择\r\n    handleMaterialSelect(value, row) {\r\n      // 根据选中的物资ID找到对应的物资详情\r\n      const selectedMaterial = this.materialOptions.find(item => item.id === value);\r\n      if (selectedMaterial) {\r\n        row.materialName = selectedMaterial.materialName;\r\n        // 如果物资信息中有型号规格和单位，可以自动填充\r\n        if (selectedMaterial.materialSpec) {\r\n          row.materialSpec = selectedMaterial.materialSpec;\r\n        }\r\n        if (selectedMaterial.measureUnit) {\r\n          row.measureUnit = selectedMaterial.measureUnit;\r\n        }\r\n      }\r\n    },\r\n    // 初始化文件列表\r\n    initFileList() {\r\n      // 处理图片\r\n      if (this.form.applyImgUrl) {\r\n        try {\r\n          // 尝试解析JSON\r\n          const imgFiles = JSON.parse(this.form.applyImgUrl);\r\n          this.imgFileList = imgFiles.map(item => {\r\n            return {\r\n              name: item.name,\r\n              url: item.url\r\n            };\r\n          });\r\n        } catch (e) {\r\n          console.log(e);\r\n        }\r\n      }\r\n      \r\n      // 处理文件\r\n      if (this.form.applyFileUrl) {\r\n        try {\r\n          // 尝试解析JSON\r\n          const files = JSON.parse(this.form.applyFileUrl);\r\n          this.fileList = files.map(item => {\r\n            return {\r\n              name: item.name,\r\n              url: item.url,\r\n            };\r\n          });\r\n        } catch (e) {\r\n          console.log(e);\r\n        }\r\n      }\r\n    },\r\n    \r\n    // 图片上传成功\r\n    handleImgSuccess(response, file, fileList) {\r\n      console.log(response);\r\n      console.log(file);\r\n      console.log(fileList);\r\n      if (response.code === 200) {\r\n        // 更新图片URL到表单\r\n        // this.imgFileList = fileList;\r\n        \r\n        // 构建JSON格式的图片数据\r\n        /**\r\n         * response格式：\r\n         * {\r\n    \"msg\": \"操作成功\",\r\n    \"originalFileName\": \"006r3PQBjw1f8p3zb1wioj30c80c8jru.jpg\",\r\n    \"fileName\": \"xctg/2025/03/25/da2a4a3f-6962-4831-a4e5-6ecc2397e483.jpg\",\r\n    \"code\": 200,\r\n    \"url\": \"https://ydxt.citicsteel.com:8099/minio/xctg/2025/03/25/da2a4a3f-6962-4831-a4e5-6ecc2397e483.jpg\"\r\n}\r\n         */\r\n\r\n        \r\n        // 只获取originalFileName、url 转换为JSON字符串保存\r\n        const imgData = {\r\n          name: response.originalFileName,\r\n          url: response.url\r\n        };\r\n        this.imgFileList.push(imgData);\r\n        console.log(this.imgFileList);\r\n        this.form.applyImgUrl = JSON.stringify(this.imgFileList);\r\n        console.log(this.form.applyImgUrl);\r\n      } else {\r\n        this.$message.error('图片上传失败');\r\n      }\r\n    },\r\n    \r\n    // 附件上传成功\r\n    handleAnnexFileSuccess(response, file, fileList) {\r\n      console.log(response);\r\n      console.log(file);\r\n      console.log(fileList);\r\n      if (response.code === 200) {\r\n        // 只获取originalFileName、url 转换为JSON字符串保存\r\n        const annexFileData = {\r\n          name: response.originalFileName,\r\n          url: response.url\r\n        };\r\n        this.fileList.push(annexFileData);\r\n        console.log(this.fileList);\r\n        this.form.applyFileUrl = JSON.stringify(this.fileList);\r\n        console.log(this.form.applyFileUrl);\r\n      } else {\r\n        this.$message.error('附件上传失败');\r\n      }\r\n    },\r\n\r\n    // 物资导入文件上传成功处理\r\n    handleMaterialFileSuccess(response, file, fileList) {\r\n      this.upload.isUploading = false;\r\n      this.$refs.upload.clearFiles();\r\n      this.importVisible = false;\r\n      \r\n      if (response.code === 200) {\r\n        this.$message.success(\"导入成功\");\r\n        // 将导入的物资列表添加到当前表单的物资列表中\r\n        if (response.data && response.data.length > 0) {\r\n          // 追加新导入的物资\r\n          const importedMaterials = response.data.map(item => {\r\n            // 如果没有materialId，使用-1作为默认ID，并确保该物资能显示\r\n            const materialId = item.materialId || -1;\r\n            // 物资名称必须有值\r\n            const materialName = item.materialName || \"未知物资\";\r\n            \r\n            // 如果是默认ID的物资，则添加到选项中\r\n            if (materialId === -1) {\r\n              this.addToMaterialOptions(\r\n                materialId,\r\n                materialName,\r\n                item.materialSpec || \"\",\r\n                item.measureUnit || \"\"\r\n              );\r\n            }\r\n            \r\n            return {\r\n              materialId: materialId,\r\n              materialName: materialName,\r\n              materialSpec: item.materialSpec || \"\",\r\n              planNum: item.planNum || 1,\r\n              measureUnit: item.measureUnit || \"\",\r\n              remark: item.remark || ''\r\n            };\r\n          });\r\n          \r\n          // 将导入的物资添加到表单中\r\n          this.form.materials = this.form.materials.concat(importedMaterials);\r\n          \r\n          // 使用nextTick确保视图更新\r\n          this.$nextTick(() => {\r\n            // 触发一次刷新，确保下拉框正确显示\r\n            this.materialOptions = [...this.materialOptions];\r\n          });\r\n        }\r\n      } else {\r\n        this.$message.error(response.msg || \"导入失败\");\r\n      }\r\n    },\r\n    \r\n    // 根据物资名称查询materialId\r\n    queryMaterialIdByName(rowIndex) {\r\n      // 此方法已不再使用，保留空方法以避免可能的调用错误\r\n    },\r\n    \r\n    // 图片预览\r\n    handleImgPreview(file) {\r\n      this.imgPreviewUrl = file.url || (file.response && file.response.data);\r\n      this.imgPreviewVisible = true;\r\n    },\r\n    \r\n    // 文件预览\r\n    handleFilePreview(file) {\r\n      window.open(file.url || (file.response && file.response.data));\r\n    },\r\n    \r\n    // 移除图片\r\n    handleImgRemove(file, fileList) {\r\n      //移除fileList中url与file.url相同的文件\r\n      this.imgFileList = this.imgFileList.filter(item => item.url !== file.url);\r\n      \r\n      // 转换为JSON字符串保存\r\n      this.form.applyImgUrl = JSON.stringify(imgFileList);\r\n    },\r\n    \r\n    // 移除文件\r\n    handleFileRemove(file, fileList) {\r\n      //移除fileList中url与file.url相同的文件\r\n      this.fileList = this.fileList.filter(item => item.url !== file.url);\r\n      \r\n      // 转换为JSON字符串保存\r\n      this.form.applyFileUrl = JSON.stringify(fileList);\r\n    },\r\n    \r\n    // 超出文件数量限制\r\n    handleExceed() {\r\n      this.$message.warning('最多只能上传3个文件');\r\n    },\r\n\r\n    // 表单提交\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          // 验证物资列表\r\n          if (this.form.materials.length === 0) {\r\n            this.$message.error(\"请至少添加一项物资\");\r\n            return;\r\n          }\r\n\r\n          // 验证物资列表的必填项\r\n          let valid = true;\r\n          this.form.materials.forEach((item, index) => {\r\n            if (!item.materialId || !item.materialName || !item.materialSpec || !item.planNum || !item.measureUnit) {\r\n              this.$message.error(`第${index + 1}行物资信息不完整，请填写完整`);\r\n              valid = false;\r\n            }\r\n          });\r\n\r\n          if (!valid) {\r\n            return;\r\n          }\r\n\r\n          // 验证计划量大于0\r\n          if (this.form.planType === 3 && this.form.businessCategory === 21 && (!this.form.plannedAmount || this.form.plannedAmount <= 0)) {\r\n            this.$message.error(\"计划量必须大于0\");\r\n            return;\r\n          }\r\n          //对有效期单独处理\r\n          if (this.form.planType === 3 && this.form.businessCategory === 21) {\r\n            if (this.form.endTime) {\r\n              this.form.expireTime = this.form.endTime + \" 23:59:59\";\r\n            }\r\n          } else if (this.form.expireTime) {\r\n            this.form.expireTime = this.form.expireTime + \" 23:59:59\";\r\n          }\r\n          //对开始结束时间单独处理\r\n          if (this.form.startTime && this.form.endTime) {\r\n            this.form.startTime = this.form.startTime + \" 00:00:00\";\r\n            this.form.endTime = this.form.endTime + \" 23:59:59\";\r\n          }\r\n\r\n          // 根据是否编辑模式调用不同的API\r\n          const apiMethod = this.isEdit ? updatePlan : addPlan;\r\n          const successMsg = this.isEdit ? \"修改成功\" : \"申请提交成功\";\r\n          console.log(this.form);\r\n          // 调用API提交数据\r\n          apiMethod(this.form).then(response => {\r\n            if (response.code === 200) {\r\n              this.$message.success(successMsg);\r\n              this.$tab.closeOpenPage(this.$route);\r\n              // 跳转到列表页面并刷新\r\n              this.$router.push({ \r\n                path: \"/leave/leavePlanList\", \r\n                query: { \r\n                  t: Date.now(),\r\n                  refresh: true // 添加刷新标记\r\n                }\r\n              });\r\n            } else {\r\n              this.$message.error(response.msg || \"提交失败\");\r\n            }\r\n          }).catch(error => {\r\n            console.error(\"提交失败\", error);\r\n            this.$message.error(\"提交过程中发生错误，请稍后再试\");\r\n          });\r\n        }\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.$router.push(\"/leave/plan\");\r\n    },\r\n    // 图片上传前的验证\r\n    beforeImgUpload(file) {\r\n      const isImage = file.type.indexOf('image/') === 0;\r\n      const isLt10M = file.size / 1024 / 1024 < 10;\r\n\r\n      if (!isImage) {\r\n        this.$message.error('只能上传图片格式文件!');\r\n        return false;\r\n      }\r\n      if (!isLt10M) {\r\n        this.$message.error('图片大小不能超过 10MB!');\r\n        return false;\r\n      }\r\n      return true;\r\n    },\r\n\r\n    // 文件上传前的验证\r\n    beforeFileUpload(file) {\r\n      const isLt20M = file.size / 1024 / 1024 < 20;\r\n      if (!isLt20M) {\r\n        this.$message.error('文件大小不能超过 20MB!');\r\n        return false;\r\n      }\r\n      return true;\r\n    },\r\n    \r\n    // 上传错误处理\r\n    handleUploadError(err, file, fileList) {\r\n      console.error(\"上传失败:\", err);\r\n      \r\n      if (err.status === 403) {\r\n        this.$message.error('上传失败：没有权限');\r\n      } else {\r\n        this.$message.error('上传失败：' + (err.message || '未知错误'));\r\n      }\r\n    },\r\n    // 获取部门名称\r\n    getDepartmentName(id) {\r\n      if (!id) return '';\r\n      \r\n      // 查询选项中是否有匹配的\r\n      const dept = this.departmentOptions.find(item => item.id == id);\r\n      if (dept) {\r\n        return dept.storeName;\r\n      }\r\n      \r\n      // 根据不同的字段ID返回对应的中文名称\r\n      if (id === this.form.sourceCompanyCode && this.form.sourceCompany) {\r\n        return this.form.sourceCompany;\r\n      }\r\n      \r\n      if (id === this.form.receiveCompanyCode && this.form.receiveCompany && this.form.planType === 3) {\r\n        return this.form.receiveCompany;\r\n      }\r\n      \r\n      if (id === this.form.targetCompanyCode && this.form.targetCompany) {\r\n        return this.form.targetCompany;\r\n      }\r\n      \r\n      if (id === this.form.refundCompanyCode && this.form.refundCompany) {\r\n        return this.form.refundCompany;\r\n      }\r\n      \r\n      // 否则显示ID\r\n      return `ID: ${id}`;\r\n    },\r\n    \r\n    // 获取客户名称\r\n    getCustomerName(id) {\r\n      if (!id) return '';\r\n      \r\n      // 查询选项中是否有匹配的\r\n      const customer = this.customerOptions.find(item => item.id == id);\r\n      if (customer) {\r\n        return customer.customerName;\r\n      }\r\n      \r\n      // 如果是收货单位且不是跨区调拨，则使用已有的中文名称\r\n      if (id === this.form.receiveCompanyCode && this.form.receiveCompany && this.form.planType !== 3) {\r\n        return this.form.receiveCompany;\r\n      }\r\n      \r\n      // 否则显示ID\r\n      return `ID: ${id}`;\r\n    },\r\n    // 添加部门到选项数组方法\r\n    addToDepartmentOptions(id, name) {\r\n      // 检查是否已存在相同ID的选项\r\n      if (!this.departmentOptions.some(item => item.id == id)) {\r\n        this.departmentOptions.push({\r\n          id: id,\r\n          storeName: name\r\n        });\r\n      }\r\n    },\r\n    \r\n    // 添加客户到选项数组方法\r\n    addToCustomerOptions(id, name) {\r\n      // 检查是否已存在相同ID的选项\r\n      if (!this.customerOptions.some(item => item.id == id)) {\r\n        this.customerOptions.push({\r\n          id: id,\r\n          customerName: name\r\n        });\r\n      }\r\n    },\r\n    \r\n    // 添加物资到选项数组方法\r\n    addToMaterialOptions(id, name, spec, unit) {\r\n      // 检查是否已存在相同ID的选项\r\n      if (!this.materialOptions.some(item => item.id == id)) {\r\n        this.materialOptions.push({\r\n          id: id,\r\n          materialName: name,\r\n          materialSpec: spec,\r\n          measureUnit: unit\r\n        });\r\n      }\r\n    },\r\n    // 导出物资模板\r\n    handleExportMaterialTemplate() {\r\n      exportMaterialTemplate().then(response => {\r\n        this.download(response.msg);\r\n      });\r\n    },\r\n    \r\n    // 显示导入物资对话框\r\n    handleImportMaterial() {\r\n      this.importVisible = true;\r\n    },\r\n    \r\n    // 文件上传中处理\r\n    handleFileUploadProgress() {\r\n      this.upload.isUploading = true;\r\n    },\r\n    \r\n    // 提交上传文件\r\n    submitFileForm() {\r\n      this.$refs.upload.submit();\r\n    },\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.app-container {\r\n  padding: 20px;\r\n}\r\n.box-card {\r\n  margin-bottom: 20px;\r\n  border-radius: 5px;\r\n}\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n.material-card {\r\n  margin-top: 20px;\r\n  margin-bottom: 20px;\r\n}\r\n.form-footer {\r\n  margin-top: 30px;\r\n  text-align: center;\r\n}\r\n.material-btn-group {\r\n  float: right;\r\n}\r\n.material-btn-group .el-button {\r\n  margin-left: 8px;\r\n  border-radius: 4px;\r\n}\r\n.material-btn-group .el-button:first-child {\r\n  margin-left: 0;\r\n}\r\n.tip-text {\r\n  color: #f56c6c;\r\n  font-size: 12px;\r\n  line-height: 1.2;\r\n  margin-top: 5px;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4aA,IAAAA,WAAA,GAAAC,OAAA;AACA,IAAAC,SAAA,GAAAD,OAAA;AACA,IAAAE,SAAA,GAAAF,OAAA;AACA,IAAAG,KAAA,GAAAH,OAAA;AACA,IAAAI,KAAA,GAAAJ,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAK,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,aAAA;MACA;MACAC,MAAA;QACA;QACAC,WAAA;QACA;QACAC,OAAA;UAAAC,aAAA,kBAAAC,cAAA;QAAA;QACA;QACAC,GAAA,EAAAC,OAAA,CAAAC,GAAA,CAAAC,gBAAA;MACA;MACA;MACAC,MAAA;MACA;MACAC,IAAA;QACAC,QAAA;QACAC,gBAAA;QACAC,WAAA;QACAC,aAAA;QACAC,iBAAA;QACAC,aAAA;QACAC,kBAAA;QACAC,cAAA;QACAC,iBAAA;QACAC,aAAA;QACAC,iBAAA;QACAC,aAAA;QACAC,UAAA;QACAC,iBAAA;QACAC,SAAA;QACAC,OAAA;QACAC,UAAA;QAAA;QACAC,OAAA;QACAC,cAAA;QACAC,QAAA;QACAC,MAAA;QACAC,cAAA;QACAC,WAAA;QAAA;QACAC,YAAA;QAAA;QACAC,SAAA;MACA;MACA;MACAC,KAAA;QACAzB,QAAA,GACA;UAAA0B,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACA3B,gBAAA,GACA;UAAAyB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACA1B,WAAA,GACA;UAAAwB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAC,YAAA,GACA;UAAAH,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACArB,cAAA,GACA;UAAAmB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAnB,aAAA,GACA;UAAAiB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAX,OAAA,GACA;UAAAS,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAV,cAAA,GACA;UAAAQ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAE,YAAA,GACA;UAAAJ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAG,WAAA,GACA;UAAAL,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAf,iBAAA,GACA;UAAAa,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACA;MACAI,iBAAA;MACAC,eAAA;MACAC,iBAAA;MACAC,eAAA;MACA;MACAC,eAAA;MACAC,eAAA;MAEA;MACAC,SAAA,EAAA3C,OAAA,CAAAC,GAAA,CAAAC,gBAAA;MACA0C,YAAA;QACA;QACAC,UAAA;MACA;MACAC,WAAA;MAAA;MACAC,QAAA;MAAA;MACAC,iBAAA;MAAA;MACAC,aAAA;MAAA;;MAEA;MACAC,aAAA,MAAAC,GAAA;MAAA;MACAC,WAAA,MAAAD,GAAA;MAAA;MACA;MACAE,iBAAA;QACAC,YAAA,WAAAA,aAAAC,IAAA;UACA;UACA,IAAAC,WAAA,OAAAC,IAAA;UACA,IAAAC,IAAA,GAAAF,WAAA,CAAAG,WAAA;UACA,IAAAC,KAAA,GAAAJ,WAAA,CAAAK,QAAA;UACA,IAAAC,UAAA,OAAAL,IAAA,CAAAC,IAAA,EAAAE,KAAA;;UAEA;UACA,OAAAL,IAAA,CAAAQ,OAAA,KAAAD,UAAA,CAAAC,OAAA;QACA;MACA;IACA;EACA;EACAC,QAAA;IACA;IACAC,kBAAA,WAAAA,mBAAA;MACA,YAAA7D,IAAA,CAAAC,QAAA,eAAAD,IAAA,CAAAE,gBAAA;IACA;EACA;EACA4D,KAAA;IACA;IACAD,kBAAA,WAAAA,mBAAAE,GAAA;MACA,IAAAA,GAAA;QACA,KAAA/D,IAAA,CAAAG,WAAA;MACA;IACA;IACA;IACA;MACA6D,OAAA,WAAAA,QAAAD,GAAA;QACA,KAAAE,cAAA;MACA;IACA;IACA;MACAD,OAAA,WAAAA,QAAAD,GAAA;QACA,KAAAE,cAAA;MACA;IACA;IACA;MACAD,OAAA,WAAAA,QAAAD,GAAA;QACA,KAAAE,cAAA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA;IACA,IAAAC,OAAA,QAAAC,MAAA,CAAAC,MAAA,CAAAF,OAAA;IACA,IAAAA,OAAA;MACA,KAAApE,MAAA;MACA,KAAAuE,SAAA,CAAAH,OAAA;IACA;MACA;MACA,IAAAI,GAAA,OAAAlB,IAAA;MACAkB,GAAA,CAAAC,OAAA,CAAAD,GAAA,CAAAE,OAAA;MACA,IAAAC,IAAA,GAAAH,GAAA,CAAAhB,WAAA;MACA,IAAAoB,EAAA,GAAAC,MAAA,CAAAL,GAAA,CAAAd,QAAA,QAAAoB,QAAA;MACA,IAAAC,EAAA,GAAAF,MAAA,CAAAL,GAAA,CAAAE,OAAA,IAAAI,QAAA;MACA,KAAA7E,IAAA,CAAAiB,UAAA,MAAA8D,MAAA,CAAAL,IAAA,OAAAK,MAAA,CAAAJ,EAAA,OAAAI,MAAA,CAAAD,EAAA;MACA;MACA,KAAAE,iBAAA;MACA;MACA,KAAAC,oBAAA,MAAAjF,IAAA,CAAAC,QAAA;MACA;MACA,KAAAiF,eAAA;IACA;EACA;EACAC,OAAA;IACA;IACAlB,cAAA,WAAAA,eAAA;MACA,SAAAjE,IAAA,CAAAC,QAAA,eAAAD,IAAA,CAAAE,gBAAA;QACA,KAAAF,IAAA,CAAAiB,UAAA,QAAAjB,IAAA,CAAAgB,OAAA;MACA,iBAAAhB,IAAA,CAAAiB,UAAA;QACA;QACA,IAAAsD,GAAA,OAAAlB,IAAA;QACAkB,GAAA,CAAAC,OAAA,CAAAD,GAAA,CAAAE,OAAA;QACA,IAAAC,IAAA,GAAAH,GAAA,CAAAhB,WAAA;QACA,IAAAoB,EAAA,GAAAC,MAAA,CAAAL,GAAA,CAAAd,QAAA,QAAAoB,QAAA;QACA,IAAAC,EAAA,GAAAF,MAAA,CAAAL,GAAA,CAAAE,OAAA,IAAAI,QAAA;QACA,KAAA7E,IAAA,CAAAiB,UAAA,MAAA8D,MAAA,CAAAL,IAAA,OAAAK,MAAA,CAAAJ,EAAA,OAAAI,MAAA,CAAAD,EAAA;MACA;IACA;IACA;IACAR,SAAA,WAAAA,UAAAH,OAAA;MAAA,IAAAiB,KAAA;MACA,IAAAC,gBAAA,EAAAlB,OAAA,EAAAmB,IAAA,WAAAC,QAAA;QACA,IAAAA,QAAA,CAAAC,IAAA;UACA,IAAAC,MAAA,GAAAF,QAAA,CAAAnG,IAAA;;UAEA;UACAgG,KAAA,CAAApF,IAAA,CAAA0F,EAAA,GAAAD,MAAA,CAAAC,EAAA;UACAN,KAAA,CAAApF,IAAA,CAAAmE,OAAA,GAAAsB,MAAA,CAAAtB,OAAA;UACAiB,KAAA,CAAApF,IAAA,CAAAC,QAAA,GAAAwF,MAAA,CAAAxF,QAAA;UACAmF,KAAA,CAAApF,IAAA,CAAAE,gBAAA,GAAAuF,MAAA,CAAAvF,gBAAA;UACAkF,KAAA,CAAApF,IAAA,CAAAG,WAAA,GAAAsF,MAAA,CAAAtF,WAAA;UACAiF,KAAA,CAAApF,IAAA,CAAAI,aAAA,GAAAqF,MAAA,CAAArF,aAAA;;UAEA;UACAgF,KAAA,CAAApF,IAAA,CAAAK,iBAAA,GAAAoF,MAAA,CAAApF,iBAAA;UACA+E,KAAA,CAAApF,IAAA,CAAAM,aAAA,GAAAmF,MAAA,CAAAnF,aAAA;UACA8E,KAAA,CAAApF,IAAA,CAAAO,kBAAA,GAAAkF,MAAA,CAAAlF,kBAAA;UACA6E,KAAA,CAAApF,IAAA,CAAAQ,cAAA,GAAAiF,MAAA,CAAAjF,cAAA;UACA4E,KAAA,CAAApF,IAAA,CAAAS,iBAAA,GAAAgF,MAAA,CAAAhF,iBAAA;UACA2E,KAAA,CAAApF,IAAA,CAAAU,aAAA,GAAA+E,MAAA,CAAA/E,aAAA;UACA0E,KAAA,CAAApF,IAAA,CAAAW,iBAAA,GAAA8E,MAAA,CAAAE,oBAAA;UACAP,KAAA,CAAApF,IAAA,CAAAY,aAAA,GAAA6E,MAAA,CAAAG,gBAAA;;UAEA;UACA;UACA,IAAAH,MAAA,CAAApF,iBAAA,IAAAoF,MAAA,CAAAnF,aAAA;YACA8E,KAAA,CAAAS,sBAAA,CAAAJ,MAAA,CAAApF,iBAAA,EAAAoF,MAAA,CAAAnF,aAAA;UACA;;UAEA;UACA,IAAAmF,MAAA,CAAAlF,kBAAA,IAAAkF,MAAA,CAAAjF,cAAA;YACA,IAAAiF,MAAA,CAAAxF,QAAA;cACA;cACAmF,KAAA,CAAAS,sBAAA,CAAAJ,MAAA,CAAAlF,kBAAA,EAAAkF,MAAA,CAAAjF,cAAA;YACA;cACA;cACA4E,KAAA,CAAAU,oBAAA,CAAAL,MAAA,CAAAlF,kBAAA,EAAAkF,MAAA,CAAAjF,cAAA;YACA;UACA;;UAEA;UACA,IAAAiF,MAAA,CAAAhF,iBAAA,IAAAgF,MAAA,CAAA/E,aAAA;YACA0E,KAAA,CAAAS,sBAAA,CAAAJ,MAAA,CAAAhF,iBAAA,EAAAgF,MAAA,CAAA/E,aAAA;UACA;;UAEA;UACA,IAAA+E,MAAA,CAAAE,oBAAA,IAAAF,MAAA,CAAAG,gBAAA;YACAR,KAAA,CAAAS,sBAAA,CAAAJ,MAAA,CAAAE,oBAAA,EAAAF,MAAA,CAAAG,gBAAA;UACA;UAEAR,KAAA,CAAApF,IAAA,CAAAc,iBAAA,GAAA2E,MAAA,CAAAM,cAAA;UACAX,KAAA,CAAApF,IAAA,CAAAe,SAAA,GAAA0E,MAAA,CAAA1E,SAAA;UACAqE,KAAA,CAAApF,IAAA,CAAAgB,OAAA,GAAAyE,MAAA,CAAAzE,OAAA;UACAoE,KAAA,CAAApF,IAAA,CAAAkB,OAAA,GAAAuE,MAAA,CAAAvE,OAAA;UACAkE,KAAA,CAAApF,IAAA,CAAAmB,cAAA,GAAAsE,MAAA,CAAAtE,cAAA;UACAiE,KAAA,CAAApF,IAAA,CAAAoB,QAAA,GAAAqE,MAAA,CAAArE,QAAA;UACAgE,KAAA,CAAApF,IAAA,CAAAqB,MAAA,GAAAoE,MAAA,CAAApE,MAAA;UACA+D,KAAA,CAAApF,IAAA,CAAAa,UAAA,GAAA4E,MAAA,CAAA5E,UAAA;UACAuE,KAAA,CAAApF,IAAA,CAAAsB,cAAA,GAAAmE,MAAA,CAAAnE,cAAA;UACA8D,KAAA,CAAApF,IAAA,CAAAuB,WAAA,GAAAkE,MAAA,CAAAlE,WAAA;UACA6D,KAAA,CAAApF,IAAA,CAAAwB,YAAA,GAAAiE,MAAA,CAAAjE,YAAA;;UAEA;UACA4D,KAAA,CAAAY,YAAA;;UAEA;UACA,IAAAP,MAAA,CAAAhE,SAAA,IAAAgE,MAAA,CAAAhE,SAAA,CAAAwE,MAAA;YACAb,KAAA,CAAApF,IAAA,CAAAyB,SAAA,GAAAgE,MAAA,CAAAhE,SAAA,CAAAyE,GAAA,WAAAC,IAAA;cAAA;gBACAT,EAAA,EAAAS,IAAA,CAAAT,EAAA;gBACAU,UAAA,EAAAD,IAAA,CAAAC,UAAA;gBACAC,YAAA,EAAAF,IAAA,CAAAE,YAAA;gBACAC,YAAA,EAAAH,IAAA,CAAAG,YAAA;gBACAC,OAAA,EAAAJ,IAAA,CAAAI,OAAA;gBACAC,WAAA,EAAAL,IAAA,CAAAK,WAAA;gBACAC,MAAA,EAAAN,IAAA,CAAAM;cACA;YAAA;;YAEA;YACAhB,MAAA,CAAAhE,SAAA,CAAAiF,OAAA,WAAAP,IAAA;cACA,IAAAA,IAAA,CAAAC,UAAA,IAAAD,IAAA,CAAAE,YAAA;gBACAjB,KAAA,CAAAuB,oBAAA,CAAAR,IAAA,CAAAC,UAAA,EAAAD,IAAA,CAAAE,YAAA,EAAAF,IAAA,CAAAG,YAAA,EAAAH,IAAA,CAAAK,WAAA;cACA;YACA;UACA;YACApB,KAAA,CAAAJ,iBAAA;UACA;;UAEA;UACAI,KAAA,CAAAF,eAAA;QACA;MACA;IACA;IACA;IACA0B,sBAAA,WAAAA,uBAAAC,KAAA;MAAA,IAAAC,MAAA;MACA,IAAAD,KAAA;QACA,KAAA1E,iBAAA;QACA,IAAA4E,0BAAA;UACAC,SAAA,EAAAH,KAAA;UACAI,OAAA;UACAC,QAAA;QACA,GAAA5B,IAAA,WAAAC,QAAA;UACAuB,MAAA,CAAA3E,iBAAA;UACA,IAAAoD,QAAA,CAAAC,IAAA;YACAsB,MAAA,CAAA7E,iBAAA,GAAAsD,QAAA,CAAA4B,IAAA;UACA;QACA,GAAAC,OAAA;UACAN,MAAA,CAAA3E,iBAAA;QACA;MACA;QACA,KAAAF,iBAAA;MACA;IACA;IAEA;IACAoF,oBAAA,WAAAA,qBAAAR,KAAA;MAAA,IAAAS,MAAA;MACA,IAAAT,KAAA;QACA,KAAAzE,eAAA;QACA,IAAAmF,sBAAA;UACAC,YAAA,EAAAX,KAAA;UACAI,OAAA;UACAC,QAAA;QACA,GAAA5B,IAAA,WAAAC,QAAA;UACA+B,MAAA,CAAAlF,eAAA;UACA,IAAAmD,QAAA,CAAAC,IAAA;YACA8B,MAAA,CAAApF,eAAA,GAAAqD,QAAA,CAAA4B,IAAA;UACA;QACA,GAAAC,OAAA;UACAE,MAAA,CAAAlF,eAAA;QACA;MACA;QACA,KAAAF,eAAA;MACA;IACA;IAEA;IACAuF,4BAAA,WAAAA,6BAAA;MACA;MACA,KAAAvC,eAAA;IACA;IAEA;IACAD,oBAAA,WAAAA,qBAAAlB,GAAA;MACA;MACA,QAAAA,GAAA;QACA;UACA,KAAA/D,IAAA,CAAAE,gBAAA;UACA;QACA;UACA,KAAAF,IAAA,CAAAE,gBAAA;UACA;QACA;UACA,KAAAF,IAAA,CAAAE,gBAAA;UACA;QACA;UACA,KAAAF,IAAA,CAAAE,gBAAA;UACA;QACA;UACA;MACA;;MAEA;MACA,IAAA6D,GAAA;QACA,KAAA/D,IAAA,CAAAU,aAAA;QACA,KAAAV,IAAA,CAAAc,iBAAA;MACA;MAEA,IAAAiD,GAAA;QACA,KAAA/D,IAAA,CAAAY,aAAA;MACA;MAEA,IAAAmD,GAAA,eAAA/D,IAAA,CAAAE,gBAAA;QACA,KAAAF,IAAA,CAAAe,SAAA;QACA,KAAAf,IAAA,CAAAgB,OAAA;MACA;;MAEA;MACA,KAAAkE,eAAA;IACA;IAEA;IACAA,eAAA,WAAAA,gBAAA;MACA,IAAAwC,SAAA;QACAzH,QAAA,GACA;UAAA0B,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACA3B,gBAAA,GACA;UAAAyB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACA1B,WAAA,GACA;UAAAwB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAC,YAAA,GACA;UAAAH,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAX,OAAA,GACA;UAAAS,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAV,cAAA,GACA;UAAAQ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAE,YAAA,GACA;UAAAJ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;;MAEA;MACA,SAAA7B,IAAA,CAAAC,QAAA;QACAyH,SAAA,CAAAlH,cAAA,IACA;UAAAmB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;MACA;MAEA,SAAA7B,IAAA,CAAAC,QAAA;QACAyH,SAAA,CAAAhH,aAAA,IACA;UAAAiB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACA6F,SAAA,CAAA5G,iBAAA,IACA;UAAAa,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;MACA;MAEA,SAAA7B,IAAA,CAAAC,QAAA;QACAyH,SAAA,CAAA9G,aAAA,IACA;UAAAe,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;MACA;MAEA,SAAA7B,IAAA,CAAAC,QAAA,eAAAD,IAAA,CAAAE,gBAAA;QACAwH,SAAA,CAAAtH,aAAA,IACA;UAAAuB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACA6F,SAAA,CAAA3G,SAAA,IACA;UAAAY,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACA6F,SAAA,CAAA1G,OAAA,IACA;UAAAW,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;MACA;;MAEA;MACA,KAAAH,KAAA,GAAAgG,SAAA;IACA;IAEA;IACA1C,iBAAA,WAAAA,kBAAA;MACA,KAAAhF,IAAA,CAAAyB,SAAA,CAAAkG,IAAA;QACAvB,UAAA;QACAC,YAAA;QACAC,YAAA;QACAC,OAAA;QACAC,WAAA;QACAC,MAAA;MACA;IACA;IACA;IACAmB,oBAAA,WAAAA,qBAAAC,KAAA;MACA,KAAA7H,IAAA,CAAAyB,SAAA,CAAAqG,MAAA,CAAAD,KAAA;MACA;MACA,SAAA7H,IAAA,CAAAyB,SAAA,CAAAwE,MAAA;QACA,KAAAjB,iBAAA;MACA;IACA;IACA;IACA+C,oBAAA,WAAAA,qBAAAlB,KAAA;MAAA,IAAAmB,MAAA;MACA,IAAAnB,KAAA;QACA,KAAAvE,eAAA;QACA,IAAA2F,sBAAA;UACA5B,YAAA,EAAAQ,KAAA;UACAI,OAAA;UACAC,QAAA;QACA,GAAA5B,IAAA,WAAAC,QAAA;UACAyC,MAAA,CAAA1F,eAAA;UACA,IAAAiD,QAAA,CAAAC,IAAA;YACAwC,MAAA,CAAA3F,eAAA,GAAAkD,QAAA,CAAA4B,IAAA;UACA;QACA,GAAAC,OAAA;UACAY,MAAA,CAAA1F,eAAA;QACA;MACA;QACA,KAAAD,eAAA;MACA;IACA;IACA;IACA6F,oBAAA,WAAAA,qBAAAC,KAAA,EAAAC,GAAA;MACA;MACA,IAAAC,gBAAA,QAAAhG,eAAA,CAAAiG,IAAA,WAAAnC,IAAA;QAAA,OAAAA,IAAA,CAAAT,EAAA,KAAAyC,KAAA;MAAA;MACA,IAAAE,gBAAA;QACAD,GAAA,CAAA/B,YAAA,GAAAgC,gBAAA,CAAAhC,YAAA;QACA;QACA,IAAAgC,gBAAA,CAAA/B,YAAA;UACA8B,GAAA,CAAA9B,YAAA,GAAA+B,gBAAA,CAAA/B,YAAA;QACA;QACA,IAAA+B,gBAAA,CAAA7B,WAAA;UACA4B,GAAA,CAAA5B,WAAA,GAAA6B,gBAAA,CAAA7B,WAAA;QACA;MACA;IACA;IACA;IACAR,YAAA,WAAAA,aAAA;MACA;MACA,SAAAhG,IAAA,CAAAuB,WAAA;QACA;UACA;UACA,IAAAgH,QAAA,GAAAC,IAAA,CAAAC,KAAA,MAAAzI,IAAA,CAAAuB,WAAA;UACA,KAAAmB,WAAA,GAAA6F,QAAA,CAAArC,GAAA,WAAAC,IAAA;YACA;cACAhH,IAAA,EAAAgH,IAAA,CAAAhH,IAAA;cACAQ,GAAA,EAAAwG,IAAA,CAAAxG;YACA;UACA;QACA,SAAA+I,CAAA;UACAC,OAAA,CAAAC,GAAA,CAAAF,CAAA;QACA;MACA;;MAEA;MACA,SAAA1I,IAAA,CAAAwB,YAAA;QACA;UACA;UACA,IAAAqH,KAAA,GAAAL,IAAA,CAAAC,KAAA,MAAAzI,IAAA,CAAAwB,YAAA;UACA,KAAAmB,QAAA,GAAAkG,KAAA,CAAA3C,GAAA,WAAAC,IAAA;YACA;cACAhH,IAAA,EAAAgH,IAAA,CAAAhH,IAAA;cACAQ,GAAA,EAAAwG,IAAA,CAAAxG;YACA;UACA;QACA,SAAA+I,CAAA;UACAC,OAAA,CAAAC,GAAA,CAAAF,CAAA;QACA;MACA;IACA;IAEA;IACAI,gBAAA,WAAAA,iBAAAvD,QAAA,EAAAwD,IAAA,EAAApG,QAAA;MACAgG,OAAA,CAAAC,GAAA,CAAArD,QAAA;MACAoD,OAAA,CAAAC,GAAA,CAAAG,IAAA;MACAJ,OAAA,CAAAC,GAAA,CAAAjG,QAAA;MACA,IAAA4C,QAAA,CAAAC,IAAA;QACA;QACA;;QAEA;QACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;QAGA;QACA,IAAAwD,OAAA;UACA7J,IAAA,EAAAoG,QAAA,CAAA0D,gBAAA;UACAtJ,GAAA,EAAA4F,QAAA,CAAA5F;QACA;QACA,KAAA+C,WAAA,CAAAiF,IAAA,CAAAqB,OAAA;QACAL,OAAA,CAAAC,GAAA,MAAAlG,WAAA;QACA,KAAA1C,IAAA,CAAAuB,WAAA,GAAAiH,IAAA,CAAAU,SAAA,MAAAxG,WAAA;QACAiG,OAAA,CAAAC,GAAA,MAAA5I,IAAA,CAAAuB,WAAA;MACA;QACA,KAAA4H,QAAA,CAAAC,KAAA;MACA;IACA;IAEA;IACAC,sBAAA,WAAAA,uBAAA9D,QAAA,EAAAwD,IAAA,EAAApG,QAAA;MACAgG,OAAA,CAAAC,GAAA,CAAArD,QAAA;MACAoD,OAAA,CAAAC,GAAA,CAAAG,IAAA;MACAJ,OAAA,CAAAC,GAAA,CAAAjG,QAAA;MACA,IAAA4C,QAAA,CAAAC,IAAA;QACA;QACA,IAAA8D,aAAA;UACAnK,IAAA,EAAAoG,QAAA,CAAA0D,gBAAA;UACAtJ,GAAA,EAAA4F,QAAA,CAAA5F;QACA;QACA,KAAAgD,QAAA,CAAAgF,IAAA,CAAA2B,aAAA;QACAX,OAAA,CAAAC,GAAA,MAAAjG,QAAA;QACA,KAAA3C,IAAA,CAAAwB,YAAA,GAAAgH,IAAA,CAAAU,SAAA,MAAAvG,QAAA;QACAgG,OAAA,CAAAC,GAAA,MAAA5I,IAAA,CAAAwB,YAAA;MACA;QACA,KAAA2H,QAAA,CAAAC,KAAA;MACA;IACA;IAEA;IACAG,yBAAA,WAAAA,0BAAAhE,QAAA,EAAAwD,IAAA,EAAApG,QAAA;MAAA,IAAA6G,MAAA;MACA,KAAAlK,MAAA,CAAAC,WAAA;MACA,KAAAkK,KAAA,CAAAnK,MAAA,CAAAoK,UAAA;MACA,KAAArK,aAAA;MAEA,IAAAkG,QAAA,CAAAC,IAAA;QACA,KAAA2D,QAAA,CAAAQ,OAAA;QACA;QACA,IAAApE,QAAA,CAAAnG,IAAA,IAAAmG,QAAA,CAAAnG,IAAA,CAAA6G,MAAA;UACA;UACA,IAAA2D,iBAAA,GAAArE,QAAA,CAAAnG,IAAA,CAAA8G,GAAA,WAAAC,IAAA;YACA;YACA,IAAAC,UAAA,GAAAD,IAAA,CAAAC,UAAA;YACA;YACA,IAAAC,YAAA,GAAAF,IAAA,CAAAE,YAAA;;YAEA;YACA,IAAAD,UAAA;cACAoD,MAAA,CAAA7C,oBAAA,CACAP,UAAA,EACAC,YAAA,EACAF,IAAA,CAAAG,YAAA,QACAH,IAAA,CAAAK,WAAA,MACA;YACA;YAEA;cACAJ,UAAA,EAAAA,UAAA;cACAC,YAAA,EAAAA,YAAA;cACAC,YAAA,EAAAH,IAAA,CAAAG,YAAA;cACAC,OAAA,EAAAJ,IAAA,CAAAI,OAAA;cACAC,WAAA,EAAAL,IAAA,CAAAK,WAAA;cACAC,MAAA,EAAAN,IAAA,CAAAM,MAAA;YACA;UACA;;UAEA;UACA,KAAAzG,IAAA,CAAAyB,SAAA,QAAAzB,IAAA,CAAAyB,SAAA,CAAAsD,MAAA,CAAA6E,iBAAA;;UAEA;UACA,KAAAC,SAAA;YACA;YACAL,MAAA,CAAAnH,eAAA,OAAAyH,mBAAA,CAAAC,OAAA,EAAAP,MAAA,CAAAnH,eAAA;UACA;QACA;MACA;QACA,KAAA8G,QAAA,CAAAC,KAAA,CAAA7D,QAAA,CAAAyE,GAAA;MACA;IACA;IAEA;IACAC,qBAAA,WAAAA,sBAAAC,QAAA;MACA;IAAA,CACA;IAEA;IACAC,gBAAA,WAAAA,iBAAApB,IAAA;MACA,KAAAlG,aAAA,GAAAkG,IAAA,CAAApJ,GAAA,IAAAoJ,IAAA,CAAAxD,QAAA,IAAAwD,IAAA,CAAAxD,QAAA,CAAAnG,IAAA;MACA,KAAAwD,iBAAA;IACA;IAEA;IACAwH,iBAAA,WAAAA,kBAAArB,IAAA;MACAsB,MAAA,CAAAC,IAAA,CAAAvB,IAAA,CAAApJ,GAAA,IAAAoJ,IAAA,CAAAxD,QAAA,IAAAwD,IAAA,CAAAxD,QAAA,CAAAnG,IAAA;IACA;IAEA;IACAmL,eAAA,WAAAA,gBAAAxB,IAAA,EAAApG,QAAA;MACA;MACA,KAAAD,WAAA,QAAAA,WAAA,CAAA8H,MAAA,WAAArE,IAAA;QAAA,OAAAA,IAAA,CAAAxG,GAAA,KAAAoJ,IAAA,CAAApJ,GAAA;MAAA;;MAEA;MACA,KAAAK,IAAA,CAAAuB,WAAA,GAAAiH,IAAA,CAAAU,SAAA,CAAAxG,WAAA;IACA;IAEA;IACA+H,gBAAA,WAAAA,iBAAA1B,IAAA,EAAApG,QAAA;MACA;MACA,KAAAA,QAAA,QAAAA,QAAA,CAAA6H,MAAA,WAAArE,IAAA;QAAA,OAAAA,IAAA,CAAAxG,GAAA,KAAAoJ,IAAA,CAAApJ,GAAA;MAAA;;MAEA;MACA,KAAAK,IAAA,CAAAwB,YAAA,GAAAgH,IAAA,CAAAU,SAAA,CAAAvG,QAAA;IACA;IAEA;IACA+H,YAAA,WAAAA,aAAA;MACA,KAAAvB,QAAA,CAAAwB,OAAA;IACA;IAEA;IACAC,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAApB,KAAA,SAAAqB,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA;UACA,IAAAF,MAAA,CAAA7K,IAAA,CAAAyB,SAAA,CAAAwE,MAAA;YACA4E,MAAA,CAAA1B,QAAA,CAAAC,KAAA;YACA;UACA;;UAEA;UACA,IAAA2B,MAAA;UACAF,MAAA,CAAA7K,IAAA,CAAAyB,SAAA,CAAAiF,OAAA,WAAAP,IAAA,EAAA0B,KAAA;YACA,KAAA1B,IAAA,CAAAC,UAAA,KAAAD,IAAA,CAAAE,YAAA,KAAAF,IAAA,CAAAG,YAAA,KAAAH,IAAA,CAAAI,OAAA,KAAAJ,IAAA,CAAAK,WAAA;cACAqE,MAAA,CAAA1B,QAAA,CAAAC,KAAA,UAAArE,MAAA,CAAA8C,KAAA;cACAkD,MAAA;YACA;UACA;UAEA,KAAAA,MAAA;YACA;UACA;;UAEA;UACA,IAAAF,MAAA,CAAA7K,IAAA,CAAAC,QAAA,UAAA4K,MAAA,CAAA7K,IAAA,CAAAE,gBAAA,aAAA2K,MAAA,CAAA7K,IAAA,CAAAI,aAAA,IAAAyK,MAAA,CAAA7K,IAAA,CAAAI,aAAA;YACAyK,MAAA,CAAA1B,QAAA,CAAAC,KAAA;YACA;UACA;UACA;UACA,IAAAyB,MAAA,CAAA7K,IAAA,CAAAC,QAAA,UAAA4K,MAAA,CAAA7K,IAAA,CAAAE,gBAAA;YACA,IAAA2K,MAAA,CAAA7K,IAAA,CAAAgB,OAAA;cACA6J,MAAA,CAAA7K,IAAA,CAAAiB,UAAA,GAAA4J,MAAA,CAAA7K,IAAA,CAAAgB,OAAA;YACA;UACA,WAAA6J,MAAA,CAAA7K,IAAA,CAAAiB,UAAA;YACA4J,MAAA,CAAA7K,IAAA,CAAAiB,UAAA,GAAA4J,MAAA,CAAA7K,IAAA,CAAAiB,UAAA;UACA;UACA;UACA,IAAA4J,MAAA,CAAA7K,IAAA,CAAAe,SAAA,IAAA8J,MAAA,CAAA7K,IAAA,CAAAgB,OAAA;YACA6J,MAAA,CAAA7K,IAAA,CAAAe,SAAA,GAAA8J,MAAA,CAAA7K,IAAA,CAAAe,SAAA;YACA8J,MAAA,CAAA7K,IAAA,CAAAgB,OAAA,GAAA6J,MAAA,CAAA7K,IAAA,CAAAgB,OAAA;UACA;;UAEA;UACA,IAAAgK,SAAA,GAAAH,MAAA,CAAA9K,MAAA,GAAAkL,gBAAA,GAAAC,aAAA;UACA,IAAAC,UAAA,GAAAN,MAAA,CAAA9K,MAAA;UACA4I,OAAA,CAAAC,GAAA,CAAAiC,MAAA,CAAA7K,IAAA;UACA;UACAgL,SAAA,CAAAH,MAAA,CAAA7K,IAAA,EAAAsF,IAAA,WAAAC,QAAA;YACA,IAAAA,QAAA,CAAAC,IAAA;cACAqF,MAAA,CAAA1B,QAAA,CAAAQ,OAAA,CAAAwB,UAAA;cACAN,MAAA,CAAAO,IAAA,CAAAC,aAAA,CAAAR,MAAA,CAAAzG,MAAA;cACA;cACAyG,MAAA,CAAAS,OAAA,CAAA3D,IAAA;gBACA4D,IAAA;gBACA1E,KAAA;kBACA2E,CAAA,EAAAnI,IAAA,CAAAkB,GAAA;kBACAkH,OAAA;gBACA;cACA;YACA;cACAZ,MAAA,CAAA1B,QAAA,CAAAC,KAAA,CAAA7D,QAAA,CAAAyE,GAAA;YACA;UACA,GAAA0B,KAAA,WAAAtC,KAAA;YACAT,OAAA,CAAAS,KAAA,SAAAA,KAAA;YACAyB,MAAA,CAAA1B,QAAA,CAAAC,KAAA;UACA;QACA;MACA;IACA;IACA;IACAuC,MAAA,WAAAA,OAAA;MACA,KAAAL,OAAA,CAAA3D,IAAA;IACA;IACA;IACAiE,eAAA,WAAAA,gBAAA7C,IAAA;MACA,IAAA8C,OAAA,GAAA9C,IAAA,CAAA+C,IAAA,CAAAC,OAAA;MACA,IAAAC,OAAA,GAAAjD,IAAA,CAAAkD,IAAA;MAEA,KAAAJ,OAAA;QACA,KAAA1C,QAAA,CAAAC,KAAA;QACA;MACA;MACA,KAAA4C,OAAA;QACA,KAAA7C,QAAA,CAAAC,KAAA;QACA;MACA;MACA;IACA;IAEA;IACA8C,gBAAA,WAAAA,iBAAAnD,IAAA;MACA,IAAAoD,OAAA,GAAApD,IAAA,CAAAkD,IAAA;MACA,KAAAE,OAAA;QACA,KAAAhD,QAAA,CAAAC,KAAA;QACA;MACA;MACA;IACA;IAEA;IACAgD,iBAAA,WAAAA,kBAAAC,GAAA,EAAAtD,IAAA,EAAApG,QAAA;MACAgG,OAAA,CAAAS,KAAA,UAAAiD,GAAA;MAEA,IAAAA,GAAA,CAAAC,MAAA;QACA,KAAAnD,QAAA,CAAAC,KAAA;MACA;QACA,KAAAD,QAAA,CAAAC,KAAA,YAAAiD,GAAA,CAAAzK,OAAA;MACA;IACA;IACA;IACA2K,iBAAA,WAAAA,kBAAA7G,EAAA;MACA,KAAAA,EAAA;;MAEA;MACA,IAAA8G,IAAA,QAAAvK,iBAAA,CAAAqG,IAAA,WAAAnC,IAAA;QAAA,OAAAA,IAAA,CAAAT,EAAA,IAAAA,EAAA;MAAA;MACA,IAAA8G,IAAA;QACA,OAAAA,IAAA,CAAAxF,SAAA;MACA;;MAEA;MACA,IAAAtB,EAAA,UAAA1F,IAAA,CAAAK,iBAAA,SAAAL,IAAA,CAAAM,aAAA;QACA,YAAAN,IAAA,CAAAM,aAAA;MACA;MAEA,IAAAoF,EAAA,UAAA1F,IAAA,CAAAO,kBAAA,SAAAP,IAAA,CAAAQ,cAAA,SAAAR,IAAA,CAAAC,QAAA;QACA,YAAAD,IAAA,CAAAQ,cAAA;MACA;MAEA,IAAAkF,EAAA,UAAA1F,IAAA,CAAAS,iBAAA,SAAAT,IAAA,CAAAU,aAAA;QACA,YAAAV,IAAA,CAAAU,aAAA;MACA;MAEA,IAAAgF,EAAA,UAAA1F,IAAA,CAAAW,iBAAA,SAAAX,IAAA,CAAAY,aAAA;QACA,YAAAZ,IAAA,CAAAY,aAAA;MACA;;MAEA;MACA,cAAAmE,MAAA,CAAAW,EAAA;IACA;IAEA;IACA+G,eAAA,WAAAA,gBAAA/G,EAAA;MACA,KAAAA,EAAA;;MAEA;MACA,IAAAgH,QAAA,QAAAxK,eAAA,CAAAoG,IAAA,WAAAnC,IAAA;QAAA,OAAAA,IAAA,CAAAT,EAAA,IAAAA,EAAA;MAAA;MACA,IAAAgH,QAAA;QACA,OAAAA,QAAA,CAAAlF,YAAA;MACA;;MAEA;MACA,IAAA9B,EAAA,UAAA1F,IAAA,CAAAO,kBAAA,SAAAP,IAAA,CAAAQ,cAAA,SAAAR,IAAA,CAAAC,QAAA;QACA,YAAAD,IAAA,CAAAQ,cAAA;MACA;;MAEA;MACA,cAAAuE,MAAA,CAAAW,EAAA;IACA;IACA;IACAG,sBAAA,WAAAA,uBAAAH,EAAA,EAAAvG,IAAA;MACA;MACA,UAAA8C,iBAAA,CAAA0K,IAAA,WAAAxG,IAAA;QAAA,OAAAA,IAAA,CAAAT,EAAA,IAAAA,EAAA;MAAA;QACA,KAAAzD,iBAAA,CAAA0F,IAAA;UACAjC,EAAA,EAAAA,EAAA;UACAsB,SAAA,EAAA7H;QACA;MACA;IACA;IAEA;IACA2G,oBAAA,WAAAA,qBAAAJ,EAAA,EAAAvG,IAAA;MACA;MACA,UAAA+C,eAAA,CAAAyK,IAAA,WAAAxG,IAAA;QAAA,OAAAA,IAAA,CAAAT,EAAA,IAAAA,EAAA;MAAA;QACA,KAAAxD,eAAA,CAAAyF,IAAA;UACAjC,EAAA,EAAAA,EAAA;UACA8B,YAAA,EAAArI;QACA;MACA;IACA;IAEA;IACAwH,oBAAA,WAAAA,qBAAAjB,EAAA,EAAAvG,IAAA,EAAAyN,IAAA,EAAAC,IAAA;MACA;MACA,UAAAxK,eAAA,CAAAsK,IAAA,WAAAxG,IAAA;QAAA,OAAAA,IAAA,CAAAT,EAAA,IAAAA,EAAA;MAAA;QACA,KAAArD,eAAA,CAAAsF,IAAA;UACAjC,EAAA,EAAAA,EAAA;UACAW,YAAA,EAAAlH,IAAA;UACAmH,YAAA,EAAAsG,IAAA;UACApG,WAAA,EAAAqG;QACA;MACA;IACA;IACA;IACAC,4BAAA,WAAAA,6BAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,4BAAA,IAAA1H,IAAA,WAAAC,QAAA;QACAwH,MAAA,CAAAE,QAAA,CAAA1H,QAAA,CAAAyE,GAAA;MACA;IACA;IAEA;IACAkD,oBAAA,WAAAA,qBAAA;MACA,KAAA7N,aAAA;IACA;IAEA;IACA8N,wBAAA,WAAAA,yBAAA;MACA,KAAA7N,MAAA,CAAAC,WAAA;IACA;IAEA;IACA6N,cAAA,WAAAA,eAAA;MACA,KAAA3D,KAAA,CAAAnK,MAAA,CAAA+N,MAAA;IACA;EACA;AACA", "ignoreList": []}]}