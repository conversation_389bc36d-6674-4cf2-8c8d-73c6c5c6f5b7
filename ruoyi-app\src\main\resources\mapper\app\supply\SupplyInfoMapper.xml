<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.app.supply.mapper.SupplyInfoMapper">
    
    <resultMap type="SupplyInfo" id="SupplyInfoResult">
        <result property="supplyCode"    column="SUPPLYCODE"    />
        <result property="supplyName"    column="SUPPLYNAME"    />
        <result property="supplyTax"     column="SUPPLYTAX"     />
        <result property="supplyAddr"    column="SUPPLYADDR"    />
        <result property="supplyCharge"  column="SUPPLYCHARGE"  />
        <result property="supplyTel"     column="SUPPLYTEL"     />
        <result property="state"         column="STATE"          />
    </resultMap>

    <sql id="selectSupplyInfo">
        select SUPPLYCODE, SUPPLYNAME, SUPPLYTAX, SUP<PERSON><PERSON>AD<PERSON>, SUPPLYCHARGE, SUPPLYTEL, STATE
        from EMPHEALTH.SUPPLY_INFO
    </sql>

    <select id="selectSupplyInfoList" parameterType="SupplyInfo" resultMap="SupplyInfoResult">
        <include refid="selectSupplyInfo"/>
        <where>
            <if test="supplyCode != null"> and SUPPLYCODE = #{supplyCode}</if>
            <if test="supplyName != null and supplyName != ''"> and SUPPLYNAME like '%'||#{supplyName}||'%'</if>
            <if test="supplyTax != null and supplyTax != ''"> and SUPPLYTAX = #{supplyTax}</if>
            <if test="supplyAddr != null and supplyAddr != ''"> and SUPPLYADDR like '%'||#{supplyAddr}||'%'</if>
            <if test="supplyCharge != null and supplyCharge != ''"> and SUPPLYCHARGE like '%'||#{supplyCharge}||'%'</if>
            <if test="supplyTel != null and supplyTel != ''"> and SUPPLYTEL like '%'||#{supplyTel}||'%'</if>
            <if test="state != null and state != ''"> and STATE = #{state}</if>
        </where>
        order by SUPPLYCODE
    </select>

    <select id="selectSupplyInfoByCode" parameterType="Integer" resultMap="SupplyInfoResult">
        <include refid="selectSupplyInfo"/>
        where SUPPLYCODE = #{supplyCode}
    </select>

    <insert id="insertSupplyInfo" parameterType="SupplyInfo">
        insert into EMPHEALTH.SUPPLY_INFO
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="supplyCode != null">SUPPLYCODE,</if>
            <if test="supplyName != null">SUPPLYNAME,</if>
            <if test="supplyTax != null">SUPPLYTAX,</if>
            <if test="supplyAddr != null">SUPPLYADDR,</if>
            <if test="supplyCharge != null">SUPPLYCHARGE,</if>
            <if test="supplyTel != null">SUPPLYTEL,</if>
            <if test="state != null">STATE,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="supplyCode != null">#{supplyCode},</if>
            <if test="supplyName != null">#{supplyName},</if>
            <if test="supplyTax != null">#{supplyTax},</if>
            <if test="supplyAddr != null">#{supplyAddr},</if>
            <if test="supplyCharge != null">#{supplyCharge},</if>
            <if test="supplyTel != null">#{supplyTel},</if>
            <if test="state != null">#{state},</if>
         </trim>
    </insert>

    <update id="updateSupplyInfo" parameterType="SupplyInfo">
        update EMPHEALTH.SUPPLY_INFO
        <trim prefix="SET" suffixOverrides=",">
            <if test="supplyName != null">SUPPLYNAME = #{supplyName},</if>
            <if test="supplyTax != null">SUPPLYTAX = #{supplyTax},</if>
            <if test="supplyAddr != null">SUPPLYADDR = #{supplyAddr},</if>
            <if test="supplyCharge != null">SUPPLYCHARGE = #{supplyCharge},</if>
            <if test="supplyTel != null">SUPPLYTEL = #{supplyTel},</if>
            <if test="state != null">STATE = #{state},</if>
        </trim>
        where SUPPLYCODE = #{supplyCode}
    </update>

    <delete id="deleteSupplyInfoByCode" parameterType="Integer">
        delete from EMPHEALTH.SUPPLY_INFO where SUPPLYCODE = #{supplyCode}
    </delete>

    <delete id="deleteSupplyInfoByCodes" parameterType="Integer">
        delete from EMPHEALTH.SUPPLY_INFO where SUPPLYCODE in
        <foreach item="supplyCode" collection="array" open="(" separator="," close=")">
            #{supplyCode}
        </foreach>
    </delete>

</mapper>
