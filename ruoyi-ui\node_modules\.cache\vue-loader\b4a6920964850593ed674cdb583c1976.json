{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\leave\\plan\\edit.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\leave\\plan\\edit.vue", "mtime": 1756084866763}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBsaXN0RGVwYXJ0bWVudCB9IGZyb20gIkAvYXBpL2xlYXZlL2RlcGFydG1lbnQiOw0KaW1wb3J0IHsgbGlzdEN1c3RvbWVyIH0gZnJvbSAiQC9hcGkvbGVhdmUvY3VzdG9tZXIiOw0KaW1wb3J0IHsgbGlzdE1hdGVyaWFsIH0gZnJvbSAiQC9hcGkvbGVhdmUvbWF0ZXJpYWwiOw0KaW1wb3J0IHsgYWRkUGxhbiwgdXBkYXRlUGxhbiwgZGV0YWlsUGxhbiwgZXhwb3J0TWF0ZXJpYWxUZW1wbGF0ZSwgaW1wb3J0TWF0ZXJpYWxMaXN0IH0gZnJvbSAiQC9hcGkvbGVhdmUvcGxhbiI7DQppbXBvcnQgeyBnZXRUb2tlbiB9IGZyb20gIkAvdXRpbHMvYXV0aCI7DQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogIkVkaXRMZWF2ZVBsYW4iLA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICAvLyDlr7zlhaXlj4LmlbANCiAgICAgIGltcG9ydFZpc2libGU6IGZhbHNlLA0KICAgICAgLy8g5LiK5Lyg5Y+C5pWwDQogICAgICB1cGxvYWQ6IHsNCiAgICAgICAgLy8g5piv5ZCm56aB55So5LiK5LygDQogICAgICAgIGlzVXBsb2FkaW5nOiBmYWxzZSwNCiAgICAgICAgLy8g6K6+572u5LiK5Lyg55qE6K+35rGC5aS06YOoDQogICAgICAgIGhlYWRlcnM6IHsgQXV0aG9yaXphdGlvbjogIkJlYXJlciAiICsgZ2V0VG9rZW4oKSB9LA0KICAgICAgICAvLyDkuIrkvKDnmoTlnLDlnYANCiAgICAgICAgdXJsOiBwcm9jZXNzLmVudi5WVUVfQVBQX0JBU0VfQVBJICsgIi93ZWIvbGVhdmUvcGxhbi9pbXBvcnRNYXRlcmlhbExpc3QiDQogICAgICB9LA0KICAgICAgLy8g5piv5ZCm5Li657yW6L6R5qih5byPDQogICAgICBpc0VkaXQ6IGZhbHNlLA0KICAgICAgLy8g6KGo5Y2V5Y+C5pWwDQogICAgICBmb3JtOiB7DQogICAgICAgIHBsYW5UeXBlOiAxLA0KICAgICAgICBidXNpbmVzc0NhdGVnb3J5OiAxLA0KICAgICAgICBtZWFzdXJlRmxhZzogMSwNCiAgICAgICAgcGxhbm5lZEFtb3VudDogMCwNCiAgICAgICAgc291cmNlQ29tcGFueUNvZGU6ICIiLA0KICAgICAgICBzb3VyY2VDb21wYW55OiAiIiwNCiAgICAgICAgcmVjZWl2ZUNvbXBhbnlDb2RlOiAiIiwNCiAgICAgICAgcmVjZWl2ZUNvbXBhbnk6ICIiLA0KICAgICAgICB0YXJnZXRDb21wYW55Q29kZTogIiIsDQogICAgICAgIHRhcmdldENvbXBhbnk6ICIiLA0KICAgICAgICByZWZ1bmRDb21wYW55Q29kZTogIiIsDQogICAgICAgIHJlZnVuZENvbXBhbnk6ICIiLA0KICAgICAgICBjb250cmFjdE5vOiAiIiwNCiAgICAgICAgcGxhbm5lZFJldHVyblRpbWU6ICIiLA0KICAgICAgICBzdGFydFRpbWU6ICIiLA0KICAgICAgICBlbmRUaW1lOiAiIiwNCiAgICAgICAgZXhwaXJlVGltZTogIiIsIC8vIOacieaViOacnw0KICAgICAgICBtb25pdG9yOiAiIiwNCiAgICAgICAgc3BlY2lhbE1hbmFnZXI6ICIiLA0KICAgICAgICBpdGVtVHlwZTogMSwNCiAgICAgICAgcmVhc29uOiAiIiwNCiAgICAgICAgc2VjQXBwcm92ZUZsYWc6IDAsDQogICAgICAgIGFwcGx5SW1nVXJsOiAiIiwgLy8g55Sz6K+35Zu+54mHVVJMDQogICAgICAgIGFwcGx5RmlsZVVybDogIiIsIC8vIOeUs+ivt+aWh+S7tlVSTA0KICAgICAgICBtYXRlcmlhbHM6IFtdDQogICAgICB9LA0KICAgICAgLy8g6KGo5Y2V5qCh6aqM6KeE5YiZDQogICAgICBydWxlczogew0KICAgICAgICBwbGFuVHlwZTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLor7fpgInmi6norqHliJLnsbvlnosiLCB0cmlnZ2VyOiAiY2hhbmdlIiB9DQogICAgICAgIF0sDQogICAgICAgIGJ1c2luZXNzQ2F0ZWdvcnk6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K+36YCJ5oup5Lia5Yqh57G75Z6LIiwgdHJpZ2dlcjogImNoYW5nZSIgfQ0KICAgICAgICBdLA0KICAgICAgICBtZWFzdXJlRmxhZzogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLor7fpgInmi6nmmK/lkKborqHph48iLCB0cmlnZ2VyOiAiY2hhbmdlIiB9DQogICAgICAgIF0sDQogICAgICAgIGFwcGx5Q29tcGFueTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLor7fpgInmi6nnlLPor7fljZXkvY0iLCB0cmlnZ2VyOiAiY2hhbmdlIiB9DQogICAgICAgIF0sDQogICAgICAgIHJlY2VpdmVDb21wYW55OiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuivt+mAieaLqeaUtui0p+WNleS9jSIsIHRyaWdnZXI6ICJjaGFuZ2UiIH0NCiAgICAgICAgXSwNCiAgICAgICAgdGFyZ2V0Q29tcGFueTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLor7fpgInmi6nov5Tlm57ljZXkvY0iLCB0cmlnZ2VyOiAiY2hhbmdlIiB9DQogICAgICAgIF0sDQogICAgICAgIG1vbml0b3I6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K+36L6T5YWl55uR6KOF5Lq6IiwgdHJpZ2dlcjogImJsdXIiIH0NCiAgICAgICAgXSwNCiAgICAgICAgc3BlY2lhbE1hbmFnZXI6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K+36L6T5YWl54mp6LWE5LiT566h5ZGYIiwgdHJpZ2dlcjogImJsdXIiIH0NCiAgICAgICAgXSwNCiAgICAgICAgbWF0ZXJpYWxUeXBlOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuivt+mAieaLqeeJqei1hOexu+WeiyIsIHRyaWdnZXI6ICJjaGFuZ2UiIH0NCiAgICAgICAgXSwNCiAgICAgICAgbGVhdmVSZWFzb246IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K+36L6T5YWl5Ye65Y6C5Y6f5ZugIiwgdHJpZ2dlcjogImJsdXIiIH0NCiAgICAgICAgXSwNCiAgICAgICAgcGxhbm5lZFJldHVyblRpbWU6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K+36YCJ5oup6K6h5YiS6L+U5Zue5pe26Ze0IiwgdHJpZ2dlcjogImNoYW5nZSIgfQ0KICAgICAgICBdDQogICAgICB9LA0KICAgICAgLy8g5Y2V5L2N5LiL5ouJ6YCJ6aG5DQogICAgICBkZXBhcnRtZW50T3B0aW9uczogW10sDQogICAgICBjdXN0b21lck9wdGlvbnM6IFtdLA0KICAgICAgZGVwYXJ0bWVudExvYWRpbmc6IGZhbHNlLA0KICAgICAgY3VzdG9tZXJMb2FkaW5nOiBmYWxzZSwNCiAgICAgIC8vIOeJqei1hOS4i+aLiemAiemhuQ0KICAgICAgbWF0ZXJpYWxPcHRpb25zOiBbXSwNCiAgICAgIG1hdGVyaWFsTG9hZGluZzogZmFsc2UsDQogICAgICANCiAgICAgIC8vIOS4iuS8oOebuOWFsw0KICAgICAgdXBsb2FkVXJsOiBwcm9jZXNzLmVudi5WVUVfQVBQX0JBU0VfQVBJICsgIi9jb21tb24vdXBsb2FkTWluaW8iLA0KICAgICAgdXBsb2FkUGFyYW1zOiB7DQogICAgICAgIC8vIOS4iuS8oOaXtuWPr+iDvemcgOimgeeahOmineWkluWPguaVsA0KICAgICAgICB1cGxvYWRUeXBlOiAnbGVhdmVQbGFuJw0KICAgICAgfSwNCiAgICAgIGltZ0ZpbGVMaXN0OiBbXSwgLy8g5Zu+54mH5paH5Lu25YiX6KGoDQogICAgICBmaWxlTGlzdDogW10sIC8vIOaWh+S7tuWIl+ihqA0KICAgICAgaW1nUHJldmlld1Zpc2libGU6IGZhbHNlLCAvLyDlm77niYfpooTop4jlr7nor53moYblj6/op4HmgKcNCiAgICAgIGltZ1ByZXZpZXdVcmw6ICIiLCAvLyDlm77niYfpooTop4hVUkwNCiAgICAgIA0KICAgICAgLy8g6YOo6Zeo5ZKM5a6i5oi36YCJ6aG55YWz6IGU5pig5bCEDQogICAgICBkZXBhcnRtZW50TWFwOiBuZXcgTWFwKCksIC8vIOmDqOmXqGlk5Yiw5ZCN56ew55qE5pig5bCEDQogICAgICBjdXN0b21lck1hcDogbmV3IE1hcCgpLCAvLyDlrqLmiLdpZOWIsOWQjeensOeahOaYoOWwhA0KICAgICAgLy8g5re75Yqg5pel5pyf6ZmQ5Yi25a+56LGhDQogICAgICBkYXRlUGlja2VyT3B0aW9uczogew0KICAgICAgICBkaXNhYmxlZERhdGUodGltZSkgew0KICAgICAgICAgIC8vIOiOt+WPluW9k+WJjeaciOS7veeahDI15Y+35pel5pyfDQogICAgICAgICAgY29uc3QgY3VycmVudERhdGUgPSBuZXcgRGF0ZSgpOw0KICAgICAgICAgIGNvbnN0IHllYXIgPSBjdXJyZW50RGF0ZS5nZXRGdWxsWWVhcigpOw0KICAgICAgICAgIGNvbnN0IG1vbnRoID0gY3VycmVudERhdGUuZ2V0TW9udGgoKTsNCiAgICAgICAgICBjb25zdCBtb250aExpbWl0ID0gbmV3IERhdGUoeWVhciwgbW9udGgsIDI1LCAyMywgNTksIDU5KTsNCiAgICAgICAgICANCiAgICAgICAgICAvLyDnpoHnlKjotoXov4flvZPmnIgyNeWPt+eahOaXpeacnw0KICAgICAgICAgIHJldHVybiB0aW1lLmdldFRpbWUoKSA+IG1vbnRoTGltaXQuZ2V0VGltZSgpOw0KICAgICAgICB9DQogICAgICB9LA0KICAgIH07DQogIH0sDQogIGNvbXB1dGVkOiB7DQogICAgLy8g54m55q6K5p2h5Lu277ya6Leo5Yy66LCD5ouo5LiU5pyJ6K6h5YiS6YeP6K6h6YeP5pe2DQogICAgaXNTcGVjaWFsQ29uZGl0aW9uKCkgew0KICAgICAgcmV0dXJuIHRoaXMuZm9ybS5wbGFuVHlwZSA9PT0gMyAmJiB0aGlzLmZvcm0uYnVzaW5lc3NDYXRlZ29yeSA9PT0gMjE7DQogICAgfQ0KICB9LA0KICB3YXRjaDogew0KICAgIC8vIOebkeaOp+eJueauiuadoeS7tuWPmOWMlu+8jOiHquWKqOiuvue9ruaYr+WQpuiuoemHj+S4uiLorqHph48iDQogICAgaXNTcGVjaWFsQ29uZGl0aW9uKHZhbCkgew0KICAgICAgaWYgKHZhbCkgew0KICAgICAgICB0aGlzLmZvcm0ubWVhc3VyZUZsYWcgPSAxOw0KICAgICAgfQ0KICAgIH0sDQogICAgLy8g6K6h5YiS57G75Z6L44CB5Lia5Yqh57G75Z6L44CB6K6h5YiS57uT5p2f5pe26Ze05Y+Y5YyW5pe277yM5ZCM5q2l5pyJ5pWI5pyfDQogICAgJ2Zvcm0ucGxhblR5cGUnOiB7DQogICAgaGFuZGxlcih2YWwpIHsNCiAgICAgIHRoaXMuc3luY0V4cGlyZVRpbWUoKTsNCiAgICB9DQogIH0sDQogICdmb3JtLmJ1c2luZXNzQ2F0ZWdvcnknOiB7DQogICAgaGFuZGxlcih2YWwpIHsNCiAgICAgIHRoaXMuc3luY0V4cGlyZVRpbWUoKTsNCiAgICB9DQogIH0sDQogICdmb3JtLmVuZFRpbWUnOiB7DQogICAgaGFuZGxlcih2YWwpIHsNCiAgICAgIHRoaXMuc3luY0V4cGlyZVRpbWUoKTsNCiAgICB9DQogIH0NCiAgfSwNCiAgY3JlYXRlZCgpIHsNCiAgICAvLyDojrflj5bot6/nlLHlj4LmlbDkuK3nmoRhcHBseU5v77yM5Yik5pat5piv5paw5aKe6L+Y5piv57yW6L6RDQogICAgY29uc3QgYXBwbHlObyA9IHRoaXMuJHJvdXRlLnBhcmFtcy5hcHBseU5vOw0KICAgIGlmIChhcHBseU5vKSB7DQogICAgICB0aGlzLmlzRWRpdCA9IHRydWU7DQogICAgICB0aGlzLmdldERldGFpbChhcHBseU5vKTsNCiAgICB9IGVsc2Ugew0KICAgICAgIC8vIOaWsOWinuaXtu+8jOiuvue9ruacieaViOacn+S4uuW9k+WJjeaXpeacnysz5aSpIDIzOjU5OjU5DQogICAgICBjb25zdCBub3cgPSBuZXcgRGF0ZSgpOw0KICAgICAgbm93LnNldERhdGUobm93LmdldERhdGUoKSArIDMpOw0KICAgICAgY29uc3QgeXl5eSA9IG5vdy5nZXRGdWxsWWVhcigpOw0KICAgICAgY29uc3QgbW0gPSBTdHJpbmcobm93LmdldE1vbnRoKCkgKyAxKS5wYWRTdGFydCgyLCAnMCcpOw0KICAgICAgY29uc3QgZGQgPSBTdHJpbmcobm93LmdldERhdGUoKSkucGFkU3RhcnQoMiwgJzAnKTsNCiAgICAgIHRoaXMuZm9ybS5leHBpcmVUaW1lID0gYCR7eXl5eX0tJHttbX0tJHtkZH1gOyANCiAgICAgIC8vIOWIneWni+WMluS4gOihjOeJqei1hOaVsOaNrg0KICAgICAgdGhpcy5oYW5kbGVBZGRNYXRlcmlhbCgpOw0KICAgICAgLy8g5qC55o2u6YCJ5Lit55qE6K6h5YiS57G75Z6L6K6+572u6buY6K6k5Lia5Yqh57G75Z6LDQogICAgICB0aGlzLmhhbmRsZVBsYW5UeXBlQ2hhbmdlKHRoaXMuZm9ybS5wbGFuVHlwZSk7DQogICAgICAvLyDliJ3lp4vljJbooajljZXmoKHpqozop4TliJkNCiAgICAgIHRoaXMudXBkYXRlRm9ybVJ1bGVzKCk7DQogICAgfQ0KICB9LA0KICBtZXRob2RzOiB7DQogICAgLy8g5ZCM5q2l5pyJ5pWI5pyfDQogICAgc3luY0V4cGlyZVRpbWUoKSB7DQogICAgaWYgKHRoaXMuZm9ybS5wbGFuVHlwZSA9PT0gMyAmJiB0aGlzLmZvcm0uYnVzaW5lc3NDYXRlZ29yeSA9PT0gMjEpIHsNCiAgICAgIHRoaXMuZm9ybS5leHBpcmVUaW1lID0gdGhpcy5mb3JtLmVuZFRpbWU7DQogICAgICB9ZWxzZSBpZiAoIXRoaXMuZm9ybS5leHBpcmVUaW1lKSB7DQogICAgICAvLyDlj6rlnKjmnInmlYjmnJ/kuLrnqbrml7borr7nva7pu5jorqTlgLzvvIgz5aSp77yJDQogICAgICBjb25zdCBub3cgPSBuZXcgRGF0ZSgpOw0KICAgICAgbm93LnNldERhdGUobm93LmdldERhdGUoKSArIDMpOw0KICAgICAgY29uc3QgeXl5eSA9IG5vdy5nZXRGdWxsWWVhcigpOw0KICAgICAgY29uc3QgbW0gPSBTdHJpbmcobm93LmdldE1vbnRoKCkgKyAxKS5wYWRTdGFydCgyLCAnMCcpOw0KICAgICAgY29uc3QgZGQgPSBTdHJpbmcobm93LmdldERhdGUoKSkucGFkU3RhcnQoMiwgJzAnKTsNCiAgICAgIHRoaXMuZm9ybS5leHBpcmVUaW1lID0gYCR7eXl5eX0tJHttbX0tJHtkZH1gOw0KICAgIH0NCiAgICB9LCAgICANCiAgICAvLyDojrflj5bnlLPor7for6bmg4UNCiAgICBnZXREZXRhaWwoYXBwbHlObykgew0KICAgICAgZGV0YWlsUGxhbihhcHBseU5vKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCkgew0KICAgICAgICAgIGNvbnN0IGRldGFpbCA9IHJlc3BvbnNlLmRhdGE7DQogICAgICAgICAgDQogICAgICAgICAgLy8g5aGr5YWF5Z+65pys5L+h5oGvDQogICAgICAgICAgdGhpcy5mb3JtLmlkID0gZGV0YWlsLmlkOw0KICAgICAgICAgIHRoaXMuZm9ybS5hcHBseU5vID0gZGV0YWlsLmFwcGx5Tm87DQogICAgICAgICAgdGhpcy5mb3JtLnBsYW5UeXBlID0gZGV0YWlsLnBsYW5UeXBlOw0KICAgICAgICAgIHRoaXMuZm9ybS5idXNpbmVzc0NhdGVnb3J5ID0gZGV0YWlsLmJ1c2luZXNzQ2F0ZWdvcnk7DQogICAgICAgICAgdGhpcy5mb3JtLm1lYXN1cmVGbGFnID0gZGV0YWlsLm1lYXN1cmVGbGFnOw0KICAgICAgICAgIHRoaXMuZm9ybS5wbGFubmVkQW1vdW50ID0gZGV0YWlsLnBsYW5uZWRBbW91bnQgfHwgMDsNCiAgICAgICAgICANCiAgICAgICAgICAvLyDkv53lrZjljZXkvY3ku6PnoIHlkozlkI3np7ANCiAgICAgICAgICB0aGlzLmZvcm0uc291cmNlQ29tcGFueUNvZGUgPSBkZXRhaWwuc291cmNlQ29tcGFueUNvZGU7DQogICAgICAgICAgdGhpcy5mb3JtLnNvdXJjZUNvbXBhbnkgPSBkZXRhaWwuc291cmNlQ29tcGFueTsNCiAgICAgICAgICB0aGlzLmZvcm0ucmVjZWl2ZUNvbXBhbnlDb2RlID0gZGV0YWlsLnJlY2VpdmVDb21wYW55Q29kZTsNCiAgICAgICAgICB0aGlzLmZvcm0ucmVjZWl2ZUNvbXBhbnkgPSBkZXRhaWwucmVjZWl2ZUNvbXBhbnk7DQogICAgICAgICAgdGhpcy5mb3JtLnRhcmdldENvbXBhbnlDb2RlID0gZGV0YWlsLnRhcmdldENvbXBhbnlDb2RlOw0KICAgICAgICAgIHRoaXMuZm9ybS50YXJnZXRDb21wYW55ID0gZGV0YWlsLnRhcmdldENvbXBhbnk7DQogICAgICAgICAgdGhpcy5mb3JtLnJlZnVuZENvbXBhbnlDb2RlID0gZGV0YWlsLnJlZnVuZERlcGFydG1lbnRDb2RlOw0KICAgICAgICAgIHRoaXMuZm9ybS5yZWZ1bmRDb21wYW55ID0gZGV0YWlsLnJlZnVuZERlcGFydG1lbnQ7DQogICAgICAgICAgDQogICAgICAgICAgLy8g5omL5Yqo5re75Yqg5bey5a2Y5Zyo55qE6YOo6Zeo5ZKM5a6i5oi35Yiw6YCJ6aG55pWw57uE77yM5Lul5L6/5q2j56Gu5pi+56S66YCJ5Lit55qE5YC8DQogICAgICAgICAgLy8g5re75Yqg55Sz6K+35Y2V5L2N5Yiw6YOo6Zeo6YCJ6aG5DQogICAgICAgICAgaWYgKGRldGFpbC5zb3VyY2VDb21wYW55Q29kZSAmJiBkZXRhaWwuc291cmNlQ29tcGFueSkgew0KICAgICAgICAgICAgdGhpcy5hZGRUb0RlcGFydG1lbnRPcHRpb25zKGRldGFpbC5zb3VyY2VDb21wYW55Q29kZSwgZGV0YWlsLnNvdXJjZUNvbXBhbnkpOw0KICAgICAgICAgIH0NCiAgICAgICAgICANCiAgICAgICAgICAvLyDmt7vliqDmlLbotKfljZXkvY3vvIjlj6/og73mmK/pg6jpl6jmiJblrqLmiLfvvIkNCiAgICAgICAgICBpZiAoZGV0YWlsLnJlY2VpdmVDb21wYW55Q29kZSAmJiBkZXRhaWwucmVjZWl2ZUNvbXBhbnkpIHsNCiAgICAgICAgICAgIGlmIChkZXRhaWwucGxhblR5cGUgPT09IDMpIHsNCiAgICAgICAgICAgICAgLy8g6Leo5Yy66LCD5ouo5pe277yM5pS26LSn5Y2V5L2N5piv6YOo6ZeoDQogICAgICAgICAgICAgIHRoaXMuYWRkVG9EZXBhcnRtZW50T3B0aW9ucyhkZXRhaWwucmVjZWl2ZUNvbXBhbnlDb2RlLCBkZXRhaWwucmVjZWl2ZUNvbXBhbnkpOw0KICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgLy8g5YW25LuW5oOF5Ya177yM5pS26LSn5Y2V5L2N5piv5a6i5oi3DQogICAgICAgICAgICAgIHRoaXMuYWRkVG9DdXN0b21lck9wdGlvbnMoZGV0YWlsLnJlY2VpdmVDb21wYW55Q29kZSwgZGV0YWlsLnJlY2VpdmVDb21wYW55KTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgICAgDQogICAgICAgICAgLy8g5re75Yqg6L+U5Zue5Y2V5L2N5Yiw6YOo6Zeo6YCJ6aG5DQogICAgICAgICAgaWYgKGRldGFpbC50YXJnZXRDb21wYW55Q29kZSAmJiBkZXRhaWwudGFyZ2V0Q29tcGFueSkgew0KICAgICAgICAgICAgdGhpcy5hZGRUb0RlcGFydG1lbnRPcHRpb25zKGRldGFpbC50YXJnZXRDb21wYW55Q29kZSwgZGV0YWlsLnRhcmdldENvbXBhbnkpOw0KICAgICAgICAgIH0NCiAgICAgICAgICANCiAgICAgICAgICAvLyDmt7vliqDpgIDotKfljZXkvY3liLDpg6jpl6jpgInpobkNCiAgICAgICAgICBpZiAoZGV0YWlsLnJlZnVuZERlcGFydG1lbnRDb2RlICYmIGRldGFpbC5yZWZ1bmREZXBhcnRtZW50KSB7DQogICAgICAgICAgICB0aGlzLmFkZFRvRGVwYXJ0bWVudE9wdGlvbnMoZGV0YWlsLnJlZnVuZERlcGFydG1lbnRDb2RlLCBkZXRhaWwucmVmdW5kRGVwYXJ0bWVudCk7DQogICAgICAgICAgfQ0KICAgICAgICAgIA0KICAgICAgICAgIHRoaXMuZm9ybS5wbGFubmVkUmV0dXJuVGltZSA9IGRldGFpbC5wbGFuUmV0dXJuVGltZTsNCiAgICAgICAgICB0aGlzLmZvcm0uc3RhcnRUaW1lID0gZGV0YWlsLnN0YXJ0VGltZTsNCiAgICAgICAgICB0aGlzLmZvcm0uZW5kVGltZSA9IGRldGFpbC5lbmRUaW1lOw0KICAgICAgICAgIHRoaXMuZm9ybS5tb25pdG9yID0gZGV0YWlsLm1vbml0b3I7DQogICAgICAgICAgdGhpcy5mb3JtLnNwZWNpYWxNYW5hZ2VyID0gZGV0YWlsLnNwZWNpYWxNYW5hZ2VyOw0KICAgICAgICAgIHRoaXMuZm9ybS5pdGVtVHlwZSA9IGRldGFpbC5pdGVtVHlwZTsNCiAgICAgICAgICB0aGlzLmZvcm0ucmVhc29uID0gZGV0YWlsLnJlYXNvbjsNCiAgICAgICAgICB0aGlzLmZvcm0uY29udHJhY3RObyA9IGRldGFpbC5jb250cmFjdE5vOw0KICAgICAgICAgIHRoaXMuZm9ybS5zZWNBcHByb3ZlRmxhZyA9IGRldGFpbC5zZWNBcHByb3ZlRmxhZzsNCiAgICAgICAgICB0aGlzLmZvcm0uYXBwbHlJbWdVcmwgPSBkZXRhaWwuYXBwbHlJbWdVcmw7DQogICAgICAgICAgdGhpcy5mb3JtLmFwcGx5RmlsZVVybCA9IGRldGFpbC5hcHBseUZpbGVVcmw7DQogICAgICAgICAgDQogICAgICAgICAgLy8g5aSE55CG5Zu+54mH5ZKM5paH5Lu25YiX6KGoDQogICAgICAgICAgdGhpcy5pbml0RmlsZUxpc3QoKTsNCiAgICAgICAgICANCiAgICAgICAgICAvLyDloavlhYXnianotYTliJfooagNCiAgICAgICAgICBpZiAoZGV0YWlsLm1hdGVyaWFscyAmJiBkZXRhaWwubWF0ZXJpYWxzLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICAgIHRoaXMuZm9ybS5tYXRlcmlhbHMgPSBkZXRhaWwubWF0ZXJpYWxzLm1hcChpdGVtID0+ICh7DQogICAgICAgICAgICAgIGlkOiBpdGVtLmlkLA0KICAgICAgICAgICAgICBtYXRlcmlhbElkOiBpdGVtLm1hdGVyaWFsSWQsDQogICAgICAgICAgICAgIG1hdGVyaWFsTmFtZTogaXRlbS5tYXRlcmlhbE5hbWUsDQogICAgICAgICAgICAgIG1hdGVyaWFsU3BlYzogaXRlbS5tYXRlcmlhbFNwZWMsDQogICAgICAgICAgICAgIHBsYW5OdW06IGl0ZW0ucGxhbk51bSwNCiAgICAgICAgICAgICAgbWVhc3VyZVVuaXQ6IGl0ZW0ubWVhc3VyZVVuaXQsDQogICAgICAgICAgICAgIHJlbWFyazogaXRlbS5yZW1hcmsNCiAgICAgICAgICAgIH0pKTsNCiAgICAgICAgICAgIA0KICAgICAgICAgICAgLy8g5re75Yqg54mp6LWE5Yiw6YCJ6aG55pWw57uEDQogICAgICAgICAgICBkZXRhaWwubWF0ZXJpYWxzLmZvckVhY2goaXRlbSA9PiB7DQogICAgICAgICAgICAgIGlmIChpdGVtLm1hdGVyaWFsSWQgJiYgaXRlbS5tYXRlcmlhbE5hbWUpIHsNCiAgICAgICAgICAgICAgICB0aGlzLmFkZFRvTWF0ZXJpYWxPcHRpb25zKGl0ZW0ubWF0ZXJpYWxJZCwgaXRlbS5tYXRlcmlhbE5hbWUsIGl0ZW0ubWF0ZXJpYWxTcGVjLCBpdGVtLm1lYXN1cmVVbml0KTsNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHRoaXMuaGFuZGxlQWRkTWF0ZXJpYWwoKTsNCiAgICAgICAgICB9DQogICAgICAgICAgDQogICAgICAgICAgLy8g5pu05paw6KGo5Y2V5qCh6aqM6KeE5YiZDQogICAgICAgICAgdGhpcy51cGRhdGVGb3JtUnVsZXMoKTsNCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCiAgICAvLyDov5znqIvmkJzntKLpg6jpl6gNCiAgICByZW1vdGVTZWFyY2hEZXBhcnRtZW50KHF1ZXJ5KSB7DQogICAgICBpZiAocXVlcnkgIT09ICcnKSB7DQogICAgICAgIHRoaXMuZGVwYXJ0bWVudExvYWRpbmcgPSB0cnVlOw0KICAgICAgICBsaXN0RGVwYXJ0bWVudCh7DQogICAgICAgICAgc3RvcmVOYW1lOiBxdWVyeSwNCiAgICAgICAgICBwYWdlTnVtOiAxLA0KICAgICAgICAgIHBhZ2VTaXplOiAyMA0KICAgICAgICB9KS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgICB0aGlzLmRlcGFydG1lbnRMb2FkaW5nID0gZmFsc2U7DQogICAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCkgew0KICAgICAgICAgICAgdGhpcy5kZXBhcnRtZW50T3B0aW9ucyA9IHJlc3BvbnNlLnJvd3M7DQogICAgICAgICAgfQ0KICAgICAgICB9KS5maW5hbGx5KCgpID0+IHsNCiAgICAgICAgICB0aGlzLmRlcGFydG1lbnRMb2FkaW5nID0gZmFsc2U7DQogICAgICAgIH0pOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5kZXBhcnRtZW50T3B0aW9ucyA9IFtdOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvLyDov5znqIvmkJzntKLlrqLmiLcNCiAgICByZW1vdGVTZWFyY2hDdXN0b21lcihxdWVyeSkgew0KICAgICAgaWYgKHF1ZXJ5ICE9PSAnJykgew0KICAgICAgICB0aGlzLmN1c3RvbWVyTG9hZGluZyA9IHRydWU7DQogICAgICAgIGxpc3RDdXN0b21lcih7DQogICAgICAgICAgY3VzdG9tZXJOYW1lOiBxdWVyeSwNCiAgICAgICAgICBwYWdlTnVtOiAxLA0KICAgICAgICAgIHBhZ2VTaXplOiAyMA0KICAgICAgICB9KS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgICB0aGlzLmN1c3RvbWVyTG9hZGluZyA9IGZhbHNlOw0KICAgICAgICAgIGlmIChyZXNwb25zZS5jb2RlID09PSAyMDApIHsNCiAgICAgICAgICAgIHRoaXMuY3VzdG9tZXJPcHRpb25zID0gcmVzcG9uc2Uucm93czsNCiAgICAgICAgICB9DQogICAgICAgIH0pLmZpbmFsbHkoKCkgPT4gew0KICAgICAgICAgIHRoaXMuY3VzdG9tZXJMb2FkaW5nID0gZmFsc2U7DQogICAgICAgIH0pOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5jdXN0b21lck9wdGlvbnMgPSBbXTsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy8g5Lia5Yqh57G75Z6L5Y+Y5pu05pe26Kem5Y+RDQogICAgaGFuZGxlQnVzaW5lc3NDYXRlZ29yeUNoYW5nZSgpIHsNCiAgICAgIC8vIOabtOaWsOihqOWNleagoemqjOinhOWImQ0KICAgICAgdGhpcy51cGRhdGVGb3JtUnVsZXMoKTsNCiAgICB9LA0KDQogICAgLy8g6K6h5YiS57G75Z6L5Y+Y5pu05pe26Kem5Y+RDQogICAgaGFuZGxlUGxhblR5cGVDaGFuZ2UodmFsKSB7DQogICAgICAvLyDmoLnmja7orqHliJLnsbvlnovorr7nva7pu5jorqTkuJrliqHnsbvlnosNCiAgICAgIHN3aXRjaCAodmFsKSB7DQogICAgICAgIGNhc2UgMToNCiAgICAgICAgICB0aGlzLmZvcm0uYnVzaW5lc3NDYXRlZ29yeSA9IDE7DQogICAgICAgICAgYnJlYWs7DQogICAgICAgIGNhc2UgMjoNCiAgICAgICAgICB0aGlzLmZvcm0uYnVzaW5lc3NDYXRlZ29yeSA9IDExOw0KICAgICAgICAgIGJyZWFrOw0KICAgICAgICBjYXNlIDM6DQogICAgICAgICAgdGhpcy5mb3JtLmJ1c2luZXNzQ2F0ZWdvcnkgPSAyMTsNCiAgICAgICAgICBicmVhazsNCiAgICAgICAgY2FzZSA0Og0KICAgICAgICAgIHRoaXMuZm9ybS5idXNpbmVzc0NhdGVnb3J5ID0gMzE7DQogICAgICAgICAgYnJlYWs7DQogICAgICAgIGRlZmF1bHQ6DQogICAgICAgICAgYnJlYWs7DQogICAgICB9DQoNCiAgICAgIC8vIOmHjee9ruS4jemcgOimgeeahOWtl+autQ0KICAgICAgaWYgKHZhbCAhPT0gMikgew0KICAgICAgICB0aGlzLmZvcm0udGFyZ2V0Q29tcGFueSA9ICIiOw0KICAgICAgICB0aGlzLmZvcm0ucGxhbm5lZFJldHVyblRpbWUgPSAiIjsNCiAgICAgIH0NCg0KICAgICAgaWYgKHZhbCAhPT0gNCkgew0KICAgICAgICB0aGlzLmZvcm0ucmVmdW5kQ29tcGFueSA9ICIiOw0KICAgICAgfQ0KDQogICAgICBpZiAodmFsICE9PSAzIHx8IHRoaXMuZm9ybS5idXNpbmVzc0NhdGVnb3J5ICE9PSAyMSkgew0KICAgICAgICB0aGlzLmZvcm0uc3RhcnRUaW1lID0gIiI7DQogICAgICAgIHRoaXMuZm9ybS5lbmRUaW1lID0gIiI7DQogICAgICB9DQoNCiAgICAgIC8vIOabtOaWsOihqOWNleagoemqjOinhOWImQ0KICAgICAgdGhpcy51cGRhdGVGb3JtUnVsZXMoKTsNCiAgICB9LA0KDQogICAgLy8g5pu05paw6KGo5Y2V5qCh6aqM6KeE5YiZDQogICAgdXBkYXRlRm9ybVJ1bGVzKCkgew0KICAgICAgY29uc3QgdGVtcFJ1bGVzID0gew0KICAgICAgICBwbGFuVHlwZTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLor7fpgInmi6norqHliJLnsbvlnosiLCB0cmlnZ2VyOiAiY2hhbmdlIiB9DQogICAgICAgIF0sDQogICAgICAgIGJ1c2luZXNzQ2F0ZWdvcnk6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K+36YCJ5oup5Lia5Yqh57G75Z6LIiwgdHJpZ2dlcjogImNoYW5nZSIgfQ0KICAgICAgICBdLA0KICAgICAgICBtZWFzdXJlRmxhZzogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLor7fpgInmi6nmmK/lkKborqHph48iLCB0cmlnZ2VyOiAiY2hhbmdlIiB9DQogICAgICAgIF0sDQogICAgICAgIGFwcGx5Q29tcGFueTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLor7fpgInmi6nnlLPor7fljZXkvY0iLCB0cmlnZ2VyOiAiY2hhbmdlIiB9DQogICAgICAgIF0sDQogICAgICAgIG1vbml0b3I6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K+36L6T5YWl55uR6KOF5Lq6IiwgdHJpZ2dlcjogImJsdXIiIH0NCiAgICAgICAgXSwNCiAgICAgICAgc3BlY2lhbE1hbmFnZXI6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K+36L6T5YWl54mp6LWE5LiT566h5ZGYIiwgdHJpZ2dlcjogImJsdXIiIH0NCiAgICAgICAgXSwNCiAgICAgICAgbWF0ZXJpYWxUeXBlOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuivt+mAieaLqeeJqei1hOexu+WeiyIsIHRyaWdnZXI6ICJjaGFuZ2UiIH0NCiAgICAgICAgXQ0KICAgICAgfTsNCg0KICAgICAgLy8g5qC55o2u5p2h5Lu25re75Yqg5qCh6aqM6KeE5YiZDQogICAgICBpZiAodGhpcy5mb3JtLnBsYW5UeXBlICE9PSAxKSB7DQogICAgICAgIHRlbXBSdWxlcy5yZWNlaXZlQ29tcGFueSA9IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K+36YCJ5oup5pS26LSn5Y2V5L2NIiwgdHJpZ2dlcjogImNoYW5nZSIgfQ0KICAgICAgICBdOw0KICAgICAgfQ0KDQogICAgICBpZiAodGhpcy5mb3JtLnBsYW5UeXBlID09PSAyKSB7DQogICAgICAgIHRlbXBSdWxlcy50YXJnZXRDb21wYW55ID0gWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLor7fpgInmi6nov5Tlm57ljZXkvY0iLCB0cmlnZ2VyOiAiY2hhbmdlIiB9DQogICAgICAgIF07DQogICAgICAgIHRlbXBSdWxlcy5wbGFubmVkUmV0dXJuVGltZSA9IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K+36YCJ5oup6K6h5YiS6L+U5Zue5pe26Ze0IiwgdHJpZ2dlcjogImNoYW5nZSIgfQ0KICAgICAgICBdOw0KICAgICAgfQ0KDQogICAgICBpZiAodGhpcy5mb3JtLnBsYW5UeXBlID09PSA0KSB7DQogICAgICAgIHRlbXBSdWxlcy5yZWZ1bmRDb21wYW55ID0gWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLor7fpgInmi6npgIDotKfljZXkvY0iLCB0cmlnZ2VyOiAiY2hhbmdlIiB9DQogICAgICAgIF07DQogICAgICB9DQoNCiAgICAgIGlmICh0aGlzLmZvcm0ucGxhblR5cGUgPT09IDMgJiYgdGhpcy5mb3JtLmJ1c2luZXNzQ2F0ZWdvcnkgPT09IDIxKSB7DQogICAgICAgIHRlbXBSdWxlcy5wbGFubmVkQW1vdW50ID0gWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLor7fovpPlhaXorqHliJLph48iLCB0cmlnZ2VyOiAiYmx1ciIgfQ0KICAgICAgICBdOw0KICAgICAgICB0ZW1wUnVsZXMuc3RhcnRUaW1lID0gWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLor7fpgInmi6nlvIDlp4vml7bpl7QiLCB0cmlnZ2VyOiAiY2hhbmdlIiB9DQogICAgICAgIF07DQogICAgICAgIHRlbXBSdWxlcy5lbmRUaW1lID0gWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLor7fpgInmi6nnu5PmnZ/ml7bpl7QiLCB0cmlnZ2VyOiAiY2hhbmdlIiB9DQogICAgICAgIF07DQogICAgICB9DQoNCiAgICAgIC8vIOabtOaWsOinhOWImQ0KICAgICAgdGhpcy5ydWxlcyA9IHRlbXBSdWxlczsNCiAgICB9LA0KDQogICAgLy8g5re75Yqg54mp6LWEDQogICAgaGFuZGxlQWRkTWF0ZXJpYWwoKSB7DQogICAgICB0aGlzLmZvcm0ubWF0ZXJpYWxzLnB1c2goew0KICAgICAgICBtYXRlcmlhbElkOiAiIiwNCiAgICAgICAgbWF0ZXJpYWxOYW1lOiAiIiwNCiAgICAgICAgbWF0ZXJpYWxTcGVjOiAiIiwNCiAgICAgICAgcGxhbk51bTogMSwNCiAgICAgICAgbWVhc3VyZVVuaXQ6ICIiLA0KICAgICAgICByZW1hcms6ICIiDQogICAgICB9KTsNCiAgICB9LA0KICAgIC8vIOWIoOmZpOeJqei1hA0KICAgIGhhbmRsZURlbGV0ZU1hdGVyaWFsKGluZGV4KSB7DQogICAgICB0aGlzLmZvcm0ubWF0ZXJpYWxzLnNwbGljZShpbmRleCwgMSk7DQogICAgICAvLyDlpoLmnpzliKDlrozkuobvvIzoh7PlsJHkv53nlZnkuIDooYwNCiAgICAgIGlmICh0aGlzLmZvcm0ubWF0ZXJpYWxzLmxlbmd0aCA9PT0gMCkgew0KICAgICAgICB0aGlzLmhhbmRsZUFkZE1hdGVyaWFsKCk7DQogICAgICB9DQogICAgfSwNCiAgICAvLyDov5znqIvmkJzntKLnianotYQNCiAgICByZW1vdGVTZWFyY2hNYXRlcmlhbChxdWVyeSkgew0KICAgICAgaWYgKHF1ZXJ5ICE9PSAnJykgew0KICAgICAgICB0aGlzLm1hdGVyaWFsTG9hZGluZyA9IHRydWU7DQogICAgICAgIGxpc3RNYXRlcmlhbCh7DQogICAgICAgICAgbWF0ZXJpYWxOYW1lOiBxdWVyeSwNCiAgICAgICAgICBwYWdlTnVtOiAxLA0KICAgICAgICAgIHBhZ2VTaXplOiAyMA0KICAgICAgICB9KS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgICB0aGlzLm1hdGVyaWFsTG9hZGluZyA9IGZhbHNlOw0KICAgICAgICAgIGlmIChyZXNwb25zZS5jb2RlID09PSAyMDApIHsNCiAgICAgICAgICAgIHRoaXMubWF0ZXJpYWxPcHRpb25zID0gcmVzcG9uc2Uucm93czsNCiAgICAgICAgICB9DQogICAgICAgIH0pLmZpbmFsbHkoKCkgPT4gew0KICAgICAgICAgIHRoaXMubWF0ZXJpYWxMb2FkaW5nID0gZmFsc2U7DQogICAgICAgIH0pOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5tYXRlcmlhbE9wdGlvbnMgPSBbXTsNCiAgICAgIH0NCiAgICB9LA0KICAgIC8vIOWkhOeQhueJqei1hOmAieaLqQ0KICAgIGhhbmRsZU1hdGVyaWFsU2VsZWN0KHZhbHVlLCByb3cpIHsNCiAgICAgIC8vIOagueaNrumAieS4reeahOeJqei1hElE5om+5Yiw5a+55bqU55qE54mp6LWE6K+m5oOFDQogICAgICBjb25zdCBzZWxlY3RlZE1hdGVyaWFsID0gdGhpcy5tYXRlcmlhbE9wdGlvbnMuZmluZChpdGVtID0+IGl0ZW0uaWQgPT09IHZhbHVlKTsNCiAgICAgIGlmIChzZWxlY3RlZE1hdGVyaWFsKSB7DQogICAgICAgIHJvdy5tYXRlcmlhbE5hbWUgPSBzZWxlY3RlZE1hdGVyaWFsLm1hdGVyaWFsTmFtZTsNCiAgICAgICAgLy8g5aaC5p6c54mp6LWE5L+h5oGv5Lit5pyJ5Z6L5Y+36KeE5qC85ZKM5Y2V5L2N77yM5Y+v5Lul6Ieq5Yqo5aGr5YWFDQogICAgICAgIGlmIChzZWxlY3RlZE1hdGVyaWFsLm1hdGVyaWFsU3BlYykgew0KICAgICAgICAgIHJvdy5tYXRlcmlhbFNwZWMgPSBzZWxlY3RlZE1hdGVyaWFsLm1hdGVyaWFsU3BlYzsNCiAgICAgICAgfQ0KICAgICAgICBpZiAoc2VsZWN0ZWRNYXRlcmlhbC5tZWFzdXJlVW5pdCkgew0KICAgICAgICAgIHJvdy5tZWFzdXJlVW5pdCA9IHNlbGVjdGVkTWF0ZXJpYWwubWVhc3VyZVVuaXQ7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9LA0KICAgIC8vIOWIneWni+WMluaWh+S7tuWIl+ihqA0KICAgIGluaXRGaWxlTGlzdCgpIHsNCiAgICAgIC8vIOWkhOeQhuWbvueJhw0KICAgICAgaWYgKHRoaXMuZm9ybS5hcHBseUltZ1VybCkgew0KICAgICAgICB0cnkgew0KICAgICAgICAgIC8vIOWwneivleino+aekEpTT04NCiAgICAgICAgICBjb25zdCBpbWdGaWxlcyA9IEpTT04ucGFyc2UodGhpcy5mb3JtLmFwcGx5SW1nVXJsKTsNCiAgICAgICAgICB0aGlzLmltZ0ZpbGVMaXN0ID0gaW1nRmlsZXMubWFwKGl0ZW0gPT4gew0KICAgICAgICAgICAgcmV0dXJuIHsNCiAgICAgICAgICAgICAgbmFtZTogaXRlbS5uYW1lLA0KICAgICAgICAgICAgICB1cmw6IGl0ZW0udXJsDQogICAgICAgICAgICB9Ow0KICAgICAgICAgIH0pOw0KICAgICAgICB9IGNhdGNoIChlKSB7DQogICAgICAgICAgY29uc29sZS5sb2coZSk7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIA0KICAgICAgLy8g5aSE55CG5paH5Lu2DQogICAgICBpZiAodGhpcy5mb3JtLmFwcGx5RmlsZVVybCkgew0KICAgICAgICB0cnkgew0KICAgICAgICAgIC8vIOWwneivleino+aekEpTT04NCiAgICAgICAgICBjb25zdCBmaWxlcyA9IEpTT04ucGFyc2UodGhpcy5mb3JtLmFwcGx5RmlsZVVybCk7DQogICAgICAgICAgdGhpcy5maWxlTGlzdCA9IGZpbGVzLm1hcChpdGVtID0+IHsNCiAgICAgICAgICAgIHJldHVybiB7DQogICAgICAgICAgICAgIG5hbWU6IGl0ZW0ubmFtZSwNCiAgICAgICAgICAgICAgdXJsOiBpdGVtLnVybCwNCiAgICAgICAgICAgIH07DQogICAgICAgICAgfSk7DQogICAgICAgIH0gY2F0Y2ggKGUpIHsNCiAgICAgICAgICBjb25zb2xlLmxvZyhlKTsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0sDQogICAgDQogICAgLy8g5Zu+54mH5LiK5Lyg5oiQ5YqfDQogICAgaGFuZGxlSW1nU3VjY2VzcyhyZXNwb25zZSwgZmlsZSwgZmlsZUxpc3QpIHsNCiAgICAgIGNvbnNvbGUubG9nKHJlc3BvbnNlKTsNCiAgICAgIGNvbnNvbGUubG9nKGZpbGUpOw0KICAgICAgY29uc29sZS5sb2coZmlsZUxpc3QpOw0KICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCkgew0KICAgICAgICAvLyDmm7TmlrDlm77niYdVUkzliLDooajljZUNCiAgICAgICAgLy8gdGhpcy5pbWdGaWxlTGlzdCA9IGZpbGVMaXN0Ow0KICAgICAgICANCiAgICAgICAgLy8g5p6E5bu6SlNPTuagvOW8j+eahOWbvueJh+aVsOaNrg0KICAgICAgICAvKioNCiAgICAgICAgICogcmVzcG9uc2XmoLzlvI/vvJoNCiAgICAgICAgICogew0KICAgICJtc2ciOiAi5pON5L2c5oiQ5YqfIiwNCiAgICAib3JpZ2luYWxGaWxlTmFtZSI6ICIwMDZyM1BRQmp3MWY4cDN6YjF3aW9qMzBjODBjOGpydS5qcGciLA0KICAgICJmaWxlTmFtZSI6ICJ4Y3RnLzIwMjUvMDMvMjUvZGEyYTRhM2YtNjk2Mi00ODMxLWE0ZTUtNmVjYzIzOTdlNDgzLmpwZyIsDQogICAgImNvZGUiOiAyMDAsDQogICAgInVybCI6ICJodHRwczovL3lkeHQuY2l0aWNzdGVlbC5jb206ODA5OS9taW5pby94Y3RnLzIwMjUvMDMvMjUvZGEyYTRhM2YtNjk2Mi00ODMxLWE0ZTUtNmVjYzIzOTdlNDgzLmpwZyINCn0NCiAgICAgICAgICovDQoNCiAgICAgICAgDQogICAgICAgIC8vIOWPquiOt+WPlm9yaWdpbmFsRmlsZU5hbWXjgIF1cmwg6L2s5o2i5Li6SlNPTuWtl+espuS4suS/neWtmA0KICAgICAgICBjb25zdCBpbWdEYXRhID0gew0KICAgICAgICAgIG5hbWU6IHJlc3BvbnNlLm9yaWdpbmFsRmlsZU5hbWUsDQogICAgICAgICAgdXJsOiByZXNwb25zZS51cmwNCiAgICAgICAgfTsNCiAgICAgICAgdGhpcy5pbWdGaWxlTGlzdC5wdXNoKGltZ0RhdGEpOw0KICAgICAgICBjb25zb2xlLmxvZyh0aGlzLmltZ0ZpbGVMaXN0KTsNCiAgICAgICAgdGhpcy5mb3JtLmFwcGx5SW1nVXJsID0gSlNPTi5zdHJpbmdpZnkodGhpcy5pbWdGaWxlTGlzdCk7DQogICAgICAgIGNvbnNvbGUubG9nKHRoaXMuZm9ybS5hcHBseUltZ1VybCk7DQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCflm77niYfkuIrkvKDlpLHotKUnKTsNCiAgICAgIH0NCiAgICB9LA0KICAgIA0KICAgIC8vIOmZhOS7tuS4iuS8oOaIkOWKnw0KICAgIGhhbmRsZUFubmV4RmlsZVN1Y2Nlc3MocmVzcG9uc2UsIGZpbGUsIGZpbGVMaXN0KSB7DQogICAgICBjb25zb2xlLmxvZyhyZXNwb25zZSk7DQogICAgICBjb25zb2xlLmxvZyhmaWxlKTsNCiAgICAgIGNvbnNvbGUubG9nKGZpbGVMaXN0KTsNCiAgICAgIGlmIChyZXNwb25zZS5jb2RlID09PSAyMDApIHsNCiAgICAgICAgLy8g5Y+q6I635Y+Wb3JpZ2luYWxGaWxlTmFtZeOAgXVybCDovazmjaLkuLpKU09O5a2X56ym5Liy5L+d5a2YDQogICAgICAgIGNvbnN0IGFubmV4RmlsZURhdGEgPSB7DQogICAgICAgICAgbmFtZTogcmVzcG9uc2Uub3JpZ2luYWxGaWxlTmFtZSwNCiAgICAgICAgICB1cmw6IHJlc3BvbnNlLnVybA0KICAgICAgICB9Ow0KICAgICAgICB0aGlzLmZpbGVMaXN0LnB1c2goYW5uZXhGaWxlRGF0YSk7DQogICAgICAgIGNvbnNvbGUubG9nKHRoaXMuZmlsZUxpc3QpOw0KICAgICAgICB0aGlzLmZvcm0uYXBwbHlGaWxlVXJsID0gSlNPTi5zdHJpbmdpZnkodGhpcy5maWxlTGlzdCk7DQogICAgICAgIGNvbnNvbGUubG9nKHRoaXMuZm9ybS5hcHBseUZpbGVVcmwpOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign6ZmE5Lu25LiK5Lyg5aSx6LSlJyk7DQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOeJqei1hOWvvOWFpeaWh+S7tuS4iuS8oOaIkOWKn+WkhOeQhg0KICAgIGhhbmRsZU1hdGVyaWFsRmlsZVN1Y2Nlc3MocmVzcG9uc2UsIGZpbGUsIGZpbGVMaXN0KSB7DQogICAgICB0aGlzLnVwbG9hZC5pc1VwbG9hZGluZyA9IGZhbHNlOw0KICAgICAgdGhpcy4kcmVmcy51cGxvYWQuY2xlYXJGaWxlcygpOw0KICAgICAgdGhpcy5pbXBvcnRWaXNpYmxlID0gZmFsc2U7DQogICAgICANCiAgICAgIGlmIChyZXNwb25zZS5jb2RlID09PSAyMDApIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCLlr7zlhaXmiJDlip8iKTsNCiAgICAgICAgLy8g5bCG5a+85YWl55qE54mp6LWE5YiX6KGo5re75Yqg5Yiw5b2T5YmN6KGo5Y2V55qE54mp6LWE5YiX6KGo5LitDQogICAgICAgIGlmIChyZXNwb25zZS5kYXRhICYmIHJlc3BvbnNlLmRhdGEubGVuZ3RoID4gMCkgew0KICAgICAgICAgIC8vIOi/veWKoOaWsOWvvOWFpeeahOeJqei1hA0KICAgICAgICAgIGNvbnN0IGltcG9ydGVkTWF0ZXJpYWxzID0gcmVzcG9uc2UuZGF0YS5tYXAoaXRlbSA9PiB7DQogICAgICAgICAgICAvLyDlpoLmnpzmsqHmnIltYXRlcmlhbElk77yM5L2/55SoLTHkvZzkuLrpu5jorqRJRO+8jOW5tuehruS/neivpeeJqei1hOiDveaYvuekug0KICAgICAgICAgICAgY29uc3QgbWF0ZXJpYWxJZCA9IGl0ZW0ubWF0ZXJpYWxJZCB8fCAtMTsNCiAgICAgICAgICAgIC8vIOeJqei1hOWQjeensOW/hemhu+acieWAvA0KICAgICAgICAgICAgY29uc3QgbWF0ZXJpYWxOYW1lID0gaXRlbS5tYXRlcmlhbE5hbWUgfHwgIuacquefpeeJqei1hCI7DQogICAgICAgICAgICANCiAgICAgICAgICAgIC8vIOWmguaenOaYr+m7mOiupElE55qE54mp6LWE77yM5YiZ5re75Yqg5Yiw6YCJ6aG55LitDQogICAgICAgICAgICBpZiAobWF0ZXJpYWxJZCA9PT0gLTEpIHsNCiAgICAgICAgICAgICAgdGhpcy5hZGRUb01hdGVyaWFsT3B0aW9ucygNCiAgICAgICAgICAgICAgICBtYXRlcmlhbElkLA0KICAgICAgICAgICAgICAgIG1hdGVyaWFsTmFtZSwNCiAgICAgICAgICAgICAgICBpdGVtLm1hdGVyaWFsU3BlYyB8fCAiIiwNCiAgICAgICAgICAgICAgICBpdGVtLm1lYXN1cmVVbml0IHx8ICIiDQogICAgICAgICAgICAgICk7DQogICAgICAgICAgICB9DQogICAgICAgICAgICANCiAgICAgICAgICAgIHJldHVybiB7DQogICAgICAgICAgICAgIG1hdGVyaWFsSWQ6IG1hdGVyaWFsSWQsDQogICAgICAgICAgICAgIG1hdGVyaWFsTmFtZTogbWF0ZXJpYWxOYW1lLA0KICAgICAgICAgICAgICBtYXRlcmlhbFNwZWM6IGl0ZW0ubWF0ZXJpYWxTcGVjIHx8ICIiLA0KICAgICAgICAgICAgICBwbGFuTnVtOiBpdGVtLnBsYW5OdW0gfHwgMSwNCiAgICAgICAgICAgICAgbWVhc3VyZVVuaXQ6IGl0ZW0ubWVhc3VyZVVuaXQgfHwgIiIsDQogICAgICAgICAgICAgIHJlbWFyazogaXRlbS5yZW1hcmsgfHwgJycNCiAgICAgICAgICAgIH07DQogICAgICAgICAgfSk7DQogICAgICAgICAgDQogICAgICAgICAgLy8g5bCG5a+85YWl55qE54mp6LWE5re75Yqg5Yiw6KGo5Y2V5LitDQogICAgICAgICAgdGhpcy5mb3JtLm1hdGVyaWFscyA9IHRoaXMuZm9ybS5tYXRlcmlhbHMuY29uY2F0KGltcG9ydGVkTWF0ZXJpYWxzKTsNCiAgICAgICAgICANCiAgICAgICAgICAvLyDkvb/nlKhuZXh0VGlja+ehruS/neinhuWbvuabtOaWsA0KICAgICAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgICAgIC8vIOinpuWPkeS4gOasoeWIt+aWsO+8jOehruS/neS4i+aLieahhuato+ehruaYvuekug0KICAgICAgICAgICAgdGhpcy5tYXRlcmlhbE9wdGlvbnMgPSBbLi4udGhpcy5tYXRlcmlhbE9wdGlvbnNdOw0KICAgICAgICAgIH0pOw0KICAgICAgICB9DQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKHJlc3BvbnNlLm1zZyB8fCAi5a+85YWl5aSx6LSlIik7DQogICAgICB9DQogICAgfSwNCiAgICANCiAgICAvLyDmoLnmja7nianotYTlkI3np7Dmn6Xor6JtYXRlcmlhbElkDQogICAgcXVlcnlNYXRlcmlhbElkQnlOYW1lKHJvd0luZGV4KSB7DQogICAgICAvLyDmraTmlrnms5Xlt7LkuI3lho3kvb/nlKjvvIzkv53nlZnnqbrmlrnms5Xku6Xpgb/lhY3lj6/og73nmoTosIPnlKjplJnor68NCiAgICB9LA0KICAgIA0KICAgIC8vIOWbvueJh+mihOiniA0KICAgIGhhbmRsZUltZ1ByZXZpZXcoZmlsZSkgew0KICAgICAgdGhpcy5pbWdQcmV2aWV3VXJsID0gZmlsZS51cmwgfHwgKGZpbGUucmVzcG9uc2UgJiYgZmlsZS5yZXNwb25zZS5kYXRhKTsNCiAgICAgIHRoaXMuaW1nUHJldmlld1Zpc2libGUgPSB0cnVlOw0KICAgIH0sDQogICAgDQogICAgLy8g5paH5Lu26aKE6KeIDQogICAgaGFuZGxlRmlsZVByZXZpZXcoZmlsZSkgew0KICAgICAgd2luZG93Lm9wZW4oZmlsZS51cmwgfHwgKGZpbGUucmVzcG9uc2UgJiYgZmlsZS5yZXNwb25zZS5kYXRhKSk7DQogICAgfSwNCiAgICANCiAgICAvLyDnp7vpmaTlm77niYcNCiAgICBoYW5kbGVJbWdSZW1vdmUoZmlsZSwgZmlsZUxpc3QpIHsNCiAgICAgIC8v56e76ZmkZmlsZUxpc3TkuK11cmzkuI5maWxlLnVybOebuOWQjOeahOaWh+S7tg0KICAgICAgdGhpcy5pbWdGaWxlTGlzdCA9IHRoaXMuaW1nRmlsZUxpc3QuZmlsdGVyKGl0ZW0gPT4gaXRlbS51cmwgIT09IGZpbGUudXJsKTsNCiAgICAgIA0KICAgICAgLy8g6L2s5o2i5Li6SlNPTuWtl+espuS4suS/neWtmA0KICAgICAgdGhpcy5mb3JtLmFwcGx5SW1nVXJsID0gSlNPTi5zdHJpbmdpZnkoaW1nRmlsZUxpc3QpOw0KICAgIH0sDQogICAgDQogICAgLy8g56e76Zmk5paH5Lu2DQogICAgaGFuZGxlRmlsZVJlbW92ZShmaWxlLCBmaWxlTGlzdCkgew0KICAgICAgLy/np7vpmaRmaWxlTGlzdOS4rXVybOS4jmZpbGUudXJs55u45ZCM55qE5paH5Lu2DQogICAgICB0aGlzLmZpbGVMaXN0ID0gdGhpcy5maWxlTGlzdC5maWx0ZXIoaXRlbSA9PiBpdGVtLnVybCAhPT0gZmlsZS51cmwpOw0KICAgICAgDQogICAgICAvLyDovazmjaLkuLpKU09O5a2X56ym5Liy5L+d5a2YDQogICAgICB0aGlzLmZvcm0uYXBwbHlGaWxlVXJsID0gSlNPTi5zdHJpbmdpZnkoZmlsZUxpc3QpOw0KICAgIH0sDQogICAgDQogICAgLy8g6LaF5Ye65paH5Lu25pWw6YeP6ZmQ5Yi2DQogICAgaGFuZGxlRXhjZWVkKCkgew0KICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfmnIDlpJrlj6rog73kuIrkvKAz5Liq5paH5Lu2Jyk7DQogICAgfSwNCg0KICAgIC8vIOihqOWNleaPkOS6pA0KICAgIHN1Ym1pdEZvcm0oKSB7DQogICAgICB0aGlzLiRyZWZzWyJmb3JtIl0udmFsaWRhdGUodmFsaWQgPT4gew0KICAgICAgICBpZiAodmFsaWQpIHsNCiAgICAgICAgICAvLyDpqozor4HnianotYTliJfooagNCiAgICAgICAgICBpZiAodGhpcy5mb3JtLm1hdGVyaWFscy5sZW5ndGggPT09IDApIHsNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIuivt+iHs+Wwkea3u+WKoOS4gOmhueeJqei1hCIpOw0KICAgICAgICAgICAgcmV0dXJuOw0KICAgICAgICAgIH0NCg0KICAgICAgICAgIC8vIOmqjOivgeeJqei1hOWIl+ihqOeahOW/heWhq+mhuQ0KICAgICAgICAgIGxldCB2YWxpZCA9IHRydWU7DQogICAgICAgICAgdGhpcy5mb3JtLm1hdGVyaWFscy5mb3JFYWNoKChpdGVtLCBpbmRleCkgPT4gew0KICAgICAgICAgICAgaWYgKCFpdGVtLm1hdGVyaWFsSWQgfHwgIWl0ZW0ubWF0ZXJpYWxOYW1lIHx8ICFpdGVtLm1hdGVyaWFsU3BlYyB8fCAhaXRlbS5wbGFuTnVtIHx8ICFpdGVtLm1lYXN1cmVVbml0KSB7DQogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoYOesrCR7aW5kZXggKyAxfeihjOeJqei1hOS/oeaBr+S4jeWujOaVtO+8jOivt+Whq+WGmeWujOaVtGApOw0KICAgICAgICAgICAgICB2YWxpZCA9IGZhbHNlOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pOw0KDQogICAgICAgICAgaWYgKCF2YWxpZCkgew0KICAgICAgICAgICAgcmV0dXJuOw0KICAgICAgICAgIH0NCg0KICAgICAgICAgIC8vIOmqjOivgeiuoeWIkumHj+Wkp+S6jjANCiAgICAgICAgICBpZiAodGhpcy5mb3JtLnBsYW5UeXBlID09PSAzICYmIHRoaXMuZm9ybS5idXNpbmVzc0NhdGVnb3J5ID09PSAyMSAmJiAoIXRoaXMuZm9ybS5wbGFubmVkQW1vdW50IHx8IHRoaXMuZm9ybS5wbGFubmVkQW1vdW50IDw9IDApKSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLorqHliJLph4/lv4XpobvlpKfkuo4wIik7DQogICAgICAgICAgICByZXR1cm47DQogICAgICAgICAgfQ0KICAgICAgICAgIC8v5a+55pyJ5pWI5pyf5Y2V54us5aSE55CGDQogICAgICAgICAgaWYgKHRoaXMuZm9ybS5wbGFuVHlwZSA9PT0gMyAmJiB0aGlzLmZvcm0uYnVzaW5lc3NDYXRlZ29yeSA9PT0gMjEpIHsNCiAgICAgICAgICAgIGlmICh0aGlzLmZvcm0uZW5kVGltZSkgew0KICAgICAgICAgICAgICB0aGlzLmZvcm0uZXhwaXJlVGltZSA9IHRoaXMuZm9ybS5lbmRUaW1lICsgIiAyMzo1OTo1OSI7DQogICAgICAgICAgICB9DQogICAgICAgICAgfSBlbHNlIGlmICh0aGlzLmZvcm0uZXhwaXJlVGltZSkgew0KICAgICAgICAgICAgdGhpcy5mb3JtLmV4cGlyZVRpbWUgPSB0aGlzLmZvcm0uZXhwaXJlVGltZSArICIgMjM6NTk6NTkiOw0KICAgICAgICAgIH0NCiAgICAgICAgICAvL+WvueW8gOWni+e7k+adn+aXtumXtOWNleeLrOWkhOeQhg0KICAgICAgICAgIGlmICh0aGlzLmZvcm0uc3RhcnRUaW1lICYmIHRoaXMuZm9ybS5lbmRUaW1lKSB7DQogICAgICAgICAgICB0aGlzLmZvcm0uc3RhcnRUaW1lID0gdGhpcy5mb3JtLnN0YXJ0VGltZSArICIgMDA6MDA6MDAiOw0KICAgICAgICAgICAgdGhpcy5mb3JtLmVuZFRpbWUgPSB0aGlzLmZvcm0uZW5kVGltZSArICIgMjM6NTk6NTkiOw0KICAgICAgICAgIH0NCg0KICAgICAgICAgIC8vIOagueaNruaYr+WQpue8lui+keaooeW8j+iwg+eUqOS4jeWQjOeahEFQSQ0KICAgICAgICAgIGNvbnN0IGFwaU1ldGhvZCA9IHRoaXMuaXNFZGl0ID8gdXBkYXRlUGxhbiA6IGFkZFBsYW47DQogICAgICAgICAgY29uc3Qgc3VjY2Vzc01zZyA9IHRoaXMuaXNFZGl0ID8gIuS/ruaUueaIkOWKnyIgOiAi55Sz6K+35o+Q5Lqk5oiQ5YqfIjsNCiAgICAgICAgICBjb25zb2xlLmxvZyh0aGlzLmZvcm0pOw0KICAgICAgICAgIC8vIOiwg+eUqEFQSeaPkOS6pOaVsOaNrg0KICAgICAgICAgIGFwaU1ldGhvZCh0aGlzLmZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCkgew0KICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3Moc3VjY2Vzc01zZyk7DQogICAgICAgICAgICAgIHRoaXMuJHRhYi5jbG9zZU9wZW5QYWdlKHRoaXMuJHJvdXRlKTsNCiAgICAgICAgICAgICAgLy8g6Lez6L2s5Yiw5YiX6KGo6aG16Z2i5bm25Yi35pawDQogICAgICAgICAgICAgIHRoaXMuJHJvdXRlci5wdXNoKHsgDQogICAgICAgICAgICAgICAgcGF0aDogIi9sZWF2ZS9sZWF2ZVBsYW5MaXN0IiwgDQogICAgICAgICAgICAgICAgcXVlcnk6IHsgDQogICAgICAgICAgICAgICAgICB0OiBEYXRlLm5vdygpLA0KICAgICAgICAgICAgICAgICAgcmVmcmVzaDogdHJ1ZSAvLyDmt7vliqDliLfmlrDmoIforrANCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihyZXNwb25zZS5tc2cgfHwgIuaPkOS6pOWksei0pSIpOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pLmNhdGNoKGVycm9yID0+IHsNCiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoIuaPkOS6pOWksei0pSIsIGVycm9yKTsNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIuaPkOS6pOi/h+eoi+S4reWPkeeUn+mUmeivr++8jOivt+eojeWQjuWGjeivlSIpOw0KICAgICAgICAgIH0pOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8vIOWPlua2iOaMiemSrg0KICAgIGNhbmNlbCgpIHsNCiAgICAgIHRoaXMuJHJvdXRlci5wdXNoKCIvbGVhdmUvcGxhbiIpOw0KICAgIH0sDQogICAgLy8g5Zu+54mH5LiK5Lyg5YmN55qE6aqM6K+BDQogICAgYmVmb3JlSW1nVXBsb2FkKGZpbGUpIHsNCiAgICAgIGNvbnN0IGlzSW1hZ2UgPSBmaWxlLnR5cGUuaW5kZXhPZignaW1hZ2UvJykgPT09IDA7DQogICAgICBjb25zdCBpc0x0MTBNID0gZmlsZS5zaXplIC8gMTAyNCAvIDEwMjQgPCAxMDsNCg0KICAgICAgaWYgKCFpc0ltYWdlKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+WPquiDveS4iuS8oOWbvueJh+agvOW8j+aWh+S7tiEnKTsNCiAgICAgICAgcmV0dXJuIGZhbHNlOw0KICAgICAgfQ0KICAgICAgaWYgKCFpc0x0MTBNKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+WbvueJh+Wkp+Wwj+S4jeiDvei2hei/hyAxME1CIScpOw0KICAgICAgICByZXR1cm4gZmFsc2U7DQogICAgICB9DQogICAgICByZXR1cm4gdHJ1ZTsNCiAgICB9LA0KDQogICAgLy8g5paH5Lu25LiK5Lyg5YmN55qE6aqM6K+BDQogICAgYmVmb3JlRmlsZVVwbG9hZChmaWxlKSB7DQogICAgICBjb25zdCBpc0x0MjBNID0gZmlsZS5zaXplIC8gMTAyNCAvIDEwMjQgPCAyMDsNCiAgICAgIGlmICghaXNMdDIwTSkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfmlofku7blpKflsI/kuI3og73otoXov4cgMjBNQiEnKTsNCiAgICAgICAgcmV0dXJuIGZhbHNlOw0KICAgICAgfQ0KICAgICAgcmV0dXJuIHRydWU7DQogICAgfSwNCiAgICANCiAgICAvLyDkuIrkvKDplJnor6/lpITnkIYNCiAgICBoYW5kbGVVcGxvYWRFcnJvcihlcnIsIGZpbGUsIGZpbGVMaXN0KSB7DQogICAgICBjb25zb2xlLmVycm9yKCLkuIrkvKDlpLHotKU6IiwgZXJyKTsNCiAgICAgIA0KICAgICAgaWYgKGVyci5zdGF0dXMgPT09IDQwMykgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfkuIrkvKDlpLHotKXvvJrmsqHmnInmnYPpmZAnKTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+S4iuS8oOWksei0pe+8micgKyAoZXJyLm1lc3NhZ2UgfHwgJ+acquefpemUmeivrycpKTsNCiAgICAgIH0NCiAgICB9LA0KICAgIC8vIOiOt+WPlumDqOmXqOWQjeensA0KICAgIGdldERlcGFydG1lbnROYW1lKGlkKSB7DQogICAgICBpZiAoIWlkKSByZXR1cm4gJyc7DQogICAgICANCiAgICAgIC8vIOafpeivoumAiemhueS4reaYr+WQpuacieWMuemFjeeahA0KICAgICAgY29uc3QgZGVwdCA9IHRoaXMuZGVwYXJ0bWVudE9wdGlvbnMuZmluZChpdGVtID0+IGl0ZW0uaWQgPT0gaWQpOw0KICAgICAgaWYgKGRlcHQpIHsNCiAgICAgICAgcmV0dXJuIGRlcHQuc3RvcmVOYW1lOw0KICAgICAgfQ0KICAgICAgDQogICAgICAvLyDmoLnmja7kuI3lkIznmoTlrZfmrrVJROi/lOWbnuWvueW6lOeahOS4reaWh+WQjeensA0KICAgICAgaWYgKGlkID09PSB0aGlzLmZvcm0uc291cmNlQ29tcGFueUNvZGUgJiYgdGhpcy5mb3JtLnNvdXJjZUNvbXBhbnkpIHsNCiAgICAgICAgcmV0dXJuIHRoaXMuZm9ybS5zb3VyY2VDb21wYW55Ow0KICAgICAgfQ0KICAgICAgDQogICAgICBpZiAoaWQgPT09IHRoaXMuZm9ybS5yZWNlaXZlQ29tcGFueUNvZGUgJiYgdGhpcy5mb3JtLnJlY2VpdmVDb21wYW55ICYmIHRoaXMuZm9ybS5wbGFuVHlwZSA9PT0gMykgew0KICAgICAgICByZXR1cm4gdGhpcy5mb3JtLnJlY2VpdmVDb21wYW55Ow0KICAgICAgfQ0KICAgICAgDQogICAgICBpZiAoaWQgPT09IHRoaXMuZm9ybS50YXJnZXRDb21wYW55Q29kZSAmJiB0aGlzLmZvcm0udGFyZ2V0Q29tcGFueSkgew0KICAgICAgICByZXR1cm4gdGhpcy5mb3JtLnRhcmdldENvbXBhbnk7DQogICAgICB9DQogICAgICANCiAgICAgIGlmIChpZCA9PT0gdGhpcy5mb3JtLnJlZnVuZENvbXBhbnlDb2RlICYmIHRoaXMuZm9ybS5yZWZ1bmRDb21wYW55KSB7DQogICAgICAgIHJldHVybiB0aGlzLmZvcm0ucmVmdW5kQ29tcGFueTsNCiAgICAgIH0NCiAgICAgIA0KICAgICAgLy8g5ZCm5YiZ5pi+56S6SUQNCiAgICAgIHJldHVybiBgSUQ6ICR7aWR9YDsNCiAgICB9LA0KICAgIA0KICAgIC8vIOiOt+WPluWuouaIt+WQjeensA0KICAgIGdldEN1c3RvbWVyTmFtZShpZCkgew0KICAgICAgaWYgKCFpZCkgcmV0dXJuICcnOw0KICAgICAgDQogICAgICAvLyDmn6Xor6LpgInpobnkuK3mmK/lkKbmnInljLnphY3nmoQNCiAgICAgIGNvbnN0IGN1c3RvbWVyID0gdGhpcy5jdXN0b21lck9wdGlvbnMuZmluZChpdGVtID0+IGl0ZW0uaWQgPT0gaWQpOw0KICAgICAgaWYgKGN1c3RvbWVyKSB7DQogICAgICAgIHJldHVybiBjdXN0b21lci5jdXN0b21lck5hbWU7DQogICAgICB9DQogICAgICANCiAgICAgIC8vIOWmguaenOaYr+aUtui0p+WNleS9jeS4lOS4jeaYr+i3qOWMuuiwg+aLqO+8jOWImeS9v+eUqOW3suacieeahOS4reaWh+WQjeensA0KICAgICAgaWYgKGlkID09PSB0aGlzLmZvcm0ucmVjZWl2ZUNvbXBhbnlDb2RlICYmIHRoaXMuZm9ybS5yZWNlaXZlQ29tcGFueSAmJiB0aGlzLmZvcm0ucGxhblR5cGUgIT09IDMpIHsNCiAgICAgICAgcmV0dXJuIHRoaXMuZm9ybS5yZWNlaXZlQ29tcGFueTsNCiAgICAgIH0NCiAgICAgIA0KICAgICAgLy8g5ZCm5YiZ5pi+56S6SUQNCiAgICAgIHJldHVybiBgSUQ6ICR7aWR9YDsNCiAgICB9LA0KICAgIC8vIOa3u+WKoOmDqOmXqOWIsOmAiemhueaVsOe7hOaWueazlQ0KICAgIGFkZFRvRGVwYXJ0bWVudE9wdGlvbnMoaWQsIG5hbWUpIHsNCiAgICAgIC8vIOajgOafpeaYr+WQpuW3suWtmOWcqOebuOWQjElE55qE6YCJ6aG5DQogICAgICBpZiAoIXRoaXMuZGVwYXJ0bWVudE9wdGlvbnMuc29tZShpdGVtID0+IGl0ZW0uaWQgPT0gaWQpKSB7DQogICAgICAgIHRoaXMuZGVwYXJ0bWVudE9wdGlvbnMucHVzaCh7DQogICAgICAgICAgaWQ6IGlkLA0KICAgICAgICAgIHN0b3JlTmFtZTogbmFtZQ0KICAgICAgICB9KTsNCiAgICAgIH0NCiAgICB9LA0KICAgIA0KICAgIC8vIOa3u+WKoOWuouaIt+WIsOmAiemhueaVsOe7hOaWueazlQ0KICAgIGFkZFRvQ3VzdG9tZXJPcHRpb25zKGlkLCBuYW1lKSB7DQogICAgICAvLyDmo4Dmn6XmmK/lkKblt7LlrZjlnKjnm7jlkIxJROeahOmAiemhuQ0KICAgICAgaWYgKCF0aGlzLmN1c3RvbWVyT3B0aW9ucy5zb21lKGl0ZW0gPT4gaXRlbS5pZCA9PSBpZCkpIHsNCiAgICAgICAgdGhpcy5jdXN0b21lck9wdGlvbnMucHVzaCh7DQogICAgICAgICAgaWQ6IGlkLA0KICAgICAgICAgIGN1c3RvbWVyTmFtZTogbmFtZQ0KICAgICAgICB9KTsNCiAgICAgIH0NCiAgICB9LA0KICAgIA0KICAgIC8vIOa3u+WKoOeJqei1hOWIsOmAiemhueaVsOe7hOaWueazlQ0KICAgIGFkZFRvTWF0ZXJpYWxPcHRpb25zKGlkLCBuYW1lLCBzcGVjLCB1bml0KSB7DQogICAgICAvLyDmo4Dmn6XmmK/lkKblt7LlrZjlnKjnm7jlkIxJROeahOmAiemhuQ0KICAgICAgaWYgKCF0aGlzLm1hdGVyaWFsT3B0aW9ucy5zb21lKGl0ZW0gPT4gaXRlbS5pZCA9PSBpZCkpIHsNCiAgICAgICAgdGhpcy5tYXRlcmlhbE9wdGlvbnMucHVzaCh7DQogICAgICAgICAgaWQ6IGlkLA0KICAgICAgICAgIG1hdGVyaWFsTmFtZTogbmFtZSwNCiAgICAgICAgICBtYXRlcmlhbFNwZWM6IHNwZWMsDQogICAgICAgICAgbWVhc3VyZVVuaXQ6IHVuaXQNCiAgICAgICAgfSk7DQogICAgICB9DQogICAgfSwNCiAgICAvLyDlr7zlh7rnianotYTmqKHmnb8NCiAgICBoYW5kbGVFeHBvcnRNYXRlcmlhbFRlbXBsYXRlKCkgew0KICAgICAgZXhwb3J0TWF0ZXJpYWxUZW1wbGF0ZSgpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLmRvd25sb2FkKHJlc3BvbnNlLm1zZyk7DQogICAgICB9KTsNCiAgICB9LA0KICAgIA0KICAgIC8vIOaYvuekuuWvvOWFpeeJqei1hOWvueivneahhg0KICAgIGhhbmRsZUltcG9ydE1hdGVyaWFsKCkgew0KICAgICAgdGhpcy5pbXBvcnRWaXNpYmxlID0gdHJ1ZTsNCiAgICB9LA0KICAgIA0KICAgIC8vIOaWh+S7tuS4iuS8oOS4reWkhOeQhg0KICAgIGhhbmRsZUZpbGVVcGxvYWRQcm9ncmVzcygpIHsNCiAgICAgIHRoaXMudXBsb2FkLmlzVXBsb2FkaW5nID0gdHJ1ZTsNCiAgICB9LA0KICAgIA0KICAgIC8vIOaPkOS6pOS4iuS8oOaWh+S7tg0KICAgIHN1Ym1pdEZpbGVGb3JtKCkgew0KICAgICAgdGhpcy4kcmVmcy51cGxvYWQuc3VibWl0KCk7DQogICAgfSwNCiAgfQ0KfTsNCg=="}, {"version": 3, "sources": ["edit.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4aA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "edit.vue", "sourceRoot": "src/views/leave/plan", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-card class=\"box-card\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <h3>{{ isEdit ? '修改申请' : '新增申请' }}</h3>\r\n      </div>\r\n\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"120px\">\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"计划类型\" prop=\"planType\">\r\n              <el-radio-group v-model=\"form.planType\" @change=\"handlePlanTypeChange\">\r\n                <el-radio :label=\"1\">出厂不返回</el-radio>\r\n                <el-radio :label=\"2\">出厂返回</el-radio>\r\n                <el-radio :label=\"3\">跨区调拨</el-radio>\r\n                <el-radio :label=\"4\">退货申请</el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n          </el-col>\r\n\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"业务类型\" prop=\"businessCategory\">\r\n              <el-radio-group v-model=\"form.businessCategory\" @change=\"handleBusinessCategoryChange\">\r\n                <el-radio v-if=\"form.planType === 1\" :label=\"1\">通用</el-radio>\r\n                <el-radio v-if=\"form.planType === 2\" :label=\"11\">通用</el-radio>\r\n                <el-radio v-if=\"form.planType === 2\" :label=\"12\">委外加工</el-radio>\r\n                <el-radio v-if=\"form.planType === 3\" :label=\"21\">有计划量计量</el-radio>\r\n                <el-radio v-if=\"form.planType === 3\" :label=\"22\">短期</el-radio>\r\n                <el-radio v-if=\"form.planType === 3\" :label=\"23\">钢板（圆钢）</el-radio>\r\n                <el-radio v-if=\"form.planType === 4\" :label=\"31\">通用</el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"是否计量\" prop=\"measureFlag\">\r\n              <el-select v-model=\"form.measureFlag\" placeholder=\"请选择是否计量\" :disabled=\"isSpecialCondition\">\r\n                <el-option label=\"计量\" :value=\"1\"></el-option>\r\n                <el-option label=\"不计量\" :value=\"0\"></el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n\r\n          <el-col :span=\"12\" v-if=\"form.planType === 3 && form.businessCategory === 21\">\r\n            <el-form-item label=\"计划量（吨）\" prop=\"plannedAmount\">\r\n              <el-input-number v-model=\"form.plannedAmount\" :min=\"0\" controls-position=\"right\" placeholder=\"请输入计划量\"></el-input-number>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-row :gutter=\"20\" v-if=\"form.planType === 3 && form.businessCategory === 21\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"开始时间\" prop=\"startTime\">\r\n              <el-tooltip class=\"item\" effect=\"dark\" content=\"开始时间默认为该日期的0时0分0秒\" placement=\"top\">\r\n                <i class=\"el-icon-question\" style=\"margin-left: 2px;\"></i>\r\n              </el-tooltip>\r\n              <el-date-picker\r\n                v-model=\"form.startTime\"\r\n                type=\"date\"\r\n                placeholder=\"选择开始时间\"\r\n                value-format=\"yyyy-MM-dd\"\r\n                :picker-options=\"form.planType === 3 && form.businessCategory === 21 ? datePickerOptions : {}\">\r\n              </el-date-picker>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"结束时间\" prop=\"endTime\">\r\n              <el-tooltip class=\"item\" effect=\"dark\" content=\"结束时间默认为该日期的23时59分59秒\" placement=\"top\">\r\n                <i class=\"el-icon-question\" style=\"margin-left: 2px;\"></i>\r\n              </el-tooltip>\r\n              <el-date-picker\r\n                v-model=\"form.endTime\"\r\n                type=\"date\"\r\n                placeholder=\"选择结束时间\"\r\n                value-format=\"yyyy-MM-dd\"\r\n                :picker-options=\"form.planType === 3 && form.businessCategory === 21 ? datePickerOptions : {}\">\r\n              </el-date-picker>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-row :gutter=\"20\" v-if=\"!(form.planType === 3 && form.businessCategory === 21)\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"有效期\" prop=\"expireTime\">\r\n              <el-tooltip class=\"item\" effect=\"dark\" content=\"有效期默认为该日期的23时59分59秒\" placement=\"top\">\r\n                <i class=\"el-icon-question\" style=\"margin-left: 2px;\"></i>\r\n              </el-tooltip>\r\n              <el-date-picker\r\n                v-model=\"form.expireTime\"\r\n                type=\"date\"\r\n                placeholder=\"选择有效期\"\r\n                value-format=\"yyyy-MM-dd\">\r\n              </el-date-picker>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"申请单位\" prop=\"sourceCompanyCode\">\r\n              <el-select\r\n                v-model=\"form.sourceCompanyCode\"\r\n                placeholder=\"请选择申请单位\"\r\n                filterable\r\n                remote\r\n                reserve-keyword\r\n                :remote-method=\"remoteSearchDepartment\"\r\n                :loading=\"departmentLoading\">\r\n                <template slot=\"selected\">\r\n                  <span>{{ getDepartmentName(form.sourceCompanyCode) }}</span>\r\n                </template>\r\n                <el-option\r\n                  v-for=\"item in departmentOptions\"\r\n                  :key=\"item.id\"\r\n                  :label=\"item.storeName\"\r\n                  :value=\"item.id\">\r\n                </el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"收货单位\" prop=\"receiveCompanyCode\">\r\n              <el-select\r\n                v-model=\"form.receiveCompanyCode\"\r\n                placeholder=\"请选择收货单位\"\r\n                filterable\r\n                remote\r\n                reserve-keyword\r\n                :remote-method=\"form.planType === 3 ? remoteSearchDepartment : remoteSearchCustomer\"\r\n                :loading=\"form.planType === 3 ? departmentLoading : customerLoading\">\r\n                <template slot=\"selected\">\r\n                  <span>{{ form.planType === 3 ? getDepartmentName(form.receiveCompanyCode) : getCustomerName(form.receiveCompanyCode) }}</span>\r\n                </template>\r\n                <el-option\r\n                  v-for=\"item in form.planType === 3 ? departmentOptions : customerOptions\"\r\n                  :key=\"item.id\"\r\n                  :label=\"form.planType === 3 ? item.storeName : item.customerName\"\r\n                  :value=\"item.id\">\r\n                </el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\" v-if=\"form.planType === 2\">\r\n            <el-form-item label=\"返回单位\" prop=\"targetCompanyCode\">\r\n              <el-select\r\n                v-model=\"form.targetCompanyCode\"\r\n                placeholder=\"请选择返回单位\"\r\n                filterable\r\n                remote\r\n                reserve-keyword\r\n                :remote-method=\"remoteSearchDepartment\"\r\n                :loading=\"departmentLoading\">\r\n                <template slot=\"selected\">\r\n                  <span>{{ getDepartmentName(form.targetCompanyCode) }}</span>\r\n                </template>\r\n                <el-option\r\n                  v-for=\"item in departmentOptions\"\r\n                  :key=\"item.id\"\r\n                  :label=\"item.storeName\"\r\n                  :value=\"item.id\">\r\n                </el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n\r\n          <el-col :span=\"12\" v-if=\"form.planType === 4\">\r\n            <el-form-item label=\"退货单位\" prop=\"refundCompanyCode\">\r\n              <el-select\r\n                v-model=\"form.refundCompanyCode\"\r\n                placeholder=\"请选择退货单位\"\r\n                filterable\r\n                remote\r\n                reserve-keyword\r\n                :remote-method=\"remoteSearchDepartment\"\r\n                :loading=\"departmentLoading\">\r\n                <template slot=\"selected\">\r\n                  <span>{{ getDepartmentName(form.refundCompanyCode) }}</span>\r\n                </template>\r\n                <el-option\r\n                  v-for=\"item in departmentOptions\"\r\n                  :key=\"item.id\"\r\n                  :label=\"item.storeName\"\r\n                  :value=\"item.id\">\r\n                </el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\" v-if=\"form.planType === 2\">\r\n            <el-form-item label=\"计划返回时间\" prop=\"plannedReturnTime\">\r\n              <el-date-picker\r\n                v-model=\"form.plannedReturnTime\"\r\n                type=\"date\"\r\n                placeholder=\"选择计划返回时间\"\r\n                value-format=\"yyyy-MM-dd\"\r\n                :picker-options=\"{\r\n                  disabledDate(time) {\r\n                    return time.getTime() < Date.now() - 8.64e7;\r\n                  }\r\n                }\">\r\n              </el-date-picker>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"监装人\" prop=\"monitor\">\r\n              <el-input v-model=\"form.monitor\" placeholder=\"请输入监装人\"></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"物资专管员\" prop=\"specialManager\">\r\n              <el-input v-model=\"form.specialManager\" placeholder=\"请输入物资专管员\"></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"物资类型\" prop=\"itemType\">\r\n              <el-select v-model=\"form.itemType\" placeholder=\"请选择物资类型\">\r\n                <el-option label=\"钢材\" :value=\"1\"></el-option>\r\n                <el-option label=\"钢板\" :value=\"2\"></el-option>\r\n                <el-option label=\"其他\" :value=\"3\"></el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"是否复审\" prop=\"secApproveFlag\">\r\n              <el-select v-model=\"form.secApproveFlag\" placeholder=\"请选择是否复审\">\r\n                <el-option label=\"是\" :value=\"1\"></el-option>\r\n                <el-option label=\"否\" :value=\"0\"></el-option>\r\n              </el-select>\r\n              <div class=\"tip-text\">在写申请单时，一般是不需要复审的，但一些物资比较特殊或者贵重等原因，必须经过更高领导审核(签字)，因此请申请人注意选择</div>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"出厂原因\" prop=\"reason\">\r\n              <el-input v-model=\"form.reason\" placeholder=\"请输入出厂原因\"></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"合同号\" prop=\"contractNo\">\r\n              <el-input v-model=\"form.contractNo\" placeholder=\"请输入合同号\"></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <!-- 图片上传 -->\r\n        <el-form-item label=\"申请图片\">\r\n          <el-upload\r\n            class=\"upload-demo\"\r\n            :action=\"uploadUrl\"\r\n            list-type=\"picture-card\"\r\n            :file-list=\"imgFileList\"\r\n            :limit=\"3\"\r\n            :on-preview=\"handleImgPreview\"\r\n            :on-remove=\"handleImgRemove\"\r\n            :on-success=\"handleImgSuccess\"\r\n            :on-error=\"handleUploadError\"\r\n            :before-upload=\"beforeImgUpload\"\r\n            :on-exceed=\"handleExceed\"\r\n            :data=\"uploadParams\"\r\n            multiple>\r\n            <i class=\"el-icon-plus\"></i>\r\n            <div slot=\"tip\" class=\"el-upload__tip\">只能上传jpg/png文件，且不超过10MB</div>\r\n          </el-upload>\r\n          <el-dialog :visible.sync=\"imgPreviewVisible\">\r\n            <img width=\"100%\" :src=\"imgPreviewUrl\" alt=\"\">\r\n          </el-dialog>\r\n        </el-form-item>\r\n\r\n        <!-- 附件上传 -->\r\n        <el-form-item label=\"申请附件\">\r\n          <el-upload\r\n            class=\"upload-demo\"\r\n            :action=\"uploadUrl\"\r\n            :file-list=\"fileList\"\r\n            :limit=\"3\"\r\n            :on-preview=\"handleFilePreview\"\r\n            :on-remove=\"handleFileRemove\"\r\n            :on-success=\"handleAnnexFileSuccess\"\r\n            :on-error=\"handleUploadError\"\r\n            :before-upload=\"beforeFileUpload\"\r\n            :on-exceed=\"handleExceed\"\r\n            :data=\"uploadParams\">\r\n            <el-button size=\"small\" type=\"primary\">点击上传</el-button>\r\n            <div slot=\"tip\" class=\"el-upload__tip\">可上传任意类型文件，且不超过20MB</div>\r\n          </el-upload>\r\n        </el-form-item>\r\n\r\n        <!-- 物资列表 -->\r\n        <el-card class=\"material-card\" shadow=\"hover\">\r\n          <div slot=\"header\" class=\"clearfix\">\r\n            <span>物资列表</span>\r\n            <div class=\"material-btn-group\">\r\n              <el-button size=\"small\" type=\"primary\" plain icon=\"el-icon-download\" @click=\"handleExportMaterialTemplate\">导出物资模板</el-button>\r\n              <el-button size=\"small\" type=\"success\" plain icon=\"el-icon-upload2\" @click=\"handleImportMaterial\">导入物资</el-button>\r\n              <el-button size=\"small\" type=\"warning\" plain icon=\"el-icon-plus\" @click=\"handleAddMaterial\">添加物资</el-button>\r\n            </div>\r\n          </div>\r\n\r\n          <el-table\r\n            :data=\"form.materials\"\r\n            style=\"width: 100%\"\r\n            border>\r\n            <el-table-column\r\n              type=\"index\"\r\n              width=\"50\"\r\n              label=\"序号\">\r\n            </el-table-column>\r\n            \r\n            <el-table-column\r\n              prop=\"materialName\"\r\n              label=\"物资名称\"\r\n              width=\"180\">\r\n              <template slot-scope=\"scope\">\r\n                <el-select\r\n                  v-model=\"scope.row.materialId\"\r\n                  placeholder=\"请输入物资名称\"\r\n                  filterable\r\n                  remote\r\n                  reserve-keyword\r\n                  :remote-method=\"remoteSearchMaterial\"\r\n                  :loading=\"materialLoading\"\r\n                  @change=\"(value) => handleMaterialSelect(value, scope.row)\">\r\n                  <el-option\r\n                    v-for=\"item in materialOptions\"\r\n                    :key=\"item.id\"\r\n                    :label=\"item.materialName\"\r\n                    :value=\"item.id\">\r\n                  </el-option>\r\n                </el-select>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column\r\n              prop=\"materialSpec\"\r\n              label=\"物资型号规格\"\r\n              width=\"180\">\r\n              <template slot-scope=\"scope\">\r\n                <el-input v-model=\"scope.row.materialSpec\" placeholder=\"请输入物资型号规格\"></el-input>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column\r\n              prop=\"planNum\"\r\n              label=\"计划数量\"\r\n              width=\"180\">\r\n              <template slot-scope=\"scope\">\r\n                <el-input-number v-model=\"scope.row.planNum\" :min=\"0\" controls-position=\"right\" placeholder=\"请输入计划数量\"></el-input-number>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column\r\n              prop=\"measureUnit\"\r\n              label=\"计量单位\"\r\n              width=\"120\">\r\n              <template slot-scope=\"scope\">\r\n                <el-input v-model=\"scope.row.measureUnit\" placeholder=\"请输入计量单位\"></el-input>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column\r\n              prop=\"remark\"\r\n              label=\"备注\">\r\n              <template slot-scope=\"scope\">\r\n                <el-input v-model=\"scope.row.remark\" placeholder=\"请输入备注\"></el-input>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"操作\"\r\n              width=\"120\">\r\n              <template slot-scope=\"scope\">\r\n                <el-button\r\n                  size=\"mini\"\r\n                  type=\"danger\"\r\n                  icon=\"el-icon-delete\"\r\n                  @click=\"handleDeleteMaterial(scope.$index)\">删除</el-button>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </el-card>\r\n\r\n        <div class=\"form-footer\">\r\n          <el-button type=\"primary\" @click=\"submitForm\">{{ isEdit ? '修 改' : '确 定' }}</el-button>\r\n          <el-button @click=\"cancel\">取 消</el-button>\r\n        </div>\r\n      </el-form>\r\n    </el-card>\r\n    \r\n    <!-- 导入物资对话框 -->\r\n    <el-dialog :title=\"'导入物资'\" :visible.sync=\"importVisible\" width=\"400px\" append-to-body>\r\n      <el-upload\r\n        ref=\"upload\"\r\n        :limit=\"1\"\r\n        accept=\".xlsx, .xls\"\r\n        :headers=\"upload.headers\"\r\n        :action=\"upload.url\"\r\n        :disabled=\"upload.isUploading\"\r\n        :on-progress=\"handleFileUploadProgress\"\r\n        :on-success=\"handleMaterialFileSuccess\"\r\n        :auto-upload=\"false\"\r\n        drag>\r\n        <i class=\"el-icon-upload\"></i>\r\n        <div class=\"el-upload__text\">将物资Excel文件拖到此处，或<em>点击上传</em></div>\r\n        <div class=\"el-upload__tip\" slot=\"tip\">只能上传xlsx/xls文件，且不超过5MB</div>\r\n      </el-upload>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitFileForm\">确 定</el-button>\r\n        <el-button @click=\"importVisible = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listDepartment } from \"@/api/leave/department\";\r\nimport { listCustomer } from \"@/api/leave/customer\";\r\nimport { listMaterial } from \"@/api/leave/material\";\r\nimport { addPlan, updatePlan, detailPlan, exportMaterialTemplate, importMaterialList } from \"@/api/leave/plan\";\r\nimport { getToken } from \"@/utils/auth\";\r\n\r\nexport default {\r\n  name: \"EditLeavePlan\",\r\n  data() {\r\n    return {\r\n      // 导入参数\r\n      importVisible: false,\r\n      // 上传参数\r\n      upload: {\r\n        // 是否禁用上传\r\n        isUploading: false,\r\n        // 设置上传的请求头部\r\n        headers: { Authorization: \"Bearer \" + getToken() },\r\n        // 上传的地址\r\n        url: process.env.VUE_APP_BASE_API + \"/web/leave/plan/importMaterialList\"\r\n      },\r\n      // 是否为编辑模式\r\n      isEdit: false,\r\n      // 表单参数\r\n      form: {\r\n        planType: 1,\r\n        businessCategory: 1,\r\n        measureFlag: 1,\r\n        plannedAmount: 0,\r\n        sourceCompanyCode: \"\",\r\n        sourceCompany: \"\",\r\n        receiveCompanyCode: \"\",\r\n        receiveCompany: \"\",\r\n        targetCompanyCode: \"\",\r\n        targetCompany: \"\",\r\n        refundCompanyCode: \"\",\r\n        refundCompany: \"\",\r\n        contractNo: \"\",\r\n        plannedReturnTime: \"\",\r\n        startTime: \"\",\r\n        endTime: \"\",\r\n        expireTime: \"\", // 有效期\r\n        monitor: \"\",\r\n        specialManager: \"\",\r\n        itemType: 1,\r\n        reason: \"\",\r\n        secApproveFlag: 0,\r\n        applyImgUrl: \"\", // 申请图片URL\r\n        applyFileUrl: \"\", // 申请文件URL\r\n        materials: []\r\n      },\r\n      // 表单校验规则\r\n      rules: {\r\n        planType: [\r\n          { required: true, message: \"请选择计划类型\", trigger: \"change\" }\r\n        ],\r\n        businessCategory: [\r\n          { required: true, message: \"请选择业务类型\", trigger: \"change\" }\r\n        ],\r\n        measureFlag: [\r\n          { required: true, message: \"请选择是否计量\", trigger: \"change\" }\r\n        ],\r\n        applyCompany: [\r\n          { required: true, message: \"请选择申请单位\", trigger: \"change\" }\r\n        ],\r\n        receiveCompany: [\r\n          { required: true, message: \"请选择收货单位\", trigger: \"change\" }\r\n        ],\r\n        targetCompany: [\r\n          { required: true, message: \"请选择返回单位\", trigger: \"change\" }\r\n        ],\r\n        monitor: [\r\n          { required: true, message: \"请输入监装人\", trigger: \"blur\" }\r\n        ],\r\n        specialManager: [\r\n          { required: true, message: \"请输入物资专管员\", trigger: \"blur\" }\r\n        ],\r\n        materialType: [\r\n          { required: true, message: \"请选择物资类型\", trigger: \"change\" }\r\n        ],\r\n        leaveReason: [\r\n          { required: true, message: \"请输入出厂原因\", trigger: \"blur\" }\r\n        ],\r\n        plannedReturnTime: [\r\n          { required: true, message: \"请选择计划返回时间\", trigger: \"change\" }\r\n        ]\r\n      },\r\n      // 单位下拉选项\r\n      departmentOptions: [],\r\n      customerOptions: [],\r\n      departmentLoading: false,\r\n      customerLoading: false,\r\n      // 物资下拉选项\r\n      materialOptions: [],\r\n      materialLoading: false,\r\n      \r\n      // 上传相关\r\n      uploadUrl: process.env.VUE_APP_BASE_API + \"/common/uploadMinio\",\r\n      uploadParams: {\r\n        // 上传时可能需要的额外参数\r\n        uploadType: 'leavePlan'\r\n      },\r\n      imgFileList: [], // 图片文件列表\r\n      fileList: [], // 文件列表\r\n      imgPreviewVisible: false, // 图片预览对话框可见性\r\n      imgPreviewUrl: \"\", // 图片预览URL\r\n      \r\n      // 部门和客户选项关联映射\r\n      departmentMap: new Map(), // 部门id到名称的映射\r\n      customerMap: new Map(), // 客户id到名称的映射\r\n      // 添加日期限制对象\r\n      datePickerOptions: {\r\n        disabledDate(time) {\r\n          // 获取当前月份的25号日期\r\n          const currentDate = new Date();\r\n          const year = currentDate.getFullYear();\r\n          const month = currentDate.getMonth();\r\n          const monthLimit = new Date(year, month, 25, 23, 59, 59);\r\n          \r\n          // 禁用超过当月25号的日期\r\n          return time.getTime() > monthLimit.getTime();\r\n        }\r\n      },\r\n    };\r\n  },\r\n  computed: {\r\n    // 特殊条件：跨区调拨且有计划量计量时\r\n    isSpecialCondition() {\r\n      return this.form.planType === 3 && this.form.businessCategory === 21;\r\n    }\r\n  },\r\n  watch: {\r\n    // 监控特殊条件变化，自动设置是否计量为\"计量\"\r\n    isSpecialCondition(val) {\r\n      if (val) {\r\n        this.form.measureFlag = 1;\r\n      }\r\n    },\r\n    // 计划类型、业务类型、计划结束时间变化时，同步有效期\r\n    'form.planType': {\r\n    handler(val) {\r\n      this.syncExpireTime();\r\n    }\r\n  },\r\n  'form.businessCategory': {\r\n    handler(val) {\r\n      this.syncExpireTime();\r\n    }\r\n  },\r\n  'form.endTime': {\r\n    handler(val) {\r\n      this.syncExpireTime();\r\n    }\r\n  }\r\n  },\r\n  created() {\r\n    // 获取路由参数中的applyNo，判断是新增还是编辑\r\n    const applyNo = this.$route.params.applyNo;\r\n    if (applyNo) {\r\n      this.isEdit = true;\r\n      this.getDetail(applyNo);\r\n    } else {\r\n       // 新增时，设置有效期为当前日期+3天 23:59:59\r\n      const now = new Date();\r\n      now.setDate(now.getDate() + 3);\r\n      const yyyy = now.getFullYear();\r\n      const mm = String(now.getMonth() + 1).padStart(2, '0');\r\n      const dd = String(now.getDate()).padStart(2, '0');\r\n      this.form.expireTime = `${yyyy}-${mm}-${dd}`; \r\n      // 初始化一行物资数据\r\n      this.handleAddMaterial();\r\n      // 根据选中的计划类型设置默认业务类型\r\n      this.handlePlanTypeChange(this.form.planType);\r\n      // 初始化表单校验规则\r\n      this.updateFormRules();\r\n    }\r\n  },\r\n  methods: {\r\n    // 同步有效期\r\n    syncExpireTime() {\r\n    if (this.form.planType === 3 && this.form.businessCategory === 21) {\r\n      this.form.expireTime = this.form.endTime;\r\n      }else if (!this.form.expireTime) {\r\n      // 只在有效期为空时设置默认值（3天）\r\n      const now = new Date();\r\n      now.setDate(now.getDate() + 3);\r\n      const yyyy = now.getFullYear();\r\n      const mm = String(now.getMonth() + 1).padStart(2, '0');\r\n      const dd = String(now.getDate()).padStart(2, '0');\r\n      this.form.expireTime = `${yyyy}-${mm}-${dd}`;\r\n    }\r\n    },    \r\n    // 获取申请详情\r\n    getDetail(applyNo) {\r\n      detailPlan(applyNo).then(response => {\r\n        if (response.code === 200) {\r\n          const detail = response.data;\r\n          \r\n          // 填充基本信息\r\n          this.form.id = detail.id;\r\n          this.form.applyNo = detail.applyNo;\r\n          this.form.planType = detail.planType;\r\n          this.form.businessCategory = detail.businessCategory;\r\n          this.form.measureFlag = detail.measureFlag;\r\n          this.form.plannedAmount = detail.plannedAmount || 0;\r\n          \r\n          // 保存单位代码和名称\r\n          this.form.sourceCompanyCode = detail.sourceCompanyCode;\r\n          this.form.sourceCompany = detail.sourceCompany;\r\n          this.form.receiveCompanyCode = detail.receiveCompanyCode;\r\n          this.form.receiveCompany = detail.receiveCompany;\r\n          this.form.targetCompanyCode = detail.targetCompanyCode;\r\n          this.form.targetCompany = detail.targetCompany;\r\n          this.form.refundCompanyCode = detail.refundDepartmentCode;\r\n          this.form.refundCompany = detail.refundDepartment;\r\n          \r\n          // 手动添加已存在的部门和客户到选项数组，以便正确显示选中的值\r\n          // 添加申请单位到部门选项\r\n          if (detail.sourceCompanyCode && detail.sourceCompany) {\r\n            this.addToDepartmentOptions(detail.sourceCompanyCode, detail.sourceCompany);\r\n          }\r\n          \r\n          // 添加收货单位（可能是部门或客户）\r\n          if (detail.receiveCompanyCode && detail.receiveCompany) {\r\n            if (detail.planType === 3) {\r\n              // 跨区调拨时，收货单位是部门\r\n              this.addToDepartmentOptions(detail.receiveCompanyCode, detail.receiveCompany);\r\n            } else {\r\n              // 其他情况，收货单位是客户\r\n              this.addToCustomerOptions(detail.receiveCompanyCode, detail.receiveCompany);\r\n            }\r\n          }\r\n          \r\n          // 添加返回单位到部门选项\r\n          if (detail.targetCompanyCode && detail.targetCompany) {\r\n            this.addToDepartmentOptions(detail.targetCompanyCode, detail.targetCompany);\r\n          }\r\n          \r\n          // 添加退货单位到部门选项\r\n          if (detail.refundDepartmentCode && detail.refundDepartment) {\r\n            this.addToDepartmentOptions(detail.refundDepartmentCode, detail.refundDepartment);\r\n          }\r\n          \r\n          this.form.plannedReturnTime = detail.planReturnTime;\r\n          this.form.startTime = detail.startTime;\r\n          this.form.endTime = detail.endTime;\r\n          this.form.monitor = detail.monitor;\r\n          this.form.specialManager = detail.specialManager;\r\n          this.form.itemType = detail.itemType;\r\n          this.form.reason = detail.reason;\r\n          this.form.contractNo = detail.contractNo;\r\n          this.form.secApproveFlag = detail.secApproveFlag;\r\n          this.form.applyImgUrl = detail.applyImgUrl;\r\n          this.form.applyFileUrl = detail.applyFileUrl;\r\n          \r\n          // 处理图片和文件列表\r\n          this.initFileList();\r\n          \r\n          // 填充物资列表\r\n          if (detail.materials && detail.materials.length > 0) {\r\n            this.form.materials = detail.materials.map(item => ({\r\n              id: item.id,\r\n              materialId: item.materialId,\r\n              materialName: item.materialName,\r\n              materialSpec: item.materialSpec,\r\n              planNum: item.planNum,\r\n              measureUnit: item.measureUnit,\r\n              remark: item.remark\r\n            }));\r\n            \r\n            // 添加物资到选项数组\r\n            detail.materials.forEach(item => {\r\n              if (item.materialId && item.materialName) {\r\n                this.addToMaterialOptions(item.materialId, item.materialName, item.materialSpec, item.measureUnit);\r\n              }\r\n            });\r\n          } else {\r\n            this.handleAddMaterial();\r\n          }\r\n          \r\n          // 更新表单校验规则\r\n          this.updateFormRules();\r\n        }\r\n      });\r\n    },\r\n    // 远程搜索部门\r\n    remoteSearchDepartment(query) {\r\n      if (query !== '') {\r\n        this.departmentLoading = true;\r\n        listDepartment({\r\n          storeName: query,\r\n          pageNum: 1,\r\n          pageSize: 20\r\n        }).then(response => {\r\n          this.departmentLoading = false;\r\n          if (response.code === 200) {\r\n            this.departmentOptions = response.rows;\r\n          }\r\n        }).finally(() => {\r\n          this.departmentLoading = false;\r\n        });\r\n      } else {\r\n        this.departmentOptions = [];\r\n      }\r\n    },\r\n\r\n    // 远程搜索客户\r\n    remoteSearchCustomer(query) {\r\n      if (query !== '') {\r\n        this.customerLoading = true;\r\n        listCustomer({\r\n          customerName: query,\r\n          pageNum: 1,\r\n          pageSize: 20\r\n        }).then(response => {\r\n          this.customerLoading = false;\r\n          if (response.code === 200) {\r\n            this.customerOptions = response.rows;\r\n          }\r\n        }).finally(() => {\r\n          this.customerLoading = false;\r\n        });\r\n      } else {\r\n        this.customerOptions = [];\r\n      }\r\n    },\r\n\r\n    // 业务类型变更时触发\r\n    handleBusinessCategoryChange() {\r\n      // 更新表单校验规则\r\n      this.updateFormRules();\r\n    },\r\n\r\n    // 计划类型变更时触发\r\n    handlePlanTypeChange(val) {\r\n      // 根据计划类型设置默认业务类型\r\n      switch (val) {\r\n        case 1:\r\n          this.form.businessCategory = 1;\r\n          break;\r\n        case 2:\r\n          this.form.businessCategory = 11;\r\n          break;\r\n        case 3:\r\n          this.form.businessCategory = 21;\r\n          break;\r\n        case 4:\r\n          this.form.businessCategory = 31;\r\n          break;\r\n        default:\r\n          break;\r\n      }\r\n\r\n      // 重置不需要的字段\r\n      if (val !== 2) {\r\n        this.form.targetCompany = \"\";\r\n        this.form.plannedReturnTime = \"\";\r\n      }\r\n\r\n      if (val !== 4) {\r\n        this.form.refundCompany = \"\";\r\n      }\r\n\r\n      if (val !== 3 || this.form.businessCategory !== 21) {\r\n        this.form.startTime = \"\";\r\n        this.form.endTime = \"\";\r\n      }\r\n\r\n      // 更新表单校验规则\r\n      this.updateFormRules();\r\n    },\r\n\r\n    // 更新表单校验规则\r\n    updateFormRules() {\r\n      const tempRules = {\r\n        planType: [\r\n          { required: true, message: \"请选择计划类型\", trigger: \"change\" }\r\n        ],\r\n        businessCategory: [\r\n          { required: true, message: \"请选择业务类型\", trigger: \"change\" }\r\n        ],\r\n        measureFlag: [\r\n          { required: true, message: \"请选择是否计量\", trigger: \"change\" }\r\n        ],\r\n        applyCompany: [\r\n          { required: true, message: \"请选择申请单位\", trigger: \"change\" }\r\n        ],\r\n        monitor: [\r\n          { required: true, message: \"请输入监装人\", trigger: \"blur\" }\r\n        ],\r\n        specialManager: [\r\n          { required: true, message: \"请输入物资专管员\", trigger: \"blur\" }\r\n        ],\r\n        materialType: [\r\n          { required: true, message: \"请选择物资类型\", trigger: \"change\" }\r\n        ]\r\n      };\r\n\r\n      // 根据条件添加校验规则\r\n      if (this.form.planType !== 1) {\r\n        tempRules.receiveCompany = [\r\n          { required: true, message: \"请选择收货单位\", trigger: \"change\" }\r\n        ];\r\n      }\r\n\r\n      if (this.form.planType === 2) {\r\n        tempRules.targetCompany = [\r\n          { required: true, message: \"请选择返回单位\", trigger: \"change\" }\r\n        ];\r\n        tempRules.plannedReturnTime = [\r\n          { required: true, message: \"请选择计划返回时间\", trigger: \"change\" }\r\n        ];\r\n      }\r\n\r\n      if (this.form.planType === 4) {\r\n        tempRules.refundCompany = [\r\n          { required: true, message: \"请选择退货单位\", trigger: \"change\" }\r\n        ];\r\n      }\r\n\r\n      if (this.form.planType === 3 && this.form.businessCategory === 21) {\r\n        tempRules.plannedAmount = [\r\n          { required: true, message: \"请输入计划量\", trigger: \"blur\" }\r\n        ];\r\n        tempRules.startTime = [\r\n          { required: true, message: \"请选择开始时间\", trigger: \"change\" }\r\n        ];\r\n        tempRules.endTime = [\r\n          { required: true, message: \"请选择结束时间\", trigger: \"change\" }\r\n        ];\r\n      }\r\n\r\n      // 更新规则\r\n      this.rules = tempRules;\r\n    },\r\n\r\n    // 添加物资\r\n    handleAddMaterial() {\r\n      this.form.materials.push({\r\n        materialId: \"\",\r\n        materialName: \"\",\r\n        materialSpec: \"\",\r\n        planNum: 1,\r\n        measureUnit: \"\",\r\n        remark: \"\"\r\n      });\r\n    },\r\n    // 删除物资\r\n    handleDeleteMaterial(index) {\r\n      this.form.materials.splice(index, 1);\r\n      // 如果删完了，至少保留一行\r\n      if (this.form.materials.length === 0) {\r\n        this.handleAddMaterial();\r\n      }\r\n    },\r\n    // 远程搜索物资\r\n    remoteSearchMaterial(query) {\r\n      if (query !== '') {\r\n        this.materialLoading = true;\r\n        listMaterial({\r\n          materialName: query,\r\n          pageNum: 1,\r\n          pageSize: 20\r\n        }).then(response => {\r\n          this.materialLoading = false;\r\n          if (response.code === 200) {\r\n            this.materialOptions = response.rows;\r\n          }\r\n        }).finally(() => {\r\n          this.materialLoading = false;\r\n        });\r\n      } else {\r\n        this.materialOptions = [];\r\n      }\r\n    },\r\n    // 处理物资选择\r\n    handleMaterialSelect(value, row) {\r\n      // 根据选中的物资ID找到对应的物资详情\r\n      const selectedMaterial = this.materialOptions.find(item => item.id === value);\r\n      if (selectedMaterial) {\r\n        row.materialName = selectedMaterial.materialName;\r\n        // 如果物资信息中有型号规格和单位，可以自动填充\r\n        if (selectedMaterial.materialSpec) {\r\n          row.materialSpec = selectedMaterial.materialSpec;\r\n        }\r\n        if (selectedMaterial.measureUnit) {\r\n          row.measureUnit = selectedMaterial.measureUnit;\r\n        }\r\n      }\r\n    },\r\n    // 初始化文件列表\r\n    initFileList() {\r\n      // 处理图片\r\n      if (this.form.applyImgUrl) {\r\n        try {\r\n          // 尝试解析JSON\r\n          const imgFiles = JSON.parse(this.form.applyImgUrl);\r\n          this.imgFileList = imgFiles.map(item => {\r\n            return {\r\n              name: item.name,\r\n              url: item.url\r\n            };\r\n          });\r\n        } catch (e) {\r\n          console.log(e);\r\n        }\r\n      }\r\n      \r\n      // 处理文件\r\n      if (this.form.applyFileUrl) {\r\n        try {\r\n          // 尝试解析JSON\r\n          const files = JSON.parse(this.form.applyFileUrl);\r\n          this.fileList = files.map(item => {\r\n            return {\r\n              name: item.name,\r\n              url: item.url,\r\n            };\r\n          });\r\n        } catch (e) {\r\n          console.log(e);\r\n        }\r\n      }\r\n    },\r\n    \r\n    // 图片上传成功\r\n    handleImgSuccess(response, file, fileList) {\r\n      console.log(response);\r\n      console.log(file);\r\n      console.log(fileList);\r\n      if (response.code === 200) {\r\n        // 更新图片URL到表单\r\n        // this.imgFileList = fileList;\r\n        \r\n        // 构建JSON格式的图片数据\r\n        /**\r\n         * response格式：\r\n         * {\r\n    \"msg\": \"操作成功\",\r\n    \"originalFileName\": \"006r3PQBjw1f8p3zb1wioj30c80c8jru.jpg\",\r\n    \"fileName\": \"xctg/2025/03/25/da2a4a3f-6962-4831-a4e5-6ecc2397e483.jpg\",\r\n    \"code\": 200,\r\n    \"url\": \"https://ydxt.citicsteel.com:8099/minio/xctg/2025/03/25/da2a4a3f-6962-4831-a4e5-6ecc2397e483.jpg\"\r\n}\r\n         */\r\n\r\n        \r\n        // 只获取originalFileName、url 转换为JSON字符串保存\r\n        const imgData = {\r\n          name: response.originalFileName,\r\n          url: response.url\r\n        };\r\n        this.imgFileList.push(imgData);\r\n        console.log(this.imgFileList);\r\n        this.form.applyImgUrl = JSON.stringify(this.imgFileList);\r\n        console.log(this.form.applyImgUrl);\r\n      } else {\r\n        this.$message.error('图片上传失败');\r\n      }\r\n    },\r\n    \r\n    // 附件上传成功\r\n    handleAnnexFileSuccess(response, file, fileList) {\r\n      console.log(response);\r\n      console.log(file);\r\n      console.log(fileList);\r\n      if (response.code === 200) {\r\n        // 只获取originalFileName、url 转换为JSON字符串保存\r\n        const annexFileData = {\r\n          name: response.originalFileName,\r\n          url: response.url\r\n        };\r\n        this.fileList.push(annexFileData);\r\n        console.log(this.fileList);\r\n        this.form.applyFileUrl = JSON.stringify(this.fileList);\r\n        console.log(this.form.applyFileUrl);\r\n      } else {\r\n        this.$message.error('附件上传失败');\r\n      }\r\n    },\r\n\r\n    // 物资导入文件上传成功处理\r\n    handleMaterialFileSuccess(response, file, fileList) {\r\n      this.upload.isUploading = false;\r\n      this.$refs.upload.clearFiles();\r\n      this.importVisible = false;\r\n      \r\n      if (response.code === 200) {\r\n        this.$message.success(\"导入成功\");\r\n        // 将导入的物资列表添加到当前表单的物资列表中\r\n        if (response.data && response.data.length > 0) {\r\n          // 追加新导入的物资\r\n          const importedMaterials = response.data.map(item => {\r\n            // 如果没有materialId，使用-1作为默认ID，并确保该物资能显示\r\n            const materialId = item.materialId || -1;\r\n            // 物资名称必须有值\r\n            const materialName = item.materialName || \"未知物资\";\r\n            \r\n            // 如果是默认ID的物资，则添加到选项中\r\n            if (materialId === -1) {\r\n              this.addToMaterialOptions(\r\n                materialId,\r\n                materialName,\r\n                item.materialSpec || \"\",\r\n                item.measureUnit || \"\"\r\n              );\r\n            }\r\n            \r\n            return {\r\n              materialId: materialId,\r\n              materialName: materialName,\r\n              materialSpec: item.materialSpec || \"\",\r\n              planNum: item.planNum || 1,\r\n              measureUnit: item.measureUnit || \"\",\r\n              remark: item.remark || ''\r\n            };\r\n          });\r\n          \r\n          // 将导入的物资添加到表单中\r\n          this.form.materials = this.form.materials.concat(importedMaterials);\r\n          \r\n          // 使用nextTick确保视图更新\r\n          this.$nextTick(() => {\r\n            // 触发一次刷新，确保下拉框正确显示\r\n            this.materialOptions = [...this.materialOptions];\r\n          });\r\n        }\r\n      } else {\r\n        this.$message.error(response.msg || \"导入失败\");\r\n      }\r\n    },\r\n    \r\n    // 根据物资名称查询materialId\r\n    queryMaterialIdByName(rowIndex) {\r\n      // 此方法已不再使用，保留空方法以避免可能的调用错误\r\n    },\r\n    \r\n    // 图片预览\r\n    handleImgPreview(file) {\r\n      this.imgPreviewUrl = file.url || (file.response && file.response.data);\r\n      this.imgPreviewVisible = true;\r\n    },\r\n    \r\n    // 文件预览\r\n    handleFilePreview(file) {\r\n      window.open(file.url || (file.response && file.response.data));\r\n    },\r\n    \r\n    // 移除图片\r\n    handleImgRemove(file, fileList) {\r\n      //移除fileList中url与file.url相同的文件\r\n      this.imgFileList = this.imgFileList.filter(item => item.url !== file.url);\r\n      \r\n      // 转换为JSON字符串保存\r\n      this.form.applyImgUrl = JSON.stringify(imgFileList);\r\n    },\r\n    \r\n    // 移除文件\r\n    handleFileRemove(file, fileList) {\r\n      //移除fileList中url与file.url相同的文件\r\n      this.fileList = this.fileList.filter(item => item.url !== file.url);\r\n      \r\n      // 转换为JSON字符串保存\r\n      this.form.applyFileUrl = JSON.stringify(fileList);\r\n    },\r\n    \r\n    // 超出文件数量限制\r\n    handleExceed() {\r\n      this.$message.warning('最多只能上传3个文件');\r\n    },\r\n\r\n    // 表单提交\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          // 验证物资列表\r\n          if (this.form.materials.length === 0) {\r\n            this.$message.error(\"请至少添加一项物资\");\r\n            return;\r\n          }\r\n\r\n          // 验证物资列表的必填项\r\n          let valid = true;\r\n          this.form.materials.forEach((item, index) => {\r\n            if (!item.materialId || !item.materialName || !item.materialSpec || !item.planNum || !item.measureUnit) {\r\n              this.$message.error(`第${index + 1}行物资信息不完整，请填写完整`);\r\n              valid = false;\r\n            }\r\n          });\r\n\r\n          if (!valid) {\r\n            return;\r\n          }\r\n\r\n          // 验证计划量大于0\r\n          if (this.form.planType === 3 && this.form.businessCategory === 21 && (!this.form.plannedAmount || this.form.plannedAmount <= 0)) {\r\n            this.$message.error(\"计划量必须大于0\");\r\n            return;\r\n          }\r\n          //对有效期单独处理\r\n          if (this.form.planType === 3 && this.form.businessCategory === 21) {\r\n            if (this.form.endTime) {\r\n              this.form.expireTime = this.form.endTime + \" 23:59:59\";\r\n            }\r\n          } else if (this.form.expireTime) {\r\n            this.form.expireTime = this.form.expireTime + \" 23:59:59\";\r\n          }\r\n          //对开始结束时间单独处理\r\n          if (this.form.startTime && this.form.endTime) {\r\n            this.form.startTime = this.form.startTime + \" 00:00:00\";\r\n            this.form.endTime = this.form.endTime + \" 23:59:59\";\r\n          }\r\n\r\n          // 根据是否编辑模式调用不同的API\r\n          const apiMethod = this.isEdit ? updatePlan : addPlan;\r\n          const successMsg = this.isEdit ? \"修改成功\" : \"申请提交成功\";\r\n          console.log(this.form);\r\n          // 调用API提交数据\r\n          apiMethod(this.form).then(response => {\r\n            if (response.code === 200) {\r\n              this.$message.success(successMsg);\r\n              this.$tab.closeOpenPage(this.$route);\r\n              // 跳转到列表页面并刷新\r\n              this.$router.push({ \r\n                path: \"/leave/leavePlanList\", \r\n                query: { \r\n                  t: Date.now(),\r\n                  refresh: true // 添加刷新标记\r\n                }\r\n              });\r\n            } else {\r\n              this.$message.error(response.msg || \"提交失败\");\r\n            }\r\n          }).catch(error => {\r\n            console.error(\"提交失败\", error);\r\n            this.$message.error(\"提交过程中发生错误，请稍后再试\");\r\n          });\r\n        }\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.$router.push(\"/leave/plan\");\r\n    },\r\n    // 图片上传前的验证\r\n    beforeImgUpload(file) {\r\n      const isImage = file.type.indexOf('image/') === 0;\r\n      const isLt10M = file.size / 1024 / 1024 < 10;\r\n\r\n      if (!isImage) {\r\n        this.$message.error('只能上传图片格式文件!');\r\n        return false;\r\n      }\r\n      if (!isLt10M) {\r\n        this.$message.error('图片大小不能超过 10MB!');\r\n        return false;\r\n      }\r\n      return true;\r\n    },\r\n\r\n    // 文件上传前的验证\r\n    beforeFileUpload(file) {\r\n      const isLt20M = file.size / 1024 / 1024 < 20;\r\n      if (!isLt20M) {\r\n        this.$message.error('文件大小不能超过 20MB!');\r\n        return false;\r\n      }\r\n      return true;\r\n    },\r\n    \r\n    // 上传错误处理\r\n    handleUploadError(err, file, fileList) {\r\n      console.error(\"上传失败:\", err);\r\n      \r\n      if (err.status === 403) {\r\n        this.$message.error('上传失败：没有权限');\r\n      } else {\r\n        this.$message.error('上传失败：' + (err.message || '未知错误'));\r\n      }\r\n    },\r\n    // 获取部门名称\r\n    getDepartmentName(id) {\r\n      if (!id) return '';\r\n      \r\n      // 查询选项中是否有匹配的\r\n      const dept = this.departmentOptions.find(item => item.id == id);\r\n      if (dept) {\r\n        return dept.storeName;\r\n      }\r\n      \r\n      // 根据不同的字段ID返回对应的中文名称\r\n      if (id === this.form.sourceCompanyCode && this.form.sourceCompany) {\r\n        return this.form.sourceCompany;\r\n      }\r\n      \r\n      if (id === this.form.receiveCompanyCode && this.form.receiveCompany && this.form.planType === 3) {\r\n        return this.form.receiveCompany;\r\n      }\r\n      \r\n      if (id === this.form.targetCompanyCode && this.form.targetCompany) {\r\n        return this.form.targetCompany;\r\n      }\r\n      \r\n      if (id === this.form.refundCompanyCode && this.form.refundCompany) {\r\n        return this.form.refundCompany;\r\n      }\r\n      \r\n      // 否则显示ID\r\n      return `ID: ${id}`;\r\n    },\r\n    \r\n    // 获取客户名称\r\n    getCustomerName(id) {\r\n      if (!id) return '';\r\n      \r\n      // 查询选项中是否有匹配的\r\n      const customer = this.customerOptions.find(item => item.id == id);\r\n      if (customer) {\r\n        return customer.customerName;\r\n      }\r\n      \r\n      // 如果是收货单位且不是跨区调拨，则使用已有的中文名称\r\n      if (id === this.form.receiveCompanyCode && this.form.receiveCompany && this.form.planType !== 3) {\r\n        return this.form.receiveCompany;\r\n      }\r\n      \r\n      // 否则显示ID\r\n      return `ID: ${id}`;\r\n    },\r\n    // 添加部门到选项数组方法\r\n    addToDepartmentOptions(id, name) {\r\n      // 检查是否已存在相同ID的选项\r\n      if (!this.departmentOptions.some(item => item.id == id)) {\r\n        this.departmentOptions.push({\r\n          id: id,\r\n          storeName: name\r\n        });\r\n      }\r\n    },\r\n    \r\n    // 添加客户到选项数组方法\r\n    addToCustomerOptions(id, name) {\r\n      // 检查是否已存在相同ID的选项\r\n      if (!this.customerOptions.some(item => item.id == id)) {\r\n        this.customerOptions.push({\r\n          id: id,\r\n          customerName: name\r\n        });\r\n      }\r\n    },\r\n    \r\n    // 添加物资到选项数组方法\r\n    addToMaterialOptions(id, name, spec, unit) {\r\n      // 检查是否已存在相同ID的选项\r\n      if (!this.materialOptions.some(item => item.id == id)) {\r\n        this.materialOptions.push({\r\n          id: id,\r\n          materialName: name,\r\n          materialSpec: spec,\r\n          measureUnit: unit\r\n        });\r\n      }\r\n    },\r\n    // 导出物资模板\r\n    handleExportMaterialTemplate() {\r\n      exportMaterialTemplate().then(response => {\r\n        this.download(response.msg);\r\n      });\r\n    },\r\n    \r\n    // 显示导入物资对话框\r\n    handleImportMaterial() {\r\n      this.importVisible = true;\r\n    },\r\n    \r\n    // 文件上传中处理\r\n    handleFileUploadProgress() {\r\n      this.upload.isUploading = true;\r\n    },\r\n    \r\n    // 提交上传文件\r\n    submitFileForm() {\r\n      this.$refs.upload.submit();\r\n    },\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.app-container {\r\n  padding: 20px;\r\n}\r\n.box-card {\r\n  margin-bottom: 20px;\r\n  border-radius: 5px;\r\n}\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n.material-card {\r\n  margin-top: 20px;\r\n  margin-bottom: 20px;\r\n}\r\n.form-footer {\r\n  margin-top: 30px;\r\n  text-align: center;\r\n}\r\n.material-btn-group {\r\n  float: right;\r\n}\r\n.material-btn-group .el-button {\r\n  margin-left: 8px;\r\n  border-radius: 4px;\r\n}\r\n.material-btn-group .el-button:first-child {\r\n  margin-left: 0;\r\n}\r\n.tip-text {\r\n  color: #f56c6c;\r\n  font-size: 12px;\r\n  line-height: 1.2;\r\n  margin-top: 5px;\r\n}\r\n</style>\r\n"]}]}