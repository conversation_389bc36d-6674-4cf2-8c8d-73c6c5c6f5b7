package com.ruoyi.app.leave.service;

import java.util.List;
import com.ruoyi.app.leave.domain.LeaveTask;

/**
 * 出门证任务Service接口
 * 
 * <AUTHOR>
 * @date 2025-03-13
 */
public interface ILeaveTaskService 
{
    /**
     * 查询出门证任务
     * 
     * @param id 出门证任务ID
     * @return 出门证任务
     */
    public LeaveTask selectLeaveTaskById(Long id);

    /**
     * 查询出门证任务列表
     * 
     * @param leaveTask 出门证任务
     * @return 出门证任务集合
     */
    public List<LeaveTask> selectLeaveTaskList(LeaveTask leaveTask);

    /**
     * 新增出门证任务
     * 
     * @param leaveTask 出门证任务
     * @return 结果
     */
    public int insertLeaveTask(LeaveTask leaveTask);

    /**
     * 修改出门证任务
     * 
     * @param leaveTask 出门证任务
     * @return 结果
     */
    public int updateLeaveTask(LeaveTask leaveTask);

    /**
     * 批量删除出门证任务
     * 
     * @param ids 需要删除的出门证任务ID
     * @return 结果
     */
    public int deleteLeaveTaskByIds(Long[] ids);

    /**
     * 删除出门证任务信息
     * 
     * @param id 出门证任务ID
     * @return 结果
     */
    public int deleteLeaveTaskById(Long id);
}
