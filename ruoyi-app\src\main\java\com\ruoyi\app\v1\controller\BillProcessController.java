package com.ruoyi.app.v1.controller;


import com.ruoyi.app.service.IDriverService;
import com.ruoyi.app.v1.domain.CustCarrier;
import com.ruoyi.app.v1.domain.LadingSubscribe;
import com.ruoyi.app.v1.service.ICustCarrierService;
import com.ruoyi.app.v1.service.IEmployeeLogV1Service;
import com.ruoyi.app.v1.service.ILadingSubscribeService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.enums.OperatorType;
import com.ruoyi.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 提单电子化流程Controller
 *
 * <AUTHOR>
 * @date 2022-03-21
 */
@RestController
@RequestMapping("/app/v1/billProcess")
public class BillProcessController extends AppBaseV1Controller {

    @Autowired
    private ILadingSubscribeService ladingSubscribeService;

    @Autowired
    private IDriverService driverService;

    @Autowired
    private ICustCarrierService custCarrierService;

    /**
     * 插入司机预约提货记录
     *
     * @param ladingSubscribe 预约信息
     * @return 结果
     */
    @RepeatSubmit
    @Log(title = "生成司机预约提货记录", businessType = BusinessType.SELECT, operatorType = OperatorType.MOBILE, isSaveRequestData = false, isSaveResultData = false)
    @PostMapping("/driver/reserveInsert")
    public AjaxResult insertDriverReserve(LadingSubscribe ladingSubscribe) {
        String openId = getOpenId();
        ladingSubscribe.setOpenId(openId);
        ladingSubscribe.setDelivyNo(ladingSubscribeService.getDelivyNo());
        return AjaxResult.success(ladingSubscribeService.insertLadingSubscribe(ladingSubscribe));
    }

    /**
     * 补充未发送产销预约信息
     *
     * @param
     * @return 结果
     */
    @PostMapping("/admin/supplyUnSentReserve")
    public AjaxResult supplyUnSentReserve(Long start, Long end){
        LadingSubscribe ladingSubscribe = new LadingSubscribe();
        ladingSubscribe.setStart(start);
        ladingSubscribe.setEnd(end);
        return AjaxResult.success(ladingSubscribeService.supplyUnSentReserve(ladingSubscribe));
    }


    /**
     * 查询具体提单信息(产销)
     *
     * @param billOfLadingNo 提单号
     * @return 提单具体信息
     */
    @GetMapping("/driver/billDetail")
    public AjaxResult getBillDetail(String billOfLadingNo) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("billDetail", ladingSubscribeService.getLadingDetailByladingNo(billOfLadingNo));
        return AjaxResult.success(map);
    }

    /**
     * 查询所有位置信息
     *
     * @return 结果
     */
    @GetMapping("/getStation")
    @Log(title = "提货点地图", businessType = BusinessType.SELECT, operatorType = OperatorType.MOBILE, isSaveRequestData = false, isSaveResultData = false)
    public AjaxResult getStation(String billOfLadingNo) {
        if (billOfLadingNo == null) {
            return AjaxResult.error("无提单号");
        }
        return AjaxResult.success(ladingSubscribeService.getStation(billOfLadingNo));
    }

    /**
     * 根据提单号查询承运单号(产销)
     *
     * @param billNo 提单号
     * @return 结果
     */
    @GetMapping("/driver/getCarryCompanyName")
    public AjaxResult getCarryCompanyName(String billNo) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("carryCompanyName", ladingSubscribeService.getCarryCompanyNameByBillNo(billNo));
        return AjaxResult.success(map);
    }

    /**
     * 根据车牌号查询提单号(产销)
     *
     * @param vehicle 车牌号
     * @return 结果
     */
    @GetMapping("/driver/getLadingNoByVehicle")
    public AjaxResult getLadingNoByVehicle(String vehicle) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("ladingNos", ladingSubscribeService.getLadingNoByVehicle(vehicle));
        return AjaxResult.success(map);
    }

    /**
     * 根据提货码查询提单号(产销)
     *
     * @param delivyNo 提货码
     * @return 结果
     */
    @GetMapping("/driver/getLadingDetailByDelivyNo")
    public AjaxResult getLadingDetailByDelivyNo(String delivyNo) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("deliveryInfo", ladingSubscribeService.getLadingDetailByDelivyNo(delivyNo));
        return AjaxResult.success(map);
    }

    /**
     * 根据OpenId查询提单号
     */
    @GetMapping("/driverLadingRecord")
    public AjaxResult list() {
        return AjaxResult.success(ladingSubscribeService.selectDriverLadingRecordResult(getOpenId()));
    }

    /**
     * 根据提货码查询服务评价
     */
    @GetMapping("/driverLadingService")
    public AjaxResult serviceDetail(String delivyNo) {
        Map<String, Object> map = new HashMap<String, Object>();

        map.put("service", ladingSubscribeService.selectLadingService(delivyNo));
        return AjaxResult.success(map);
    }

    /**
     * 插入服务评价
     *
     * @param ladingSubscribe 服务评价
     * @return 结果
     */
    @RepeatSubmit
    @PostMapping("/driverService")
    public AjaxResult changeLadingService(LadingSubscribe ladingSubscribe) {
        //获取司机openId
        ladingSubscribe.setOpenId(getOpenId());
        String delivyNo = ladingSubscribe.getDelivyNo();
        Map<String, Object> map = ladingSubscribeService.selectLadingService(delivyNo);
        if (null == map || map.isEmpty() || (map.get("rate").toString().equals("") && map.get("service").toString().equals(""))) {
            return AjaxResult.success(ladingSubscribeService.insertLadingService(ladingSubscribe));
        } else {
            return AjaxResult.success(ladingSubscribeService.updateLadingService(ladingSubscribe));
        }

    }

    /**
     * 插入/更新提单备注
     *
     * @param ladingSubscribe 备注信息
     * @return 结果
     */
    @RepeatSubmit
    @PostMapping("/sale/insertRemark")
    public AjaxResult insertLadingRemark(LadingSubscribe ladingSubscribe) {
        if (StringUtils.isNotNullAndNotEmpty(ladingSubscribe.getBillOfLadingNo())) {
            //获取工号、姓名
//            String workNo = getWorkNo();
//            String workName = getWorkName(workNo);
//            ladingSubscribe.setWorkNo(workNo);
//            ladingSubscribe.setWorkName(workName);
            return AjaxResult.success(ladingSubscribeService.insertLadingRemark(ladingSubscribe));
        } else {
            return AjaxResult.error("缺少必要的参数");
        }
    }

    /**
     * 根据提单号查询提单备注
     *
     * @param billOfLadingNo
     * @return 结果
     */
    @GetMapping("/sale/remarkDetail")
    public AjaxResult getLadingRemarkByBillNo(String billOfLadingNo) {
        return AjaxResult.success(ladingSubscribeService.selectLadingRemarkByBillNo(billOfLadingNo));
    }

    /**
     * 查询提单信息列表
     *
     * @param identifier 客户/承运商编号, consignUserName
     * @return 提单信息集合
     */
    @GetMapping("/customerOrCarrier/ladingInfo")
    public AjaxResult getLadingInfo(String identifier, String consignUserName) {
        return AjaxResult.success(custCarrierService.getMaterialStowageList(identifier, consignUserName));
    }

    /**
     * 插入货车轨迹
     *
     * @param ladingSubscribe 轨迹信息
     * @return 结果
     */
    @RepeatSubmit
    @Log(title = "插入货车轨迹", businessType = BusinessType.SELECT, operatorType = OperatorType.MOBILE, isSaveRequestData = false, isSaveResultData = false)
    @PostMapping("/driver/locusInsert")
    public AjaxResult insertLocus(LadingSubscribe ladingSubscribe) {
        String openId = getOpenId();
        ladingSubscribe.setOpenId(openId);
        return AjaxResult.success(ladingSubscribeService.insertLocus(ladingSubscribe));
    }

    /**
     * 查询出入物资
     */
    @GetMapping("/driverLadingGoods")
    public AjaxResult ladingList(String billOfLadingNo) {
        List<Map<String, Object>> list = ladingSubscribeService.selectLadingGoods(billOfLadingNo);
        return AjaxResult.success(list);
    }

    /**
     * 插入配车记录
     *
     * @param custCarrier
     * @return 结果
     */
    @RepeatSubmit
    @Log(title = "新增配车", businessType = BusinessType.SELECT, operatorType = OperatorType.MOBILE, isSaveRequestData = false, isSaveResultData = false)
    @PostMapping("/customerOrCarrier/insertCarryRecord")
    public AjaxResult insertCarryRecord(CustCarrier custCarrier) {
        if (StringUtils.isNotNullAndNotEmpty(custCarrier.getBillOfLadingNo())) {
            return AjaxResult.success(custCarrierService.insertCarryRecord(custCarrier));
        } else {
            return AjaxResult.error("缺少必要的参数");
        }
    }

    /**
     * 删除配车记录
     *
     * @param custCarrier 提单预约
     * @return 结果
     */
    @PostMapping("/customerOrCarrier/deleteAllocateVehicle")
    public AjaxResult deleteAllocateVehicle(CustCarrier custCarrier) {
        return AjaxResult.success(custCarrierService.deleteAllocateVehicle(custCarrier));
    }

    /**
     * 查询提单分配车辆
     *
     * @param billOfLadingNo
     * @return 结果
     */
    @GetMapping("/customerOrCarrier/getCarList")
    public AjaxResult getCarList(String billOfLadingNo) {
        return AjaxResult.success(custCarrierService.getCarList(billOfLadingNo));

    }

    /**
     * 根据预约号查询提单信息
     *
     * @param delivyNo
     * @return 结果
     */
    @GetMapping("/driver/subscribeDetail")
    public AjaxResult getLadingSubscribeByDelivyNo(String delivyNo) {
        return AjaxResult.success(ladingSubscribeService.selectLadingSubscribeByDelivyNo(delivyNo));
    }

    /**
     * 司机完成预约(补充预约时间)
     *
     * @param ladingSubscribe
     * @return 结果
     */
    @RepeatSubmit
    @PostMapping("/driver/replenishSubscribe")
    public AjaxResult replenishSubscribe(LadingSubscribe ladingSubscribe) {
        ladingSubscribe.setOpenId(getOpenId());
        if (StringUtils.isNotNullAndNotEmpty(ladingSubscribe.getBillOfLadingNo())) {
            return AjaxResult.success(ladingSubscribeService.replenishSubscribe(ladingSubscribe));
        } else {
            return AjaxResult.error("缺少必要的参数");
        }
    }

    /**
     * 查询提单分配车辆
     *
     * @param identifier
     * @return 结果
     */
    @GetMapping("/customerOrCarrier/getCompanyList")
    public AjaxResult getCompanyList(String identifier) {
        return AjaxResult.success(custCarrierService.getCompanyList(identifier));

    }

    /**
     * 查询提单预约列表（返回TableDataInfo）
     *
     * @param query 提单预约查询条件
     * @return 提单预约列表
     */
    @GetMapping("/getLadingSubscribeTableApp")
    @Log(title = "查询提单预约列表", businessType = BusinessType.SELECT, operatorType = OperatorType.MOBILE, isSaveRequestData = false, isSaveResultData = false)
    public TableDataInfo getLadingSubscribeList(LadingSubscribe query) {
        try {
            TableDataInfo dataInfo = new TableDataInfo();
            startPage();
            dataInfo = ladingSubscribeService.getLadingSubscribeTableApp(query);
            return getDataTable(dataInfo, pageNum, pageSize);
        } catch (Exception e) {
            throw new RuntimeException("预约列表查询异常");
        }
    }

    /**
     * 获取提单预约详情
     *
     * @param query 提单预约
     * @return 提单预约详情
     */
    @GetMapping("/getLadingSubscribeDetail")
    @Log(title = "获取提单预约详情", businessType = BusinessType.SELECT, operatorType = OperatorType.MOBILE, isSaveRequestData = false, isSaveResultData = false)
    public AjaxResult getLadingSubscribeDetail(LadingSubscribe query) {
        try {
            Map<String, Object> result = ladingSubscribeService.getLadingSubscribeDetail(query.getId());
            if (result.containsKey("success") && (Boolean) result.get("success")) {
                return AjaxResult.success(result);
            } else {
                return AjaxResult.error((String) result.get("message"));
            }
        } catch (Exception e) {
            return AjaxResult.error("获取提单预约详情失败：" + e.getMessage());
        }
    }

}
