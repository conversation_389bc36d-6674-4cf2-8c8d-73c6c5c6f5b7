import request from '@/utils/request'

// 查询出门证物资列表
export function listMaterial(query) {
  return request({
    url: '/leave/material/list',
    method: 'get',
    params: query
  })
}

// 查询出门证物资详细
export function getMaterial(id) {
  return request({
    url: '/leave/material/' + id,
    method: 'get'
  })
}

// 新增出门证物资
export function addMaterial(data) {
  return request({
    url: '/leave/material',
    method: 'post',
    data: data
  })
}

// 修改出门证物资
export function updateMaterial(data) {
  return request({
    url: '/leave/material',
    method: 'put',
    data: data
  })
}

// 删除出门证物资
export function delMaterial(id) {
  return request({
    url: '/leave/material/' + id,
    method: 'delete'
  })
}

// 导出出门证物资
export function exportMaterial(query) {
  return request({
    url: '/leave/material/export',
    method: 'get',
    params: query
  })
}