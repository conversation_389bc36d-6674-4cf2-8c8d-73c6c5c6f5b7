import request from '@/utils/request'

// 查询出门证计划申请列表
export function listPlan(query) {
  return request({
    url: '/leave/plan/list',
    method: 'get',
    params: query
  })
}

// 查询出门证计划申请详细
export function getPlan(id) {
  return request({
    url: '/leave/plan/' + id,
    method: 'get'
  })
}

// 新增出门证计划申请
export function addPlan(data) {
  return request({
    url: '/leave/plan',
    method: 'post',
    data: data
  })
}

// 修改出门证计划申请
export function updatePlan(data) {
  return request({
    url: '/leave/plan',
    method: 'put',
    data: data
  })
}

// 删除出门证计划申请
export function delPlan(id) {
  return request({
    url: '/leave/plan/' + id,
    method: 'delete'
  })
}

// 导出出门证计划申请
export function exportPlan(query) {
  return request({
    url: '/leave/plan/export',
    method: 'get',
    params: query
  })
}