{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\leave\\plan\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\leave\\plan\\index.vue", "mtime": 1756099891074}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBsaXN0UGxhbiwgZ2V0UGxhbiwgZGVsUGxhbiwgYWRkUGxhbiwgdXBkYXRlUGxhbiwgZXhwb3J0UGxhbiB9IGZyb20gIkAvYXBpL2xlYXZlL3BsYW4iOw0KaW1wb3J0IEVkaXRvciBmcm9tICdAL2NvbXBvbmVudHMvRWRpdG9yJzsNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAiUGxhbiIsDQogIGNvbXBvbmVudHM6IHsNCiAgICBFZGl0b3IsDQogIH0sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIC8vIOmBrue9qeWxgg0KICAgICAgbG9hZGluZzogdHJ1ZSwNCiAgICAgIC8vIOmAieS4reaVsOe7hA0KICAgICAgaWRzOiBbXSwNCiAgICAgIC8vIOmdnuWNleS4quemgeeUqA0KICAgICAgc2luZ2xlOiB0cnVlLA0KICAgICAgLy8g6Z2e5aSa5Liq56aB55SoDQogICAgICBtdWx0aXBsZTogdHJ1ZSwNCiAgICAgIC8vIOaYvuekuuaQnOe0ouadoeS7tg0KICAgICAgc2hvd1NlYXJjaDogdHJ1ZSwNCiAgICAgIC8vIOaAu+adoeaVsA0KICAgICAgdG90YWw6IDAsDQogICAgICAvLyDlh7rpl6jor4HorqHliJLnlLPor7fooajmoLzmlbDmja4NCiAgICAgIHBsYW5MaXN0OiBbXSwNCiAgICAgIC8vIOW8ueWHuuWxguagh+mimA0KICAgICAgdGl0bGU6ICIiLA0KICAgICAgLy8g5piv5ZCm5pi+56S65by55Ye65bGCDQogICAgICBvcGVuOiBmYWxzZSwNCiAgICAgIC8vIOafpeivouWPguaVsA0KICAgICAgcXVlcnlQYXJhbXM6IHsNCiAgICAgICAgcGFnZU51bTogMSwNCiAgICAgICAgcGFnZVNpemU6IDEwLA0KICAgICAgICBwbGFuTm86IG51bGwsDQogICAgICAgIHBsYW5UeXBlOiBudWxsLA0KICAgICAgICBidXNpbmVzc0NhdGVnb3J5OiBudWxsLA0KICAgICAgICBtZWFzdXJlRmxhZzogbnVsbCwNCiAgICAgICAgcGxhbm5lZEFtb3VudDogbnVsbCwNCiAgICAgICAgcmVjZWl2ZUNvbXBhbnk6IG51bGwsDQogICAgICAgIHJlY2VpdmVDb21wYW55Q29kZTogbnVsbCwNCiAgICAgICAgdGFyZ2V0Q29tcGFueTogbnVsbCwNCiAgICAgICAgdGFyZ2V0Q29tcGFueUNvZGU6IG51bGwsDQogICAgICAgIHNvdXJjZUNvbXBhbnk6IG51bGwsDQogICAgICAgIHNvdXJjZUNvbXBhbnlDb2RlOiBudWxsLA0KICAgICAgICBwbGFuUmV0dXJuVGltZTogbnVsbCwNCiAgICAgICAgcmVhbFJldHVyblRpbWU6IG51bGwsDQogICAgICAgIG1vbml0b3I6IG51bGwsDQogICAgICAgIHNwZWNpYWxNYW5hZ2VyOiBudWxsLA0KICAgICAgICBleHBpcmVUaW1lOiBudWxsLA0KICAgICAgICByZWFzb246IG51bGwsDQogICAgICAgIGl0ZW1UeXBlOiBudWxsLA0KICAgICAgICBwbGFuU3RhdHVzOiBudWxsLA0KICAgICAgICBhcHBseVRpbWU6IG51bGwsDQogICAgICAgIGFwcGx5V29ya05vOiBudWxsLA0KICAgICAgICBmYWN0b3J5QXBwcm92ZVRpbWU6IG51bGwsDQogICAgICAgIGZhY3RvcnlBcHByb3ZlV29ya05vOiBudWxsLA0KICAgICAgICBmYWN0b3J5QXBwcm92ZUZsYWc6IG51bGwsDQogICAgICAgIGZhY3RvcnlBcHByb3ZlQ29udGVudDogbnVsbCwNCiAgICAgICAgZmFjdG9yeVNlY0FwcHJvdmVGbGFnOiBudWxsLA0KICAgICAgICBmYWN0b3J5U2VjQXBwcm92ZVRpbWU6IG51bGwsDQogICAgICAgIGZhY3RvcnlTZWNBcHByb3ZlV29ya05vOiBudWxsLA0KICAgICAgICBmYWN0b3J5U2VjQXBwcm92ZUNvbnRlbnQ6IG51bGwsDQogICAgICAgIGNlbnRlckFwcHJvdmVUaW1lOiBudWxsLA0KICAgICAgICBjZW50ZXJBcHByb3ZlV29ya05vOiBudWxsLA0KICAgICAgICBjZW50ZXJBcHByb3ZlRmxhZzogbnVsbCwNCiAgICAgICAgY2VudGVyQXBwcm92ZUNvbnRlbnQ6IG51bGwsDQogICAgICAgIGFwcGx5RmlsZVVybDogbnVsbCwNCiAgICAgIH0sDQogICAgICAvLyDooajljZXlj4LmlbANCiAgICAgIGZvcm06IHt9LA0KICAgICAgLy8g6KGo5Y2V5qCh6aqMDQogICAgICBydWxlczogew0KICAgICAgfQ0KICAgIH07DQogIH0sDQogIGNyZWF0ZWQoKSB7DQogICAgdGhpcy5nZXRMaXN0KCk7DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICAvKiog5p+l6K+i5Ye66Zeo6K+B6K6h5YiS55Sz6K+35YiX6KGoICovDQogICAgZ2V0TGlzdCgpIHsNCiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7DQogICAgICBsaXN0UGxhbih0aGlzLnF1ZXJ5UGFyYW1zKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy5wbGFuTGlzdCA9IHJlc3BvbnNlLnJvd3M7DQogICAgICAgIHRoaXMudG90YWwgPSByZXNwb25zZS50b3RhbDsNCiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8vIOWPlua2iOaMiemSrg0KICAgIGNhbmNlbCgpIHsNCiAgICAgIHRoaXMub3BlbiA9IGZhbHNlOw0KICAgICAgdGhpcy5yZXNldCgpOw0KICAgIH0sDQogICAgLy8g6KGo5Y2V6YeN572uDQogICAgcmVzZXQoKSB7DQogICAgICB0aGlzLmZvcm0gPSB7DQogICAgICAgIGlkOiBudWxsLA0KICAgICAgICBhcHBseU5vOiBudWxsLA0KICAgICAgICBwbGFuTm86IG51bGwsDQogICAgICAgIHBsYW5UeXBlOiBudWxsLA0KICAgICAgICBidXNpbmVzc0NhdGVnb3J5OiBudWxsLA0KICAgICAgICBtZWFzdXJlRmxhZzogbnVsbCwNCiAgICAgICAgcGxhbm5lZEFtb3VudDogbnVsbCwNCiAgICAgICAgcmVjZWl2ZUNvbXBhbnk6IG51bGwsDQogICAgICAgIHJlY2VpdmVDb21wYW55Q29kZTogbnVsbCwNCiAgICAgICAgdGFyZ2V0Q29tcGFueTogbnVsbCwNCiAgICAgICAgdGFyZ2V0Q29tcGFueUNvZGU6IG51bGwsDQogICAgICAgIHNvdXJjZUNvbXBhbnk6IG51bGwsDQogICAgICAgIHNvdXJjZUNvbXBhbnlDb2RlOiBudWxsLA0KICAgICAgICBwbGFuUmV0dXJuVGltZTogbnVsbCwNCiAgICAgICAgcmVhbFJldHVyblRpbWU6IG51bGwsDQogICAgICAgIG1vbml0b3I6IG51bGwsDQogICAgICAgIHNwZWNpYWxNYW5hZ2VyOiBudWxsLA0KICAgICAgICBleHBpcmVUaW1lOiBudWxsLA0KICAgICAgICByZWFzb246IG51bGwsDQogICAgICAgIGl0ZW1UeXBlOiBudWxsLA0KICAgICAgICBwbGFuU3RhdHVzOiAwLA0KICAgICAgICBhcHBseVRpbWU6IG51bGwsDQogICAgICAgIGFwcGx5V29ya05vOiBudWxsLA0KICAgICAgICBmYWN0b3J5QXBwcm92ZVRpbWU6IG51bGwsDQogICAgICAgIGZhY3RvcnlBcHByb3ZlV29ya05vOiBudWxsLA0KICAgICAgICBmYWN0b3J5QXBwcm92ZUZsYWc6IG51bGwsDQogICAgICAgIGZhY3RvcnlBcHByb3ZlQ29udGVudDogbnVsbCwNCiAgICAgICAgZmFjdG9yeVNlY0FwcHJvdmVGbGFnOiBudWxsLA0KICAgICAgICBmYWN0b3J5U2VjQXBwcm92ZVRpbWU6IG51bGwsDQogICAgICAgIGZhY3RvcnlTZWNBcHByb3ZlV29ya05vOiBudWxsLA0KICAgICAgICBmYWN0b3J5U2VjQXBwcm92ZUNvbnRlbnQ6IG51bGwsDQogICAgICAgIGNlbnRlckFwcHJvdmVUaW1lOiBudWxsLA0KICAgICAgICBjZW50ZXJBcHByb3ZlV29ya05vOiBudWxsLA0KICAgICAgICBjZW50ZXJBcHByb3ZlRmxhZzogbnVsbCwNCiAgICAgICAgY2VudGVyQXBwcm92ZUNvbnRlbnQ6IG51bGwsDQogICAgICAgIGFwcGx5RmlsZVVybDogbnVsbCwNCiAgICAgICAgcmVtYXJrOiBudWxsLA0KICAgICAgICBjcmVhdGVUaW1lOiBudWxsLA0KICAgICAgICBjcmVhdGVCeTogbnVsbCwNCiAgICAgICAgdXBkYXRlVGltZTogbnVsbCwNCiAgICAgICAgdXBkYXRlQnk6IG51bGwNCiAgICAgIH07DQogICAgICB0aGlzLnJlc2V0Rm9ybSgiZm9ybSIpOw0KICAgIH0sDQogICAgLyoqIOaQnOe0ouaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZVF1ZXJ5KCkgew0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMTsNCiAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgIH0sDQogICAgLyoqIOmHjee9ruaMiemSruaTjeS9nCAqLw0KICAgIHJlc2V0UXVlcnkoKSB7DQogICAgICB0aGlzLnJlc2V0Rm9ybSgicXVlcnlGb3JtIik7DQogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCk7DQogICAgfSwNCiAgICAvLyDlpJrpgInmoYbpgInkuK3mlbDmja4NCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7DQogICAgICB0aGlzLmlkcyA9IHNlbGVjdGlvbi5tYXAoaXRlbSA9PiBpdGVtLmlkKQ0KICAgICAgdGhpcy5zaW5nbGUgPSBzZWxlY3Rpb24ubGVuZ3RoIT09MQ0KICAgICAgdGhpcy5tdWx0aXBsZSA9ICFzZWxlY3Rpb24ubGVuZ3RoDQogICAgfSwNCiAgICAvKiog5paw5aKe5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlQWRkKCkgew0KICAgICAgdGhpcy5yZXNldCgpOw0KICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsNCiAgICAgIHRoaXMudGl0bGUgPSAi5re75Yqg5Ye66Zeo6K+B6K6h5YiS55Sz6K+3IjsNCiAgICB9LA0KICAgIC8qKiDkv67mlLnmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVVcGRhdGUocm93KSB7DQogICAgICB0aGlzLnJlc2V0KCk7DQogICAgICBjb25zdCBpZCA9IHJvdy5pZCB8fCB0aGlzLmlkcw0KICAgICAgZ2V0UGxhbihpZCkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMuZm9ybSA9IHJlc3BvbnNlLmRhdGE7DQogICAgICAgIHRoaXMub3BlbiA9IHRydWU7DQogICAgICAgIHRoaXMudGl0bGUgPSAi5L+u5pS55Ye66Zeo6K+B6K6h5YiS55Sz6K+3IjsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOaPkOS6pOaMiemSriAqLw0KICAgIHN1Ym1pdEZvcm0oKSB7DQogICAgICB0aGlzLiRyZWZzWyJmb3JtIl0udmFsaWRhdGUodmFsaWQgPT4gew0KICAgICAgICBpZiAodmFsaWQpIHsNCiAgICAgICAgICBpZiAodGhpcy5mb3JtLmlkICE9IG51bGwpIHsNCiAgICAgICAgICAgIHVwZGF0ZVBsYW4odGhpcy5mb3JtKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgICAgICAgdGhpcy5tc2dTdWNjZXNzKCLkv67mlLnmiJDlip8iKTsNCiAgICAgICAgICAgICAgdGhpcy5vcGVuID0gZmFsc2U7DQogICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIGFkZFBsYW4odGhpcy5mb3JtKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgICAgICAgdGhpcy5tc2dTdWNjZXNzKCLmlrDlop7miJDlip8iKTsNCiAgICAgICAgICAgICAgdGhpcy5vcGVuID0gZmFsc2U7DQogICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8qKiDliKDpmaTmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVEZWxldGUocm93KSB7DQogICAgICBjb25zdCBpZHMgPSByb3cuaWQgfHwgdGhpcy5pZHM7DQogICAgICB0aGlzLiRjb25maXJtKCfmmK/lkKbnoa7orqTliKDpmaTlh7rpl6jor4HorqHliJLnlLPor7fnvJblj7fkuLoiJyArIGlkcyArICci55qE5pWw5o2u6aG5PycsICLorablkYoiLCB7DQogICAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICLnoa7lrpoiLA0KICAgICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICLlj5bmtogiLA0KICAgICAgICAgIHR5cGU6ICJ3YXJuaW5nIg0KICAgICAgICB9KS50aGVuKGZ1bmN0aW9uKCkgew0KICAgICAgICAgIHJldHVybiBkZWxQbGFuKGlkcyk7DQogICAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICAgIHRoaXMubXNnU3VjY2Vzcygi5Yig6Zmk5oiQ5YqfIik7DQogICAgICAgIH0pDQogICAgfSwNCiAgICAvKiog5a+85Ye65oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlRXhwb3J0KCkgew0KICAgICAgY29uc3QgcXVlcnlQYXJhbXMgPSB0aGlzLnF1ZXJ5UGFyYW1zOw0KICAgICAgdGhpcy4kY29uZmlybSgn5piv5ZCm56Gu6K6k5a+85Ye65omA5pyJ5Ye66Zeo6K+B6K6h5YiS55Sz6K+35pWw5o2u6aG5PycsICLorablkYoiLCB7DQogICAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICLnoa7lrpoiLA0KICAgICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICLlj5bmtogiLA0KICAgICAgICAgIHR5cGU6ICJ3YXJuaW5nIg0KICAgICAgICB9KS50aGVuKGZ1bmN0aW9uKCkgew0KICAgICAgICAgIHJldHVybiBleHBvcnRQbGFuKHF1ZXJ5UGFyYW1zKTsNCiAgICAgICAgfSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgdGhpcy5kb3dubG9hZChyZXNwb25zZS5tc2cpOw0KICAgICAgICB9KQ0KICAgIH0NCiAgfQ0KfTsNCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4iBA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/leave/plan", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n      <el-form-item label=\"计划号\" prop=\"planNo\">\r\n        <el-input\r\n          v-model=\"queryParams.planNo\"\r\n          placeholder=\"请输入计划号\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"计划类型 1-出厂不返回 2-出厂返回 3-跨区调拨 4-退货申请\" prop=\"planType\">\r\n        <el-select v-model=\"queryParams.planType\" placeholder=\"请选择计划类型 1-出厂不返回 2-出厂返回 3-跨区调拨 4-退货申请\" clearable size=\"small\">\r\n          <el-option label=\"请选择字典生成\" value=\"\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"业务类型\r\n1-通用\" prop=\"businessCategory\">\r\n        <el-input\r\n          v-model=\"queryParams.businessCategory\"\r\n          placeholder=\"请输入业务类型\r\n1-通用\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"是否计量 计量-1 不计量-0\" prop=\"measureFlag\">\r\n        <el-input\r\n          v-model=\"queryParams.measureFlag\"\r\n          placeholder=\"请输入是否计量 计量-1 不计量-0\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"计划量 计划量计量时才存在\" prop=\"plannedAmount\">\r\n        <el-input\r\n          v-model=\"queryParams.plannedAmount\"\r\n          placeholder=\"请输入计划量 计划量计量时才存在\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"收货单位\" prop=\"receiveCompany\">\r\n        <el-input\r\n          v-model=\"queryParams.receiveCompany\"\r\n          placeholder=\"请输入收货单位\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"收货单位code\" prop=\"receiveCompanyCode\">\r\n        <el-input\r\n          v-model=\"queryParams.receiveCompanyCode\"\r\n          placeholder=\"请输入收货单位code\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"返回单位\" prop=\"targetCompany\">\r\n        <el-input\r\n          v-model=\"queryParams.targetCompany\"\r\n          placeholder=\"请输入返回单位\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"返回单位code\" prop=\"targetCompanyCode\">\r\n        <el-input\r\n          v-model=\"queryParams.targetCompanyCode\"\r\n          placeholder=\"请输入返回单位code\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"申请单位\" prop=\"sourceCompany\">\r\n        <el-input\r\n          v-model=\"queryParams.sourceCompany\"\r\n          placeholder=\"请输入申请单位\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"申请单位code\" prop=\"sourceCompanyCode\">\r\n        <el-input\r\n          v-model=\"queryParams.sourceCompanyCode\"\r\n          placeholder=\"请输入申请单位code\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"计划返回时间\" prop=\"planReturnTime\">\r\n        <el-date-picker clearable size=\"small\" style=\"width: 200px\"\r\n          v-model=\"queryParams.planReturnTime\"\r\n          type=\"date\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          placeholder=\"选择计划返回时间\">\r\n        </el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item label=\"实际返回时间  多次返厂情况下填最新一次\" prop=\"realReturnTime\">\r\n        <el-date-picker clearable size=\"small\" style=\"width: 200px\"\r\n          v-model=\"queryParams.realReturnTime\"\r\n          type=\"date\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          placeholder=\"选择实际返回时间  多次返厂情况下填最新一次\">\r\n        </el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item label=\"监装人\" prop=\"monitor\">\r\n        <el-input\r\n          v-model=\"queryParams.monitor\"\r\n          placeholder=\"请输入监装人\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"物资专管员\" prop=\"specialManager\">\r\n        <el-input\r\n          v-model=\"queryParams.specialManager\"\r\n          placeholder=\"请输入物资专管员\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"申请有效期\" prop=\"expireTime\">\r\n        <el-date-picker clearable size=\"small\" style=\"width: 200px\"\r\n          v-model=\"queryParams.expireTime\"\r\n          type=\"date\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          placeholder=\"选择申请有效期\">\r\n        </el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item label=\"物资类型 1-钢材 2-钢板 3-其他\" prop=\"itemType\">\r\n        <el-select v-model=\"queryParams.itemType\" placeholder=\"请选择物资类型 1-钢材 2-钢板 3-其他\" clearable size=\"small\">\r\n          <el-option label=\"请选择字典生成\" value=\"\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"计划状态 1-待分厂审批 2-待分厂复审 3-待生产指挥中心审批 4-审批完成  5-已出厂 6-部分收货 7-已完成\r\n11-驳回 12-废弃 13-过期\" prop=\"planStatus\">\r\n        <el-select v-model=\"queryParams.planStatus\" placeholder=\"请选择计划状态 1-待分厂审批 2-待分厂复审 3-待生产指挥中心审批 4-审批完成  5-已出厂 6-部分收货 7-已完成\r\n11-驳回 12-废弃 13-过期\" clearable size=\"small\">\r\n          <el-option label=\"请选择字典生成\" value=\"\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"申请时间\" prop=\"applyTime\">\r\n        <el-date-picker clearable size=\"small\" style=\"width: 200px\"\r\n          v-model=\"queryParams.applyTime\"\r\n          type=\"date\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          placeholder=\"选择申请时间\">\r\n        </el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item label=\"申请人\" prop=\"applyWorkNo\">\r\n        <el-input\r\n          v-model=\"queryParams.applyWorkNo\"\r\n          placeholder=\"请输入申请人\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"分厂领导审批时间\" prop=\"factoryApproveTime\">\r\n        <el-date-picker clearable size=\"small\" style=\"width: 200px\"\r\n          v-model=\"queryParams.factoryApproveTime\"\r\n          type=\"date\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          placeholder=\"选择分厂领导审批时间\">\r\n        </el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item label=\"分厂领导工号\" prop=\"factoryApproveWorkNo\">\r\n        <el-input\r\n          v-model=\"queryParams.factoryApproveWorkNo\"\r\n          placeholder=\"请输入分厂领导工号\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"分厂审核结果 0-拒绝 1-同意\" prop=\"factoryApproveFlag\">\r\n        <el-input\r\n          v-model=\"queryParams.factoryApproveFlag\"\r\n          placeholder=\"请输入分厂审核结果 0-拒绝 1-同意\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"分厂复审结果 0-拒绝 1-同意\" prop=\"factorySecApproveFlag\">\r\n        <el-input\r\n          v-model=\"queryParams.factorySecApproveFlag\"\r\n          placeholder=\"请输入分厂复审结果 0-拒绝 1-同意\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"分厂复审时间\" prop=\"factorySecApproveTime\">\r\n        <el-date-picker clearable size=\"small\" style=\"width: 200px\"\r\n          v-model=\"queryParams.factorySecApproveTime\"\r\n          type=\"date\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          placeholder=\"选择分厂复审时间\">\r\n        </el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item label=\"分厂复审审批人工号\" prop=\"factorySecApproveWorkNo\">\r\n        <el-input\r\n          v-model=\"queryParams.factorySecApproveWorkNo\"\r\n          placeholder=\"请输入分厂复审审批人工号\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"生产指挥中心审批时间\" prop=\"centerApproveTime\">\r\n        <el-date-picker clearable size=\"small\" style=\"width: 200px\"\r\n          v-model=\"queryParams.centerApproveTime\"\r\n          type=\"date\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          placeholder=\"选择生产指挥中心审批时间\">\r\n        </el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item label=\"生产指挥中心审批人工号\" prop=\"centerApproveWorkNo\">\r\n        <el-input\r\n          v-model=\"queryParams.centerApproveWorkNo\"\r\n          placeholder=\"请输入生产指挥中心审批人工号\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"生产指挥中心审核结果 0-拒绝 1-同意\" prop=\"centerApproveFlag\">\r\n        <el-input\r\n          v-model=\"queryParams.centerApproveFlag\"\r\n          placeholder=\"请输入生产指挥中心审核结果 0-拒绝 1-同意\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"cyan\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          icon=\"el-icon-plus\"\r\n          size=\"mini\"\r\n          @click=\"handleAdd\"\r\n          v-hasPermi=\"['leave:plan:add']\"\r\n        >新增</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"success\"\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"single\"\r\n          @click=\"handleUpdate\"\r\n          v-hasPermi=\"['leave:plan:edit']\"\r\n        >修改</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n          v-hasPermi=\"['leave:plan:remove']\"\r\n        >删除</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n          v-hasPermi=\"['leave:plan:export']\"\r\n        >导出</el-button>\r\n      </el-col>\r\n\t  <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table v-loading=\"loading\" :data=\"planList\" @selection-change=\"handleSelectionChange\">\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column label=\"主键\" align=\"center\" prop=\"id\" />\r\n      <el-table-column label=\"申请编号\" align=\"center\" prop=\"applyNo\" />\r\n      <el-table-column label=\"计划号\" align=\"center\" prop=\"planNo\" />\r\n      <el-table-column label=\"计划类型 1-出厂不返回 2-出厂返回 3-跨区调拨 4-退货申请\" align=\"center\" prop=\"planType\" />\r\n      <el-table-column label=\"业务类型\r\n1-通用\" align=\"center\" prop=\"businessCategory\" />\r\n      <el-table-column label=\"是否计量 计量-1 不计量-0\" align=\"center\" prop=\"measureFlag\" />\r\n      <el-table-column label=\"计划量 计划量计量时才存在\" align=\"center\" prop=\"plannedAmount\" />\r\n      <el-table-column label=\"收货单位\" align=\"center\" prop=\"receiveCompany\" />\r\n      <el-table-column label=\"收货单位code\" align=\"center\" prop=\"receiveCompanyCode\" />\r\n      <el-table-column label=\"返回单位\" align=\"center\" prop=\"targetCompany\" />\r\n      <el-table-column label=\"返回单位code\" align=\"center\" prop=\"targetCompanyCode\" />\r\n      <el-table-column label=\"申请单位\" align=\"center\" prop=\"sourceCompany\" />\r\n      <el-table-column label=\"申请单位code\" align=\"center\" prop=\"sourceCompanyCode\" />\r\n      <el-table-column label=\"计划返回时间\" align=\"center\" prop=\"planReturnTime\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.planReturnTime, '{y}-{m}-{d}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"实际返回时间  多次返厂情况下填最新一次\" align=\"center\" prop=\"realReturnTime\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.realReturnTime, '{y}-{m}-{d}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"监装人\" align=\"center\" prop=\"monitor\" />\r\n      <el-table-column label=\"物资专管员\" align=\"center\" prop=\"specialManager\" />\r\n      <el-table-column label=\"申请有效期\" align=\"center\" prop=\"expireTime\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.expireTime, '{y}-{m}-{d}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"出厂原因\" align=\"center\" prop=\"reason\" />\r\n      <el-table-column label=\"物资类型 1-钢材 2-钢板 3-其他\" align=\"center\" prop=\"itemType\" />\r\n      <el-table-column label=\"计划状态 1-待分厂审批 2-待分厂复审 3-待生产指挥中心审批 4-审批完成  5-已出厂 6-部分收货 7-已完成\r\n11-驳回 12-废弃 13-过期\" align=\"center\" prop=\"planStatus\" />\r\n      <el-table-column label=\"申请时间\" align=\"center\" prop=\"applyTime\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.applyTime, '{y}-{m}-{d}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"申请人\" align=\"center\" prop=\"applyWorkNo\" />\r\n      <el-table-column label=\"分厂领导审批时间\" align=\"center\" prop=\"factoryApproveTime\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.factoryApproveTime, '{y}-{m}-{d}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"分厂领导工号\" align=\"center\" prop=\"factoryApproveWorkNo\" />\r\n      <el-table-column label=\"分厂审核结果 0-拒绝 1-同意\" align=\"center\" prop=\"factoryApproveFlag\" />\r\n      <el-table-column label=\"分厂领导审核意见\" align=\"center\" prop=\"factoryApproveContent\" />\r\n      <el-table-column label=\"分厂复审结果 0-拒绝 1-同意\" align=\"center\" prop=\"factorySecApproveFlag\" />\r\n      <el-table-column label=\"分厂复审时间\" align=\"center\" prop=\"factorySecApproveTime\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.factorySecApproveTime, '{y}-{m}-{d}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"分厂复审审批人工号\" align=\"center\" prop=\"factorySecApproveWorkNo\" />\r\n      <el-table-column label=\"分厂复审审核意见\" align=\"center\" prop=\"factorySecApproveContent\" />\r\n      <el-table-column label=\"生产指挥中心审批时间\" align=\"center\" prop=\"centerApproveTime\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.centerApproveTime, '{y}-{m}-{d}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"生产指挥中心审批人工号\" align=\"center\" prop=\"centerApproveWorkNo\" />\r\n      <el-table-column label=\"生产指挥中心审核结果 0-拒绝 1-同意\" align=\"center\" prop=\"centerApproveFlag\" />\r\n      <el-table-column label=\"生产指挥中心审核意见\" align=\"center\" prop=\"centerApproveContent\" />\r\n      <el-table-column label=\"申请文件，允许多个\" align=\"center\" prop=\"applyFileUrl\" />\r\n      <el-table-column label=\"备注\" align=\"center\" prop=\"remark\" />\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleUpdate(scope.row)\"\r\n            v-hasPermi=\"['leave:plan:edit']\"\r\n          >修改</el-button>\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleDelete(scope.row)\"\r\n            v-hasPermi=\"['leave:plan:remove']\"\r\n          >删除</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n    \r\n    <pagination\r\n      v-show=\"total>0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 添加或修改出门证计划申请对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"500px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\r\n        <el-form-item label=\"计划号\" prop=\"planNo\">\r\n          <el-input v-model=\"form.planNo\" placeholder=\"请输入计划号\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"计划类型 1-出厂不返回 2-出厂返回 3-跨区调拨 4-退货申请\" prop=\"planType\">\r\n          <el-select v-model=\"form.planType\" placeholder=\"请选择计划类型 1-出厂不返回 2-出厂返回 3-跨区调拨 4-退货申请\">\r\n            <el-option label=\"请选择字典生成\" value=\"\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"业务类型\r\n1-通用\" prop=\"businessCategory\">\r\n          <el-input v-model=\"form.businessCategory\" placeholder=\"请输入业务类型\r\n1-通用\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"是否计量 计量-1 不计量-0\" prop=\"measureFlag\">\r\n          <el-input v-model=\"form.measureFlag\" placeholder=\"请输入是否计量 计量-1 不计量-0\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"计划量 计划量计量时才存在\" prop=\"plannedAmount\">\r\n          <el-input v-model=\"form.plannedAmount\" placeholder=\"请输入计划量 计划量计量时才存在\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"收货单位\" prop=\"receiveCompany\">\r\n          <el-input v-model=\"form.receiveCompany\" placeholder=\"请输入收货单位\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"收货单位code\" prop=\"receiveCompanyCode\">\r\n          <el-input v-model=\"form.receiveCompanyCode\" placeholder=\"请输入收货单位code\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"返回单位\" prop=\"targetCompany\">\r\n          <el-input v-model=\"form.targetCompany\" placeholder=\"请输入返回单位\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"返回单位code\" prop=\"targetCompanyCode\">\r\n          <el-input v-model=\"form.targetCompanyCode\" placeholder=\"请输入返回单位code\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"申请单位\" prop=\"sourceCompany\">\r\n          <el-input v-model=\"form.sourceCompany\" placeholder=\"请输入申请单位\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"申请单位code\" prop=\"sourceCompanyCode\">\r\n          <el-input v-model=\"form.sourceCompanyCode\" placeholder=\"请输入申请单位code\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"计划返回时间\" prop=\"planReturnTime\">\r\n          <el-date-picker clearable size=\"small\" style=\"width: 200px\"\r\n            v-model=\"form.planReturnTime\"\r\n            type=\"date\"\r\n            value-format=\"yyyy-MM-dd\"\r\n            placeholder=\"选择计划返回时间\">\r\n          </el-date-picker>\r\n        </el-form-item>\r\n        <el-form-item label=\"实际返回时间  多次返厂情况下填最新一次\" prop=\"realReturnTime\">\r\n          <el-date-picker clearable size=\"small\" style=\"width: 200px\"\r\n            v-model=\"form.realReturnTime\"\r\n            type=\"date\"\r\n            value-format=\"yyyy-MM-dd\"\r\n            placeholder=\"选择实际返回时间  多次返厂情况下填最新一次\">\r\n          </el-date-picker>\r\n        </el-form-item>\r\n        <el-form-item label=\"监装人\" prop=\"monitor\">\r\n          <el-input v-model=\"form.monitor\" placeholder=\"请输入监装人\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"物资专管员\" prop=\"specialManager\">\r\n          <el-input v-model=\"form.specialManager\" placeholder=\"请输入物资专管员\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"申请有效期\" prop=\"expireTime\">\r\n          <el-date-picker clearable size=\"small\" style=\"width: 200px\"\r\n            v-model=\"form.expireTime\"\r\n            type=\"date\"\r\n            value-format=\"yyyy-MM-dd\"\r\n            placeholder=\"选择申请有效期\">\r\n          </el-date-picker>\r\n        </el-form-item>\r\n        <el-form-item label=\"出厂原因\" prop=\"reason\">\r\n          <el-input v-model=\"form.reason\" type=\"textarea\" placeholder=\"请输入内容\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"物资类型 1-钢材 2-钢板 3-其他\" prop=\"itemType\">\r\n          <el-select v-model=\"form.itemType\" placeholder=\"请选择物资类型 1-钢材 2-钢板 3-其他\">\r\n            <el-option label=\"请选择字典生成\" value=\"\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"计划状态 1-待分厂审批 2-待分厂复审 3-待生产指挥中心审批 4-审批完成  5-已出厂 6-部分收货 7-已完成\r\n11-驳回 12-废弃 13-过期\">\r\n          <el-radio-group v-model=\"form.planStatus\">\r\n            <el-radio label=\"1\">请选择字典生成</el-radio>\r\n          </el-radio-group>\r\n        </el-form-item>\r\n        <el-form-item label=\"申请时间\" prop=\"applyTime\">\r\n          <el-date-picker clearable size=\"small\" style=\"width: 200px\"\r\n            v-model=\"form.applyTime\"\r\n            type=\"date\"\r\n            value-format=\"yyyy-MM-dd\"\r\n            placeholder=\"选择申请时间\">\r\n          </el-date-picker>\r\n        </el-form-item>\r\n        <el-form-item label=\"申请人\" prop=\"applyWorkNo\">\r\n          <el-input v-model=\"form.applyWorkNo\" placeholder=\"请输入申请人\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"分厂领导审批时间\" prop=\"factoryApproveTime\">\r\n          <el-date-picker clearable size=\"small\" style=\"width: 200px\"\r\n            v-model=\"form.factoryApproveTime\"\r\n            type=\"date\"\r\n            value-format=\"yyyy-MM-dd\"\r\n            placeholder=\"选择分厂领导审批时间\">\r\n          </el-date-picker>\r\n        </el-form-item>\r\n        <el-form-item label=\"分厂领导工号\" prop=\"factoryApproveWorkNo\">\r\n          <el-input v-model=\"form.factoryApproveWorkNo\" placeholder=\"请输入分厂领导工号\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"分厂审核结果 0-拒绝 1-同意\" prop=\"factoryApproveFlag\">\r\n          <el-input v-model=\"form.factoryApproveFlag\" placeholder=\"请输入分厂审核结果 0-拒绝 1-同意\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"分厂领导审核意见\">\r\n          <editor v-model=\"form.factoryApproveContent\" :min-height=\"192\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"分厂复审结果 0-拒绝 1-同意\" prop=\"factorySecApproveFlag\">\r\n          <el-input v-model=\"form.factorySecApproveFlag\" placeholder=\"请输入分厂复审结果 0-拒绝 1-同意\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"分厂复审时间\" prop=\"factorySecApproveTime\">\r\n          <el-date-picker clearable size=\"small\" style=\"width: 200px\"\r\n            v-model=\"form.factorySecApproveTime\"\r\n            type=\"date\"\r\n            value-format=\"yyyy-MM-dd\"\r\n            placeholder=\"选择分厂复审时间\">\r\n          </el-date-picker>\r\n        </el-form-item>\r\n        <el-form-item label=\"分厂复审审批人工号\" prop=\"factorySecApproveWorkNo\">\r\n          <el-input v-model=\"form.factorySecApproveWorkNo\" placeholder=\"请输入分厂复审审批人工号\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"分厂复审审核意见\">\r\n          <editor v-model=\"form.factorySecApproveContent\" :min-height=\"192\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"生产指挥中心审批时间\" prop=\"centerApproveTime\">\r\n          <el-date-picker clearable size=\"small\" style=\"width: 200px\"\r\n            v-model=\"form.centerApproveTime\"\r\n            type=\"date\"\r\n            value-format=\"yyyy-MM-dd\"\r\n            placeholder=\"选择生产指挥中心审批时间\">\r\n          </el-date-picker>\r\n        </el-form-item>\r\n        <el-form-item label=\"生产指挥中心审批人工号\" prop=\"centerApproveWorkNo\">\r\n          <el-input v-model=\"form.centerApproveWorkNo\" placeholder=\"请输入生产指挥中心审批人工号\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"生产指挥中心审核结果 0-拒绝 1-同意\" prop=\"centerApproveFlag\">\r\n          <el-input v-model=\"form.centerApproveFlag\" placeholder=\"请输入生产指挥中心审核结果 0-拒绝 1-同意\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"生产指挥中心审核意见\">\r\n          <editor v-model=\"form.centerApproveContent\" :min-height=\"192\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"申请文件，允许多个\" prop=\"applyFileUrl\">\r\n          <el-input v-model=\"form.applyFileUrl\" type=\"textarea\" placeholder=\"请输入内容\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\" prop=\"remark\">\r\n          <el-input v-model=\"form.remark\" type=\"textarea\" placeholder=\"请输入内容\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listPlan, getPlan, delPlan, addPlan, updatePlan, exportPlan } from \"@/api/leave/plan\";\r\nimport Editor from '@/components/Editor';\r\n\r\nexport default {\r\n  name: \"Plan\",\r\n  components: {\r\n    Editor,\r\n  },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 出门证计划申请表格数据\r\n      planList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        planNo: null,\r\n        planType: null,\r\n        businessCategory: null,\r\n        measureFlag: null,\r\n        plannedAmount: null,\r\n        receiveCompany: null,\r\n        receiveCompanyCode: null,\r\n        targetCompany: null,\r\n        targetCompanyCode: null,\r\n        sourceCompany: null,\r\n        sourceCompanyCode: null,\r\n        planReturnTime: null,\r\n        realReturnTime: null,\r\n        monitor: null,\r\n        specialManager: null,\r\n        expireTime: null,\r\n        reason: null,\r\n        itemType: null,\r\n        planStatus: null,\r\n        applyTime: null,\r\n        applyWorkNo: null,\r\n        factoryApproveTime: null,\r\n        factoryApproveWorkNo: null,\r\n        factoryApproveFlag: null,\r\n        factoryApproveContent: null,\r\n        factorySecApproveFlag: null,\r\n        factorySecApproveTime: null,\r\n        factorySecApproveWorkNo: null,\r\n        factorySecApproveContent: null,\r\n        centerApproveTime: null,\r\n        centerApproveWorkNo: null,\r\n        centerApproveFlag: null,\r\n        centerApproveContent: null,\r\n        applyFileUrl: null,\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    /** 查询出门证计划申请列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listPlan(this.queryParams).then(response => {\r\n        this.planList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        id: null,\r\n        applyNo: null,\r\n        planNo: null,\r\n        planType: null,\r\n        businessCategory: null,\r\n        measureFlag: null,\r\n        plannedAmount: null,\r\n        receiveCompany: null,\r\n        receiveCompanyCode: null,\r\n        targetCompany: null,\r\n        targetCompanyCode: null,\r\n        sourceCompany: null,\r\n        sourceCompanyCode: null,\r\n        planReturnTime: null,\r\n        realReturnTime: null,\r\n        monitor: null,\r\n        specialManager: null,\r\n        expireTime: null,\r\n        reason: null,\r\n        itemType: null,\r\n        planStatus: 0,\r\n        applyTime: null,\r\n        applyWorkNo: null,\r\n        factoryApproveTime: null,\r\n        factoryApproveWorkNo: null,\r\n        factoryApproveFlag: null,\r\n        factoryApproveContent: null,\r\n        factorySecApproveFlag: null,\r\n        factorySecApproveTime: null,\r\n        factorySecApproveWorkNo: null,\r\n        factorySecApproveContent: null,\r\n        centerApproveTime: null,\r\n        centerApproveWorkNo: null,\r\n        centerApproveFlag: null,\r\n        centerApproveContent: null,\r\n        applyFileUrl: null,\r\n        remark: null,\r\n        createTime: null,\r\n        createBy: null,\r\n        updateTime: null,\r\n        updateBy: null\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.id)\r\n      this.single = selection.length!==1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加出门证计划申请\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const id = row.id || this.ids\r\n      getPlan(id).then(response => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"修改出门证计划申请\";\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.id != null) {\r\n            updatePlan(this.form).then(response => {\r\n              this.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addPlan(this.form).then(response => {\r\n              this.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const ids = row.id || this.ids;\r\n      this.$confirm('是否确认删除出门证计划申请编号为\"' + ids + '\"的数据项?', \"警告\", {\r\n          confirmButtonText: \"确定\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\"\r\n        }).then(function() {\r\n          return delPlan(ids);\r\n        }).then(() => {\r\n          this.getList();\r\n          this.msgSuccess(\"删除成功\");\r\n        })\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      const queryParams = this.queryParams;\r\n      this.$confirm('是否确认导出所有出门证计划申请数据项?', \"警告\", {\r\n          confirmButtonText: \"确定\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\"\r\n        }).then(function() {\r\n          return exportPlan(queryParams);\r\n        }).then(response => {\r\n          this.download(response.msg);\r\n        })\r\n    }\r\n  }\r\n};\r\n</script>\r\n"]}]}