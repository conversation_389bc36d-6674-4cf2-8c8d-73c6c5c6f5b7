import request from '@/utils/request'

// 查询出门证部门（厂内单位）列表
export function listDepartment(query) {
  return request({
    url: '/leave/department/list',
    method: 'get',
    params: query
  })
}

// 查询出门证部门（厂内单位）详细
export function getDepartment(id) {
  return request({
    url: '/leave/department/' + id,
    method: 'get'
  })
}

// 新增出门证部门（厂内单位）
export function addDepartment(data) {
  return request({
    url: '/leave/department',
    method: 'post',
    data: data
  })
}

// 修改出门证部门（厂内单位）
export function updateDepartment(data) {
  return request({
    url: '/leave/department',
    method: 'put',
    data: data
  })
}

// 删除出门证部门（厂内单位）
export function delDepartment(id) {
  return request({
    url: '/leave/department/' + id,
    method: 'delete'
  })
}

// 导出出门证部门（厂内单位）
export function exportDepartment(query) {
  return request({
    url: '/leave/department/export',
    method: 'get',
    params: query
  })
}