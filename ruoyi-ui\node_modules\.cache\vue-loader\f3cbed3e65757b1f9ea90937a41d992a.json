{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\leave\\plan\\detail.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\leave\\plan\\detail.vue", "mtime": 1756084866758}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBkZXRhaWxQbGFuLCBhcHByb3ZlLCBkaXNjYXJkLCBsaXN0VGFza01hdGVyaWFsLCBjb25maXJtTWF0ZXJpYWwgfSBmcm9tICJAL2FwaS9sZWF2ZS9wbGFuIjsNCmltcG9ydCB7IGxpc3RUYXNrLCBhZGRUYXNrLCBhZGRUYXNrTWF0ZXJpYWwsIGFkZFRhc2tBbmRNYXRlcmlhbCwgYWRkTGVhdmVMb2csIGlzQWxsb3dEaXNwYXRjaCwgYWRkVGFza0FuZE1hdGVyaWFsQW5kQWRkTGVhdmVMb2cgfSBmcm9tICJAL2FwaS9sZWF2ZS90YXNrIjsNCmltcG9ydCB7IGxpc3RBbGxEcml2ZXIsIGdldFhjdGdEcml2ZXJVc2VyTGlzdCwgZ2V0WGN0Z0RyaXZlckNhckxpc3QgfSBmcm9tICJAL2FwaS9kZ2NiL2RyaXZlci9kcml2ZXIiOw0KaW1wb3J0IHsgbW91bnQgfSBmcm9tICJzb3J0YWJsZWpzIjsNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogIkRldGFpbExlYXZlUGxhbiIsDQogIGRhdGEoKSB7DQogICAgLy8g6aqM6K+B6L2m54mM5Y+3DQogICAgY29uc3QgdmFsaWRhdGVDYXJOdW1iZXIgPSAocnVsZSwgdmFsdWUsIGNhbGxiYWNrKSA9PiB7DQogICAgICBjb25zdCBwYXR0ZXJuID0gL15b5Lqs5rSl5rKq5rid5YaA6LGr5LqR6L696buR5rmY55qW6bKB5paw6IuP5rWZ6LWj6YSC5qGC55SY5pmL6JKZ6ZmV5ZCJ6Ze96LS157Kk6Z2S6JeP5bed5a6B55C85L2/6aKGQS1aXXsxfVtBLVpdezF9W0EtWjAtOV17NH1bQS1aMC055oyC5a2m6K2m5riv5r6zXXsxfSQvOw0KICAgICAgaWYgKCFwYXR0ZXJuLnRlc3QodmFsdWUpKSB7DQogICAgICAgIGNhbGxiYWNrKG5ldyBFcnJvcign6K+36L6T5YWl5q2j56Gu55qE6L2m54mM5Y+3JykpOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgY2FsbGJhY2soKTsNCiAgICAgIH0NCiAgICB9Ow0KDQogICAgLy8g6aqM6K+B5omL5py65Y+3DQogICAgY29uc3QgdmFsaWRhdGVQaG9uZSA9IChydWxlLCB2YWx1ZSwgY2FsbGJhY2spID0+IHsNCiAgICAgIGNvbnN0IHBhdHRlcm4gPSAvXjFbMy05XVxkezl9JC87DQogICAgICBpZiAoIXBhdHRlcm4udGVzdCh2YWx1ZSkpIHsNCiAgICAgICAgY2FsbGJhY2sobmV3IEVycm9yKCfor7fovpPlhaXmraPnoa7nmoTmiYvmnLrlj7cnKSk7DQogICAgICB9IGVsc2Ugew0KICAgICAgICBjYWxsYmFjaygpOw0KICAgICAgfQ0KICAgIH07DQoNCiAgICAvLyDpqozor4Houqvku73or4Hlj7cNCiAgICBjb25zdCB2YWxpZGF0ZUlkQ2FyZCA9IChydWxlLCB2YWx1ZSwgY2FsbGJhY2spID0+IHsNCiAgICAgIGNvbnN0IHBhdHRlcm4gPSAvKF5cZHsxNX0kKXwoXlxkezE4fSQpfCheXGR7MTd9KFxkfFh8eCkkKS87DQogICAgICBpZiAoIXBhdHRlcm4udGVzdCh2YWx1ZSkpIHsNCiAgICAgICAgY2FsbGJhY2sobmV3IEVycm9yKCfor7fovpPlhaXmraPnoa7nmoTouqvku73or4Hlj7cnKSk7DQogICAgICB9IGVsc2Ugew0KICAgICAgICBjYWxsYmFjaygpOw0KICAgICAgfQ0KICAgIH07DQoNCiAgICByZXR1cm4gew0KICAgICAgaXNUYXNrVHlwZUVkaXQ6IHRydWUsDQogICAgICB2ZWhpY2xlRW1pc3Npb25TdGFuZGFyZHNPcHRpb25zOiBbXSwNCiAgICAgIHRhc2tUeXBlT3B0aW9uczogW10sDQogICAgICBjYXJMaXN0OiBbXSwNCiAgICAgIHNlYXJjaENhclF1ZXJ5OiAnJywNCiAgICAgIGZpbHRlcmVkQ2FyT3B0aW9uczogW10sDQogICAgICBkcml2ZXJMaXN0OiBbXSwNCiAgICAgIHNlYXJjaERyaXZlclF1ZXJ5OiAnJywNCiAgICAgIGZpbHRlcmVkRHJpdmVyT3B0aW9uczogW10sDQogICAgICAvL+WuoeaguOihqOWNlQ0KICAgICAgYXBwcm92ZUZvcm06IHsNCiAgICAgICAgYXBwbHlObzogbnVsbCwNCiAgICAgICAgYXBwcm92ZUNvbnRlbnQ6ICcnLC8v5a6h5qC45oSP6KeBDQogICAgICAgIGFwcHJvdmVGbGFnOiB0cnVlLC8v5a6h5qC454q25oCBDQogICAgICB9LA0KDQogICAgICAvLyDlm77niYfliJfooagNCiAgICAgIGltYWdlTGlzdDogW10sDQoNCiAgICAgIC8vIOaWh+S7tuWIl+ihqA0KICAgICAgZmlsZUxpc3Q6IFtdLA0KDQogICAgICAvLyDmtL7ovablvLnmoYblj6/op4HmgKcNCiAgICAgIGRpc3BhdGNoRGlhbG9nVmlzaWJsZTogZmFsc2UsDQoNCiAgICAgIHRhc2tMaXN0SW5mbzogW10sDQoNCiAgICAgIC8vIOa0vui9puihqOWNleaVsOaNrg0KICAgICAgZGlzcGF0Y2hGb3JtOiB7DQogICAgICAgIC8vIGNhck51bWJlcjogJycsDQogICAgICAgIC8vIGRyaXZlck5hbWU6ICcnLA0KICAgICAgICAvLyBkcml2ZXJQaG9uZTogJycsDQogICAgICAgIC8vIGRyaXZlcklkQ2FyZDogJycNCiAgICAgIH0sDQoNCiAgICAgIC8vIOa0vui9puihqOWNlemqjOivgeinhOWImQ0KICAgICAgZGlzcGF0Y2hSdWxlczogew0KICAgICAgICBjYXJOdW1iZXI6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36L6T5YWl6L2m54mM5Y+3JywgdHJpZ2dlcjogJ2JsdXInIH0sDQogICAgICAgICAgeyB2YWxpZGF0b3I6IHZhbGlkYXRlQ2FyTnVtYmVyLCB0cmlnZ2VyOiAnYmx1cicgfQ0KICAgICAgICBdLA0KICAgICAgICBkcml2ZXJOYW1lOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+i+k+WFpeWPuOacuuWnk+WQjScsIHRyaWdnZXI6ICdibHVyJyB9LA0KICAgICAgICAgIHsgbWluOiAyLCBtYXg6IDIwLCBtZXNzYWdlOiAn6ZW/5bqm5ZyoIDIg5YiwIDIwIOS4quWtl+espicsIHRyaWdnZXI6ICdibHVyJyB9DQogICAgICAgIF0sDQogICAgICAgIGRyaXZlclBob25lOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+i+k+WFpeWPuOacuuaJi+acuuWPtycsIHRyaWdnZXI6ICdibHVyJyB9LA0KICAgICAgICAgIHsgdmFsaWRhdG9yOiB2YWxpZGF0ZVBob25lLCB0cmlnZ2VyOiAnYmx1cicgfQ0KICAgICAgICBdLA0KICAgICAgICBkcml2ZXJJZENhcmQ6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36L6T5YWl5Y+45py66Lqr5Lu96K+B5Y+3JywgdHJpZ2dlcjogJ2JsdXInIH0sDQogICAgICAgICAgeyB2YWxpZGF0b3I6IHZhbGlkYXRlSWRDYXJkLCB0cmlnZ2VyOiAnYmx1cicgfQ0KICAgICAgICBdDQogICAgICB9LA0KDQogICAgICAvLyDmtL7ovabliJfooajmlbDmja4NCiAgICAgIGRpc3BhdGNoTGlzdDogWw0KICAgICAgICB7DQogICAgICAgICAgaWQ6IDEsDQogICAgICAgICAgY2FyTnVtYmVyOiAn5LqsQTEyMzQ1JywNCiAgICAgICAgICBkcml2ZXJOYW1lOiAn546L5bCP5piOJywNCiAgICAgICAgICBkcml2ZXJQaG9uZTogJzEzODAwMTM4MDAwJywNCiAgICAgICAgICBkcml2ZXJJZENhcmQ6ICcxMTAxMDExOTkwMDEwMTAwMDEnLA0KICAgICAgICAgIGRpc3BhdGNoVGltZTogJzIwMjUtMDMtMTggMDk6MzA6MDAnLA0KICAgICAgICAgIHN0YXR1czogMiwNCiAgICAgICAgICB0YXJlV2VpZ2h0OiA4NTAwLA0KICAgICAgICAgIGdyb3NzV2VpZ2h0OiAxNTgwMCwNCiAgICAgICAgICByZWNoZWNrZWRHcm9zc1dlaWdodDogMTU3NTAsDQogICAgICAgICAgcmVjaGVja2VkVGFyZVdlaWdodDogODQ4MA0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgaWQ6IDIsDQogICAgICAgICAgY2FyTnVtYmVyOiAn5LqsQjk4NzY1JywNCiAgICAgICAgICBkcml2ZXJOYW1lOiAn5p2O5aSn5aOuJywNCiAgICAgICAgICBkcml2ZXJQaG9uZTogJzEzOTAwMTM5MDAwJywNCiAgICAgICAgICBkcml2ZXJJZENhcmQ6ICcxMTAxMDExOTkxMDIwMjAwMDInLA0KICAgICAgICAgIGRpc3BhdGNoVGltZTogJzIwMjUtMDMtMTkgMTQ6MTU6MDAnLA0KICAgICAgICAgIHN0YXR1czogMSwNCiAgICAgICAgICB0YXJlV2VpZ2h0OiA3ODAwLA0KICAgICAgICAgIGdyb3NzV2VpZ2h0OiAxMjYwMCwNCiAgICAgICAgICByZWNoZWNrZWRHcm9zc1dlaWdodDogbnVsbCwNCiAgICAgICAgICByZWNoZWNrZWRUYXJlV2VpZ2h0OiBudWxsDQogICAgICAgIH0NCiAgICAgIF0sDQoNCiAgICAgIC8vIOiuoeWIkuivpuaDheS/oeaBrw0KICAgICAgcGxhbkluZm86IHt9LA0KICAgICAgYXBwbHlObzogbnVsbCwNCiAgICAgIHRhc2tRdWVyeVBhcmFtczogew0KICAgICAgICBhcHBseU5vOiBudWxsLA0KICAgICAgfSwNCg0KICAgICAgdGFza01hdGVyaWFsTGlzdDogbnVsbCwNCiAgICAgIC8vIOeJqei1hOmAieaLqeebuOWFs+aVsOaNrg0KICAgICAgbWF0ZXJpYWxTZWxlY3Rpb25MaXN0OiBbew0KICAgICAgICBtYXRlcmlhbElkOiBudWxsLA0KICAgICAgICBtYXRlcmlhbE5hbWU6ICcnLA0KICAgICAgICBtYXRlcmlhbFNwZWM6ICcnLA0KICAgICAgICBwbGFuTnVtOiAwLA0KICAgICAgICB1c2VkTnVtOiAwLA0KICAgICAgICByZW1haW5pbmdOdW06IDAsDQogICAgICAgIGN1cnJlbnROdW06IDANCiAgICAgIH1dLA0KICAgICAgYXZhaWxhYmxlTWF0ZXJpYWxzOiBbXSwgLy8g5Y+v6YCJ55qE54mp6LWE5YiX6KGoDQogICAgICB0YXNrTWF0ZXJpYWxMaXN0TWFwOiBuZXcgTWFwKCksIC8vIOW3sua0vui9pueahOeJqei1hOWIl+ihqA0KICAgICAgdGFza01hdGVyaWFsTWFwOiBuZXcgTWFwKCksIC8vIOWtmOWCqOaJgOacieS7u+WKoeeJqei1hOeahOaYoOWwhA0KICAgIH07DQogIH0sDQogIGNvbXB1dGVkOiB7DQogICAgLy8g5Yik5pat5piv5ZCm5Y+v5Lul5rS+6L2mDQogICAgY2FuRGlzcGF0Y2hDYXIoKSB7DQogICAgICAvLyDliKTmlq3nlLPor7fljZXmmK/lkKblt7LpgJrov4cNCiAgICAgIC8vIGNvbnN0IGlzUGxhbkFwcHJvdmVkID0gdGhpcy5wbGFuSW5mby5wbGFuU3RhdHVzID09PSAyOw0KDQogICAgICAvLyAvLyDlpoLmnpzmmK/pnZ7orqHph4/nsbvlnovvvIzkuJTlt7Lnu4/mtL7ov4fovabvvIzliJnkuI3og73lho3mtL7ovaYNCiAgICAgIC8vIGlmICh0aGlzLnBsYW5JbmZvLm1lYXN1cmVGbGFnICE9PSAxICYmIHRoaXMuZGlzcGF0Y2hMaXN0Lmxlbmd0aCA+IDApIHsNCiAgICAgIC8vICAgcmV0dXJuIGZhbHNlOw0KICAgICAgLy8gfQ0KDQogICAgICByZXR1cm4gdHJ1ZTsNCiAgICB9LA0KICAgIC8vIOm7mOiupOaYvuekuuWJjTUw5p2h77yM6Iul5pyJ5pCc57Si77yM5YiZ5pi+56S65pCc57Si5ZCO55qE5pWw5o2uDQogICAgZGlzcGxheURyaXZlckxpc3RPcHRpb25zKCkgew0KICAgICAgcmV0dXJuIHRoaXMuc2VhcmNoRHJpdmVyUXVlcnkgPyB0aGlzLmZpbHRlcmVkRHJpdmVyT3B0aW9ucyA6IHRoaXMuZHJpdmVyTGlzdC5zbGljZSgwLCA1MCk7DQogICAgfSwNCiAgICBkaXNwbGF5Q2FyTGlzdE9wdGlvbnMoKSB7DQogICAgICByZXR1cm4gdGhpcy5zZWFyY2hDYXJRdWVyeSA/IHRoaXMuZmlsdGVyZWRDYXJPcHRpb25zIDogdGhpcy5jYXJMaXN0LnNsaWNlKDAsIDUwKTsNCiAgICB9LA0KICAgIGNhblNob3dNYXRlcmlhbENvbmZpcm0oKSB7DQogICAgICAvLyDlj6rmnIlwbGFuU3RhdHVz5Li6NeaIljbml7bmmL7npLrvvIjlt7Llh7rljoIv6YOo5YiG5pS26LSn77yJ77yM5LiU5LiN5piv5bey5a6M5oiQL+W6n+W8gy/pqbPlm54v6L+H5pyfDQogICAgICByZXR1cm4gWzUsIDZdLmluY2x1ZGVzKHRoaXMucGxhbkluZm8ucGxhblN0YXR1cyk7DQogICAgfQ0KICB9LA0KICBhY3RpdmF0ZWQoKSB7DQogICAgdGhpcy5nZXREaWN0cygieGN0Z19kcml2ZXJfY2FyX2VtaXNzaW9uX3N0YW5kYXJkcyIpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgdGhpcy52ZWhpY2xlRW1pc3Npb25TdGFuZGFyZHNPcHRpb25zID0gcmVzcG9uc2UuZGF0YTsNCiAgICB9KTsNCiAgICB0aGlzLmdldERpY3RzKCJsZWF2ZV90YXNrX3R5cGUiKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgIHRoaXMudGFza1R5cGVPcHRpb25zID0gcmVzcG9uc2UuZGF0YTsNCiAgICAgIGlmICh0aGlzLnBsYW5JbmZvLnBsYW5UeXBlICE9PSAzKSB7DQogICAgICAgIHRoaXMudGFza1R5cGVPcHRpb25zLnNwbGljZSgyLCAxKTsNCiAgICAgIH0NCiAgICB9KTsNCiAgICAvLyDojrflj5bot6/nlLHlj4LmlbDkuK3nmoRJRA0KICAgIGNvbnN0IGFwcGx5Tm8gPSB0aGlzLiRyb3V0ZS5wYXJhbXMuYXBwbHlObzsNCiAgICB0aGlzLmFwcGx5Tm8gPSBhcHBseU5vDQogICAgdGhpcy50YXNrUXVlcnlQYXJhbXMuYXBwbHlObyA9IGFwcGx5Tm87DQogICAgdGhpcy5hcHByb3ZlRm9ybS5hcHBseU5vID0gYXBwbHlObzsNCiAgICBpZiAoYXBwbHlObykgew0KICAgICAgZGV0YWlsUGxhbihhcHBseU5vKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy5wbGFuSW5mbyA9IHJlc3BvbnNlLmRhdGE7DQogICAgICAgIHRoaXMudGFza1R5cGVFZGl0VXBkYXRlKCk7DQogICAgICAgIGNvbnNvbGUubG9nKCJ0aGlzLnBsYW5JbmZvIiwgdGhpcy5wbGFuSW5mbyk7DQogICAgICAgIC8vIOino+aekOWbvueJh+WSjOaWh+S7tuaVsOaNrg0KICAgICAgICB0aGlzLnBhcnNlSW1hZ2VBbmRGaWxlRGF0YSgpOw0KICAgICAgfSk7DQogICAgfTsNCiAgICB0aGlzLmdldExpc3RUYXNrSW5mbygpOw0KICAgIHRoaXMuZ2V0RHJpdmVyTGlzdCgpOw0KICAgIHRoaXMuZ2V0Q2FyTGlzdCgpOw0KDQoNCg0KICB9LA0KDQoNCiAgbWV0aG9kczogew0KICAgIGhhbmRsZUFwcHJvdmUoKSB7DQogICAgICB0aGlzLmFwcHJvdmVGb3JtLmFwcHJvdmVGbGFnID0gdHJ1ZTsNCiAgICAgIGNvbnNvbGUubG9nKCJ0aGlzLmFwcHJvdmVGb3JtIiwgdGhpcy5hcHByb3ZlRm9ybSk7DQogICAgICBhcHByb3ZlKHRoaXMuYXBwcm92ZUZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+WuoeaguOmAmui/hycpOw0KICAgICAgICAvLyDot7PovazliLDliJfooajpobXpnaLlubbliLfmlrANCiAgICAgICAgdGhpcy4kcm91dGVyLnB1c2goew0KICAgICAgICAgIHBhdGg6ICIvbGVhdmUvbGVhdmVQbGFuTGlzdCIsDQogICAgICAgICAgcXVlcnk6IHsNCiAgICAgICAgICAgIHQ6IERhdGUubm93KCksDQogICAgICAgICAgICByZWZyZXNoOiB0cnVlIC8vIOa3u+WKoOWIt+aWsOagh+iusA0KICAgICAgICAgIH0NCiAgICAgICAgfSk7DQogICAgICB9KS5jYXRjaChlcnJvciA9PiB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+WuoeaguOWksei0pScpOw0KICAgICAgICBjb25zb2xlLmVycm9yKCdBcHByb3ZhbCBlcnJvcjonLCBlcnJvcik7DQogICAgICB9KTsNCiAgICB9LA0KICAgIGhhbmRsZVJlamVjdCgpIHsNCiAgICAgIHRoaXMuYXBwcm92ZUZvcm0uYXBwcm92ZUZsYWcgPSBmYWxzZTsNCiAgICAgIGNvbnNvbGUubG9nKCJ0aGlzLmFwcHJvdmVGb3JtIiwgdGhpcy5hcHByb3ZlRm9ybSk7DQogICAgICBhcHByb3ZlKHRoaXMuYXBwcm92ZUZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+mps+WbnuaIkOWKnycpOw0KICAgICAgICAvLyDot7PovazliLDliJfooajpobXpnaLlubbliLfmlrANCiAgICAgICAgdGhpcy4kcm91dGVyLnB1c2goew0KICAgICAgICAgIHBhdGg6ICIvbGVhdmUvbGVhdmVQbGFuTGlzdCIsDQogICAgICAgICAgcXVlcnk6IHsNCiAgICAgICAgICAgIHQ6IERhdGUubm93KCksDQogICAgICAgICAgICByZWZyZXNoOiB0cnVlIC8vIOa3u+WKoOWIt+aWsOagh+iusA0KICAgICAgICAgIH0NCiAgICAgICAgfSk7DQogICAgICB9KS5jYXRjaChlcnJvciA9PiB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+WuoeaguOWksei0pScpOw0KICAgICAgICBjb25zb2xlLmVycm9yKCdBcHByb3ZhbCBlcnJvcjonLCBlcnJvcik7DQogICAgICB9KTsNCiAgICB9LA0KICAgIGhhbmRsZURpc2NhcmQoKSB7DQogICAgICBkaXNjYXJkKHRoaXMucGxhbkluZm8pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+W6n+W8g+aIkOWKnycpOw0KDQogICAgICAgIGlmICh3aW5kb3cuaGlzdG9yeS5sZW5ndGggPiAxKSB7DQogICAgICAgICAgdGhpcy4kcm91dGVyLmdvKC0xKTsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRyb3V0ZXIucHVzaCh7IHBhdGg6ICIvbGVhdmUvbGVhdmVQbGFuTGlzdCIsIHF1ZXJ5OiB7IHQ6IERhdGUubm93KCkgfSB9KTsNCiAgICAgICAgfQ0KICAgICAgfSkuY2F0Y2goZXJyb3IgPT4gew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCflup/lvIPlpLHotKUnKTsNCiAgICAgICAgY29uc29sZS5lcnJvcignQXBwcm92YWwgZXJyb3I6JywgZXJyb3IpOw0KICAgICAgfSk7DQogICAgfSwNCiAgICB0YXNrVHlwZUVkaXRVcGRhdGUoKSB7DQogICAgICBpZiAodGhpcy5wbGFuSW5mby5wbGFuVHlwZSAhPT0gMikgew0KICAgICAgICB0aGlzLmlzVGFza1R5cGVFZGl0ID0gZmFsc2U7DQogICAgICB9DQogICAgfSwNCiAgICBnZXRMaXN0VGFza0luZm8oKSB7DQogICAgICBsaXN0VGFzayh0aGlzLnRhc2tRdWVyeVBhcmFtcykudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIGNvbnNvbGUubG9nKCJyZXNwb25zZS5kYXRhIiwgcmVzcG9uc2Uucm93cyk7DQogICAgICAgIHRoaXMudGFza0xpc3RJbmZvID0gcmVzcG9uc2Uucm93czsNCiAgICAgICAgY29uc29sZS5sb2coInRoaXMudGFza0xpc3RJbmZvIiwgdGhpcy50YXNrTGlzdEluZm8pOw0KICAgICAgICAvLyDojrflj5bmiYDmnInku7vliqHnianotYQNCiAgICAgICAgdGhpcy5nZXRBbGxUYXNrTWF0ZXJpYWxzKCk7DQogICAgICB9KTsNCiAgICB9LA0KICAgIG9wZW5OZXdEcml2ZXJXaW5kb3coKSB7DQogICAgICBjb25zdCBuZXdXaW5kb3dVcmwgPSAnaHR0cHM6Ly95ZHh0LmNpdGljc3RlZWwuY29tOjgwOTkvdHJ1Y2tNYW5hZ2UveGN0Z0RyaXZlclVzZXInOyAvLyDmm7/mjaLkuLrlrp7pmYXopoHot7PovaznmoTpobXpnaIgVVJMDQogICAgICB3aW5kb3cub3BlbihuZXdXaW5kb3dVcmwsICdfYmxhbmsnKTsgLy8g5omT5byA5paw56qX5Y+j5bm26Lez6L2s6Iez5oyH5a6aIFVSTA0KICAgIH0sDQogICAgb3Blbk5ld0NhcldpbmRvdygpIHsNCiAgICAgIGNvbnN0IG5ld1dpbmRvd1VybCA9ICdodHRwczovL3lkeHQuY2l0aWNzdGVlbC5jb206ODA5OS90cnVja01hbmFnZS94Y3RnRHJpdmVyQ2FyJzsgLy8g5pu/5o2i5Li65a6e6ZmF6KaB6Lez6L2s55qE6aG16Z2iIFVSTA0KICAgICAgd2luZG93Lm9wZW4obmV3V2luZG93VXJsLCAnX2JsYW5rJyk7IC8vIOaJk+W8gOaWsOeql+WPo+W5tui3s+i9rOiHs+aMh+WumiBVUkwNCiAgICB9LA0KICAgIC8vIDHlm73kupTvvIwy5Zu95YWt77yMM+aWsOiDvea6kOWtl+WFuOe/u+ivkQ0KICAgIHZlaGljbGVFbWlzc2lvblN0YW5kYXJkc0Zvcm1hdChyb3csIGNvbHVtbikgew0KICAgICAgcmV0dXJuIHRoaXMuc2VsZWN0RGljdExhYmVsKHRoaXMudmVoaWNsZUVtaXNzaW9uU3RhbmRhcmRzT3B0aW9ucywgcm93LnZlaGljbGVFbWlzc2lvblN0YW5kYXJkcyk7DQogICAgfSwNCg0KICAgIHRhc2tUeXBlRm9ybWF0KHJvdywgY29sdW1uKSB7DQogICAgICByZXR1cm4gdGhpcy5nZXRUYXNrVHlwZVRleHQocm93LnRhc2tUeXBlKTsNCiAgICB9LA0KICAgIHRhc2tTdGF0dXNGb3JtYXQocm93LCBjb2x1bW4pIHsNCiAgICAgIHJldHVybiB0aGlzLmdldFN0YXR1c1RleHQocm93LnRhc2tTdGF0dXMpOw0KICAgIH0sDQoNCiAgICAvKiog5p+l6K+i5Y+45py65L+h5oGv5YiX6KGoICovDQogICAgZ2V0Q2FyTGlzdCgpIHsNCiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7DQogICAgICAvLyBsaXN0QWxsRHJpdmVyKCkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAvLyAgIHRoaXMuZHJpdmVyTGlzdCA9IHJlc3BvbnNlLmRhdGE7DQogICAgICAvLyAgIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgICAgLy8gfSk7DQogICAgICBnZXRYY3RnRHJpdmVyQ2FyTGlzdCgpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLmNhckxpc3QgPSByZXNwb25zZS5kYXRhOw0KICAgICAgICB0aGlzLmZpbHRlcmVkQ2FyT3B0aW9ucyA9IHRoaXMuY2FyTGlzdDsNCiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8vIOaQnOe0oui/h+a7pOmAu+i+kQ0KICAgIGZpbHRlckNhckRhdGEocXVlcnkpIHsNCiAgICAgIHRoaXMuc2VhcmNoQ2FyUXVlcnkgPSBxdWVyeTsNCg0KICAgICAgaWYgKHRoaXMuc2VhcmNoQ2FyUXVlcnkpIHsNCg0KICAgICAgICB0aGlzLmZpbHRlcmVkQ2FyT3B0aW9ucyA9IHRoaXMuY2FyTGlzdC5maWx0ZXIoaXRlbSA9Pg0KICAgICAgICAgIGl0ZW0uY2FyTnVtYmVyLmluY2x1ZGVzKHF1ZXJ5KQ0KICAgICAgICApOw0KICAgICAgfSBlbHNlIHsNCg0KICAgICAgICB0aGlzLmZpbHRlcmVkQ2FyT3B0aW9ucyA9IHRoaXMuY2FyTGlzdC5zbGljZSgwLCA1MCk7DQogICAgICB9DQogICAgfSwNCiAgICAvL+mAmui/h2RyaXZlcklk6I635Y+W5Y+45py65L+h5oGvDQogICAgaGFuZGxlRHJpdmVyQ2hhbmdlKCkgew0KICAgICAgaWYgKHRoaXMuZGlzcGF0Y2hGb3JtLmRyaXZlcklkICE9IG51bGwpIHsNCiAgICAgICAgdGhpcy5kcml2ZXJMaXN0LmZvckVhY2goaXRlbSA9PiB7DQogICAgICAgICAgaWYgKGl0ZW0uaWQgPT0gdGhpcy5kaXNwYXRjaEZvcm0uZHJpdmVySWQpIHsNCiAgICAgICAgICAgIHRoaXMuZGlzcGF0Y2hGb3JtLm5hbWUgPSBpdGVtLm5hbWU7DQogICAgICAgICAgICB0aGlzLmRpc3BhdGNoRm9ybS5pZENhcmQgPSBpdGVtLmlkQ2FyZDsNCiAgICAgICAgICAgIHRoaXMuZGlzcGF0Y2hGb3JtLmNvbXBhbnkgPSBpdGVtLmNvbXBhbnk7DQogICAgICAgICAgICB0aGlzLmRpc3BhdGNoRm9ybS5waG9uZSA9IGl0ZW0ucGhvbmU7DQogICAgICAgICAgICB0aGlzLmRpc3BhdGNoRm9ybS5waG90byA9IGl0ZW0ucGhvdG87DQogICAgICAgICAgICB0aGlzLmRpc3BhdGNoRm9ybS5mYWNlSW1nTGlzdCA9IGl0ZW0uZmFjZUltZ0xpc3Q7DQogICAgICAgICAgICB0aGlzLmRpc3BhdGNoRm9ybS5kcml2ZXJMaWNlbnNlSW1ncyA9IGl0ZW0uZHJpdmVyTGljZW5zZUltZ3M7DQogICAgICAgICAgICB0aGlzLmRpc3BhdGNoRm9ybS52ZWhpY2xlTGljZW5zZUltZ3MgPSBpdGVtLnZlaGljbGVMaWNlbnNlSW1nczsNCiAgICAgICAgICAgIHRoaXMuZGlzcGF0Y2hGb3JtLnNleCA9IGl0ZW0uZ2VuZGVyOw0KDQogICAgICAgICAgfQ0KICAgICAgICB9KTsNCiAgICAgIH0NCiAgICB9LA0KICAgIC8v6YCa6L+HZHJpdmVySWTojrflj5blj7jmnLrkv6Hmga8NCiAgICBoYW5kbGVDYXJDaGFuZ2UoKSB7DQogICAgICBjb25zb2xlLmxvZygiaGFuZGxlQ2FyQ2hhbmdlIikNCiAgICAgIGlmICh0aGlzLmRpc3BhdGNoRm9ybS5jYXJVVUlkICE9IG51bGwpIHsNCiAgICAgICAgdGhpcy5jYXJMaXN0LmZvckVhY2goaXRlbSA9PiB7DQogICAgICAgICAgaWYgKGl0ZW0uaWQgPT0gdGhpcy5kaXNwYXRjaEZvcm0uY2FyVVVJZCkgew0KICAgICAgICAgICAgdGhpcy5kaXNwYXRjaEZvcm0uY2FyTnVtYmVyID0gaXRlbS5jYXJOdW1iZXI7DQoNCiAgICAgICAgICAgIGlmIChpdGVtLnZlaGljbGVFbWlzc2lvblN0YW5kYXJkcyA9PSAxKSB7DQogICAgICAgICAgICAgIHRoaXMuZGlzcGF0Y2hGb3JtLnZlaGljbGVFbWlzc2lvblN0YW5kYXJkcyA9ICLlm73kupQiOw0KICAgICAgICAgICAgfSBlbHNlIGlmIChpdGVtLnZlaGljbGVFbWlzc2lvblN0YW5kYXJkcyA9PSAyKSB7DQogICAgICAgICAgICAgIHRoaXMuZGlzcGF0Y2hGb3JtLnZlaGljbGVFbWlzc2lvblN0YW5kYXJkcyA9ICLlm73lha0iOw0KICAgICAgICAgICAgfSBlbHNlIGlmIChpdGVtLnZlaGljbGVFbWlzc2lvblN0YW5kYXJkcyA9PSAzKSB7DQogICAgICAgICAgICAgIHRoaXMuZGlzcGF0Y2hGb3JtLnZlaGljbGVFbWlzc2lvblN0YW5kYXJkcyA9ICLmlrDog73mupAiOw0KICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgdGhpcy5kaXNwYXRjaEZvcm0udmVoaWNsZUVtaXNzaW9uU3RhbmRhcmRzID0gIiI7DQogICAgICAgICAgICB9DQogICAgICAgICAgICB0aGlzLmRpc3BhdGNoRm9ybS5saWNlbnNlUGxhdGVDb2xvciA9IGl0ZW0ubGljZW5zZVBsYXRlQ29sb3I7DQogICAgICAgICAgICB0aGlzLmRpc3BhdGNoRm9ybS5jYXJJZCA9IGl0ZW0uY2FySWQ7DQogICAgICAgICAgICB0aGlzLmRpc3BhdGNoRm9ybS50cmFpbGVyTnVtYmVyID0gaXRlbS50cmFpbGVyTnVtYmVyOw0KICAgICAgICAgICAgdGhpcy5kaXNwYXRjaEZvcm0udHJhaWxlcklkID0gaXRlbS50cmFpbGVySWQ7DQogICAgICAgICAgICB0aGlzLmRpc3BhdGNoRm9ybS5heGlzVHlwZSA9IGl0ZW0uYXhpc1R5cGU7DQogICAgICAgICAgICB0aGlzLmRpc3BhdGNoRm9ybS5kcml2ZXJXZWlnaHQgPSBpdGVtLmRyaXZlcldlaWdodDsNCiAgICAgICAgICAgIHRoaXMuZGlzcGF0Y2hGb3JtLm1heFdlaWdodCA9IGl0ZW0ubWF4V2VpZ2h0Ow0KICAgICAgICAgICAgdGhpcy5kaXNwYXRjaEZvcm0uZW5naW5lTnVtYmVyID0gaXRlbS5lbmdpbmVOdW1iZXI7DQogICAgICAgICAgICB0aGlzLmRpc3BhdGNoRm9ybS52aW5OdW1iZXIgPSBpdGVtLnZpbk51bWJlcjsNCiAgICAgICAgICB9DQoNCiAgICAgICAgfSk7DQogICAgICB9DQogICAgfSwNCiAgICAvKiog5p+l6K+i5Y+45py65L+h5oGv5YiX6KGoICovDQogICAgZ2V0RHJpdmVyTGlzdCgpIHsNCiAgICAgIC8vIGxpc3RBbGxEcml2ZXIoKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgIC8vICAgdGhpcy5kcml2ZXJMaXN0ID0gcmVzcG9uc2UuZGF0YTsNCiAgICAgIC8vICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7DQogICAgICAvLyB9KTsNCiAgICAgIGdldFhjdGdEcml2ZXJVc2VyTGlzdCgpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLmRyaXZlckxpc3QgPSByZXNwb25zZS5kYXRhOw0KICAgICAgICBjb25zb2xlLmxvZygidGhpcy5kcml2ZXJMaXN0IiwgdGhpcy5kcml2ZXJMaXN0KTsNCiAgICAgICAgdGhpcy5maWx0ZXJlZERyaXZlck9wdGlvbnMgPSB0aGlzLmRyaXZlckxpc3Q7DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8vIOaQnOe0oui/h+a7pOmAu+i+kQ0KICAgIGZpbHRlckRyaXZlckRhdGEocXVlcnkpIHsNCiAgICAgIHRoaXMuc2VhcmNoRHJpdmVyUXVlcnkgPSBxdWVyeTsNCg0KICAgICAgaWYgKHRoaXMuc2VhcmNoRHJpdmVyUXVlcnkpIHsNCg0KICAgICAgICB0aGlzLmZpbHRlcmVkRHJpdmVyT3B0aW9ucyA9IHRoaXMuZHJpdmVyTGlzdC5maWx0ZXIoaXRlbSA9Pg0KICAgICAgICAgIGl0ZW0uZHJpdmVySW5mby5pbmNsdWRlcyhxdWVyeSkNCiAgICAgICAgKTsNCiAgICAgIH0gZWxzZSB7DQoNCiAgICAgICAgdGhpcy5maWx0ZXJlZERyaXZlck9wdGlvbnMgPSB0aGlzLmRyaXZlckxpc3Quc2xpY2UoMCwgNTApOw0KICAgICAgfQ0KICAgIH0sDQogICAgLy8g6Kej5p6Q5Zu+54mH5ZKM5paH5Lu25pWw5o2uDQogICAgcGFyc2VJbWFnZUFuZEZpbGVEYXRhKCkgew0KICAgICAgLy8g6Kej5p6Q5Zu+54mH5pWw5o2uDQogICAgICBpZiAodGhpcy5wbGFuSW5mby5hcHBseUltZ1VybCkgew0KICAgICAgICB0cnkgew0KICAgICAgICAgIHRoaXMuaW1hZ2VMaXN0ID0gSlNPTi5wYXJzZSh0aGlzLnBsYW5JbmZvLmFwcGx5SW1nVXJsKTsNCiAgICAgICAgfSBjYXRjaCAoZSkgew0KICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+ino+aekOWbvueJh+aVsOaNruWksei0pTonLCBlKTsNCiAgICAgICAgICB0aGlzLmltYWdlTGlzdCA9IFtdOw0KICAgICAgICB9DQogICAgICB9DQoNCiAgICAgIC8vIOino+aekOaWh+S7tuaVsOaNrg0KICAgICAgaWYgKHRoaXMucGxhbkluZm8uYXBwbHlGaWxlVXJsKSB7DQogICAgICAgIHRyeSB7DQogICAgICAgICAgdGhpcy5maWxlTGlzdCA9IEpTT04ucGFyc2UodGhpcy5wbGFuSW5mby5hcHBseUZpbGVVcmwpOw0KICAgICAgICB9IGNhdGNoIChlKSB7DQogICAgICAgICAgY29uc29sZS5lcnJvcign6Kej5p6Q5paH5Lu25pWw5o2u5aSx6LSlOicsIGUpOw0KICAgICAgICAgIHRoaXMuZmlsZUxpc3QgPSBbXTsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvLyDkuIvovb3mlofku7YNCiAgICBkb3dubG9hZEZpbGUodXJsLCBmaWxlTmFtZSkgew0KICAgICAgaWYgKCF1cmwpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5paH5Lu26ZO+5o6l5peg5pWIJyk7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCg0KICAgICAgLy8g5Yib5bu65LiA5LiqYeWFg+e0oOeUqOS6juS4i+i9vQ0KICAgICAgY29uc3QgbGluayA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoJ2EnKTsNCiAgICAgIGxpbmsuaHJlZiA9IHVybDsNCiAgICAgIGxpbmsuZG93bmxvYWQgPSBmaWxlTmFtZSB8fCAn5LiL6L295paH5Lu2JzsNCiAgICAgIGRvY3VtZW50LmJvZHkuYXBwZW5kQ2hpbGQobGluayk7DQogICAgICBsaW5rLmNsaWNrKCk7DQogICAgICBkb2N1bWVudC5ib2R5LnJlbW92ZUNoaWxkKGxpbmspOw0KICAgIH0sDQoNCiAgICAvLyDojrflj5borqHliJLnsbvlnovmlofmnKwNCiAgICBnZXRQbGFuVHlwZVRleHQodHlwZSkgew0KICAgICAgY29uc3QgdHlwZU1hcCA9IHsNCiAgICAgICAgMTogJ+WHuuWOguS4jei/lOWbnicsDQogICAgICAgIDI6ICflh7rljoLov5Tlm54nLA0KICAgICAgICAzOiAn6Leo5Yy66LCD5ouoJywNCiAgICAgICAgNDogJ+mAgOi0p+eUs+ivtycNCiAgICAgIH07DQogICAgICByZXR1cm4gdHlwZU1hcFt0eXBlXSB8fCAn5pyq55+l57G75Z6LJzsNCiAgICB9LA0KDQogICAgLy8g6I635Y+W5Lia5Yqh57G75Z6L5paH5pysDQogICAgZ2V0QnVzaW5lc3NDYXRlZ29yeVRleHQoY2F0ZWdvcnkpIHsNCiAgICAgIGNvbnN0IGNhdGVnb3J5TWFwID0gew0KICAgICAgICAxOiAn6YCa55SoJywNCiAgICAgICAgMTE6ICfpgJrnlKgnLA0KICAgICAgICAxMjogJ+WnlOWkluWKoOW3pScsDQogICAgICAgIDIxOiAn5pyJ6K6h5YiS6YeP6K6h6YePJywNCiAgICAgICAgMjI6ICfnn63mnJ8nLA0KICAgICAgICAyMzogJ+mSouadv++8iOWchumSou+8iScsDQogICAgICAgIDMxOiAn6YCa55SoJw0KICAgICAgfTsNCiAgICAgIHJldHVybiBjYXRlZ29yeU1hcFtjYXRlZ29yeV0gfHwgJ+acquefpeexu+Weiyc7DQogICAgfSwNCg0KICAgIC8vIOiOt+WPlueJqei1hOexu+Wei+aWh+acrA0KICAgIGdldE1hdGVyaWFsVHlwZVRleHQodHlwZSkgew0KICAgICAgY29uc3QgdHlwZU1hcCA9IHsNCiAgICAgICAgMTogJ+mSouadkCcsDQogICAgICAgIDI6ICfpkqLmnb8nLA0KICAgICAgICAzOiAn5YW25LuWJw0KICAgICAgfTsNCiAgICAgIHJldHVybiB0eXBlTWFwW3R5cGVdIHx8ICfmnKrnn6XnsbvlnosnOw0KICAgIH0sDQoNCiAgICAvLyDojrflj5borqHliJLnirbmgIHmlofmnKwNCiAgICBnZXRQbGFuU3RhdHVzVGV4dChzdGF0dXMpIHsNCiAgICAgIGNvbnN0IHN0YXR1c01hcCA9IHsNCiAgICAgICAgMTogJ+W+heWIhuWOguWuoeaJuScsDQogICAgICAgIDI6ICflvoXliIbljoLlpI3lrqEnLA0KICAgICAgICAzOiAn5b6F55Sf5Lqn5oyH5oyl5Lit5b+D5a6h5om5JywNCiAgICAgICAgNDogJ+WuoeaJueWujOaIkCcsDQogICAgICAgIDU6ICflt7Llh7rljoInLA0KICAgICAgICA2OiAn6YOo5YiG5pS26LSnJywNCiAgICAgICAgNzogJ+W3suWujOaIkCcsDQogICAgICAgIDExOiAn6amz5ZueJywNCiAgICAgICAgMTI6ICflup/lvIMnLA0KICAgICAgICAxMzogJ+i/h+acnycNCiAgICAgIH07DQogICAgICByZXR1cm4gc3RhdHVzTWFwW3N0YXR1c10gfHwgJ+acquefpeeKtuaAgSc7DQogICAgfSwNCg0KICAgIC8vIOiOt+WPluaXpeW/l+minOiJsg0KICAgIGdldExvZ0NvbG9yKGxvZykgew0KICAgICAgY29uc3QgbG9nVHlwZUNvbG9yTWFwID0gew0KICAgICAgICAxOiAnIzQwOUVGRicsIC8vIOWIm+W7ug0KICAgICAgICAyOiAnIzY3QzIzQScsIC8vIOWuoeaJuQ0KICAgICAgICAzOiAnI0U2QTIzQycsIC8vIOa1gei9rA0KICAgICAgICA0OiAnI0Y1NkM2QycsIC8vIOmps+Wbng0KICAgICAgICA1OiAnIzkwOTM5OScgIC8vIOWFtuS7lg0KICAgICAgfTsNCiAgICAgIHJldHVybiBsb2dUeXBlQ29sb3JNYXBbbG9nLmxvZ1R5cGVdIHx8ICcjNDA5RUZGJzsNCiAgICB9LA0KDQogICAgLy8g6I635Y+W5rS+6L2m54q25oCB5paH5pysDQogICAgZ2V0RGlzcGF0Y2hTdGF0dXNUZXh0KHN0YXR1cykgew0KICAgICAgY29uc3Qgc3RhdHVzTWFwID0gew0KICAgICAgICAwOiAn5b6F5Ye65Y+RJywNCiAgICAgICAgMTogJ+W3suWHuuWPkScsDQogICAgICAgIDI6ICflt7LliLDovr4nLA0KICAgICAgICAzOiAn5bey5a6M5oiQJywNCiAgICAgICAgNDogJ+W3suWPlua2iCcNCiAgICAgIH07DQogICAgICByZXR1cm4gc3RhdHVzTWFwW3N0YXR1c10gfHwgJ+acquefpeeKtuaAgSc7DQogICAgfSwNCg0KICAgIC8vIOiOt+WPlua0vui9pueKtuaAgeexu+Wei++8iOeUqOS6juagh+etvuminOiJsu+8iQ0KICAgIGdldERpc3BhdGNoU3RhdHVzVHlwZShzdGF0dXMpIHsNCiAgICAgIGNvbnN0IHN0YXR1c01hcCA9IHsNCiAgICAgICAgMDogJ2luZm8nLA0KICAgICAgICAxOiAncHJpbWFyeScsDQogICAgICAgIDI6ICdzdWNjZXNzJywNCiAgICAgICAgMzogJ3N1Y2Nlc3MnLA0KICAgICAgICA0OiAnZGFuZ2VyJw0KICAgICAgfTsNCiAgICAgIHJldHVybiBzdGF0dXNNYXBbc3RhdHVzXSB8fCAnaW5mbyc7DQogICAgfSwNCg0KICAgIC8vIOiOt+WPluiuoeWIkuexu+Wei+agh+etvuagt+W8jw0KICAgIGdldFBsYW5UeXBlVGFnVHlwZSh0eXBlKSB7DQogICAgICBjb25zdCB0eXBlTWFwID0gew0KICAgICAgICAxOiAnc3VjY2VzcycsICAvLyDlh7rljoLkuI3ov5Tlm54NCiAgICAgICAgMjogJ3dhcm5pbmcnLCAgLy8g5Ye65Y6C6L+U5ZueDQogICAgICAgIDM6ICdpbmZvJywgICAgIC8vIOi3qOWMuuiwg+aLqA0KICAgICAgICA0OiAnZGFuZ2VyJyAgICAvLyDpgIDotKfnlLPor7cNCiAgICAgIH07DQogICAgICByZXR1cm4gdHlwZU1hcFt0eXBlXSB8fCAnaW5mbyc7DQogICAgfSwNCg0KICAgIC8vIOiOt+WPlueJqei1hOexu+Wei+agh+etvuagt+W8jw0KICAgIGdldE1hdGVyaWFsVHlwZVRhZ1R5cGUodHlwZSkgew0KICAgICAgY29uc3QgdHlwZU1hcCA9IHsNCiAgICAgICAgMTogJ3ByaW1hcnknLCAgLy8g6ZKi5p2QDQogICAgICAgIDI6ICdzdWNjZXNzJywgIC8vIOmSouadvw0KICAgICAgICAzOiAnaW5mbycgICAgICAvLyDlhbbku5YNCiAgICAgIH07DQogICAgICByZXR1cm4gdHlwZU1hcFt0eXBlXSB8fCAnaW5mbyc7DQogICAgfSwNCg0KICAgIC8vIOiOt+WPluS4muWKoeexu+Wei+agh+etvuagt+W8jw0KICAgIGdldEJ1c2luZXNzQ2F0ZWdvcnlUYWdUeXBlKGNhdGVnb3J5KSB7DQogICAgICBjb25zdCB0eXBlTWFwID0gew0KICAgICAgICAnMSc6ICdwcmltYXJ5JywgICAvLyDpgJrnlKgNCiAgICAgICAgJzExJzogJ3ByaW1hcnknLCAgLy8g6YCa55SoDQogICAgICAgICcxMic6ICd3YXJuaW5nJywgIC8vIOWnlOWkluWKoOW3pQ0KICAgICAgICAnMjEnOiAnc3VjY2VzcycsICAvLyDmnInorqHliJLph4/orqHph48NCiAgICAgICAgJzIyJzogJ2luZm8nLCAgICAgLy8g55+t5pyfDQogICAgICAgICcyMyc6ICdkYW5nZXInLCAgIC8vIOmSouadv++8iOWchumSou+8iQ0KICAgICAgICAnMzEnOiAncHJpbWFyeScgICAvLyDpgJrnlKgNCiAgICAgIH07DQogICAgICByZXR1cm4gdHlwZU1hcFtjYXRlZ29yeV0gfHwgJ2luZm8nOw0KICAgIH0sDQoNCiAgICAvLyDmiZPlvIDmtL7ovablvLnmoYYNCiAgICBvcGVuRGlzcGF0Y2hEaWFsb2coKSB7DQogICAgICAvLyDliJ3lp4vljJbnianotYTmlbDmja4NCiAgICAgIHRoaXMuYXZhaWxhYmxlTWF0ZXJpYWxzID0gdGhpcy5wbGFuSW5mby5tYXRlcmlhbHMgfHwgW107DQogICAgICBjb25zb2xlLmxvZygidGhpcy5hdmFpbGFibGVNYXRlcmlhbHMiLCB0aGlzLmF2YWlsYWJsZU1hdGVyaWFscyk7DQoNCiAgICAgIC8vIOiOt+WPluW3sua0vui9pueahOeJqei1hOWIl+ihqO+8jOW5tuWcqOWbnuiwg+S4reWIneWni+WMliBtYXRlcmlhbFNlbGVjdGlvbkxpc3QNCiAgICAgIHRoaXMuZ2V0VGFza01hdGVyaWFsTGlzdEFuZEluaXRTZWxlY3Rpb24oKTsNCiAgICAgIC8vIOWIpOaWremdnuiuoemHj+S4lHRhc2tUeXBl5Li6MeeahOaDheWGtQ0KICAgICAgaWYgKHRoaXMucGxhbkluZm8ubWVhc3VyZUZsYWcgPT0gMCkgew0KICAgICAgICBpZiAodGhpcy5kaXNwYXRjaEZvcm0udGFza1R5cGUgPT0gMSkgew0KICAgICAgICAgIC8vIOajgOafpeaYr+WQpuW3sue7j+aciXRhc2tUeXBl5Li6MeeahOS7u+WKoQ0KICAgICAgICAgIGNvbnN0IGhhc1R5cGUxVGFzayA9IHRoaXMudGFza0xpc3RJbmZvLnNvbWUodGFzayA9PiB0YXNrLnRhc2tUeXBlID09PSAxKTsNCiAgICAgICAgICBpZiAoaGFzVHlwZTFUYXNrKSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+mdnuiuoemHj+WPquiDvea0vui9puWHuuWOguS4gOasoScpOw0KICAgICAgICAgICAgcmV0dXJuOw0KICAgICAgICAgIH0NCiAgICAgICAgICBjb25zb2xlLmxvZygiaGFzVHlwZTFUYXNrIiwgaGFzVHlwZTFUYXNrKQ0KICAgICAgICB9DQogICAgICB9DQoNCg0KICAgICAgLy8g5Yik5pat55So5oi36KeS6Imy5p2D6ZmQDQogICAgICBjb25zdCByb2xlcyA9IHRoaXMuJHN0b3JlLmdldHRlcnMucm9sZXM7DQogICAgICBjb25zb2xlLmxvZygicm9sZXMiLCByb2xlcyk7DQogICAgICBpZiAoIXJvbGVzLmluY2x1ZGVzKCdsZWF2ZS5zdXBwbGllcicpICYmICFyb2xlcy5pbmNsdWRlcygnbGVhdmUuYXBwbGljYW50JykpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5oKo5rKh5pyJ5rS+6L2m5p2D6ZmQJyk7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCg0KICAgICAgY29uc29sZS5sb2coInRoaXMucGxhbkluZm8ucGxhblN0YXR1cyIsIHRoaXMucGxhbkluZm8ucGxhblN0YXR1cyk7DQogICAgICBpZiAoIVs0LCA1LCA2XS5pbmNsdWRlcyh0aGlzLnBsYW5JbmZvLnBsYW5TdGF0dXMpKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn5b2T5YmN54q25oCB5peg5rOV5rS+6L2mJyk7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCg0KDQoNCg0KICAgICAgY29uc29sZS5sb2coIm9wZW5EaXNwYXRjaERpYWxvZyIsIHRoaXMudGFza0xpc3RJbmZvLmxlbmd0aCk7DQogICAgICBpZiAodGhpcy5wbGFuSW5mby5idXNpbmVzc0NhdGVnb3J5ID09IDIyICYmIHRoaXMudGFza0xpc3RJbmZvLmxlbmd0aCA+PSAxKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn55+t5pyf6K6h5YiS5Y+q5YWB6K645rS+5LiA5qyh6L2mJyk7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCg0KICAgICAgaWYgKHRoaXMucGxhbkluZm8uYnVzaW5lc3NDYXRlZ29yeSA9PSAyMyAmJiB0aGlzLnRhc2tMaXN0SW5mby5sZW5ndGggPj0gMSkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+mSouadv++8iOWchumSou+8ieiuoeWIkuWPquWFgeiuuOa0vuS4gOasoei9picpOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQoNCiAgICAgIHRoaXMuZGlzcGF0Y2hGb3JtID0ge307DQoNCiAgICAgIGlmICh0aGlzLnBsYW5JbmZvLnBsYW5UeXBlID09IDEpIHsNCiAgICAgICAgdGhpcy5kaXNwYXRjaEZvcm0udGFza1R5cGUgPSAxDQogICAgICB9IGVsc2UgaWYgKHRoaXMucGxhbkluZm8ucGxhblR5cGUgPT0gMykgew0KICAgICAgICB0aGlzLmRpc3BhdGNoRm9ybS50YXNrVHlwZSA9IDMNCiAgICAgIH0gZWxzZSBpZiAodGhpcy5wbGFuSW5mby5wbGFuVHlwZSA9PSA0KSB7DQogICAgICAgIHRoaXMuZGlzcGF0Y2hGb3JtLnRhc2tUeXBlID0gMQ0KICAgICAgfQ0KICAgICAgY29uc29sZS5sb2codGhpcy5kaXNwYXRjaEZvcm0udGFza1R5cGUpLA0KICAgICAgICB0aGlzLmRpc3BhdGNoRGlhbG9nVmlzaWJsZSA9IHRydWU7DQoNCg0KICAgIH0sDQoNCiAgICAvLyDmlrDlop7mlrnms5UNCiAgICBnZXRUYXNrTWF0ZXJpYWxMaXN0QW5kSW5pdFNlbGVjdGlvbigpIHsNCiAgICAgIC8vIOa4heepuuW3sueUqOaVsOmHj+aYoOWwhA0KICAgICAgdGhpcy50YXNrTWF0ZXJpYWxMaXN0TWFwLmNsZWFyKCk7DQogICAgICAvLyDnu5/orqHmiYDmnInlt7LmtL7ovabnianotYQNCiAgICAgIGNvbnN0IHR5cGUyTGlzdCA9IHRoaXMudGFza0xpc3RJbmZvLmZpbHRlcihpdGVtID0+IGl0ZW0udGFza1R5cGUgPT09IDIpOw0KICAgICAgY29uc29sZS5sb2coInR5cGUyTGlzdCIsIHR5cGUyTGlzdCk7DQogICAgICBpZiAoIXR5cGUyTGlzdCB8fCB0eXBlMkxpc3QubGVuZ3RoID09PSAwKSB7DQogICAgICAgIC8vIOWIneWni+WMliBtYXRlcmlhbFNlbGVjdGlvbkxpc3TvvJrlhajpg6jpgInkuIrkuJTmlbDph4/kuLrliankvZnmlbDph48NCiAgICAgICAgdGhpcy5tYXRlcmlhbFNlbGVjdGlvbkxpc3QgPSAodGhpcy5wbGFuSW5mby5tYXRlcmlhbHMgfHwgW10pLm1hcChtYXQgPT4gew0KICAgICAgICAgIC8vIGNvbnN0IHVzZWROdW0gPSAodGhpcy50YXNrTWF0ZXJpYWxMaXN0TWFwLmdldChtYXQubWF0ZXJpYWxJZCk/LnVzZWROdW0pIHx8IDA7DQogICAgICAgICAgLy8gY29uc3QgcmVtYWluaW5nTnVtID0gTWF0aC5tYXgoKG1hdC5wbGFuTnVtIHx8IDApIC0gdXNlZE51bSwgMCk7DQogICAgICAgICAgcmV0dXJuIHsNCiAgICAgICAgICAgIG1hdGVyaWFsSWQ6IG1hdC5tYXRlcmlhbElkLA0KICAgICAgICAgICAgbWF0ZXJpYWxOYW1lOiBtYXQubWF0ZXJpYWxOYW1lLA0KICAgICAgICAgICAgbWF0ZXJpYWxTcGVjOiBtYXQubWF0ZXJpYWxTcGVjLA0KICAgICAgICAgICAgcGxhbk51bTogbWF0LnBsYW5OdW0sDQogICAgICAgICAgICB1c2VkTnVtOiAwLA0KICAgICAgICAgICAgcmVtYWluaW5nTnVtOiBtYXQucGxhbk51bSwNCiAgICAgICAgICAgIGN1cnJlbnROdW06IG1hdC5wbGFuTnVtDQogICAgICAgICAgfTsNCiAgICAgICAgfSk7DQogICAgICB9IGVsc2Ugew0KICAgICAgICBjb25zb2xlLmxvZygidGhpcy50YXNrTGlzdEluZm8iLCB0aGlzLnRhc2tMaXN0SW5mbyk7DQogICAgICAgIHR5cGUyTGlzdC5mb3JFYWNoKHRhc2sgPT4gew0KICAgICAgICAgIGNvbnN0IHBhcmFtcyA9IHsgdGFza05vOiB0YXNrLnRhc2tObyB9Ow0KICAgICAgICAgIGxpc3RUYXNrTWF0ZXJpYWwocGFyYW1zKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgICAgIGxldCB0YXNrTWF0ZXJpYWxzID0gcmVzcG9uc2Uucm93cyB8fCBbXTsNCiAgICAgICAgICAgIHRhc2tNYXRlcmlhbHMuZm9yRWFjaChtYXRlcmlhbCA9PiB7DQogICAgICAgICAgICAgIGlmICghdGhpcy50YXNrTWF0ZXJpYWxMaXN0TWFwLmhhcyhtYXRlcmlhbC5tYXRlcmlhbElkKSkgew0KICAgICAgICAgICAgICAgIHRoaXMudGFza01hdGVyaWFsTGlzdE1hcC5zZXQobWF0ZXJpYWwubWF0ZXJpYWxJZCwgew0KICAgICAgICAgICAgICAgICAgdGFza01hdGVyaWFsSW5mbzogbWF0ZXJpYWwsDQogICAgICAgICAgICAgICAgICB1c2VkTnVtOiBtYXRlcmlhbC5wbGFuTnVtDQogICAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgICAgY29uc3QgZXhpc3RpbmdNYXRlcmlhbCA9IHRoaXMudGFza01hdGVyaWFsTGlzdE1hcC5nZXQobWF0ZXJpYWwubWF0ZXJpYWxJZCk7DQogICAgICAgICAgICAgICAgZXhpc3RpbmdNYXRlcmlhbC51c2VkTnVtICs9IG1hdGVyaWFsLnBsYW5OdW07DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0pOw0KDQogICAgICAgICAgICAvLyDlsIZ0YXNrTWF0ZXJpYWxMaXN0TWFw6L2s5o2i5Li65pWw57uE6ZuG5ZCIDQogICAgICAgICAgICB0aGlzLnRhc2tNYXRlcmlhbExpc3QgPSBBcnJheS5mcm9tKHRoaXMudGFza01hdGVyaWFsTGlzdE1hcCwgKFtrZXksIHZhbHVlXSkgPT4gKHsNCiAgICAgICAgICAgICAgbWF0ZXJpYWxJZDoga2V5LA0KICAgICAgICAgICAgICAuLi52YWx1ZQ0KICAgICAgICAgICAgfSkpOw0KDQogICAgICAgICAgICAvLyDliJ3lp4vljJYgbWF0ZXJpYWxTZWxlY3Rpb25MaXN077ya5YWo6YOo6YCJ5LiK5LiU5pWw6YeP5Li65Ymp5L2Z5pWw6YePDQogICAgICAgICAgICB0aGlzLm1hdGVyaWFsU2VsZWN0aW9uTGlzdCA9ICh0aGlzLnBsYW5JbmZvLm1hdGVyaWFscyB8fCBbXSkubWFwKG1hdCA9PiB7DQogICAgICAgICAgICAgIGNvbnN0IHVzZWROdW0gPSAodGhpcy50YXNrTWF0ZXJpYWxMaXN0TWFwLmdldChtYXQubWF0ZXJpYWxJZCk/LnVzZWROdW0pIHx8IDA7DQogICAgICAgICAgICAgIGNvbnN0IHJlbWFpbmluZ051bSA9IE1hdGgubWF4KChtYXQucGxhbk51bSB8fCAwKSAtIHVzZWROdW0sIDApOw0KDQogICAgICAgICAgICAgIHJldHVybiB7DQogICAgICAgICAgICAgICAgbWF0ZXJpYWxJZDogbWF0Lm1hdGVyaWFsSWQsDQogICAgICAgICAgICAgICAgbWF0ZXJpYWxOYW1lOiBtYXQubWF0ZXJpYWxOYW1lLA0KICAgICAgICAgICAgICAgIG1hdGVyaWFsU3BlYzogbWF0Lm1hdGVyaWFsU3BlYywNCiAgICAgICAgICAgICAgICBwbGFuTnVtOiBtYXQucGxhbk51bSwNCiAgICAgICAgICAgICAgICB1c2VkTnVtOiB1c2VkTnVtLA0KICAgICAgICAgICAgICAgIHJlbWFpbmluZ051bTogcmVtYWluaW5nTnVtLA0KICAgICAgICAgICAgICAgIGN1cnJlbnROdW06IHJlbWFpbmluZ051bQ0KICAgICAgICAgICAgICB9Ow0KICAgICAgICAgICAgfSk7DQoNCiAgICAgICAgICAgIHRoaXMubWF0ZXJpYWxTZWxlY3Rpb25MaXN0ID0gdGhpcy5tYXRlcmlhbFNlbGVjdGlvbkxpc3QuZmlsdGVyKGl0ZW0gPT4gaXRlbS5yZW1haW5pbmdOdW0gPiAwKTsNCiAgICAgICAgICB9KTsNCiAgICAgICAgfSk7DQogICAgICB9DQoNCiAgICAgICAgIC8vIOWIpOaWremdnuiuoemHj+S4lHRhc2tUeXBl5Li6MeeahOaDheWGtQ0KICAgICAgaWYgKHRoaXMucGxhbkluZm8ubWVhc3VyZUZsYWcgPT0gMCkgew0KICAgICAgICBpZiAodGhpcy5kaXNwYXRjaEZvcm0udGFza1R5cGUgPT0gMSkgew0KICAgICAgICAgIC8vIOajgOafpeaYr+WQpuW3sue7j+aciXRhc2tUeXBl5Li6MeeahOS7u+WKoQ0KICAgICAgICAgIGNvbnN0IGhhc1R5cGUxVGFzayA9IHRoaXMudGFza0xpc3RJbmZvLnNvbWUodGFzayA9PiB0YXNrLnRhc2tUeXBlID09PSAxKTsNCiAgICAgICAgICBpZiAoaGFzVHlwZTFUYXNrKSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+mdnuiuoemHj+WPquiDvea0vui9puWHuuWOguS4gOasoScpOw0KICAgICAgICAgICAgcmV0dXJuOw0KICAgICAgICAgIH0NCiAgICAgICAgICBjb25zb2xlLmxvZygiaGFzVHlwZTFUYXNrIiwgaGFzVHlwZTFUYXNrKQ0KICAgICAgICB9DQogICAgICB9DQoNCg0KICAgIH0sDQoNCiAgICAvLyDph43nva7mtL7ovabooajljZUNCiAgICByZXNldERpc3BhdGNoRm9ybSgpIHsNCiAgICAgIHRoaXMuJHJlZnMuZGlzcGF0Y2hGb3JtICYmIHRoaXMuJHJlZnMuZGlzcGF0Y2hGb3JtLnJlc2V0RmllbGRzKCk7DQogICAgICB0aGlzLm1hdGVyaWFsU2VsZWN0aW9uTGlzdCA9IFt7DQogICAgICAgIG1hdGVyaWFsSWQ6IG51bGwsDQogICAgICAgIG1hdGVyaWFsTmFtZTogJycsDQogICAgICAgIG1hdGVyaWFsU3BlYzogJycsDQogICAgICAgIHBsYW5OdW06IDAsDQogICAgICAgIHJlbWFpbmluZ051bTogMCwNCiAgICAgICAgdXNlZE51bTogMCwNCiAgICAgICAgY3VycmVudE51bTogMA0KICAgICAgfV07DQogICAgfSwNCg0KICAgIC8vIOiOt+WPluW3sua0vui9pueahOeJqei1hOWIl+ihqA0KICAgIGdldFRhc2tNYXRlcmlhbExpc3QoKSB7DQogICAgICAvLyDku450YXNrTGlzdEluZm/kuK3ojrflj5blt7LmtL7ovabnmoTnianotYTkv6Hmga8NCiAgICAgIHRoaXMudGFza01hdGVyaWFsTGlzdE1hcC5jbGVhcigpOw0KICAgICAgdGhpcy50YXNrTGlzdEluZm8uZm9yRWFjaCh0YXNrID0+IHsNCiAgICAgICAgY29uc3QgcGFyYW1zID0gew0KICAgICAgICAgIHRhc2tObzogdGFzay50YXNrTm8sDQogICAgICAgIH07DQogICAgICAgIGxpc3RUYXNrTWF0ZXJpYWwocGFyYW1zKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgICBjb25zb2xlLmxvZygibGlzdFRhc2tNYXRlcmlhbCIsIHJlc3BvbnNlLnJvd3MpOw0KICAgICAgICAgIGxldCB0YXNrTWF0ZXJpYWxzID0gW107DQogICAgICAgICAgdGFza01hdGVyaWFscyA9IHJlc3BvbnNlLnJvd3M7DQogICAgICAgICAgdGFza01hdGVyaWFscy5mb3JFYWNoKG1hdGVyaWFsID0+IHsNCiAgICAgICAgICAgIGlmICghdGhpcy50YXNrTWF0ZXJpYWxMaXN0TWFwLmhhcyhtYXRlcmlhbC5tYXRlcmlhbElkKSkgew0KICAgICAgICAgICAgICB0aGlzLnRhc2tNYXRlcmlhbExpc3RNYXAuc2V0KG1hdGVyaWFsLm1hdGVyaWFsSWQsIHsNCiAgICAgICAgICAgICAgICB0YXNrTWF0ZXJpYWxJbmZvOiBtYXRlcmlhbCwNCiAgICAgICAgICAgICAgICB1c2VkTnVtOiBtYXRlcmlhbC5wbGFuTnVtDQogICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgY29uc3QgZXhpc3RpbmdNYXRlcmlhbCA9IHRoaXMudGFza01hdGVyaWFsTGlzdE1hcC5nZXQobWF0ZXJpYWwubWF0ZXJpYWxJZCk7DQogICAgICAgICAgICAgIGV4aXN0aW5nTWF0ZXJpYWwudXNlZE51bSArPSBtYXRlcmlhbC5wbGFuTnVtOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pOw0KICAgICAgICAgIC8vIOWwhnRhc2tNYXRlcmlhbExpc3RNYXDovazmjaLkuLrmlbDnu4Tpm4blkIgNCiAgICAgICAgICB0aGlzLnRhc2tNYXRlcmlhbExpc3QgPSBBcnJheS5mcm9tKHRoaXMudGFza01hdGVyaWFsTGlzdE1hcCwgKFtrZXksIHZhbHVlXSkgPT4gKHsNCiAgICAgICAgICAgIG1hdGVyaWFsSWQ6IGtleSwNCiAgICAgICAgICAgIC4uLnZhbHVlDQogICAgICAgICAgfSkpOw0KICAgICAgICAgIGNvbnNvbGUubG9nKCJ0YXNrTWF0ZXJpYWxBcnJheSIsIHRoaXMudGFza01hdGVyaWFsTGlzdCk7DQogICAgICAgICAgY29uc29sZS5sb2coInRhc2tNYXRlcmlhbExpc3RNYXAiLCB0aGlzLnRhc2tNYXRlcmlhbExpc3RNYXApOw0KICAgICAgICB9KTsNCiAgICAgIH0pOw0KICAgIH0sDQoNCiAgICAvLyDmt7vliqDnianotYTooYwNCiAgICBhZGRNYXRlcmlhbFJvdygpIHsNCiAgICAgIHRoaXMubWF0ZXJpYWxTZWxlY3Rpb25MaXN0LnB1c2goew0KICAgICAgICBtYXRlcmlhbElkOiBudWxsLA0KICAgICAgICBtYXRlcmlhbE5hbWU6ICcnLA0KICAgICAgICBtYXRlcmlhbFNwZWM6ICcnLA0KICAgICAgICBwbGFuTnVtOiAwLA0KICAgICAgICByZW1haW5pbmdOdW06IDAsDQogICAgICAgIHVzZWROdW06IDAsDQogICAgICAgIGN1cnJlbnROdW06IDANCiAgICAgIH0pOw0KICAgIH0sDQoNCiAgICAvLyDnp7vpmaTnianotYTooYwNCiAgICByZW1vdmVNYXRlcmlhbChpbmRleCkgew0KICAgICAgdGhpcy5tYXRlcmlhbFNlbGVjdGlvbkxpc3Quc3BsaWNlKGluZGV4LCAxKTsNCiAgICB9LA0KDQogICAgLy8g5aSE55CG54mp6LWE6YCJ5oup5Y+Y5YyWDQogICAgaGFuZGxlTWF0ZXJpYWxDaGFuZ2Uocm93LCBpbmRleCkgew0KICAgICAgY29uc29sZS5sb2coImhhbmRsZU1hdGVyaWFsQ2hhbmdlIiwgdGhpcy50YXNrTWF0ZXJpYWxMaXN0KTsNCg0KDQogICAgICBjb25zdCBzZWxlY3RlZE1hdGVyaWFsID0gdGhpcy50YXNrTWF0ZXJpYWxMaXN0LmZpbmQoaXRlbSA9PiBpdGVtLm1hdGVyaWFsSWQgPT09IHJvdy5tYXRlcmlhbElkKTsNCiAgICAgIGlmIChzZWxlY3RlZE1hdGVyaWFsKSB7DQogICAgICAgIHJvdy51c2VkTnVtID0gc2VsZWN0ZWRNYXRlcmlhbC51c2VkTnVtOw0KICAgICAgfQ0KICAgICAgY29uc3Qgc2VsZWN0UGxhbk1hdGVyaWFsID0gdGhpcy5wbGFuSW5mby5tYXRlcmlhbHMuZmluZChpdGVtID0+IGl0ZW0ubWF0ZXJpYWxJZCA9PT0gcm93Lm1hdGVyaWFsSWQpOw0KDQogICAgICBpZiAoc2VsZWN0UGxhbk1hdGVyaWFsKSB7DQogICAgICAgIHJvdy5wbGFuTnVtID0gc2VsZWN0UGxhbk1hdGVyaWFsLnBsYW5OdW07DQogICAgICAgIHJvdy5tYXRlcmlhbE5hbWUgPSBzZWxlY3RQbGFuTWF0ZXJpYWwubWF0ZXJpYWxOYW1lOw0KICAgICAgICByb3cubWF0ZXJpYWxTcGVjID0gc2VsZWN0UGxhbk1hdGVyaWFsLm1hdGVyaWFsU3BlYzsNCiAgICAgIH0NCg0KICAgICAgcm93LnJlbWFpbmluZ051bSA9IHJvdy5wbGFuTnVtIC0gcm93LnVzZWROdW07DQogICAgICByb3cuY3VycmVudE51bSA9IHJvdy5wbGFuTnVtIC0gcm93LnVzZWROdW07DQoNCiAgICAgIGNvbnNvbGUubG9nKCJoYW5kbGVNYXRlcmlhbENoYW5nZSIsIHJvdywgaW5kZXgpOw0KDQogICAgfSwNCg0KICAgIC8vIOiOt+WPlueJqei1hOacgOWkp+WPr+eUqOaVsOmHjw0KICAgIGdldE1heEF2YWlsYWJsZU51bShyb3cpIHsNCiAgICAgIGlmICghcm93Lm1hdGVyaWFsSWQpIHJldHVybiAwOw0KDQogICAgICAvLyDku450YXNrTWF0ZXJpYWxMaXN0TWFw5Lit6I635Y+W5bey55So5pWw6YePDQogICAgICBjb25zdCBtYXRlcmlhbEluZm8gPSB0aGlzLnRhc2tNYXRlcmlhbExpc3RNYXAuZ2V0KHJvdy5tYXRlcmlhbElkKTsNCiAgICAgIGNvbnN0IHVzZWROdW0gPSBtYXRlcmlhbEluZm8gPyBtYXRlcmlhbEluZm8udXNlZE51bSA6IDA7DQoNCiAgICAgIHJldHVybiByb3cucGxhbk51bSAtIHVzZWROdW07DQogICAgfSwNCg0KICAgIC8vIOWIpOaWreeJqei1hOaYr+WQpuWPr+mAiQ0KICAgIGlzTWF0ZXJpYWxBdmFpbGFibGUobWF0ZXJpYWwpIHsNCiAgICAgIC8vIOS7jnRhc2tNYXRlcmlhbExpc3RNYXDkuK3ojrflj5blt7LnlKjmlbDph48NCiAgICAgIC8vIGNvbnN0IG1hdGVyaWFsSW5mbyA9IHRoaXMudGFza01hdGVyaWFsTGlzdE1hcC5nZXQobWF0ZXJpYWwuaWQpOw0KICAgICAgLy8gY29uc3QgdXNlZE51bSA9IG1hdGVyaWFsSW5mbyA/IG1hdGVyaWFsSW5mby51c2VkTnVtIDogMDsNCg0KICAgICAgLy8gbGV0IHNlbGVjdGVkID0gZmFsc2U7DQoNCiAgICAgIC8vIHRoaXMuYXZhaWxhYmxlTWF0ZXJpYWxzLmZvckVhY2goaXRlbSA9PiB7DQogICAgICAvLyAgIGlmIChpdGVtLm1hdGVyaWFsSWQgPT09IG1hdGVyaWFsLm1hdGVyaWFsSWQpIHsNCiAgICAgIC8vICAgICBzZWxlY3RlZCA9IHRydWU7DQogICAgICAvLyAgIH0NCiAgICAgIC8vIH0pOw0KDQogICAgICByZXR1cm4gdGhpcy5tYXRlcmlhbFNlbGVjdGlvbkxpc3Quc29tZShyb3cgPT4gcm93Lm1hdGVyaWFsSWQgPT09IG1hdGVyaWFsLm1hdGVyaWFsSWQpOzsNCiAgICB9LA0KDQogICAgLy8g5L+u5pS55o+Q5Lqk5rS+6L2m6KGo5Y2V5pa55rOVDQogICAgc3VibWl0RGlzcGF0Y2hGb3JtKCkgew0KICAgICAgdGhpcy4kcmVmcy5kaXNwYXRjaEZvcm0udmFsaWRhdGUodmFsaWQgPT4gew0KICAgICAgICBpZiAodmFsaWQpIHsNCiAgICAgICAgICAvLyDliKTmlq3pnZ7orqHph4/kuJR0YXNrVHlwZeS4ujHnmoTmg4XlhrUNCiAgICAgICAgICBpZiAodGhpcy5wbGFuSW5mby5tZWFzdXJlRmxhZyA9PSAwKSB7DQogICAgICAgICAgICBpZiAodGhpcy5kaXNwYXRjaEZvcm0udGFza1R5cGUgPT0gMSkgew0KICAgICAgICAgICAgICAvLyDmo4Dmn6XmmK/lkKblt7Lnu4/mnIl0YXNrVHlwZeS4ujHnmoTku7vliqENCiAgICAgICAgICAgICAgY29uc3QgaGFzVHlwZTFUYXNrID0gdGhpcy50YXNrTGlzdEluZm8uc29tZSh0YXNrID0+IHRhc2sudGFza1R5cGUgPT09IDEpOw0KICAgICAgICAgICAgICBpZiAoaGFzVHlwZTFUYXNrKSB7DQogICAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfpnZ7orqHph4/lj6rog73mtL7ovablh7rljoLkuIDmrKEnKTsNCiAgICAgICAgICAgICAgICByZXR1cm47DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQoNCiAgICAgICAgICAvLyDmlrDpm4blkIgNCiAgICAgICAgICBsZXQgcmVzdWx0TGlzdCA9IFtdOw0KDQogICAgICAgICAgY29uc29sZS5sb2coInRoaXMucGxhbkluZm8ubWVhc3VyZUZsYWciLCB0aGlzLnBsYW5JbmZvLm1lYXN1cmVGbGFnKTsNCiAgICAgICAgICBjb25zb2xlLmxvZygidGhpcy5kaXNwYXRjaEZvcm0udGFza1R5cGUiLCB0aGlzLmRpc3BhdGNoRm9ybS50YXNrVHlwZSk7DQoNCiAgICAgICAgICBpZiAodGhpcy5wbGFuSW5mby5tZWFzdXJlRmxhZyA9PSAwICYmIHRoaXMuZGlzcGF0Y2hGb3JtLnRhc2tUeXBlID09IDIpIHsNCiAgICAgICAgICAgIHRoaXMubWF0ZXJpYWxTZWxlY3Rpb25MaXN0LmZvckVhY2goc2VsUm93ID0+IHsNCiAgICAgICAgICAgICAgLy8g5ZyoIHBsYW5JbmZvLm1hdGVyaWFscyDkuK3mn6Xmib7nm7jlkIwgbWF0ZXJpYWxJZCDnmoTlhYPntKANCiAgICAgICAgICAgICAgY29uc3QgcGxhbk1hdGVyaWFsID0gKHRoaXMucGxhbkluZm8ubWF0ZXJpYWxzIHx8IFtdKS5maW5kKA0KICAgICAgICAgICAgICAgIG1hdCA9PiBtYXQubWF0ZXJpYWxJZCA9PT0gc2VsUm93Lm1hdGVyaWFsSWQNCiAgICAgICAgICAgICAgKTsNCiAgICAgICAgICAgICAgaWYgKHBsYW5NYXRlcmlhbCkgew0KICAgICAgICAgICAgICAgIC8vIOa3seaLt+i0neS4gOS7ve+8jOmBv+WFjeW9seWTjeWOn+aVsOaNrg0KICAgICAgICAgICAgICAgIGNvbnN0IG5ld0l0ZW0gPSB7IC4uLnBsYW5NYXRlcmlhbCB9Ow0KICAgICAgICAgICAgICAgIG5ld0l0ZW0ucGxhbk51bSA9IHNlbFJvdy5jdXJyZW50TnVtOyAvLyDorr7nva7kuLrmnKzmrKHmlbDph48NCiAgICAgICAgICAgICAgICByZXN1bHRMaXN0LnB1c2gobmV3SXRlbSk7DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0pOw0KDQogICAgICAgICAgICAvLyByZXN1bHRMaXN0IOWNs+S4uuS9oOmcgOimgeeahOaWsOmbhuWQiA0KICAgICAgICAgICAgY29uc29sZS5sb2coJ3RoaXMubWF0ZXJpYWxTZWxlY3Rpb25MaXN0JywgdGhpcy5tYXRlcmlhbFNlbGVjdGlvbkxpc3QpOw0KICAgICAgICAgICAgY29uc29sZS5sb2coJ3Jlc3VsdExpc3QnLCByZXN1bHRMaXN0KTsNCg0KICAgICAgICAgICAgLy8g54mp6LWE5qCh6aqM77ya5b+F6aG75pyJ54mp6LWEDQogICAgICAgICAgICBpZiAoIXRoaXMubWF0ZXJpYWxTZWxlY3Rpb25MaXN0Lmxlbmd0aCkgew0KICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+ivt+iHs+WwkemAieaLqeS4gOenjeeJqei1hCcpOw0KICAgICAgICAgICAgICByZXR1cm47DQogICAgICAgICAgICB9DQoNCiAgICAgICAgICAgIC8vIOagoemqjOavj+S4gOihjOeJqei1hA0KICAgICAgICAgICAgY29uc3QgaGFzSW52YWxpZE1hdGVyaWFsID0gdGhpcy5tYXRlcmlhbFNlbGVjdGlvbkxpc3Quc29tZShyb3cgPT4gew0KICAgICAgICAgICAgICAvLyDlv4XpobvpgInmi6nnianotYTvvIzmlbDph48+MO+8jOS4lOaVsOmHjzw95Ymp5L2Z5pWw6YePDQogICAgICAgICAgICAgIHJldHVybiAoDQogICAgICAgICAgICAgICAgIXJvdy5tYXRlcmlhbElkIHx8DQogICAgICAgICAgICAgICAgcm93LmN1cnJlbnROdW0gPD0gMCB8fA0KICAgICAgICAgICAgICAgIHJvdy5jdXJyZW50TnVtID4gcm93LnJlbWFpbmluZ051bQ0KICAgICAgICAgICAgICApOw0KICAgICAgICAgICAgfSk7DQoNCiAgICAgICAgICAgIGlmIChoYXNJbnZhbGlkTWF0ZXJpYWwpIHsNCiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfor7fpgInmi6nnianotYTkuJTmnKzmrKHmlbDph4/pnIDlpKfkuo4w5LiU5LiN6LaF6L+H5Ymp5L2Z5pWw6YePJyk7DQogICAgICAgICAgICAgIHJldHVybjsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgY29uc29sZS5sb2coInRoaXMucGxhbkluZm8ubWF0ZXJpYWxzIiwgdGhpcy5wbGFuSW5mby5tYXRlcmlhbHMpOw0KICAgICAgICAgICAgcmVzdWx0TGlzdCA9IHRoaXMucGxhbkluZm8ubWF0ZXJpYWxzID8gdGhpcy5wbGFuSW5mby5tYXRlcmlhbHMubWFwKGl0ZW0gPT4gKHsgLi4uaXRlbSB9KSkgOiBbXTsNCiAgICAgICAgICAgIGNvbnNvbGUubG9nKCIxMjMzMjEiLCByZXN1bHRMaXN0KTsNCiAgICAgICAgICB9DQoNCg0KDQoNCg0KICAgICAgICAgIGlmICh0aGlzLnBsYW5JbmZvLm1lYXN1cmVGbGFnID09IDEgJiYgdGhpcy5kaXNwYXRjaEZvcm0udGFza1R5cGUgIT09IDIpIHsNCiAgICAgICAgICAgIHRoaXMuZGlzcGF0Y2hGb3JtLnRhc2tTdGF0dXMgPSAxOw0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICB0aGlzLmRpc3BhdGNoRm9ybS50YXNrU3RhdHVzID0gNDsNCiAgICAgICAgICB9DQoNCiAgICAgICAgICBpZiAodGhpcy5kaXNwYXRjaEZvcm0udGFza1R5cGUgPT0gMikgew0KICAgICAgICAgICAgdGhpcy5kaXNwYXRjaEZvcm0udGFza1N0YXR1cyA9IDU7DQogICAgICAgICAgfQ0KDQoNCiAgICAgICAgICAvL+aYr+WQpuebtOS+m+m7mOiupOS4ujANCiAgICAgICAgICB0aGlzLmRpc3BhdGNoRm9ybS5pc0RpcmVjdFN1cHBseSA9IDA7DQogICAgICAgICAgLy8gdG9kbyDku7vliqHnirbmgIHnoa7orqQNCiAgICAgICAgICB0aGlzLmRpc3BhdGNoRm9ybS5hcHBseU5vID0gdGhpcy5hcHBseU5vOw0KICAgICAgICAgIHRoaXMuZGlzcGF0Y2hGb3JtLnBsYW5ObyA9IHRoaXMucGxhbkluZm8ucGxhbk5vOw0KICAgICAgICAgIHRoaXMuZGlzcGF0Y2hGb3JtLmNhck51bSA9IHRoaXMuZGlzcGF0Y2hGb3JtLmNhck51bWJlcjsNCiAgICAgICAgICB0aGlzLmRpc3BhdGNoRm9ybS5jb21wYW55TmFtZSA9IHRoaXMuZGlzcGF0Y2hGb3JtLmNvbXBhbnk7DQogICAgICAgICAgdGhpcy5kaXNwYXRjaEZvcm0uZHJpdmVyTGljZW5zZUltZyA9IHRoaXMuZGlzcGF0Y2hGb3JtLmRyaXZlckxpY2Vuc2VJbWdzOw0KICAgICAgICAgIHRoaXMuZGlzcGF0Y2hGb3JtLmRyaXZlck5hbWUgPSB0aGlzLmRpc3BhdGNoRm9ybS5uYW1lOw0KICAgICAgICAgIHRoaXMuZGlzcGF0Y2hGb3JtLm1vYmlsZVBob25lID0gdGhpcy5kaXNwYXRjaEZvcm0ucGhvbmU7DQogICAgICAgICAgdGhpcy5kaXNwYXRjaEZvcm0uZmFjZUltZyA9IHRoaXMuZGlzcGF0Y2hGb3JtLnBob3RvOw0KICAgICAgICAgIHRoaXMuZGlzcGF0Y2hGb3JtLmRyaXZpbmdMaWNlbnNlSW1nID0gdGhpcy5kaXNwYXRjaEZvcm0udmVoaWNsZUxpY2Vuc2VJbWdzOw0KICAgICAgICAgIHRoaXMuZGlzcGF0Y2hGb3JtLmlkQ2FyZE5vID0gdGhpcy5kaXNwYXRjaEZvcm0uaWRDYXJkOw0KICAgICAgICAgIGlmICh0aGlzLmRpc3BhdGNoRm9ybS5zZXggPT0gIjEiKSB7DQogICAgICAgICAgICB0aGlzLmRpc3BhdGNoRm9ybS5zZXggPSAxOw0KICAgICAgICAgIH0gZWxzZSBpZiAodGhpcy5kaXNwYXRjaEZvcm0uc2V4ID09ICIyIikgew0KICAgICAgICAgICAgdGhpcy5kaXNwYXRjaEZvcm0uc2V4ID0gMjsNCiAgICAgICAgICB9DQogICAgICAgICAgaWYgKHRoaXMuZGlzcGF0Y2hGb3JtLnZlaGljbGVFbWlzc2lvblN0YW5kYXJkcyA9PSAi5Zu95LqUIikgew0KICAgICAgICAgICAgdGhpcy5kaXNwYXRjaEZvcm0udmVoaWNsZUVtaXNzaW9uU3RhbmRhcmRzID0gMTsNCiAgICAgICAgICB9IGVsc2UgaWYgKHRoaXMuZGlzcGF0Y2hGb3JtLnZlaGljbGVFbWlzc2lvblN0YW5kYXJkcyA9PSAi5Zu95YWtIikgew0KICAgICAgICAgICAgdGhpcy5kaXNwYXRjaEZvcm0udmVoaWNsZUVtaXNzaW9uU3RhbmRhcmRzID0gMjsNCiAgICAgICAgICB9IGVsc2UgaWYgKHRoaXMuZGlzcGF0Y2hGb3JtLnZlaGljbGVFbWlzc2lvblN0YW5kYXJkcyA9PSAi5paw6IO95rqQIikgew0KICAgICAgICAgICAgdGhpcy5kaXNwYXRjaEZvcm0udmVoaWNsZUVtaXNzaW9uU3RhbmRhcmRzID0gMzsNCiAgICAgICAgICB9DQogICAgICAgICAgY29uc29sZS5sb2coInRoaXMuZGlzcGF0Y2hGb3JtIiwgdGhpcy5kaXNwYXRjaEZvcm0pOw0KDQogICAgICAgICAgbGV0IGRpc3BhdGNoSW5mbyA9IHt9Ow0KICAgICAgICAgIGRpc3BhdGNoSW5mby5jYXJOdW0gPSB0aGlzLmRpc3BhdGNoRm9ybS5jYXJOdW07DQoNCiAgICAgICAgICBpc0FsbG93RGlzcGF0Y2goZGlzcGF0Y2hJbmZvKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgICAgIGxldCByb3cgPSByZXNwb25zZS5kYXRhOw0KICAgICAgICAgICAgaWYgKHJvdyA+IDApIHsNCiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi5b2T5YmN6L2m5pyJ5q2j5Zyo5omn6KGM55qE5Lu75YqhIikNCiAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgIGxldCBwYXJhbSA9IHt9Ow0KICAgICAgICAgICAgICBwYXJhbS5sZWF2ZVRhc2sgPSB0aGlzLmRpc3BhdGNoRm9ybTsNCiAgICAgICAgICAgICAgcGFyYW0ubGVhdmVUYXNrTWF0ZXJpYWxMaXN0ID0gcmVzdWx0TGlzdDsNCiAgICAgICAgICAgICAgYWRkVGFza0FuZE1hdGVyaWFsQW5kQWRkTGVhdmVMb2cocGFyYW0pLnRoZW4ocmVzID0+IHsNCiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygiYWRkVGFza0FuZE1hdGVyaWFsQW5kQWRkTGVhdmVMb2ciLCByZXMpDQogICAgICAgICAgICAgICAgaWYgKHJlcy5jb2RlID09IDIwMCkgew0KICAgICAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfmtL7ovabmiJDlip8nKTsNCiAgICAgICAgICAgICAgICAgIHRoaXMuZGlzcGF0Y2hEaWFsb2dWaXNpYmxlID0gZmFsc2U7DQogICAgICAgICAgICAgICAgICB0aGlzLmdldExpc3RUYXNrSW5mbygpOw0KICAgICAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgICAgICAvLyDlhbbku5blpLHotKXljp/lm6ANCiAgICAgICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IocmVzLm1lc3NhZ2UgfHwgJ+a0vui9puWksei0pScpOw0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgfSkuY2F0Y2goZXJyID0+IHsNCiAgICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKCdkaXNwYXRjaCBlcnJvcjonLCBlcnIpOw0KICAgICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+e9kee7nOW8guW4uO+8jOeojeWQjumHjeivlScpOw0KICAgICAgICAgICAgICB9KTsNCg0KICAgICAgICAgICAgICAvLyBhZGRUYXNrQW5kTWF0ZXJpYWwodGhpcy5kaXNwYXRjaEZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICAgICAgICAvLyAgIGNvbnNvbGUubG9nKCJhZGRUYXNrQW5kTWF0ZXJpYWwiLCByZXNwb25zZSk7DQogICAgICAgICAgICAgIC8vICAgbGV0IHNub3dJZCA9IHJlc3BvbnNlLmRhdGE7DQogICAgICAgICAgICAgIC8vICAgdGhpcy5wbGFuSW5mby5tYXRlcmlhbHMuZm9yRWFjaChpdGVtID0+IHsNCiAgICAgICAgICAgICAgLy8gICAgIGl0ZW0udGFza05vID0gc25vd0lkOw0KICAgICAgICAgICAgICAvLyAgICAgYWRkVGFza01hdGVyaWFsKGl0ZW0pOw0KICAgICAgICAgICAgICAvLyAgIH0pOw0KDQogICAgICAgICAgICAgIC8vICAgY29uc29sZS5sb2coIueUn+aIkOa0vui9puaXpeW/lyIpOw0KDQogICAgICAgICAgICAgIC8vICAgLy/nlJ/miJDmtL7ovabml6Xlv5cNCiAgICAgICAgICAgICAgLy8gICBsZXQgbGVhdmVUYXNrTG9nID0ge307DQoNCg0KICAgICAgICAgICAgICAvLyAgIGxlYXZlVGFza0xvZy5sb2dUeXBlID0gMjsNCiAgICAgICAgICAgICAgLy8gICBsZWF2ZVRhc2tMb2cudGFza05vID0gc25vd0lkOw0KICAgICAgICAgICAgICAvLyAgIGxlYXZlVGFza0xvZy5hcHBseU5vID0gdGhpcy5hcHBseU5vOw0KICAgICAgICAgICAgICAvLyAgIGxlYXZlVGFza0xvZy5pbmZvID0gJ+a0vui9puS7u+WKoeWIm+W7uu+8micgKyB0aGlzLmRpc3BhdGNoRm9ybS5jYXJOdW0gKyAnICcgKyB0aGlzLmRpc3BhdGNoRm9ybS5kcml2ZXJOYW1lDQogICAgICAgICAgICAgIC8vICAgYWRkTGVhdmVMb2cobGVhdmVUYXNrTG9nKTsNCg0KICAgICAgICAgICAgICAvLyAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5rS+6L2m5oiQ5YqfJyk7DQogICAgICAgICAgICAgIC8vICAgdGhpcy5kaXNwYXRjaERpYWxvZ1Zpc2libGUgPSBmYWxzZTsNCiAgICAgICAgICAgICAgLy8gICB0aGlzLmdldExpc3RUYXNrSW5mbygpOw0KICAgICAgICAgICAgICAvLyB9KTsNCg0KICAgICAgICAgICAgICB0aGlzLmRpc3BhdGNoRGlhbG9nVmlzaWJsZSA9IGZhbHNlOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgICAgY29uc29sZS5sb2coInRoaXMuaXNBbGxvd0Rpc3BhdGNoIiwgcmVzcG9uc2UpOw0KICAgICAgICAgIH0pLmNhdGNoKGVyciA9PiB7DQogICAgICAgICAgICBjb25zb2xlLmVycm9yKCdkaXNwYXRjaCBlcnJvcjonLCBlcnIpOw0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign572R57uc5byC5bi477yM56iN5ZCO6YeN6K+VJyk7DQogICAgICAgICAgfSk7DQoNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICByZXR1cm4gZmFsc2U7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQoNCiAgICAvLyDmoLzlvI/ljJbml6XmnJ/ml7bpl7QNCiAgICBmb3JtYXREYXRlVGltZShkYXRlKSB7DQogICAgICBjb25zdCB5ZWFyID0gZGF0ZS5nZXRGdWxsWWVhcigpOw0KICAgICAgY29uc3QgbW9udGggPSAoZGF0ZS5nZXRNb250aCgpICsgMSkudG9TdHJpbmcoKS5wYWRTdGFydCgyLCAnMCcpOw0KICAgICAgY29uc3QgZGF5ID0gZGF0ZS5nZXREYXRlKCkudG9TdHJpbmcoKS5wYWRTdGFydCgyLCAnMCcpOw0KICAgICAgY29uc3QgaG91cnMgPSBkYXRlLmdldEhvdXJzKCkudG9TdHJpbmcoKS5wYWRTdGFydCgyLCAnMCcpOw0KICAgICAgY29uc3QgbWludXRlcyA9IGRhdGUuZ2V0TWludXRlcygpLnRvU3RyaW5nKCkucGFkU3RhcnQoMiwgJzAnKTsNCiAgICAgIGNvbnN0IHNlY29uZHMgPSBkYXRlLmdldFNlY29uZHMoKS50b1N0cmluZygpLnBhZFN0YXJ0KDIsICcwJyk7DQoNCiAgICAgIHJldHVybiBgJHt5ZWFyfS0ke21vbnRofS0ke2RheX0gJHtob3Vyc306JHttaW51dGVzfToke3NlY29uZHN9YDsNCiAgICB9LA0KDQogICAgLy8g5omT5Y2w5Yqf6IO9DQogICAgaGFuZGxlUHJpbnQoKSB7DQogICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+aJk+WNsOWKn+iDveWwmuacquWunueOsCcpOw0KICAgICAgLy8g5a6e6ZmF6aG555uu5Lit5Y+v5Lul6LCD55So5rWP6KeI5Zmo5omT5Y2w5Yqf6IO9DQogICAgICAvLyB3aW5kb3cucHJpbnQoKTsNCiAgICB9LA0KDQogICAgLy8g6L+U5Zue5oyJ6ZKuDQogICAgY2FuY2VsKCkgew0KICAgICAgdGhpcy4kdGFiLmNsb3NlT3BlblBhZ2UodGhpcy4kcm91dGUpOw0KICAgICAgdGhpcy4kcm91dGVyLnB1c2goeyBwYXRoOiAiL2xlYXZlL2xlYXZlUGxhbkxpc3QiLCBxdWVyeTogeyB0OiBEYXRlLm5vdygpIH0gfSk7DQogICAgfSwNCg0KICAgIC8vIOi3s+i9rOWIsOS7u+WKoeivpuaDhemhtemdog0KICAgIGdvVG9UYXNrRGV0YWlsKHJvdykgew0KICAgICAgdGhpcy4kcm91dGVyLnB1c2goew0KICAgICAgICBwYXRoOiBgL2xlYXZlL3BsYW4vdGFzay8ke3Jvdy50YXNrTm99YA0KICAgICAgfSk7DQogICAgfSwNCg0KICAgIGdldFRhc2tUeXBlVGV4dCh0YXNrVHlwZSkgew0KICAgICAgY29uc3Qgc3RhbmRhcmRNYXAgPSB7DQogICAgICAgIDE6ICflh7rljoInLA0KICAgICAgICAyOiAn6L+U5Y6CJywNCiAgICAgICAgMzogJ+i3qOWMuuiwg+aLqCcNCiAgICAgIH07DQogICAgICByZXR1cm4gc3RhbmRhcmRNYXBbdGFza1R5cGVdIHx8ICfmnKrnn6UnOw0KICAgIH0sDQoNCiAgICBnZXRTdGF0dXNUZXh0KHN0YW5kYXJkKSB7DQogICAgICBjb25zdCBzdGFuZGFyZE1hcCA9IHsNCiAgICAgICAgMTogJ+W+hei/h+earumHjScsDQogICAgICAgIDI6ICflvoXoo4XotKcnLA0KICAgICAgICAzOiAn5b6F6L+H5q+b6YeNJywNCiAgICAgICAgNDogJ+W+heWHuuWOgicsDQogICAgICAgIDU6ICflvoXov5TljoInLA0KICAgICAgICA2OiAn5b6F6L+H5q+b6YeNKOWkjeejhSknLA0KICAgICAgICA3OiAn5b6F5Y246LSnJywNCiAgICAgICAgODogJ+W+hei/h+earumHjSjlpI3no4UpJywNCiAgICAgICAgOTogJ+WujOaIkCcNCiAgICAgIH07DQogICAgICByZXR1cm4gc3RhbmRhcmRNYXBbc3RhbmRhcmRdIHx8ICfmnKrnn6UnOw0KICAgIH0sDQoNCiAgICAvLyDojrflj5borqHliJLnirbmgIHnsbvlnosNCiAgICBnZXRQbGFuU3RhdHVzVHlwZShzdGF0dXMpIHsNCiAgICAgIGNvbnN0IHN0YXR1c01hcCA9IHsNCiAgICAgICAgJzEnOiAnd2FybmluZycsICAvLyDlvoXliIbljoLlrqHmibkNCiAgICAgICAgJzInOiAnd2FybmluZycsICAvLyDlvoXliIbljoLlpI3lrqENCiAgICAgICAgJzMnOiAnd2FybmluZycsICAvLyDlvoXnlJ/kuqfmjIfmjKXkuK3lv4PlrqHmibkNCiAgICAgICAgJzQnOiAnc3VjY2VzcycsICAvLyDlrqHmibnlrozmiJANCiAgICAgICAgJzUnOiAncHJpbWFyeScsICAvLyDlt7Llh7rljoINCiAgICAgICAgJzYnOiAnaW5mbycsICAgICAvLyDpg6jliIbmlLbotKcNCiAgICAgICAgJzcnOiAnc3VjY2VzcycsICAvLyDlt7LlrozmiJANCiAgICAgICAgJzExJzogJ2RhbmdlcicsICAvLyDpqbPlm54NCiAgICAgICAgJzEyJzogJ2RhbmdlcicsICAvLyDlup/lvIMNCiAgICAgICAgJzEzJzogJ2RhbmdlcicgICAvLyDov4fmnJ8NCiAgICAgIH0NCiAgICAgIHJldHVybiBzdGF0dXNNYXBbc3RhdHVzXSB8fCAnaW5mbycNCiAgICB9LA0KDQogICAgLyoqDQogICAgICog6I635Y+W6K6h5YiS5LiL5omA5pyJ5Lu75Yqh55qE5Lu75Yqh54mp6LWEDQogICAgICogQHJldHVybnMge1Byb21pc2U8dm9pZD59DQogICAgICovDQogICAgYXN5bmMgZ2V0QWxsVGFza01hdGVyaWFscygpIHsNCiAgICAgIHRyeSB7DQogICAgICAgIC8vIOa4heepuueOsOacieaVsOaNrg0KICAgICAgICB0aGlzLnRhc2tNYXRlcmlhbE1hcC5jbGVhcigpOw0KDQogICAgICAgIC8vIOiOt+WPluivpeiuoeWIkuS4i+aJgOacieS7u+WKoeeahOS7u+WKoeeJqei1hA0KICAgICAgICBjb25zdCBwYXJhbXMgPSB7DQogICAgICAgICAgYXBwbHlObzogdGhpcy5hcHBseU5vDQogICAgICAgIH07DQoNCiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBsaXN0VGFza01hdGVyaWFsKHBhcmFtcyk7DQogICAgICAgIGlmIChyZXNwb25zZS5jb2RlID09PSAyMDAgJiYgcmVzcG9uc2Uucm93cykgew0KICAgICAgICAgIC8vIOWwhuS7u+WKoeeJqei1hOaMieeJqei1hElE5YiG57uE5a2Y5YKoDQogICAgICAgICAgcmVzcG9uc2Uucm93cy5mb3JFYWNoKG1hdGVyaWFsID0+IHsNCiAgICAgICAgICAgIGNvbnN0IGtleSA9IG1hdGVyaWFsLm1hdGVyaWFsSWQ7DQogICAgICAgICAgICBpZiAoIXRoaXMudGFza01hdGVyaWFsTWFwLmhhcyhrZXkpKSB7DQogICAgICAgICAgICAgIHRoaXMudGFza01hdGVyaWFsTWFwLnNldChrZXksIHsNCiAgICAgICAgICAgICAgICBtYXRlcmlhbElkOiBtYXRlcmlhbC5tYXRlcmlhbElkLA0KICAgICAgICAgICAgICAgIG1hdGVyaWFsTmFtZTogbWF0ZXJpYWwubWF0ZXJpYWxOYW1lLA0KICAgICAgICAgICAgICAgIG1hdGVyaWFsU3BlYzogbWF0ZXJpYWwubWF0ZXJpYWxTcGVjLA0KICAgICAgICAgICAgICAgIHBsYW5OdW06IG1hdGVyaWFsLnBsYW5OdW0sDQogICAgICAgICAgICAgICAgdXNlZE51bTogMCwNCiAgICAgICAgICAgICAgICB0YXNrTWF0ZXJpYWxzOiBbXSAvLyDlrZjlgqjmr4/kuKrku7vliqHnmoTlhbfkvZPnianotYTkv6Hmga8NCiAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICB9DQoNCiAgICAgICAgICAgIGNvbnN0IG1hdGVyaWFsSW5mbyA9IHRoaXMudGFza01hdGVyaWFsTWFwLmdldChrZXkpOw0KICAgICAgICAgICAgLy8g57Sv5Yqg5q+P5Liq5Lu75Yqh54mp6LWE55qE6K6h5YiS5pWw6YeP5L2c5Li65bey5L2/55So5pWw6YePDQogICAgICAgICAgICBtYXRlcmlhbEluZm8udXNlZE51bSArPSBtYXRlcmlhbC5wbGFuTnVtOw0KICAgICAgICAgICAgbWF0ZXJpYWxJbmZvLnRhc2tNYXRlcmlhbHMucHVzaCh7DQogICAgICAgICAgICAgIHRhc2tObzogbWF0ZXJpYWwudGFza05vLA0KICAgICAgICAgICAgICBjYXJOdW06IG1hdGVyaWFsLmNhck51bSwNCiAgICAgICAgICAgICAgcGxhbk51bTogbWF0ZXJpYWwucGxhbk51bSwNCiAgICAgICAgICAgICAgY3JlYXRlVGltZTogbWF0ZXJpYWwuY3JlYXRlVGltZQ0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgfSk7DQogICAgICAgIH0NCg0KICAgICAgICAvLyDmm7TmlrDnianotYTpgInmi6nliJfooajkuK3nmoTlt7Lkvb/nlKjmlbDph48NCiAgICAgICAgdGhpcy51cGRhdGVNYXRlcmlhbFVzZWROdW0oKTsNCg0KICAgICAgICBjb25zb2xlLmxvZygnVGFzayBNYXRlcmlhbCBNYXA6JywgdGhpcy50YXNrTWF0ZXJpYWxNYXApOw0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcign6I635Y+W5Lu75Yqh54mp6LWE5aSx6LSlOicsIGVycm9yKTsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign6I635Y+W5Lu75Yqh54mp6LWE5aSx6LSlJyk7DQogICAgICB9DQogICAgfSwNCg0KICAgIC8qKg0KICAgICAqIOabtOaWsOeJqei1hOmAieaLqeWIl+ihqOS4reeahOW3suS9v+eUqOaVsOmHjw0KICAgICAqLw0KICAgIHVwZGF0ZU1hdGVyaWFsVXNlZE51bSgpIHsNCiAgICAgIHRoaXMubWF0ZXJpYWxTZWxlY3Rpb25MaXN0LmZvckVhY2gocm93ID0+IHsNCiAgICAgICAgaWYgKHJvdy5tYXRlcmlhbElkKSB7DQogICAgICAgICAgY29uc3QgbWF0ZXJpYWxJbmZvID0gdGhpcy50YXNrTWF0ZXJpYWxNYXAuZ2V0KHJvdy5tYXRlcmlhbElkKTsNCiAgICAgICAgICBpZiAobWF0ZXJpYWxJbmZvKSB7DQogICAgICAgICAgICAvLyDnm7TmjqXkvb/nlKjntK/liqDnmoTorqHliJLmlbDph4/kvZzkuLrlt7Lkvb/nlKjmlbDph48NCiAgICAgICAgICAgIHJvdy51c2VkTnVtID0gbWF0ZXJpYWxJbmZvLnVzZWROdW07DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KDQogICAgLy8g54mp6LWE56Gu6K6k5oyJ6ZKu54K55Ye75LqL5Lu2DQogICAgYXN5bmMgaGFuZGxlTWF0ZXJpYWxDb25maXJtKCkgew0KICAgICAgdHJ5IHsNCiAgICAgICAgLy8g5qCh6aqM5omA5pyJ5Lu75Yqh55qEdGFza1N0YXR1c+aYr+WQpuS4ujkNCiAgICAgICAgaWYgKHRoaXMudGFza0xpc3RJbmZvICYmIHRoaXMudGFza0xpc3RJbmZvLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICBjb25zdCB1bmZpbmlzaGVkVGFza3MgPSB0aGlzLnRhc2tMaXN0SW5mby5maWx0ZXIodGFzayA9PiB0YXNrLnRhc2tTdGF0dXMgIT09IDkpOw0KICAgICAgICAgIGlmICh1bmZpbmlzaGVkVGFza3MubGVuZ3RoID4gMCkgew0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5a2Y5Zyo5pyq5a6M5oiQ55qE5Lu75Yqh77yM5peg5rOV6L+b6KGM54mp6LWE56Gu6K6kJyk7DQogICAgICAgICAgICByZXR1cm47DQogICAgICAgICAgfQ0KICAgICAgICB9DQoNCiAgICAgICAgLy8g6LCD55So5ZCO56uv5o6l5Y+j77yM5Lyg6YCSYXBwbHlObw0KICAgICAgICBhd2FpdCBjb25maXJtTWF0ZXJpYWwoeyBhcHBseU5vOiB0aGlzLmFwcGx5Tm8gfSk7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn54mp6LWE56Gu6K6k5oiQ5YqfJyk7DQogICAgICAgIC8vIOWIt+aWsOivpuaDhQ0KICAgICAgICB0aGlzLmdldExpc3RUYXNrSW5mbygpOw0KICAgICAgICAvLyDph43mlrDojrflj5ZwbGFuSW5mbw0KICAgICAgICBkZXRhaWxQbGFuKHRoaXMuYXBwbHlObykudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgdGhpcy5wbGFuSW5mbyA9IHJlc3BvbnNlLmRhdGE7DQogICAgICAgIH0pOw0KICAgICAgfSBjYXRjaCAoZSkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfnianotYTnoa7orqTlpLHotKUnKTsNCiAgICAgIH0NCiAgICB9LA0KDQogIH0NCn07DQo="}, {"version": 3, "sources": ["detail.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4cA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;;;;AAKA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;AAMA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;;AAGA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA", "file": "detail.vue", "sourceRoot": "src/views/leave/plan", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-card class=\"box-card\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <h3>申请详情</h3>\r\n      </div>\r\n\r\n      <!-- 基本信息部分 -->\r\n      <div class=\"section-container\">\r\n        <div class=\"section-title\">基本信息</div>\r\n        <el-descriptions :column=\"2\" border>\r\n          <el-descriptions-item label=\"申请编号\">\r\n            <template slot=\"label\"><i class=\"el-icon-document\"></i> 申请编号</template>\r\n            {{ planInfo.applyNo }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"计划号\">\r\n            <template slot=\"label\"><i class=\"el-icon-document\"></i> 计划号</template>\r\n            {{ planInfo.planNo }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"计划状态\">\r\n            <template slot=\"label\"><i class=\"el-icon-s-flag\"></i> 计划状态</template>\r\n            <el-tag :type=\"getPlanStatusType(planInfo.planStatus)\">{{ getPlanStatusText(planInfo.planStatus) }}</el-tag>\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"计划类型\">\r\n            <template slot=\"label\"><i class=\"el-icon-s-order\"></i> 计划类型</template>\r\n            <el-tag :type=\"getPlanTypeTagType(planInfo.planType)\">{{ getPlanTypeText(planInfo.planType) }}</el-tag>\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"业务类型\">\r\n            <template slot=\"label\"><i class=\"el-icon-s-management\"></i> 业务类型</template>\r\n            <el-tag :type=\"getBusinessCategoryTagType(planInfo.businessCategory)\">{{\r\n              getBusinessCategoryText(planInfo.businessCategory) }}</el-tag>\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"是否计量\">\r\n            <template slot=\"label\"><i class=\"el-icon-s-operation\"></i> 是否计量</template>\r\n            <el-tag :type=\"planInfo.measureFlag === 1 ? 'success' : 'danger'\">\r\n              {{ planInfo.measureFlag === 1 ? '计量' : '不计量' }}\r\n            </el-tag>\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"计划量\" v-if=\"planInfo.plannedAmount\">\r\n            <template slot=\"label\"><i class=\"el-icon-document\"></i> 计划量（吨）</template>\r\n            {{ planInfo.plannedAmount }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"是否复审\">\r\n            <template slot=\"label\"><i class=\"el-icon-s-check\"></i> 是否复审</template>\r\n            <el-tag :type=\"planInfo.secApproveFlag === 1 ? 'warning' : 'info'\">\r\n              {{ planInfo.secApproveFlag === 1 ? '是' : '否' }}\r\n            </el-tag>\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"申请单位\" v-if=\"planInfo.sourceCompany\">\r\n            <template slot=\"label\"><i class=\"el-icon-office-building\"></i> 申请单位</template>\r\n            {{ planInfo.sourceCompany }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"收货单位\" v-if=\"planInfo.receiveCompany\">\r\n            <template slot=\"label\"><i class=\"el-icon-school\"></i> 收货单位</template>\r\n            {{ planInfo.receiveCompany }}\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"返回单位\" v-if=\"planInfo.targetCompany\">\r\n            <template slot=\"label\"><i class=\"el-icon-s-home\"></i> 返回单位</template>\r\n            {{ planInfo.targetCompany }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"计划返回时间\" v-if=\"planInfo.planReturnTime\">\r\n            <template slot=\"label\"><i class=\"el-icon-time\"></i> 计划返回时间</template>\r\n            {{ planInfo.planReturnTime }}\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"退货单位\" v-if=\"planInfo.refundDepartment\">\r\n            <template slot=\"label\"><i class=\"el-icon-s-shop\"></i> 退货单位</template>\r\n            {{ planInfo.refundDepartment }}\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"开始时间\" v-if=\"planInfo.startTime\">\r\n            <template slot=\"label\"><i class=\"el-icon-date\"></i> 开始时间</template>\r\n            {{ planInfo.startTime }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"结束时间\" v-if=\"planInfo.endTime\">\r\n            <template slot=\"label\"><i class=\"el-icon-date\"></i> 结束时间</template>\r\n            {{ planInfo.endTime }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"有效期\" v-if=\"!planInfo.endTime\">\r\n            <template slot=\"label\"><i class=\"el-icon-date\"></i> 有效期</template>\r\n            {{ planInfo.expireTime }}\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"监装人\" v-if=\"planInfo.monitor\">\r\n            <template slot=\"label\"><i class=\"el-icon-user\"></i> 监装人</template>\r\n            {{ planInfo.monitor }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"物资专管员\" v-if=\"planInfo.specialManager\">\r\n            <template slot=\"label\"><i class=\"el-icon-s-custom\"></i> 物资专管员</template>\r\n            {{ planInfo.specialManager }}\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"物资类型\" v-if=\"planInfo.itemType\">\r\n            <template slot=\"label\"><i class=\"el-icon-goods\"></i> 物资类型</template>\r\n            <el-tag :type=\"getMaterialTypeTagType(planInfo.itemType)\">\r\n              {{ getMaterialTypeText(planInfo.itemType) }}\r\n            </el-tag>\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"出厂原因\" v-if=\"planInfo.reason\">\r\n            <template slot=\"label\"><i class=\"el-icon-info\"></i> 出厂原因</template>\r\n            {{ planInfo.reason }}\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"合同号\" v-if=\"planInfo.contractNo\">\r\n            <template slot=\"label\"><i class=\"el-icon-tickets\"></i> 合同号</template>\r\n            {{ planInfo.contractNo }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"申请时间\" v-if=\"planInfo.applyTime\">\r\n            <template slot=\"label\"><i class=\"el-icon-timer\"></i> 申请时间</template>\r\n            {{ planInfo.applyTime }}\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"申请人\" v-if=\"planInfo.applyUserName\">\r\n            <template slot=\"label\"><i class=\"el-icon-user-solid\"></i> 申请人</template>\r\n            {{ planInfo.applyUserName }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"计划量\" v-if=\"planInfo.planned_amount\">\r\n            <template slot=\"label\"><i class=\"el-icon-user-solid\"></i> 计划量(吨)</template>\r\n            {{ planInfo.planned_amount }}\r\n          </el-descriptions-item>\r\n        </el-descriptions>\r\n      </div>\r\n\r\n      <!-- 新增图片列表部分 -->\r\n      <div class=\"section-container\" v-if=\"imageList.length > 0\">\r\n        <div class=\"section-title\">\r\n          <span><i class=\"el-icon-picture-outline\"></i> 申请图片</span>\r\n        </div>\r\n        <div class=\"image-container\">\r\n          <viewer :images=\"imageList\">\r\n            <div class=\"image-list\">\r\n              <div class=\"image-item\" v-for=\"(image, index) in imageList\" :key=\"'img-' + index\">\r\n                <img :src=\"image.url\" :alt=\"image.name\">\r\n                <div class=\"image-name\">{{ image.name }}</div>\r\n              </div>\r\n            </div>\r\n          </viewer>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 新增文件列表部分 -->\r\n      <div class=\"section-container\" v-if=\"fileList.length > 0\">\r\n        <div class=\"section-title\">\r\n          <span><i class=\"el-icon-document\"></i> 申请附件</span>\r\n        </div>\r\n        <div class=\"file-container\">\r\n          <div class=\"file-list\">\r\n            <div class=\"file-item\" v-for=\"(file, index) in fileList\" :key=\"'file-' + index\"\r\n              @click=\"downloadFile(file.url, file.name)\">\r\n              <i class=\"el-icon-document file-icon\"></i>\r\n              <div class=\"file-name\">{{ file.name }}</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 物资列表部分 -->\r\n      <div class=\"section-container\">\r\n        <div class=\"section-title\">物资列表</div>\r\n        <el-table :data=\"planInfo.materials\" style=\"width: 100%\" border>\r\n          <el-table-column type=\"index\" width=\"50\" label=\"序号\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"materialName\" label=\"物资名称\" width=\"180\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"materialSpec\" label=\"物资型号规格\" width=\"180\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"planNum\" label=\"计划数量\" width=\"120\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"measureUnit\" label=\"单位\" width=\"120\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"remark\" label=\"备注\" width=\"150\">\r\n          </el-table-column>\r\n        </el-table>\r\n      </div>\r\n\r\n      <!-- 派车信息部分 -->\r\n      <div class=\"section-container\">\r\n        <div class=\"section-title\">\r\n          <span>派车信息</span>\r\n          <el-button type=\"primary\" size=\"small\" icon=\"el-icon-truck\" @click=\"openDispatchDialog\"\r\n            :disabled=\"!canDispatchCar\" class=\"dispatch-btn\">\r\n            派车\r\n          </el-button>\r\n        </div>\r\n\r\n        <el-table v-if=\"taskListInfo.length > 0\" :data=\"taskListInfo\" style=\"width: 100%\" border>\r\n          <el-table-column type=\"index\" width=\"50\" label=\"序号\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"carNum\" label=\"车牌号\" width=\"120\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"driverName\" label=\"司机姓名\" width=\"100\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"mobilePhone\" label=\"司机手机号\" width=\"120\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"taskType\" label=\"任务类型\" width=\"120\" :formatter=\"taskTypeFormat\">\r\n          </el-table-column>\r\n          <!-- <el-table-column prop=\"planNum\" label=\"计划数量\" width=\"180\" v-if=\"planInfo.measureFlag == 0\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"doormanReceiveNum\" label=\"门卫确认数量\" width=\"180\" v-if=\"planInfo.measureFlag == 0 \">\r\n          </el-table-column>\r\n          <el-table-column prop=\"factoryReceiveNum\" label=\"分厂确认数量\" width=\"180\" v-if=\"planInfo.measureFlag == 0\">\r\n          </el-table-column> -->\r\n          <!-- <el-table-column prop=\"planNum\" label=\"计划数量\" width=\"120\" v-if=\"planInfo.measureFlag == 1\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"measureUnit\" label=\"计量单位\" width=\"120\" v-if=\"planInfo.measureFlag == 1\">\r\n          </el-table-column> -->\r\n          <el-table-column prop=\"tareWeight\" label=\"皮重\" width=\"100\" v-if=\"planInfo.measureFlag == 1\">\r\n            <template slot-scope=\"scope\">\r\n              {{ scope.row.tare || '-' }} {{ scope.row.tare ? 't' : '' }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"grossWeight\" label=\"毛重\" width=\"100\" v-if=\"planInfo.measureFlag == 1\">\r\n            <template slot-scope=\"scope\">\r\n              {{ scope.row.gross || '-' }} {{ scope.row.gross ? 't' : '' }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"tareWeight\" label=\"皮重(复磅)\" width=\"100\"\r\n            v-if=\"planInfo.measureFlag == 1 && planInfo.planType !== 1\">\r\n            <template slot-scope=\"scope\">\r\n              {{ scope.row.secTare || '-' }} {{ scope.row.secTare ? 't' : '' }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"grossWeight\" label=\"毛重(复磅)\" width=\"100\"\r\n            v-if=\"planInfo.measureFlag == 1 && planInfo.planType !== 1\">\r\n            <template slot-scope=\"scope\">\r\n              {{ scope.row.secGross || '-' }} {{ scope.row.secGross ? 't' : '' }}\r\n            </template>\r\n          </el-table-column>\r\n\r\n\r\n          <el-table-column prop=\"createTime\" label=\"派车时间\" width=\"160\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"taskStatus\" label=\"任务状态\" width=\"120\" :formatter=\"taskStatusFormat\">\r\n          </el-table-column>\r\n          <el-table-column label=\"操作\" width=\"120\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button size=\"mini\" type=\"text\" icon=\"el-icon-view\" @click=\"goToTaskDetail(scope.row)\">\r\n                任务详情\r\n              </el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n\r\n        <div v-else class=\"empty-data\">\r\n          <el-empty description=\"暂无派车记录\"></el-empty>\r\n        </div>\r\n\r\n        <!-- 物资确认按钮 -->\r\n        <div style=\"text-align: right; margin-top: 15px;\">\r\n          <el-button type=\"primary\" icon=\"el-icon-finished\" @click=\"handleMaterialConfirm\">\r\n            物资确认\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n      <!-- v-if=\"canShowMaterialConfirm\" -->\r\n\r\n      <!-- 审核内容部分 -->\r\n      <div class=\"section-container\" v-if=\"planInfo.approveButtonShow\">\r\n        <div class=\"section-title\">审核内容</div>\r\n        <el-form label-width=\"80px\" :model=\"approveForm\" ref=\"approveForm\">\r\n          <el-form-item label=\"审核建议\">\r\n            <el-input type=\"textarea\" v-model=\"approveForm.approveContent\" :rows=\"4\" placeholder=\"请输入审核建议\"\r\n              maxlength=\"200\" show-word-limit></el-input>\r\n            <div style=\"color: #909399; font-size: 12px; margin-top: 5px; margin-bottom: 10px;\">审核建议可不填，默认通过为同意，驳回为拒绝\r\n            </div>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n\r\n      <!-- 日志列表部分 -->\r\n      <div class=\"section-container\">\r\n        <div class=\"section-title\">日志列表</div>\r\n        <el-timeline>\r\n          <el-timeline-item v-for=\"(log, index) in planInfo.leaveLogs\" :key=\"index\" :timestamp=\"log.createTime\"\r\n            :color=\"getLogColor(log)\">\r\n            {{ log.info }}\r\n          </el-timeline-item>\r\n        </el-timeline>\r\n      </div>\r\n\r\n      <!-- 固定底部操作栏 -->\r\n      <div class=\"fixed-bottom-action\">\r\n        <el-row :gutter=\"10\" type=\"flex\" justify=\"center\" align=\"middle\">\r\n          <!-- 返回按钮 -->\r\n          <el-col :span=\"2\" :xs=\"6\">\r\n            <el-button size=\"medium\" @click=\"cancel\">返回</el-button>\r\n          </el-col>\r\n\r\n          <!-- 通过按钮 -->\r\n          <el-col :span=\"2\" :xs=\"6\" v-if=\"planInfo.approveButtonShow\">\r\n            <el-button size=\"medium\" type=\"primary\" icon=\"el-icon-check\" @click=\"handleApprove\">通过</el-button>\r\n          </el-col>\r\n\r\n          <!-- 驳回按钮 -->\r\n          <el-col :span=\"2\" :xs=\"6\" v-if=\"planInfo.rejectButtonShow\">\r\n            <el-button size=\"medium\" type=\"danger\" icon=\"el-icon-close\" @click=\"handleReject\">驳回</el-button>\r\n          </el-col>\r\n\r\n          <!-- 废弃按钮 -->\r\n          <el-col :span=\"2\" :xs=\"6\" v-if=\"planInfo.discardButtonShow\">\r\n            <el-button size=\"medium\" type=\"success\" icon=\"el-icon-delete\" @click=\"handleDiscard\">废弃</el-button>\r\n          </el-col>\r\n        </el-row>\r\n      </div>\r\n\r\n    </el-card>\r\n\r\n    <!-- 派车弹框 -->\r\n    <el-dialog title=\"派车\" :visible.sync=\"dispatchDialogVisible\" width=\"1200px\" append-to-body destroy-on-close\r\n      @closed=\"resetDispatchForm\">\r\n      <el-form ref=\"dispatchForm\" :model=\"dispatchForm\" :rules=\"dispatchRules\" label-width=\"100px\"\r\n        class=\"dispatch-form\">\r\n\r\n        <el-form-item label=\"货车司机\" prop=\"driverId\" :rules=\"[{ required: true, message: '司机信息不能为空' }]\">\r\n          <el-select style=\"width:300px\" v-model=\"dispatchForm.driverId\" filterable :filter-method=\"filterDriverData\"\r\n            placeholder=\"请选择（如果显示不出请在输入框搜索）\" @change=\"handleDriverChange\">\r\n            <el-option v-for=\"item in filteredDriverOptions.slice(0, 50)\" :key=\"item.id\" :label=\"item.driverInfo\"\r\n              :value=\"item.id\">\r\n            </el-option>\r\n          </el-select>\r\n          <el-button style=\"margin-left: 10px; font-size: 14px; padding: 5px 10px;\" type=\"primary\"\r\n            @click=\"openNewDriverWindow\">前往新增或修改司机信息\r\n          </el-button>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"司机名称\" prop=\"name\" v-if=\"dispatchForm.name != null\">\r\n          <el-input v-model=\"dispatchForm.name\" placeholder=\"请输入司机名称\" disabled style=\"width:300px\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"手机号\" prop=\"phone\" v-if=\"dispatchForm.phone != null\">\r\n          <el-input v-model=\"dispatchForm.phone\" placeholder=\"请输入手机号\" disabled style=\"width:300px\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"身份证号\" prop=\"idCard\" v-if=\"dispatchForm.idCard != null\">\r\n          <el-input v-model=\"dispatchForm.idCard\" placeholder=\"请输入身份证号\" disabled style=\"width:300px\" />\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"人脸照片\" prop=\"faceImg\" v-if=\"dispatchForm.photo != null && dispatchForm.photo != ''\">\r\n          <div class=\"image-grid\">\r\n            <!-- <el-image v-for=\"(image, index) in form.faceImgList\" :key=\"index\" :src=\"image\" fit=\"cover\"\r\n                      lazy></el-image> -->\r\n            <el-image style=\"width: 200px; height: 200px\" :src=\"dispatchForm.photo\" fit=\"cover\"></el-image>\r\n          </div>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"驾驶证照片\" prop=\"driverLicenseImgs\"\r\n          v-if=\"dispatchForm.driverLicenseImgs != null && dispatchForm.driverLicenseImgs != ''\">\r\n          <div class=\"image-grid\">\r\n            <!-- <el-image v-for=\"(image, index) in form.drivingLicenseImgList\" :key=\"index\" :src=\"image\" fit=\"cover\"\r\n              lazy></el-image> -->\r\n            <el-image style=\"width: 200px; height: 200px\" :src=\"dispatchForm.driverLicenseImgs\" fit=\"cover\"></el-image>\r\n          </div>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"行驶证照片\" prop=\"vehicleLicenseImgs\"\r\n          v-if=\"dispatchForm.vehicleLicenseImgs != null && dispatchForm.vehicleLicenseImgs != ''\">\r\n          <div class=\"image-grid\">\r\n            <!-- <el-image v-for=\"(image, index) in form.driverLicenseImgList\" :key=\"index\" :src=\"image\" fit=\"cover\"\r\n              lazy></el-image> -->\r\n            <el-image style=\"width: 200px; height: 200px\" :src=\"dispatchForm.vehicleLicenseImgs\" fit=\"cover\"></el-image>\r\n          </div>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"货车\" prop=\"carUUId\" :rules=\"[{ required: true, message: '货车信息不能为空' }]\">\r\n          <el-select style=\"width:300px\" v-model=\"dispatchForm.carUUId\" filterable :filter-method=\"filterCarData\"\r\n            placeholder=\"请选择（如果显示不出请在输入框搜索）\" @change=\"handleCarChange\">\r\n            <el-option v-for=\"item in filteredCarOptions.slice(0, 50)\" :key=\"item.id\" :label=\"item.carNumber\"\r\n              :value=\"item.id\">\r\n            </el-option>\r\n          </el-select>\r\n\r\n          <el-button style=\"margin-left: 10px; font-size: 14px; padding: 5px 10px;\" type=\"primary\"\r\n            @click=\"openNewCarWindow\">前往新增或修改货车信息\r\n          </el-button>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"车牌号\" prop=\"carNumber\" v-if=\"dispatchForm.carNumber != null\">\r\n          <el-input v-model=\"dispatchForm.carNumber\" placeholder=\"请输入车牌号\" disabled style=\"width:300px\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"车辆排放标准\" prop=\"vehicleEmissionStandards\"\r\n          v-if=\"dispatchForm.vehicleEmissionStandards != null\">\r\n          <el-select v-model=\"dispatchForm.vehicleEmissionStandards\" placeholder=\"请选择车辆排放标准\" disabled\r\n            style=\"width:300px\">\r\n            <el-option v-for=\"dict in vehicleEmissionStandardsOptions\" :key=\"dict.dictValue\" :label=\"dict.dictLabel\"\r\n              :value=\"dict.dictValue\"></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"任务类型\" prop=\"taskType\" :rules=\"[{ required: true, message: '任务类型不能为空' }]\"\r\n          v-if=\"isTaskTypeEdit == true\">\r\n          <el-select v-model=\"dispatchForm.taskType\" placeholder=\"请选择车任务类型\" style=\"width:300px\">\r\n            <el-option v-for=\"dict in taskTypeOptions\" :key=\"dict.dictValue\" :label=\"dict.dictLabel\"\r\n              :value=\"dict.dictValue\"></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <!-- 新增物资选择表格 -->\r\n        <el-form-item label=\"物资选择\" prop=\"selectedMaterials\"\r\n          v-if=\"planInfo.measureFlag == 0 && dispatchForm.taskType == 2\">\r\n          <el-table :data=\"materialSelectionList\" border style=\"width: 100%\">\r\n            <el-table-column type=\"index\" width=\"50\" label=\"序号\"></el-table-column>\r\n            <el-table-column prop=\"materialName\" label=\"物资名称\" width=\"180\">\r\n              <template slot-scope=\"scope\">\r\n                <el-select v-model=\"scope.row.materialId\" placeholder=\"请选择物资\"\r\n                  @change=\"handleMaterialChange(scope.row, scope.$index)\">\r\n                  <el-option v-for=\"item in availableMaterials\" :key=\"item.materialId\" :label=\"item.materialName\"\r\n                    :value=\"item.materialId\" :disabled=\"isMaterialAvailable(item)\">\r\n\r\n                  </el-option>\r\n                </el-select>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"materialSpec\" label=\"物资规格\" width=\"180\">\r\n              <template slot-scope=\"scope\">\r\n                {{ scope.row.materialSpec }}\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"planNum\" label=\"计划数量\" width=\"120\">\r\n              <template slot-scope=\"scope\">\r\n                {{ scope.row.planNum }}\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"remainingNum\" label=\"剩余数量\" width=\"120\">\r\n              <template slot-scope=\"scope\">\r\n                {{ scope.row.remainingNum }}\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"currentNum\" label=\"本次数量\" width=\"150\">\r\n              <template slot-scope=\"scope\">\r\n                <el-input-number v-model=\"scope.row.currentNum\" :min=\"0\" :max=\"getMaxAvailableNum(scope.row)\"\r\n                  @change=\"handleNumChange($event, scope.$index)\" :disabled=\"!scope.row.materialId\">\r\n                </el-input-number>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"操作\" width=\"80\">\r\n              <template slot-scope=\"scope\">\r\n                <el-button type=\"text\" icon=\"el-icon-delete\" @click=\"removeMaterial(scope.$index)\"\r\n                  :disabled=\"materialSelectionList.length === 1\">\r\n                  删除\r\n                </el-button>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n          <div style=\"margin-top: 10px;\">\r\n            <el-button type=\"primary\" size=\"small\" icon=\"el-icon-plus\" @click=\"addMaterialRow\">添加物资</el-button>\r\n          </div>\r\n        </el-form-item>\r\n\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dispatchDialogVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"submitDispatchForm\">确 认</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { detailPlan, approve, discard, listTaskMaterial, confirmMaterial } from \"@/api/leave/plan\";\r\nimport { listTask, addTask, addTaskMaterial, addTaskAndMaterial, addLeaveLog, isAllowDispatch, addTaskAndMaterialAndAddLeaveLog } from \"@/api/leave/task\";\r\nimport { listAllDriver, getXctgDriverUserList, getXctgDriverCarList } from \"@/api/dgcb/driver/driver\";\r\nimport { mount } from \"sortablejs\";\r\nexport default {\r\n  name: \"DetailLeavePlan\",\r\n  data() {\r\n    // 验证车牌号\r\n    const validateCarNumber = (rule, value, callback) => {\r\n      const pattern = /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-Z0-9]{4}[A-Z0-9挂学警港澳]{1}$/;\r\n      if (!pattern.test(value)) {\r\n        callback(new Error('请输入正确的车牌号'));\r\n      } else {\r\n        callback();\r\n      }\r\n    };\r\n\r\n    // 验证手机号\r\n    const validatePhone = (rule, value, callback) => {\r\n      const pattern = /^1[3-9]\\d{9}$/;\r\n      if (!pattern.test(value)) {\r\n        callback(new Error('请输入正确的手机号'));\r\n      } else {\r\n        callback();\r\n      }\r\n    };\r\n\r\n    // 验证身份证号\r\n    const validateIdCard = (rule, value, callback) => {\r\n      const pattern = /(^\\d{15}$)|(^\\d{18}$)|(^\\d{17}(\\d|X|x)$)/;\r\n      if (!pattern.test(value)) {\r\n        callback(new Error('请输入正确的身份证号'));\r\n      } else {\r\n        callback();\r\n      }\r\n    };\r\n\r\n    return {\r\n      isTaskTypeEdit: true,\r\n      vehicleEmissionStandardsOptions: [],\r\n      taskTypeOptions: [],\r\n      carList: [],\r\n      searchCarQuery: '',\r\n      filteredCarOptions: [],\r\n      driverList: [],\r\n      searchDriverQuery: '',\r\n      filteredDriverOptions: [],\r\n      //审核表单\r\n      approveForm: {\r\n        applyNo: null,\r\n        approveContent: '',//审核意见\r\n        approveFlag: true,//审核状态\r\n      },\r\n\r\n      // 图片列表\r\n      imageList: [],\r\n\r\n      // 文件列表\r\n      fileList: [],\r\n\r\n      // 派车弹框可见性\r\n      dispatchDialogVisible: false,\r\n\r\n      taskListInfo: [],\r\n\r\n      // 派车表单数据\r\n      dispatchForm: {\r\n        // carNumber: '',\r\n        // driverName: '',\r\n        // driverPhone: '',\r\n        // driverIdCard: ''\r\n      },\r\n\r\n      // 派车表单验证规则\r\n      dispatchRules: {\r\n        carNumber: [\r\n          { required: true, message: '请输入车牌号', trigger: 'blur' },\r\n          { validator: validateCarNumber, trigger: 'blur' }\r\n        ],\r\n        driverName: [\r\n          { required: true, message: '请输入司机姓名', trigger: 'blur' },\r\n          { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }\r\n        ],\r\n        driverPhone: [\r\n          { required: true, message: '请输入司机手机号', trigger: 'blur' },\r\n          { validator: validatePhone, trigger: 'blur' }\r\n        ],\r\n        driverIdCard: [\r\n          { required: true, message: '请输入司机身份证号', trigger: 'blur' },\r\n          { validator: validateIdCard, trigger: 'blur' }\r\n        ]\r\n      },\r\n\r\n      // 派车列表数据\r\n      dispatchList: [\r\n        {\r\n          id: 1,\r\n          carNumber: '京A12345',\r\n          driverName: '王小明',\r\n          driverPhone: '13800138000',\r\n          driverIdCard: '110101199001010001',\r\n          dispatchTime: '2025-03-18 09:30:00',\r\n          status: 2,\r\n          tareWeight: 8500,\r\n          grossWeight: 15800,\r\n          recheckedGrossWeight: 15750,\r\n          recheckedTareWeight: 8480\r\n        },\r\n        {\r\n          id: 2,\r\n          carNumber: '京B98765',\r\n          driverName: '李大壮',\r\n          driverPhone: '13900139000',\r\n          driverIdCard: '110101199102020002',\r\n          dispatchTime: '2025-03-19 14:15:00',\r\n          status: 1,\r\n          tareWeight: 7800,\r\n          grossWeight: 12600,\r\n          recheckedGrossWeight: null,\r\n          recheckedTareWeight: null\r\n        }\r\n      ],\r\n\r\n      // 计划详情信息\r\n      planInfo: {},\r\n      applyNo: null,\r\n      taskQueryParams: {\r\n        applyNo: null,\r\n      },\r\n\r\n      taskMaterialList: null,\r\n      // 物资选择相关数据\r\n      materialSelectionList: [{\r\n        materialId: null,\r\n        materialName: '',\r\n        materialSpec: '',\r\n        planNum: 0,\r\n        usedNum: 0,\r\n        remainingNum: 0,\r\n        currentNum: 0\r\n      }],\r\n      availableMaterials: [], // 可选的物资列表\r\n      taskMaterialListMap: new Map(), // 已派车的物资列表\r\n      taskMaterialMap: new Map(), // 存储所有任务物资的映射\r\n    };\r\n  },\r\n  computed: {\r\n    // 判断是否可以派车\r\n    canDispatchCar() {\r\n      // 判断申请单是否已通过\r\n      // const isPlanApproved = this.planInfo.planStatus === 2;\r\n\r\n      // // 如果是非计量类型，且已经派过车，则不能再派车\r\n      // if (this.planInfo.measureFlag !== 1 && this.dispatchList.length > 0) {\r\n      //   return false;\r\n      // }\r\n\r\n      return true;\r\n    },\r\n    // 默认显示前50条，若有搜索，则显示搜索后的数据\r\n    displayDriverListOptions() {\r\n      return this.searchDriverQuery ? this.filteredDriverOptions : this.driverList.slice(0, 50);\r\n    },\r\n    displayCarListOptions() {\r\n      return this.searchCarQuery ? this.filteredCarOptions : this.carList.slice(0, 50);\r\n    },\r\n    canShowMaterialConfirm() {\r\n      // 只有planStatus为5或6时显示（已出厂/部分收货），且不是已完成/废弃/驳回/过期\r\n      return [5, 6].includes(this.planInfo.planStatus);\r\n    }\r\n  },\r\n  activated() {\r\n    this.getDicts(\"xctg_driver_car_emission_standards\").then(response => {\r\n      this.vehicleEmissionStandardsOptions = response.data;\r\n    });\r\n    this.getDicts(\"leave_task_type\").then(response => {\r\n      this.taskTypeOptions = response.data;\r\n      if (this.planInfo.planType !== 3) {\r\n        this.taskTypeOptions.splice(2, 1);\r\n      }\r\n    });\r\n    // 获取路由参数中的ID\r\n    const applyNo = this.$route.params.applyNo;\r\n    this.applyNo = applyNo\r\n    this.taskQueryParams.applyNo = applyNo;\r\n    this.approveForm.applyNo = applyNo;\r\n    if (applyNo) {\r\n      detailPlan(applyNo).then(response => {\r\n        this.planInfo = response.data;\r\n        this.taskTypeEditUpdate();\r\n        console.log(\"this.planInfo\", this.planInfo);\r\n        // 解析图片和文件数据\r\n        this.parseImageAndFileData();\r\n      });\r\n    };\r\n    this.getListTaskInfo();\r\n    this.getDriverList();\r\n    this.getCarList();\r\n\r\n\r\n\r\n  },\r\n\r\n\r\n  methods: {\r\n    handleApprove() {\r\n      this.approveForm.approveFlag = true;\r\n      console.log(\"this.approveForm\", this.approveForm);\r\n      approve(this.approveForm).then(response => {\r\n        this.$message.success('审核通过');\r\n        // 跳转到列表页面并刷新\r\n        this.$router.push({\r\n          path: \"/leave/leavePlanList\",\r\n          query: {\r\n            t: Date.now(),\r\n            refresh: true // 添加刷新标记\r\n          }\r\n        });\r\n      }).catch(error => {\r\n        this.$message.error('审核失败');\r\n        console.error('Approval error:', error);\r\n      });\r\n    },\r\n    handleReject() {\r\n      this.approveForm.approveFlag = false;\r\n      console.log(\"this.approveForm\", this.approveForm);\r\n      approve(this.approveForm).then(response => {\r\n        this.$message.success('驳回成功');\r\n        // 跳转到列表页面并刷新\r\n        this.$router.push({\r\n          path: \"/leave/leavePlanList\",\r\n          query: {\r\n            t: Date.now(),\r\n            refresh: true // 添加刷新标记\r\n          }\r\n        });\r\n      }).catch(error => {\r\n        this.$message.error('审核失败');\r\n        console.error('Approval error:', error);\r\n      });\r\n    },\r\n    handleDiscard() {\r\n      discard(this.planInfo).then(response => {\r\n        this.$message.success('废弃成功');\r\n\r\n        if (window.history.length > 1) {\r\n          this.$router.go(-1);\r\n        } else {\r\n          this.$router.push({ path: \"/leave/leavePlanList\", query: { t: Date.now() } });\r\n        }\r\n      }).catch(error => {\r\n        this.$message.error('废弃失败');\r\n        console.error('Approval error:', error);\r\n      });\r\n    },\r\n    taskTypeEditUpdate() {\r\n      if (this.planInfo.planType !== 2) {\r\n        this.isTaskTypeEdit = false;\r\n      }\r\n    },\r\n    getListTaskInfo() {\r\n      listTask(this.taskQueryParams).then(response => {\r\n        console.log(\"response.data\", response.rows);\r\n        this.taskListInfo = response.rows;\r\n        console.log(\"this.taskListInfo\", this.taskListInfo);\r\n        // 获取所有任务物资\r\n        this.getAllTaskMaterials();\r\n      });\r\n    },\r\n    openNewDriverWindow() {\r\n      const newWindowUrl = 'https://ydxt.citicsteel.com:8099/truckManage/xctgDriverUser'; // 替换为实际要跳转的页面 URL\r\n      window.open(newWindowUrl, '_blank'); // 打开新窗口并跳转至指定 URL\r\n    },\r\n    openNewCarWindow() {\r\n      const newWindowUrl = 'https://ydxt.citicsteel.com:8099/truckManage/xctgDriverCar'; // 替换为实际要跳转的页面 URL\r\n      window.open(newWindowUrl, '_blank'); // 打开新窗口并跳转至指定 URL\r\n    },\r\n    // 1国五，2国六，3新能源字典翻译\r\n    vehicleEmissionStandardsFormat(row, column) {\r\n      return this.selectDictLabel(this.vehicleEmissionStandardsOptions, row.vehicleEmissionStandards);\r\n    },\r\n\r\n    taskTypeFormat(row, column) {\r\n      return this.getTaskTypeText(row.taskType);\r\n    },\r\n    taskStatusFormat(row, column) {\r\n      return this.getStatusText(row.taskStatus);\r\n    },\r\n\r\n    /** 查询司机信息列表 */\r\n    getCarList() {\r\n      this.loading = true;\r\n      // listAllDriver().then(response => {\r\n      //   this.driverList = response.data;\r\n      //   this.loading = false;\r\n      // });\r\n      getXctgDriverCarList().then(response => {\r\n        this.carList = response.data;\r\n        this.filteredCarOptions = this.carList;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    // 搜索过滤逻辑\r\n    filterCarData(query) {\r\n      this.searchCarQuery = query;\r\n\r\n      if (this.searchCarQuery) {\r\n\r\n        this.filteredCarOptions = this.carList.filter(item =>\r\n          item.carNumber.includes(query)\r\n        );\r\n      } else {\r\n\r\n        this.filteredCarOptions = this.carList.slice(0, 50);\r\n      }\r\n    },\r\n    //通过driverId获取司机信息\r\n    handleDriverChange() {\r\n      if (this.dispatchForm.driverId != null) {\r\n        this.driverList.forEach(item => {\r\n          if (item.id == this.dispatchForm.driverId) {\r\n            this.dispatchForm.name = item.name;\r\n            this.dispatchForm.idCard = item.idCard;\r\n            this.dispatchForm.company = item.company;\r\n            this.dispatchForm.phone = item.phone;\r\n            this.dispatchForm.photo = item.photo;\r\n            this.dispatchForm.faceImgList = item.faceImgList;\r\n            this.dispatchForm.driverLicenseImgs = item.driverLicenseImgs;\r\n            this.dispatchForm.vehicleLicenseImgs = item.vehicleLicenseImgs;\r\n            this.dispatchForm.sex = item.gender;\r\n\r\n          }\r\n        });\r\n      }\r\n    },\r\n    //通过driverId获取司机信息\r\n    handleCarChange() {\r\n      console.log(\"handleCarChange\")\r\n      if (this.dispatchForm.carUUId != null) {\r\n        this.carList.forEach(item => {\r\n          if (item.id == this.dispatchForm.carUUId) {\r\n            this.dispatchForm.carNumber = item.carNumber;\r\n\r\n            if (item.vehicleEmissionStandards == 1) {\r\n              this.dispatchForm.vehicleEmissionStandards = \"国五\";\r\n            } else if (item.vehicleEmissionStandards == 2) {\r\n              this.dispatchForm.vehicleEmissionStandards = \"国六\";\r\n            } else if (item.vehicleEmissionStandards == 3) {\r\n              this.dispatchForm.vehicleEmissionStandards = \"新能源\";\r\n            } else {\r\n              this.dispatchForm.vehicleEmissionStandards = \"\";\r\n            }\r\n            this.dispatchForm.licensePlateColor = item.licensePlateColor;\r\n            this.dispatchForm.carId = item.carId;\r\n            this.dispatchForm.trailerNumber = item.trailerNumber;\r\n            this.dispatchForm.trailerId = item.trailerId;\r\n            this.dispatchForm.axisType = item.axisType;\r\n            this.dispatchForm.driverWeight = item.driverWeight;\r\n            this.dispatchForm.maxWeight = item.maxWeight;\r\n            this.dispatchForm.engineNumber = item.engineNumber;\r\n            this.dispatchForm.vinNumber = item.vinNumber;\r\n          }\r\n\r\n        });\r\n      }\r\n    },\r\n    /** 查询司机信息列表 */\r\n    getDriverList() {\r\n      // listAllDriver().then(response => {\r\n      //   this.driverList = response.data;\r\n      //   this.loading = false;\r\n      // });\r\n      getXctgDriverUserList().then(response => {\r\n        this.driverList = response.data;\r\n        console.log(\"this.driverList\", this.driverList);\r\n        this.filteredDriverOptions = this.driverList;\r\n      });\r\n    },\r\n    // 搜索过滤逻辑\r\n    filterDriverData(query) {\r\n      this.searchDriverQuery = query;\r\n\r\n      if (this.searchDriverQuery) {\r\n\r\n        this.filteredDriverOptions = this.driverList.filter(item =>\r\n          item.driverInfo.includes(query)\r\n        );\r\n      } else {\r\n\r\n        this.filteredDriverOptions = this.driverList.slice(0, 50);\r\n      }\r\n    },\r\n    // 解析图片和文件数据\r\n    parseImageAndFileData() {\r\n      // 解析图片数据\r\n      if (this.planInfo.applyImgUrl) {\r\n        try {\r\n          this.imageList = JSON.parse(this.planInfo.applyImgUrl);\r\n        } catch (e) {\r\n          console.error('解析图片数据失败:', e);\r\n          this.imageList = [];\r\n        }\r\n      }\r\n\r\n      // 解析文件数据\r\n      if (this.planInfo.applyFileUrl) {\r\n        try {\r\n          this.fileList = JSON.parse(this.planInfo.applyFileUrl);\r\n        } catch (e) {\r\n          console.error('解析文件数据失败:', e);\r\n          this.fileList = [];\r\n        }\r\n      }\r\n    },\r\n\r\n    // 下载文件\r\n    downloadFile(url, fileName) {\r\n      if (!url) {\r\n        this.$message.error('文件链接无效');\r\n        return;\r\n      }\r\n\r\n      // 创建一个a元素用于下载\r\n      const link = document.createElement('a');\r\n      link.href = url;\r\n      link.download = fileName || '下载文件';\r\n      document.body.appendChild(link);\r\n      link.click();\r\n      document.body.removeChild(link);\r\n    },\r\n\r\n    // 获取计划类型文本\r\n    getPlanTypeText(type) {\r\n      const typeMap = {\r\n        1: '出厂不返回',\r\n        2: '出厂返回',\r\n        3: '跨区调拨',\r\n        4: '退货申请'\r\n      };\r\n      return typeMap[type] || '未知类型';\r\n    },\r\n\r\n    // 获取业务类型文本\r\n    getBusinessCategoryText(category) {\r\n      const categoryMap = {\r\n        1: '通用',\r\n        11: '通用',\r\n        12: '委外加工',\r\n        21: '有计划量计量',\r\n        22: '短期',\r\n        23: '钢板（圆钢）',\r\n        31: '通用'\r\n      };\r\n      return categoryMap[category] || '未知类型';\r\n    },\r\n\r\n    // 获取物资类型文本\r\n    getMaterialTypeText(type) {\r\n      const typeMap = {\r\n        1: '钢材',\r\n        2: '钢板',\r\n        3: '其他'\r\n      };\r\n      return typeMap[type] || '未知类型';\r\n    },\r\n\r\n    // 获取计划状态文本\r\n    getPlanStatusText(status) {\r\n      const statusMap = {\r\n        1: '待分厂审批',\r\n        2: '待分厂复审',\r\n        3: '待生产指挥中心审批',\r\n        4: '审批完成',\r\n        5: '已出厂',\r\n        6: '部分收货',\r\n        7: '已完成',\r\n        11: '驳回',\r\n        12: '废弃',\r\n        13: '过期'\r\n      };\r\n      return statusMap[status] || '未知状态';\r\n    },\r\n\r\n    // 获取日志颜色\r\n    getLogColor(log) {\r\n      const logTypeColorMap = {\r\n        1: '#409EFF', // 创建\r\n        2: '#67C23A', // 审批\r\n        3: '#E6A23C', // 流转\r\n        4: '#F56C6C', // 驳回\r\n        5: '#909399'  // 其他\r\n      };\r\n      return logTypeColorMap[log.logType] || '#409EFF';\r\n    },\r\n\r\n    // 获取派车状态文本\r\n    getDispatchStatusText(status) {\r\n      const statusMap = {\r\n        0: '待出发',\r\n        1: '已出发',\r\n        2: '已到达',\r\n        3: '已完成',\r\n        4: '已取消'\r\n      };\r\n      return statusMap[status] || '未知状态';\r\n    },\r\n\r\n    // 获取派车状态类型（用于标签颜色）\r\n    getDispatchStatusType(status) {\r\n      const statusMap = {\r\n        0: 'info',\r\n        1: 'primary',\r\n        2: 'success',\r\n        3: 'success',\r\n        4: 'danger'\r\n      };\r\n      return statusMap[status] || 'info';\r\n    },\r\n\r\n    // 获取计划类型标签样式\r\n    getPlanTypeTagType(type) {\r\n      const typeMap = {\r\n        1: 'success',  // 出厂不返回\r\n        2: 'warning',  // 出厂返回\r\n        3: 'info',     // 跨区调拨\r\n        4: 'danger'    // 退货申请\r\n      };\r\n      return typeMap[type] || 'info';\r\n    },\r\n\r\n    // 获取物资类型标签样式\r\n    getMaterialTypeTagType(type) {\r\n      const typeMap = {\r\n        1: 'primary',  // 钢材\r\n        2: 'success',  // 钢板\r\n        3: 'info'      // 其他\r\n      };\r\n      return typeMap[type] || 'info';\r\n    },\r\n\r\n    // 获取业务类型标签样式\r\n    getBusinessCategoryTagType(category) {\r\n      const typeMap = {\r\n        '1': 'primary',   // 通用\r\n        '11': 'primary',  // 通用\r\n        '12': 'warning',  // 委外加工\r\n        '21': 'success',  // 有计划量计量\r\n        '22': 'info',     // 短期\r\n        '23': 'danger',   // 钢板（圆钢）\r\n        '31': 'primary'   // 通用\r\n      };\r\n      return typeMap[category] || 'info';\r\n    },\r\n\r\n    // 打开派车弹框\r\n    openDispatchDialog() {\r\n      // 初始化物资数据\r\n      this.availableMaterials = this.planInfo.materials || [];\r\n      console.log(\"this.availableMaterials\", this.availableMaterials);\r\n\r\n      // 获取已派车的物资列表，并在回调中初始化 materialSelectionList\r\n      this.getTaskMaterialListAndInitSelection();\r\n      // 判断非计量且taskType为1的情况\r\n      if (this.planInfo.measureFlag == 0) {\r\n        if (this.dispatchForm.taskType == 1) {\r\n          // 检查是否已经有taskType为1的任务\r\n          const hasType1Task = this.taskListInfo.some(task => task.taskType === 1);\r\n          if (hasType1Task) {\r\n            this.$message.warning('非计量只能派车出厂一次');\r\n            return;\r\n          }\r\n          console.log(\"hasType1Task\", hasType1Task)\r\n        }\r\n      }\r\n\r\n\r\n      // 判断用户角色权限\r\n      const roles = this.$store.getters.roles;\r\n      console.log(\"roles\", roles);\r\n      if (!roles.includes('leave.supplier') && !roles.includes('leave.applicant')) {\r\n        this.$message.error('您没有派车权限');\r\n        return;\r\n      }\r\n\r\n      console.log(\"this.planInfo.planStatus\", this.planInfo.planStatus);\r\n      if (![4, 5, 6].includes(this.planInfo.planStatus)) {\r\n        this.$message.warning('当前状态无法派车');\r\n        return;\r\n      }\r\n\r\n\r\n\r\n\r\n      console.log(\"openDispatchDialog\", this.taskListInfo.length);\r\n      if (this.planInfo.businessCategory == 22 && this.taskListInfo.length >= 1) {\r\n        this.$message.warning('短期计划只允许派一次车');\r\n        return;\r\n      }\r\n\r\n      if (this.planInfo.businessCategory == 23 && this.taskListInfo.length >= 1) {\r\n        this.$message.warning('钢板（圆钢）计划只允许派一次车');\r\n        return;\r\n      }\r\n\r\n      this.dispatchForm = {};\r\n\r\n      if (this.planInfo.planType == 1) {\r\n        this.dispatchForm.taskType = 1\r\n      } else if (this.planInfo.planType == 3) {\r\n        this.dispatchForm.taskType = 3\r\n      } else if (this.planInfo.planType == 4) {\r\n        this.dispatchForm.taskType = 1\r\n      }\r\n      console.log(this.dispatchForm.taskType),\r\n        this.dispatchDialogVisible = true;\r\n\r\n\r\n    },\r\n\r\n    // 新增方法\r\n    getTaskMaterialListAndInitSelection() {\r\n      // 清空已用数量映射\r\n      this.taskMaterialListMap.clear();\r\n      // 统计所有已派车物资\r\n      const type2List = this.taskListInfo.filter(item => item.taskType === 2);\r\n      console.log(\"type2List\", type2List);\r\n      if (!type2List || type2List.length === 0) {\r\n        // 初始化 materialSelectionList：全部选上且数量为剩余数量\r\n        this.materialSelectionList = (this.planInfo.materials || []).map(mat => {\r\n          // const usedNum = (this.taskMaterialListMap.get(mat.materialId)?.usedNum) || 0;\r\n          // const remainingNum = Math.max((mat.planNum || 0) - usedNum, 0);\r\n          return {\r\n            materialId: mat.materialId,\r\n            materialName: mat.materialName,\r\n            materialSpec: mat.materialSpec,\r\n            planNum: mat.planNum,\r\n            usedNum: 0,\r\n            remainingNum: mat.planNum,\r\n            currentNum: mat.planNum\r\n          };\r\n        });\r\n      } else {\r\n        console.log(\"this.taskListInfo\", this.taskListInfo);\r\n        type2List.forEach(task => {\r\n          const params = { taskNo: task.taskNo };\r\n          listTaskMaterial(params).then(response => {\r\n            let taskMaterials = response.rows || [];\r\n            taskMaterials.forEach(material => {\r\n              if (!this.taskMaterialListMap.has(material.materialId)) {\r\n                this.taskMaterialListMap.set(material.materialId, {\r\n                  taskMaterialInfo: material,\r\n                  usedNum: material.planNum\r\n                });\r\n              } else {\r\n                const existingMaterial = this.taskMaterialListMap.get(material.materialId);\r\n                existingMaterial.usedNum += material.planNum;\r\n              }\r\n            });\r\n\r\n            // 将taskMaterialListMap转换为数组集合\r\n            this.taskMaterialList = Array.from(this.taskMaterialListMap, ([key, value]) => ({\r\n              materialId: key,\r\n              ...value\r\n            }));\r\n\r\n            // 初始化 materialSelectionList：全部选上且数量为剩余数量\r\n            this.materialSelectionList = (this.planInfo.materials || []).map(mat => {\r\n              const usedNum = (this.taskMaterialListMap.get(mat.materialId)?.usedNum) || 0;\r\n              const remainingNum = Math.max((mat.planNum || 0) - usedNum, 0);\r\n\r\n              return {\r\n                materialId: mat.materialId,\r\n                materialName: mat.materialName,\r\n                materialSpec: mat.materialSpec,\r\n                planNum: mat.planNum,\r\n                usedNum: usedNum,\r\n                remainingNum: remainingNum,\r\n                currentNum: remainingNum\r\n              };\r\n            });\r\n\r\n            this.materialSelectionList = this.materialSelectionList.filter(item => item.remainingNum > 0);\r\n          });\r\n        });\r\n      }\r\n\r\n         // 判断非计量且taskType为1的情况\r\n      if (this.planInfo.measureFlag == 0) {\r\n        if (this.dispatchForm.taskType == 1) {\r\n          // 检查是否已经有taskType为1的任务\r\n          const hasType1Task = this.taskListInfo.some(task => task.taskType === 1);\r\n          if (hasType1Task) {\r\n            this.$message.warning('非计量只能派车出厂一次');\r\n            return;\r\n          }\r\n          console.log(\"hasType1Task\", hasType1Task)\r\n        }\r\n      }\r\n\r\n\r\n    },\r\n\r\n    // 重置派车表单\r\n    resetDispatchForm() {\r\n      this.$refs.dispatchForm && this.$refs.dispatchForm.resetFields();\r\n      this.materialSelectionList = [{\r\n        materialId: null,\r\n        materialName: '',\r\n        materialSpec: '',\r\n        planNum: 0,\r\n        remainingNum: 0,\r\n        usedNum: 0,\r\n        currentNum: 0\r\n      }];\r\n    },\r\n\r\n    // 获取已派车的物资列表\r\n    getTaskMaterialList() {\r\n      // 从taskListInfo中获取已派车的物资信息\r\n      this.taskMaterialListMap.clear();\r\n      this.taskListInfo.forEach(task => {\r\n        const params = {\r\n          taskNo: task.taskNo,\r\n        };\r\n        listTaskMaterial(params).then(response => {\r\n          console.log(\"listTaskMaterial\", response.rows);\r\n          let taskMaterials = [];\r\n          taskMaterials = response.rows;\r\n          taskMaterials.forEach(material => {\r\n            if (!this.taskMaterialListMap.has(material.materialId)) {\r\n              this.taskMaterialListMap.set(material.materialId, {\r\n                taskMaterialInfo: material,\r\n                usedNum: material.planNum\r\n              });\r\n            } else {\r\n              const existingMaterial = this.taskMaterialListMap.get(material.materialId);\r\n              existingMaterial.usedNum += material.planNum;\r\n            }\r\n          });\r\n          // 将taskMaterialListMap转换为数组集合\r\n          this.taskMaterialList = Array.from(this.taskMaterialListMap, ([key, value]) => ({\r\n            materialId: key,\r\n            ...value\r\n          }));\r\n          console.log(\"taskMaterialArray\", this.taskMaterialList);\r\n          console.log(\"taskMaterialListMap\", this.taskMaterialListMap);\r\n        });\r\n      });\r\n    },\r\n\r\n    // 添加物资行\r\n    addMaterialRow() {\r\n      this.materialSelectionList.push({\r\n        materialId: null,\r\n        materialName: '',\r\n        materialSpec: '',\r\n        planNum: 0,\r\n        remainingNum: 0,\r\n        usedNum: 0,\r\n        currentNum: 0\r\n      });\r\n    },\r\n\r\n    // 移除物资行\r\n    removeMaterial(index) {\r\n      this.materialSelectionList.splice(index, 1);\r\n    },\r\n\r\n    // 处理物资选择变化\r\n    handleMaterialChange(row, index) {\r\n      console.log(\"handleMaterialChange\", this.taskMaterialList);\r\n\r\n\r\n      const selectedMaterial = this.taskMaterialList.find(item => item.materialId === row.materialId);\r\n      if (selectedMaterial) {\r\n        row.usedNum = selectedMaterial.usedNum;\r\n      }\r\n      const selectPlanMaterial = this.planInfo.materials.find(item => item.materialId === row.materialId);\r\n\r\n      if (selectPlanMaterial) {\r\n        row.planNum = selectPlanMaterial.planNum;\r\n        row.materialName = selectPlanMaterial.materialName;\r\n        row.materialSpec = selectPlanMaterial.materialSpec;\r\n      }\r\n\r\n      row.remainingNum = row.planNum - row.usedNum;\r\n      row.currentNum = row.planNum - row.usedNum;\r\n\r\n      console.log(\"handleMaterialChange\", row, index);\r\n\r\n    },\r\n\r\n    // 获取物资最大可用数量\r\n    getMaxAvailableNum(row) {\r\n      if (!row.materialId) return 0;\r\n\r\n      // 从taskMaterialListMap中获取已用数量\r\n      const materialInfo = this.taskMaterialListMap.get(row.materialId);\r\n      const usedNum = materialInfo ? materialInfo.usedNum : 0;\r\n\r\n      return row.planNum - usedNum;\r\n    },\r\n\r\n    // 判断物资是否可选\r\n    isMaterialAvailable(material) {\r\n      // 从taskMaterialListMap中获取已用数量\r\n      // const materialInfo = this.taskMaterialListMap.get(material.id);\r\n      // const usedNum = materialInfo ? materialInfo.usedNum : 0;\r\n\r\n      // let selected = false;\r\n\r\n      // this.availableMaterials.forEach(item => {\r\n      //   if (item.materialId === material.materialId) {\r\n      //     selected = true;\r\n      //   }\r\n      // });\r\n\r\n      return this.materialSelectionList.some(row => row.materialId === material.materialId);;\r\n    },\r\n\r\n    // 修改提交派车表单方法\r\n    submitDispatchForm() {\r\n      this.$refs.dispatchForm.validate(valid => {\r\n        if (valid) {\r\n          // 判断非计量且taskType为1的情况\r\n          if (this.planInfo.measureFlag == 0) {\r\n            if (this.dispatchForm.taskType == 1) {\r\n              // 检查是否已经有taskType为1的任务\r\n              const hasType1Task = this.taskListInfo.some(task => task.taskType === 1);\r\n              if (hasType1Task) {\r\n                this.$message.warning('非计量只能派车出厂一次');\r\n                return;\r\n              }\r\n            }\r\n          }\r\n\r\n          // 新集合\r\n          let resultList = [];\r\n\r\n          console.log(\"this.planInfo.measureFlag\", this.planInfo.measureFlag);\r\n          console.log(\"this.dispatchForm.taskType\", this.dispatchForm.taskType);\r\n\r\n          if (this.planInfo.measureFlag == 0 && this.dispatchForm.taskType == 2) {\r\n            this.materialSelectionList.forEach(selRow => {\r\n              // 在 planInfo.materials 中查找相同 materialId 的元素\r\n              const planMaterial = (this.planInfo.materials || []).find(\r\n                mat => mat.materialId === selRow.materialId\r\n              );\r\n              if (planMaterial) {\r\n                // 深拷贝一份，避免影响原数据\r\n                const newItem = { ...planMaterial };\r\n                newItem.planNum = selRow.currentNum; // 设置为本次数量\r\n                resultList.push(newItem);\r\n              }\r\n            });\r\n\r\n            // resultList 即为你需要的新集合\r\n            console.log('this.materialSelectionList', this.materialSelectionList);\r\n            console.log('resultList', resultList);\r\n\r\n            // 物资校验：必须有物资\r\n            if (!this.materialSelectionList.length) {\r\n              this.$message.warning('请至少选择一种物资');\r\n              return;\r\n            }\r\n\r\n            // 校验每一行物资\r\n            const hasInvalidMaterial = this.materialSelectionList.some(row => {\r\n              // 必须选择物资，数量>0，且数量<=剩余数量\r\n              return (\r\n                !row.materialId ||\r\n                row.currentNum <= 0 ||\r\n                row.currentNum > row.remainingNum\r\n              );\r\n            });\r\n\r\n            if (hasInvalidMaterial) {\r\n              this.$message.warning('请选择物资且本次数量需大于0且不超过剩余数量');\r\n              return;\r\n            }\r\n          } else {\r\n            console.log(\"this.planInfo.materials\", this.planInfo.materials);\r\n            resultList = this.planInfo.materials ? this.planInfo.materials.map(item => ({ ...item })) : [];\r\n            console.log(\"123321\", resultList);\r\n          }\r\n\r\n\r\n\r\n\r\n\r\n          if (this.planInfo.measureFlag == 1 && this.dispatchForm.taskType !== 2) {\r\n            this.dispatchForm.taskStatus = 1;\r\n          } else {\r\n            this.dispatchForm.taskStatus = 4;\r\n          }\r\n\r\n          if (this.dispatchForm.taskType == 2) {\r\n            this.dispatchForm.taskStatus = 5;\r\n          }\r\n\r\n\r\n          //是否直供默认为0\r\n          this.dispatchForm.isDirectSupply = 0;\r\n          // todo 任务状态确认\r\n          this.dispatchForm.applyNo = this.applyNo;\r\n          this.dispatchForm.planNo = this.planInfo.planNo;\r\n          this.dispatchForm.carNum = this.dispatchForm.carNumber;\r\n          this.dispatchForm.companyName = this.dispatchForm.company;\r\n          this.dispatchForm.driverLicenseImg = this.dispatchForm.driverLicenseImgs;\r\n          this.dispatchForm.driverName = this.dispatchForm.name;\r\n          this.dispatchForm.mobilePhone = this.dispatchForm.phone;\r\n          this.dispatchForm.faceImg = this.dispatchForm.photo;\r\n          this.dispatchForm.drivingLicenseImg = this.dispatchForm.vehicleLicenseImgs;\r\n          this.dispatchForm.idCardNo = this.dispatchForm.idCard;\r\n          if (this.dispatchForm.sex == \"1\") {\r\n            this.dispatchForm.sex = 1;\r\n          } else if (this.dispatchForm.sex == \"2\") {\r\n            this.dispatchForm.sex = 2;\r\n          }\r\n          if (this.dispatchForm.vehicleEmissionStandards == \"国五\") {\r\n            this.dispatchForm.vehicleEmissionStandards = 1;\r\n          } else if (this.dispatchForm.vehicleEmissionStandards == \"国六\") {\r\n            this.dispatchForm.vehicleEmissionStandards = 2;\r\n          } else if (this.dispatchForm.vehicleEmissionStandards == \"新能源\") {\r\n            this.dispatchForm.vehicleEmissionStandards = 3;\r\n          }\r\n          console.log(\"this.dispatchForm\", this.dispatchForm);\r\n\r\n          let dispatchInfo = {};\r\n          dispatchInfo.carNum = this.dispatchForm.carNum;\r\n\r\n          isAllowDispatch(dispatchInfo).then(response => {\r\n            let row = response.data;\r\n            if (row > 0) {\r\n              this.$message.error(\"当前车有正在执行的任务\")\r\n            } else {\r\n              let param = {};\r\n              param.leaveTask = this.dispatchForm;\r\n              param.leaveTaskMaterialList = resultList;\r\n              addTaskAndMaterialAndAddLeaveLog(param).then(res => {\r\n                console.log(\"addTaskAndMaterialAndAddLeaveLog\", res)\r\n                if (res.code == 200) {\r\n                  this.$message.success('派车成功');\r\n                  this.dispatchDialogVisible = false;\r\n                  this.getListTaskInfo();\r\n                } else {\r\n                  // 其他失败原因\r\n                  this.$message.error(res.message || '派车失败');\r\n                }\r\n              }).catch(err => {\r\n                console.error('dispatch error:', err);\r\n                this.$message.error('网络异常，稍后重试');\r\n              });\r\n\r\n              // addTaskAndMaterial(this.dispatchForm).then(response => {\r\n              //   console.log(\"addTaskAndMaterial\", response);\r\n              //   let snowId = response.data;\r\n              //   this.planInfo.materials.forEach(item => {\r\n              //     item.taskNo = snowId;\r\n              //     addTaskMaterial(item);\r\n              //   });\r\n\r\n              //   console.log(\"生成派车日志\");\r\n\r\n              //   //生成派车日志\r\n              //   let leaveTaskLog = {};\r\n\r\n\r\n              //   leaveTaskLog.logType = 2;\r\n              //   leaveTaskLog.taskNo = snowId;\r\n              //   leaveTaskLog.applyNo = this.applyNo;\r\n              //   leaveTaskLog.info = '派车任务创建：' + this.dispatchForm.carNum + ' ' + this.dispatchForm.driverName\r\n              //   addLeaveLog(leaveTaskLog);\r\n\r\n              //   this.$message.success('派车成功');\r\n              //   this.dispatchDialogVisible = false;\r\n              //   this.getListTaskInfo();\r\n              // });\r\n\r\n              this.dispatchDialogVisible = false;\r\n            }\r\n            console.log(\"this.isAllowDispatch\", response);\r\n          }).catch(err => {\r\n            console.error('dispatch error:', err);\r\n            this.$message.error('网络异常，稍后重试');\r\n          });\r\n\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n\r\n    // 格式化日期时间\r\n    formatDateTime(date) {\r\n      const year = date.getFullYear();\r\n      const month = (date.getMonth() + 1).toString().padStart(2, '0');\r\n      const day = date.getDate().toString().padStart(2, '0');\r\n      const hours = date.getHours().toString().padStart(2, '0');\r\n      const minutes = date.getMinutes().toString().padStart(2, '0');\r\n      const seconds = date.getSeconds().toString().padStart(2, '0');\r\n\r\n      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\r\n    },\r\n\r\n    // 打印功能\r\n    handlePrint() {\r\n      this.$message.success('打印功能尚未实现');\r\n      // 实际项目中可以调用浏览器打印功能\r\n      // window.print();\r\n    },\r\n\r\n    // 返回按钮\r\n    cancel() {\r\n      this.$tab.closeOpenPage(this.$route);\r\n      this.$router.push({ path: \"/leave/leavePlanList\", query: { t: Date.now() } });\r\n    },\r\n\r\n    // 跳转到任务详情页面\r\n    goToTaskDetail(row) {\r\n      this.$router.push({\r\n        path: `/leave/plan/task/${row.taskNo}`\r\n      });\r\n    },\r\n\r\n    getTaskTypeText(taskType) {\r\n      const standardMap = {\r\n        1: '出厂',\r\n        2: '返厂',\r\n        3: '跨区调拨'\r\n      };\r\n      return standardMap[taskType] || '未知';\r\n    },\r\n\r\n    getStatusText(standard) {\r\n      const standardMap = {\r\n        1: '待过皮重',\r\n        2: '待装货',\r\n        3: '待过毛重',\r\n        4: '待出厂',\r\n        5: '待返厂',\r\n        6: '待过毛重(复磅)',\r\n        7: '待卸货',\r\n        8: '待过皮重(复磅)',\r\n        9: '完成'\r\n      };\r\n      return standardMap[standard] || '未知';\r\n    },\r\n\r\n    // 获取计划状态类型\r\n    getPlanStatusType(status) {\r\n      const statusMap = {\r\n        '1': 'warning',  // 待分厂审批\r\n        '2': 'warning',  // 待分厂复审\r\n        '3': 'warning',  // 待生产指挥中心审批\r\n        '4': 'success',  // 审批完成\r\n        '5': 'primary',  // 已出厂\r\n        '6': 'info',     // 部分收货\r\n        '7': 'success',  // 已完成\r\n        '11': 'danger',  // 驳回\r\n        '12': 'danger',  // 废弃\r\n        '13': 'danger'   // 过期\r\n      }\r\n      return statusMap[status] || 'info'\r\n    },\r\n\r\n    /**\r\n     * 获取计划下所有任务的任务物资\r\n     * @returns {Promise<void>}\r\n     */\r\n    async getAllTaskMaterials() {\r\n      try {\r\n        // 清空现有数据\r\n        this.taskMaterialMap.clear();\r\n\r\n        // 获取该计划下所有任务的任务物资\r\n        const params = {\r\n          applyNo: this.applyNo\r\n        };\r\n\r\n        const response = await listTaskMaterial(params);\r\n        if (response.code === 200 && response.rows) {\r\n          // 将任务物资按物资ID分组存储\r\n          response.rows.forEach(material => {\r\n            const key = material.materialId;\r\n            if (!this.taskMaterialMap.has(key)) {\r\n              this.taskMaterialMap.set(key, {\r\n                materialId: material.materialId,\r\n                materialName: material.materialName,\r\n                materialSpec: material.materialSpec,\r\n                planNum: material.planNum,\r\n                usedNum: 0,\r\n                taskMaterials: [] // 存储每个任务的具体物资信息\r\n              });\r\n            }\r\n\r\n            const materialInfo = this.taskMaterialMap.get(key);\r\n            // 累加每个任务物资的计划数量作为已使用数量\r\n            materialInfo.usedNum += material.planNum;\r\n            materialInfo.taskMaterials.push({\r\n              taskNo: material.taskNo,\r\n              carNum: material.carNum,\r\n              planNum: material.planNum,\r\n              createTime: material.createTime\r\n            });\r\n          });\r\n        }\r\n\r\n        // 更新物资选择列表中的已使用数量\r\n        this.updateMaterialUsedNum();\r\n\r\n        console.log('Task Material Map:', this.taskMaterialMap);\r\n      } catch (error) {\r\n        console.error('获取任务物资失败:', error);\r\n        this.$message.error('获取任务物资失败');\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 更新物资选择列表中的已使用数量\r\n     */\r\n    updateMaterialUsedNum() {\r\n      this.materialSelectionList.forEach(row => {\r\n        if (row.materialId) {\r\n          const materialInfo = this.taskMaterialMap.get(row.materialId);\r\n          if (materialInfo) {\r\n            // 直接使用累加的计划数量作为已使用数量\r\n            row.usedNum = materialInfo.usedNum;\r\n          }\r\n        }\r\n      });\r\n    },\r\n\r\n    // 物资确认按钮点击事件\r\n    async handleMaterialConfirm() {\r\n      try {\r\n        // 校验所有任务的taskStatus是否为9\r\n        if (this.taskListInfo && this.taskListInfo.length > 0) {\r\n          const unfinishedTasks = this.taskListInfo.filter(task => task.taskStatus !== 9);\r\n          if (unfinishedTasks.length > 0) {\r\n            this.$message.error('存在未完成的任务，无法进行物资确认');\r\n            return;\r\n          }\r\n        }\r\n\r\n        // 调用后端接口，传递applyNo\r\n        await confirmMaterial({ applyNo: this.applyNo });\r\n        this.$message.success('物资确认成功');\r\n        // 刷新详情\r\n        this.getListTaskInfo();\r\n        // 重新获取planInfo\r\n        detailPlan(this.applyNo).then(response => {\r\n          this.planInfo = response.data;\r\n        });\r\n      } catch (e) {\r\n        this.$message.error('物资确认失败');\r\n      }\r\n    },\r\n\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.app-container {\r\n  padding: 20px;\r\n}\r\n\r\n.box-card {\r\n  margin-bottom: 20px;\r\n  border-radius: 5px;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.section-container {\r\n  margin-bottom: 30px;\r\n  border-radius: 8px;\r\n  background: #fff;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);\r\n  overflow: hidden;\r\n  border: 1px solid #ebeef5;\r\n}\r\n\r\n.section-container:nth-child(1) {\r\n  border-top: 4px solid #8957e5;\r\n  /* 基本信息模块 - 紫色 */\r\n}\r\n\r\n.section-container:nth-child(2) {\r\n  border-top: 4px solid #409EFF;\r\n  /* 图片列表模块 - 蓝色 */\r\n}\r\n\r\n.section-container:nth-child(3) {\r\n  border-top: 4px solid #F56C6C;\r\n  /* 文件列表模块 - 红色 */\r\n}\r\n\r\n.section-container:nth-child(4) {\r\n  border-top: 4px solid #67C23A;\r\n  /* 物资列表模块 - 绿色 */\r\n}\r\n\r\n.section-container:nth-child(5) {\r\n  border-top: 4px solid #E6A23C;\r\n  /* 派车信息模块 - 橙色 */\r\n}\r\n\r\n.section-container:nth-child(6) {\r\n  border-top: 4px solid #909399;\r\n  /* 日志列表模块 - 灰色 */\r\n}\r\n\r\n.section-title {\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  padding: 15px 20px;\r\n  margin-bottom: 15px;\r\n  border-bottom: 1px solid #ebeef5;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  background: #fafafa;\r\n  position: relative;\r\n  padding-left: 30px;\r\n}\r\n\r\n.section-title::before {\r\n  content: '';\r\n  width: 4px;\r\n  height: 16px;\r\n  background: currentColor;\r\n  position: absolute;\r\n  left: 15px;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  border-radius: 2px;\r\n}\r\n\r\n.section-container:nth-child(1) .section-title {\r\n  color: #8957e5;\r\n}\r\n\r\n.section-container:nth-child(2) .section-title {\r\n  color: #409EFF;\r\n}\r\n\r\n.section-container:nth-child(3) .section-title {\r\n  color: #F56C6C;\r\n}\r\n\r\n.section-container:nth-child(4) .section-title {\r\n  color: #67C23A;\r\n}\r\n\r\n.section-container:nth-child(5) .section-title {\r\n  color: #E6A23C;\r\n}\r\n\r\n.section-container:nth-child(6) .section-title {\r\n  color: #909399;\r\n}\r\n\r\n.section-container .el-descriptions,\r\n.section-container .el-table,\r\n.section-container .el-timeline {\r\n  padding: 0 20px 20px;\r\n}\r\n\r\n.fixed-bottom-action {\r\n  position: fixed;\r\n  bottom: 0;\r\n  left: 0;\r\n  right: 0;\r\n  z-index: 999;\r\n  background-color: #fff;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n  padding: 15px 0;\r\n  text-align: center;\r\n}\r\n\r\n.dispatch-btn {\r\n  margin-left: 15px;\r\n}\r\n\r\n.empty-data {\r\n  padding: 30px 0;\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n\r\n.el-dialog__body {\r\n  padding: 20px 30px 0;\r\n}\r\n\r\n.image-container,\r\n.file-container {\r\n  padding: 20px;\r\n}\r\n\r\n.image-list,\r\n.file-list {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 15px;\r\n}\r\n\r\n.image-item {\r\n  width: 150px;\r\n  height: 180px;\r\n  border-radius: 4px;\r\n  overflow: hidden;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n  transition: all 0.3s;\r\n  cursor: pointer;\r\n}\r\n\r\n.image-item:hover {\r\n  transform: translateY(-5px);\r\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.image-item img {\r\n  width: 100%;\r\n  height: 150px;\r\n  object-fit: cover;\r\n  display: block;\r\n}\r\n\r\n.image-name {\r\n  padding: 5px;\r\n  text-align: center;\r\n  font-size: 12px;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n.file-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 10px 15px;\r\n  border-radius: 4px;\r\n  background-color: #f5f7fa;\r\n  cursor: pointer;\r\n  transition: all 0.3s;\r\n  min-width: 180px;\r\n  max-width: 250px;\r\n}\r\n\r\n.file-item:hover {\r\n  background-color: #ecf5ff;\r\n  color: #409EFF;\r\n}\r\n\r\n.file-icon {\r\n  font-size: 24px;\r\n  margin-right: 8px;\r\n  color: #909399;\r\n}\r\n\r\n.file-item:hover .file-icon {\r\n  color: #409EFF;\r\n}\r\n\r\n.file-name {\r\n  font-size: 14px;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n/* 新增物资选择相关样式 */\r\n.el-input-number {\r\n  width: 120px;\r\n}\r\n\r\n.material-selection {\r\n  margin-top: 20px;\r\n}\r\n\r\n.material-selection .el-table {\r\n  margin-bottom: 10px;\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n.dispatch-log-dialog .el-dialog__body {\r\n  padding: 20px;\r\n}\r\n\r\n.el-table {\r\n  border-radius: 4px;\r\n  overflow: hidden;\r\n\r\n  th {\r\n    background-color: #fafafa !important;\r\n    color: #606266;\r\n    font-weight: bold;\r\n  }\r\n\r\n  td {\r\n    padding: 12px 0;\r\n  }\r\n}\r\n\r\n.el-timeline {\r\n  padding: 20px !important;\r\n\r\n  .el-timeline-item__node {\r\n    width: 12px;\r\n    height: 12px;\r\n  }\r\n\r\n  .el-timeline-item__content {\r\n    padding: 0 0 0 0px;\r\n  }\r\n}\r\n\r\n.el-descriptions {\r\n  .el-descriptions-item__label {\r\n    background-color: #fafafa;\r\n  }\r\n}\r\n\r\n.el-tag {\r\n  border-radius: 12px;\r\n  padding: 0 10px;\r\n}\r\n\r\n.el-button--text {\r\n  color: #409EFF;\r\n  padding-left: 0;\r\n  padding-right: 0;\r\n\r\n  &:hover {\r\n    color: #66b1ff;\r\n    background-color: transparent;\r\n  }\r\n\r\n  &:focus {\r\n    color: #409EFF;\r\n  }\r\n}\r\n</style>\r\n"]}]}