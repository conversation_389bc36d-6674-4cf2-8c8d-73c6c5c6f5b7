package com.ruoyi.app.leave.controller;

import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.app.leave.domain.LeavePlan;
import com.ruoyi.app.leave.service.ILeavePlanService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 出门证计划申请Controller
 * 
 * <AUTHOR>
 * @date 2025-03-13
 */
@RestController
@RequestMapping("/leave/plan")
public class LeavePlanController extends BaseController
{
    @Autowired
    private ILeavePlanService leavePlanService;

    /**
     * 查询出门证计划申请列表
     */
    @PreAuthorize("@ss.hasPermi('leave:plan:list')")
    @GetMapping("/list")
    public TableDataInfo list(LeavePlan leavePlan)
    {
        startPage();
        List<LeavePlan> list = leavePlanService.selectLeavePlanList(leavePlan);
        return getDataTable(list);
    }

    /**
     * 导出出门证计划申请列表
     */
    @PreAuthorize("@ss.hasPermi('leave:plan:export')")
    @Log(title = "出门证计划申请", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(LeavePlan leavePlan)
    {
        List<LeavePlan> list = leavePlanService.selectLeavePlanList(leavePlan);
        ExcelUtil<LeavePlan> util = new ExcelUtil<LeavePlan>(LeavePlan.class);
        return util.exportExcel(list, "plan");
    }

    /**
     * 获取出门证计划申请详细信息
     */
    @PreAuthorize("@ss.hasPermi('leave:plan:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(leavePlanService.selectLeavePlanById(id));
    }

    /**
     * 新增出门证计划申请
     */
    @PreAuthorize("@ss.hasPermi('leave:plan:add')")
    @Log(title = "出门证计划申请", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody LeavePlan leavePlan)
    {
        return toAjax(leavePlanService.insertLeavePlan(leavePlan));
    }

    /**
     * 修改出门证计划申请
     */
    @PreAuthorize("@ss.hasPermi('leave:plan:edit')")
    @Log(title = "出门证计划申请", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody LeavePlan leavePlan)
    {
        return toAjax(leavePlanService.updateLeavePlan(leavePlan));
    }

    /**
     * 删除出门证计划申请
     */
    @PreAuthorize("@ss.hasPermi('leave:plan:remove')")
    @Log(title = "出门证计划申请", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(leavePlanService.deleteLeavePlanByIds(ids));
    }
}
