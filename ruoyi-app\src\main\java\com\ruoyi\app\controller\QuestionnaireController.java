package com.ruoyi.app.controller;

import java.util.*;
import java.util.concurrent.TimeUnit;

import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.constant.HttpStatus;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.mail.IMailService;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.http.HttpUtils;
import com.ruoyi.common.utils.ip.AddressUtils;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.questionnaire.domain.Safe;
import com.ruoyi.questionnaire.domain.SafeEmail;
import com.ruoyi.questionnaire.service.ISafeEmailService;
import com.ruoyi.questionnaire.service.ISafeService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * 安全问卷Controller
 * 
 * <AUTHOR>
 * @date 2021-01-15
 */
@RestController
@RequestMapping("/app/questionnaire")
public class QuestionnaireController extends BaseController
{
    private static final Logger log = LoggerFactory.getLogger(QuestionnaireController.class);

    @Autowired
    private ISafeService safeService;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private IMailService mailService;

    @Autowired
    private ISafeEmailService safeEmailService;

    /**
     * 配置文件中appid
     */
    @Value("${wx.xctg.appid}")
    private String appid;

    /**
     * 配置文件中secret
     */
    @Value("${wx.xctg.secret}")
    private String secret;

    /**
     * 微信获取openId
     */
    @Value("${wx.jsCodeUrl}")
    private String jsCodeUrl;

    /**
     * 新增安全问卷
     */
    @PostMapping("/insert")
    @Log(title = "新增安全问卷", businessType = BusinessType.INSERT)
    public AjaxResult insertSafe(Safe safe)
    {
        //获取header参数
        ServletRequestAttributes attrs = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        assert attrs != null;
        HttpServletRequest request = attrs.getRequest();
        String token = request.getHeader("token");
        // 获取真实的openId
        String openId = redisCache.getCacheObject("UUID:" + token);
        if (token == null || openId == null)
            return AjaxResult.error("身份无法识别");
        safe.setOpenId(openId);
        if (safe.getType() == null || safe.getLocationStation() == null)
            return AjaxResult.error("缺少必填参数");
        int row = safeService.insertSafeQuestionnaire(safe);
        if (row > 0) {
            try{
                // 获取配置邮箱 有则发送
                String[] users = safeEmailService.selectSafeEmailListBySafe(safe);
                if (users.length > 0)
                    mailService.sendHtmlMail(users,"收到一封安全举报","<h2>收到来自"+ safe.getPhone() +"的举报，内容如下：</h2><h3>" + safe.getRemark() + "</h3><a href='http://jiangyin.red/questionnaire/questionnaire'>点击链接查看详情</a>");
                return AjaxResult.success("新增成功，邮件发送成功");
            }catch (Exception e) {
                return AjaxResult.success("新增成功，邮件发送失败");
            }
        } else {
            return AjaxResult.error("新增失败");
        }
    }

    /**
     * 获取安全问卷详细信息
     */
    @GetMapping("/getDetail")
    public AjaxResult getInfo(Long id)
    {
        return AjaxResult.success(safeService.getDetail(id));
    }

    /**
     * 获取下拉菜单
     */
    @GetMapping("/getDict")
    public AjaxResult getDict(Long id)
    {
        return AjaxResult.success(safeService.getDict());
    }

    /**
     * 获取下拉列表
     */
    @GetMapping("/getList")
    public TableDataInfo getList()
    {
        //存放数据
        TableDataInfo dataInfo = new TableDataInfo();
        //获取header参数
        ServletRequestAttributes attrs = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        assert attrs != null;
        HttpServletRequest request = attrs.getRequest();
        String token = request.getHeader("token");
        // 获取真实的openId
        String openId = redisCache.getCacheObject("UUID:" + token);
        if (token == null || openId == null) {
            dataInfo.setCode(HttpStatus.ERROR);
            dataInfo.setMsg("身份无法识别");
            dataInfo.setRows(new ArrayList<>());
            return dataInfo;
        }

        startPage();
        dataInfo = safeService.getList(openId);
        return getDataTable(dataInfo, pageNum, pageSize);
    }

    /**
     * 获取用户token
     * @param code
     * @return
     */
    @GetMapping("/getToken")
    public AjaxResult getToken(String code)
    {
        if (code == null || StringUtils.isEmpty(code)) {
            return AjaxResult.error("code为空");
        }
        String UUID = IdUtils.simpleUUID();
        String rspStr = HttpUtils.sendGet(jsCodeUrl, "appid=" + appid + "&secret=" + secret + "&js_code=" + code + "&grant_type=authorization_code", Constants.UTF8);
        JSONObject obj = JSONObject.parseObject(rspStr);
        if (obj.get("errcode") == null) {
            String openId = obj.getString("openid");
            // token有效期1天
            redisCache.setCacheObject("UUID:"+UUID, openId, 1, TimeUnit.DAYS);
            JSONObject dataJson = new JSONObject();
            dataJson.put("token", UUID);
            return AjaxResult.success(dataJson);
        } else {
            return AjaxResult.error(obj.getString("errmsg"));
        }
    }

    @GetMapping("/send")
    public AjaxResult send()
    {
        mailService.sendSimpleMail("<EMAIL>", "测试", "测试数据");
        return AjaxResult.success();
    }
}
