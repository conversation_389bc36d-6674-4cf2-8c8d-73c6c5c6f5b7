<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.app.leave.mapper.LeaveMaterialMapper">
    
    <resultMap type="LeaveMaterial" id="LeaveMaterialResult">
        <result property="id"    column="id"    />
        <result property="materialName"    column="material_name"    />
        <result property="materialSpec"    column="material_spec"    />
        <result property="measureUnit"    column="measure_unit"    />
        <result property="measureFlag"    column="measure_flag"    />
        <result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="selectLeaveMaterialVo">
        select id, material_name, material_spec, measure_unit, measure_flag, remark, create_time, create_by, update_time, update_by from leave_material
    </sql>

    <select id="selectLeaveMaterialList" parameterType="LeaveMaterial" resultMap="LeaveMaterialResult">
        <include refid="selectLeaveMaterialVo"/>
        <where>  
            <if test="materialName != null  and materialName != ''"> and material_name like concat('%', #{materialName}, '%')</if>
            <if test="materialSpec != null  and materialSpec != ''"> and material_spec = #{materialSpec}</if>
            <if test="measureUnit != null  and measureUnit != ''"> and measure_unit = #{measureUnit}</if>
            <if test="measureFlag != null "> and measure_flag = #{measureFlag}</if>
        </where>
    </select>
    
    <select id="selectLeaveMaterialById" parameterType="Long" resultMap="LeaveMaterialResult">
        <include refid="selectLeaveMaterialVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertLeaveMaterial" parameterType="LeaveMaterial" useGeneratedKeys="true" keyProperty="id">
        insert into leave_material
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="materialName != null">material_name,</if>
            <if test="materialSpec != null">material_spec,</if>
            <if test="measureUnit != null">measure_unit,</if>
            <if test="measureFlag != null">measure_flag,</if>
            <if test="remark != null">remark,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="materialName != null">#{materialName},</if>
            <if test="materialSpec != null">#{materialSpec},</if>
            <if test="measureUnit != null">#{measureUnit},</if>
            <if test="measureFlag != null">#{measureFlag},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
         </trim>
    </insert>

    <update id="updateLeaveMaterial" parameterType="LeaveMaterial">
        update leave_material
        <trim prefix="SET" suffixOverrides=",">
            <if test="materialName != null">material_name = #{materialName},</if>
            <if test="materialSpec != null">material_spec = #{materialSpec},</if>
            <if test="measureUnit != null">measure_unit = #{measureUnit},</if>
            <if test="measureFlag != null">measure_flag = #{measureFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteLeaveMaterialById" parameterType="Long">
        delete from leave_material where id = #{id}
    </delete>

    <delete id="deleteLeaveMaterialByIds" parameterType="String">
        delete from leave_material where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
</mapper>