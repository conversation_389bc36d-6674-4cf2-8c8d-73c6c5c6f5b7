package com.ruoyi.app.vehicleAccess.service;

import com.ruoyi.app.vehicleAccess.domain.XctgVehicleAuditFlow;

/**
 * 车辆进出厂爱锁Service接口
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface IVehicleAccessAiSuoService
{
    /**
     * 推送车辆入厂信息
     *
     * @param xctgVehicleAuditFlow 车辆审核流程对象
     * @return 推送结果
     */
    public String pushVehicleEntryInfo(XctgVehicleAuditFlow xctgVehicleAuditFlow);

    /**
     * 车辆现场失效处理
     *
     * @param xctgVehicleAuditFlow 车辆审核流程对象
     * @return 处理结果码
     */
    public int cancelVehicleInfo(XctgVehicleAuditFlow xctgVehicleAuditFlow);
}

