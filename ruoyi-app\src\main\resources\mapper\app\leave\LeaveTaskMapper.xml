<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.app.leave.mapper.LeaveTaskMapper">
    
    <resultMap type="LeaveTask" id="LeaveTaskResult">
        <result property="id"    column="id"    />
        <result property="taskNo"    column="task_no"    />
        <result property="applyNo"    column="apply_no"    />
        <result property="taskType"    column="task_type"    />
        <result property="planNo"    column="plan_no"    />
        <result property="taskStatus"    column="task_status"    />
        <result property="heatNo"    column="heat_no"    />
        <result property="steelGrade"    column="steel_grade"    />
        <result property="leaveDoor"    column="leave_door"    />
        <result property="leaveTime"    column="leave_time"    />
        <result property="enterDoor"    column="enter_door"    />
        <result property="enterTime"    column="enter_time"    />
        <result property="loadingWorkNo"    column="loading_work_no"    />
        <result property="loadingTime"    column="loading_time"    />
        <result property="unloadingWorkNo"    column="unloading_work_no"    />
        <result property="unloadingTime"    column="unloading_time"    />
        <result property="driverName"    column="driver_name"    />
        <result property="sex"    column="sex"    />
        <result property="mobilePhone"    column="mobile_phone"    />
        <result property="idCardNo"    column="id_card_no"    />
        <result property="carNum"    column="car_num"    />
        <result property="vehicleEmissionStandards"    column="vehicle_emission_standards"    />
        <result property="faceImg"    column="face_img"    />
        <result property="drivingLicenseImg"    column="driving_license_img"    />
        <result property="driverLicenseImg"    column="driver_license_img"    />
        <result property="companyName"    column="company_name"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="selectLeaveTaskVo">
        select id, task_no, apply_no, task_type, plan_no, task_status, heat_no, steel_grade, leave_door, leave_time, enter_door, enter_time, loading_work_no, loading_time, unloading_work_no, unloading_time, driver_name, sex, mobile_phone, id_card_no, car_num, vehicle_emission_standards, face_img, driving_license_img, driver_license_img, company_name, create_time, create_by, update_time, update_by from leave_task
    </sql>

    <select id="selectLeaveTaskList" parameterType="LeaveTask" resultMap="LeaveTaskResult">
        <include refid="selectLeaveTaskVo"/>
        <where>  
            <if test="taskNo != null  and taskNo != ''"> and task_no = #{taskNo}</if>
            <if test="applyNo != null  and applyNo != ''"> and apply_no = #{applyNo}</if>
            <if test="taskType != null "> and task_type = #{taskType}</if>
            <if test="planNo != null  and planNo != ''"> and plan_no = #{planNo}</if>
            <if test="taskStatus != null "> and task_status = #{taskStatus}</if>
            <if test="heatNo != null  and heatNo != ''"> and heat_no = #{heatNo}</if>
            <if test="steelGrade != null  and steelGrade != ''"> and steel_grade = #{steelGrade}</if>
            <if test="leaveDoor != null "> and leave_door = #{leaveDoor}</if>
            <if test="leaveTime != null "> and leave_time = #{leaveTime}</if>
            <if test="enterDoor != null "> and enter_door = #{enterDoor}</if>
            <if test="enterTime != null "> and enter_time = #{enterTime}</if>
            <if test="loadingWorkNo != null  and loadingWorkNo != ''"> and loading_work_no = #{loadingWorkNo}</if>
            <if test="loadingTime != null "> and loading_time = #{loadingTime}</if>
            <if test="unloadingWorkNo != null  and unloadingWorkNo != ''"> and unloading_work_no = #{unloadingWorkNo}</if>
            <if test="unloadingTime != null "> and unloading_time = #{unloadingTime}</if>
            <if test="driverName != null  and driverName != ''"> and driver_name like concat('%', #{driverName}, '%')</if>
            <if test="sex != null "> and sex = #{sex}</if>
            <if test="mobilePhone != null  and mobilePhone != ''"> and mobile_phone = #{mobilePhone}</if>
            <if test="idCardNo != null  and idCardNo != ''"> and id_card_no = #{idCardNo}</if>
            <if test="carNum != null  and carNum != ''"> and car_num = #{carNum}</if>
            <if test="vehicleEmissionStandards != null "> and vehicle_emission_standards = #{vehicleEmissionStandards}</if>
            <if test="faceImg != null  and faceImg != ''"> and face_img = #{faceImg}</if>
            <if test="drivingLicenseImg != null  and drivingLicenseImg != ''"> and driving_license_img = #{drivingLicenseImg}</if>
            <if test="driverLicenseImg != null  and driverLicenseImg != ''"> and driver_license_img = #{driverLicenseImg}</if>
            <if test="companyName != null  and companyName != ''"> and company_name like concat('%', #{companyName}, '%')</if>
        </where>
    </select>
    
    <select id="selectLeaveTaskById" parameterType="Long" resultMap="LeaveTaskResult">
        <include refid="selectLeaveTaskVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertLeaveTask" parameterType="LeaveTask" useGeneratedKeys="true" keyProperty="id">
        insert into leave_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskNo != null">task_no,</if>
            <if test="applyNo != null">apply_no,</if>
            <if test="taskType != null">task_type,</if>
            <if test="planNo != null">plan_no,</if>
            <if test="taskStatus != null">task_status,</if>
            <if test="heatNo != null">heat_no,</if>
            <if test="steelGrade != null">steel_grade,</if>
            <if test="leaveDoor != null">leave_door,</if>
            <if test="leaveTime != null">leave_time,</if>
            <if test="enterDoor != null">enter_door,</if>
            <if test="enterTime != null">enter_time,</if>
            <if test="loadingWorkNo != null">loading_work_no,</if>
            <if test="loadingTime != null">loading_time,</if>
            <if test="unloadingWorkNo != null">unloading_work_no,</if>
            <if test="unloadingTime != null">unloading_time,</if>
            <if test="driverName != null">driver_name,</if>
            <if test="sex != null">sex,</if>
            <if test="mobilePhone != null">mobile_phone,</if>
            <if test="idCardNo != null">id_card_no,</if>
            <if test="carNum != null">car_num,</if>
            <if test="vehicleEmissionStandards != null">vehicle_emission_standards,</if>
            <if test="faceImg != null">face_img,</if>
            <if test="drivingLicenseImg != null">driving_license_img,</if>
            <if test="driverLicenseImg != null">driver_license_img,</if>
            <if test="companyName != null">company_name,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskNo != null">#{taskNo},</if>
            <if test="applyNo != null">#{applyNo},</if>
            <if test="taskType != null">#{taskType},</if>
            <if test="planNo != null">#{planNo},</if>
            <if test="taskStatus != null">#{taskStatus},</if>
            <if test="heatNo != null">#{heatNo},</if>
            <if test="steelGrade != null">#{steelGrade},</if>
            <if test="leaveDoor != null">#{leaveDoor},</if>
            <if test="leaveTime != null">#{leaveTime},</if>
            <if test="enterDoor != null">#{enterDoor},</if>
            <if test="enterTime != null">#{enterTime},</if>
            <if test="loadingWorkNo != null">#{loadingWorkNo},</if>
            <if test="loadingTime != null">#{loadingTime},</if>
            <if test="unloadingWorkNo != null">#{unloadingWorkNo},</if>
            <if test="unloadingTime != null">#{unloadingTime},</if>
            <if test="driverName != null">#{driverName},</if>
            <if test="sex != null">#{sex},</if>
            <if test="mobilePhone != null">#{mobilePhone},</if>
            <if test="idCardNo != null">#{idCardNo},</if>
            <if test="carNum != null">#{carNum},</if>
            <if test="vehicleEmissionStandards != null">#{vehicleEmissionStandards},</if>
            <if test="faceImg != null">#{faceImg},</if>
            <if test="drivingLicenseImg != null">#{drivingLicenseImg},</if>
            <if test="driverLicenseImg != null">#{driverLicenseImg},</if>
            <if test="companyName != null">#{companyName},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
         </trim>
    </insert>

    <update id="updateLeaveTask" parameterType="LeaveTask">
        update leave_task
        <trim prefix="SET" suffixOverrides=",">
            <if test="taskNo != null">task_no = #{taskNo},</if>
            <if test="applyNo != null">apply_no = #{applyNo},</if>
            <if test="taskType != null">task_type = #{taskType},</if>
            <if test="planNo != null">plan_no = #{planNo},</if>
            <if test="taskStatus != null">task_status = #{taskStatus},</if>
            <if test="heatNo != null">heat_no = #{heatNo},</if>
            <if test="steelGrade != null">steel_grade = #{steelGrade},</if>
            <if test="leaveDoor != null">leave_door = #{leaveDoor},</if>
            <if test="leaveTime != null">leave_time = #{leaveTime},</if>
            <if test="enterDoor != null">enter_door = #{enterDoor},</if>
            <if test="enterTime != null">enter_time = #{enterTime},</if>
            <if test="loadingWorkNo != null">loading_work_no = #{loadingWorkNo},</if>
            <if test="loadingTime != null">loading_time = #{loadingTime},</if>
            <if test="unloadingWorkNo != null">unloading_work_no = #{unloadingWorkNo},</if>
            <if test="unloadingTime != null">unloading_time = #{unloadingTime},</if>
            <if test="driverName != null">driver_name = #{driverName},</if>
            <if test="sex != null">sex = #{sex},</if>
            <if test="mobilePhone != null">mobile_phone = #{mobilePhone},</if>
            <if test="idCardNo != null">id_card_no = #{idCardNo},</if>
            <if test="carNum != null">car_num = #{carNum},</if>
            <if test="vehicleEmissionStandards != null">vehicle_emission_standards = #{vehicleEmissionStandards},</if>
            <if test="faceImg != null">face_img = #{faceImg},</if>
            <if test="drivingLicenseImg != null">driving_license_img = #{drivingLicenseImg},</if>
            <if test="driverLicenseImg != null">driver_license_img = #{driverLicenseImg},</if>
            <if test="companyName != null">company_name = #{companyName},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteLeaveTaskById" parameterType="Long">
        delete from leave_task where id = #{id}
    </delete>

    <delete id="deleteLeaveTaskByIds" parameterType="String">
        delete from leave_task where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
</mapper>