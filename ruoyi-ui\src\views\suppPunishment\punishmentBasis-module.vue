<template>
  <el-dialog
    title="处罚依据选择"
    :visible.sync="visible"
    width="500px"
    top="5vh"
    append-to-body
    @close="handleClose"
    :close-on-click-modal="false"
    custom-class="basis-dialog"
  >
    <div class="basis-dialog-content">
      <!-- 处罚依据选项 -->
      <div class="basis-options">
        <h4 class="section-title"><span class="required-mark">*</span>选择依据类型：</h4>
        <el-radio-group v-model="selectedBasisType" @change="handleBasisTypeChange">
          <!-- 质量异议单号 -->
          <div class="basis-item">
            <div class="basis-row">
              <div class="radio-wrapper">
                <el-radio label="quality" @change="handleBasisTypeChange">质量异议单号</el-radio>
              </div>
              <div class="input-wrapper">
                <el-input
                  v-model="qualityNumber"
                  placeholder="请输入质量异议单号"
                  class="aligned-input"
                  @input="updateBasisText"
                  @focus="checkQuality"
                />
              </div>
            </div>
          </div>

          <!-- 制度名称 -->
          <div class="basis-item">
            <div class="basis-row">
              <div class="radio-wrapper">
                <el-radio label="system" @change="handleBasisTypeChange">制度名称</el-radio>
              </div>
              <div class="input-wrapper">
                <el-input
                  v-model="systemName"
                  placeholder="请输入制度名称"
                  class="aligned-input"
                  @input="updateBasisText"
                  @focus="checkSystem"
                />
              </div>
            </div>
          </div>

          <!-- 文件报批单号 -->
          <div class="basis-item">
            <div class="basis-row">
              <div class="radio-wrapper">
                <el-radio label="report" @change="handleBasisTypeChange">文件报批单号</el-radio>
              </div>
              <div class="input-wrapper">
                <el-input
                  v-model="reportName"
                  placeholder="请输入文件报批单号"
                  class="aligned-input"
                  @input="updateBasisText"
                  @focus="checkReport"
                />
              </div>
            </div>
          </div>

          <!-- 巡检处罚单号 -->
          <div class="basis-item">
            <div class="basis-row">
              <div class="radio-wrapper">
                <el-radio label="inspection" @change="handleBasisTypeChange">巡检处罚单号</el-radio>
              </div>
              <div class="input-wrapper">
                <el-input
                  v-model="inspectionNumber"
                  placeholder="请输入巡检处罚单号"
                  class="aligned-input"
                  @input="updateBasisText"
                  @focus="checkInspection"
                />
              </div>
            </div>
          </div>

          <!-- 安管处罚单号 -->
          <div class="basis-item">
            <div class="basis-row">
              <div class="radio-wrapper">
                <el-radio label="safety" @change="handleBasisTypeChange">安管处罚单号</el-radio>
              </div>
              <div class="input-wrapper">
                <el-input
                  v-model="safetyNumber"
                  placeholder="请输入安管处罚单号"
                  class="aligned-input"
                  @input="updateBasisText"
                  @focus="checkSafety"
                />
              </div>
            </div>
          </div>
        </el-radio-group>
      </div>

      <!-- 依据内容 -->
      <div class="basis-content">
        <h4 class="section-title"><span class="required-mark">*</span>依据内容：</h4>
        <div class="content-wrapper">
          <el-input
            v-model="basisContent"
            type="textarea"
            :rows="4"
            placeholder="请输入依据内容"
            @input="updateBasisText"
            class="content-textarea"
          />
        </div>
      </div>

    </div>

    <!-- 底部按钮 -->
    <div slot="footer" class="dialog-footer">
      <div class="footer-buttons">
        <el-button type="primary" @click="handleConfirm">确 定</el-button>
        <el-button @click="handleClose">取 消</el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: "PunishmentBasisDialog",
  data() {
    return {
      // 是否显示弹出层
      visible: false,
      // 选中的依据类型
      selectedBasisType: '',
      // 质量异议单号
      qualityNumber: '',
      // 制度名称
      systemName: '',
      // 报告名称
      reportName: '',
      // 巡检处罚单号
      inspectionNumber: '',
      // 安管处罚单号
      safetyNumber: '',
      // 依据内容
      basisContent: '',
      // 预览文本
      previewText: ''
    };
  },
  methods: {
    /** 显示弹窗 */
    show(currentValue = '') {
      this.visible = true;
      this.parseCurrentValue(currentValue);
      this.updatePreview();
      // 确保弹窗完全打开后再进行其他操作
      this.$nextTick(() => {
        console.log('弹窗已显示，当前数据：', {
          selectedBasisType: this.selectedBasisType,
          qualityNumber: this.qualityNumber,
          systemName: this.systemName,
          reportName: this.reportName,
          inspectionNumber: this.inspectionNumber,
          safetyNumber: this.safetyNumber,
          basisContent: this.basisContent
        });
      });
    },
    
    /** 隐藏弹窗 */
    hide() {
      this.visible = false;
    },
    
    /** 关闭弹窗 */
    handleClose() {
      this.visible = false;
      this.reset();
    },
    
    /** 重置数据 */
    reset() {
      this.selectedBasisType = '';
      this.qualityNumber = '';
      this.systemName = '';
      this.reportName = '';
      this.inspectionNumber = '';
      this.safetyNumber = '';
      this.basisContent = '';
      this.previewText = '';
    },
    
    /** 解析当前值 */
    parseCurrentValue(value) {
      if (!value) {
        this.reset();
        return;
      }
      
      // 尝试解析现有的依据内容
      if (value.includes('质量异议单号：')) {
        this.selectedBasisType = 'quality';
        const match = value.match(/质量异议单号：([^；\n]*)/);
        if (match) {
          this.qualityNumber = match[1].trim();
        }
      } else if (value.includes('制度名称：')) {
        this.selectedBasisType = 'system';
        const match = value.match(/制度名称：([^；\n]*)/);
        if (match) {
          this.systemName = match[1].trim();
        }
      } else if (value.includes('报告：')) {
        this.selectedBasisType = 'report';
        const match = value.match(/报告：([^；\n]*)/);
        if (match) {
          this.reportName = match[1].trim();
        }
      } else if (value.includes('巡检处罚单号：')) {
        this.selectedBasisType = 'inspection';
        const match = value.match(/巡检处罚单号：([^；\n]*)/);
        if (match) {
          this.inspectionNumber = match[1].trim();
        }
      } else if (value.includes('安管处罚单号：')) {
        this.selectedBasisType = 'safety';
        const match = value.match(/安管处罚单号：([^；\n]*)/);
        if (match) {
          this.safetyNumber = match[1].trim();
        }
      }
      
      // 解析依据内容
      const contentMatch = value.match(/依据内容：([^]*)/);
      if (contentMatch) {
        this.basisContent = contentMatch[1].trim();
      } else {
        // 如果没有找到依据内容标识，将整个内容作为依据内容
        this.basisContent = value;
      }
    },
    
    /** 依据类型变化 */
    handleBasisTypeChange() {
      this.updatePreview();
    },
    
    /** 更新依据文本 */
    updateBasisText() {
      this.updatePreview();
    },

    /** 点击质量异议单号输入框时自动选中 */
    checkQuality() {
      if (this.selectedBasisType !== 'quality') {
        this.selectedBasisType = 'quality';
        this.updatePreview();
      }
    },

    /** 点击制度名称输入框时自动选中 */
    checkSystem() {
      if (this.selectedBasisType !== 'system') {
        this.selectedBasisType = 'system';
        this.updatePreview();
      }
    },

    /** 点击报告输入框时自动选中 */
    checkReport() {
      if (this.selectedBasisType !== 'report') {
        this.selectedBasisType = 'report';
        this.updatePreview();
      }
    },

    /** 点击巡检处罚单号输入框时自动选中 */
    checkInspection() {
      if (this.selectedBasisType !== 'inspection') {
        this.selectedBasisType = 'inspection';
        this.updatePreview();
      }
    },

    /** 点击安管处罚单号输入框时自动选中 */
    checkSafety() {
      if (this.selectedBasisType !== 'safety') {
        this.selectedBasisType = 'safety';
        this.updatePreview();
      }
    },
    
    /** 更新预览 */
    updatePreview() {
      const parts = [];

      // 添加选中的依据类型信息
      if (this.selectedBasisType === 'quality' && this.qualityNumber) {
        parts.push(`质量异议单号：${this.qualityNumber}`);
      } else if (this.selectedBasisType === 'system' && this.systemName) {
        parts.push(`制度名称：${this.systemName}`);
      } else if (this.selectedBasisType === 'report' && this.reportName) {
        parts.push(`报告：${this.reportName}`);
      } else if (this.selectedBasisType === 'inspection' && this.inspectionNumber) {
        parts.push(`巡检处罚单号：${this.inspectionNumber}`);
      } else if (this.selectedBasisType === 'safety' && this.safetyNumber) {
        parts.push(`安管处罚单号：${this.safetyNumber}`);
      }

      // 添加依据内容
      if (this.basisContent) {
        parts.push(`依据内容：${this.basisContent}`);
      }

      this.previewText = parts.join('；');

      console.log('预览更新：', {
        selectedBasisType: this.selectedBasisType,
        qualityNumber: this.qualityNumber,
        systemName: this.systemName,
        reportName: this.reportName,
        inspectionNumber: this.inspectionNumber,
        safetyNumber: this.safetyNumber,
        basisContent: this.basisContent,
        previewText: this.previewText
      });
    },
    
    /** 确认选择 */
    handleConfirm() {
      this.updatePreview();

      // 验证是否填写了必要信息
      if (!this.selectedBasisType) {
        this.$message.warning('请选择依据类型');
        return;
      }

      if (this.selectedBasisType === 'quality' && !this.qualityNumber) {
        this.$message.warning('请输入质量异议单号');
        return;
      }

      if (this.selectedBasisType === 'system' && !this.systemName) {
        this.$message.warning('请输入制度名称');
        return;
      }

      if (this.selectedBasisType === 'report' && !this.reportName) {
        this.$message.warning('请输入文件报批单号');
        return;
      }

      if (this.selectedBasisType === 'inspection' && !this.inspectionNumber) {
        this.$message.warning('请输入巡检处罚单号');
        return;
      }

      if (this.selectedBasisType === 'safety' && !this.safetyNumber) {
        this.$message.warning('请输入安管处罚单号');
        return;
      }

      if (!this.basisContent) {
        this.$message.warning('请输入依据内容');
        return;
      }

      this.$emit('select', this.previewText);
      this.handleClose();
    }
  }
};
</script>

<style scoped>
/* 弹窗内容容器 */
.basis-dialog-content {
  padding: 10px 0;
}

/* 章节标题样式 */
.section-title {
  font-size: 16px;
  color: #303133;
  margin: 0 0 15px 0;
  font-weight: 600;
}

/* 顶级标题样式（选择依据类型） */
.basis-options .section-title {
  margin-bottom: 15px;
}

/* 处罚依据选项样式 */
.basis-options {
  margin-bottom: 20px;
}

.basis-item {
  margin-bottom: 15px;
  padding: 0;
}

/* 新的行布局 */
.basis-row {
  display: flex;
  align-items: center;
  width: 100%;
  min-height: 36px;
}

.radio-wrapper {
  width: 120px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  height: 36px;
}

.input-wrapper {
  width: calc(100% - 135px);
  margin-left: 14px;
}

.aligned-input {
  width: 100%;
}

.aligned-input ::v-deep .el-input__inner {
  height: 36px;
  line-height: 36px;
  border-radius: 4px;
  font-size: 14px;
}

/* 单选框对齐样式 */
.radio-wrapper ::v-deep .el-radio {
  height: 36px;
  display: flex;
  align-items: center;
  margin: 0;
}

.radio-wrapper ::v-deep .el-radio__input {
  display: flex;
  align-items: center;
  margin-right: 8px;
}

.radio-wrapper ::v-deep .el-radio__inner {
  width: 16px;
  height: 16px;
}

.radio-wrapper ::v-deep .el-radio__label {
  font-size: 14px;
  line-height: 36px;
  padding-left: 8px;
  color: #606266;
}

/* 依据内容区域样式 */
.basis-content {
  margin-bottom: 20px;
}

.basis-content .section-title {
  margin-bottom: 15px;
}

.content-wrapper {
  padding: 0;
}

.content-textarea {
  width: 100%;
}

.content-textarea ::v-deep .el-textarea__inner {
  border-radius: 4px;
  font-family: inherit;
  font-size: 14px;
  line-height: 1.5;
  min-height: 120px;
}

/* 预览区域样式 */
.preview-area {
  margin-bottom: 0;
}

.preview-area .section-title {
  margin-bottom: 15px;
}

.preview-wrapper {
  padding: 0;
}

.preview-textarea {
  width: 100%;
}

.preview-textarea ::v-deep .el-textarea__inner {
  border-radius: 4px;
  background-color: #ffffff;
  font-family: inherit;
  font-size: 14px;
  line-height: 1.5;
  min-height: 120px;
}

/* 弹窗标题居中 */
::v-deep .el-dialog__header {
  text-align: center;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

::v-deep .el-dialog__title {
  text-align: center;
  width: 100%;
  display: block;
  font-weight: 600;
  color: #303133;
}

/* 专门的弹窗样式 */
::v-deep .basis-dialog {
  margin-top: 5vh !important;
}

::v-deep .basis-dialog .el-dialog__body {
  padding: 25px;
  max-height: 75vh;
  overflow-y: auto;
  background-color: #ffffff;
}

::v-deep .basis-dialog .el-dialog__header {
  padding: 20px 25px 15px;
}

::v-deep .basis-dialog .el-dialog__footer {
  padding: 15px 25px 20px;
  text-align: right;
}

/* 底部按钮样式 */
.footer-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 单选框组样式 */
::v-deep .el-radio-group {
  width: 100%;
}

::v-deep .el-radio {
  margin-right: 0;
  margin-bottom: 0;
}

::v-deep .el-radio__label {
  display: flex;
  align-items: center;
  width: 100%;
  font-size: 14px;
  color: #606266;
}

::v-deep .el-radio__input.is-checked + .el-radio__label {
  color: #409EFF;
}

/* 必填标识符样式 */
.required-mark {
  color: #F56C6C;
  margin-right: 4px;
  font-weight: bold;
}
</style>
