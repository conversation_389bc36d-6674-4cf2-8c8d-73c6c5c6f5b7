{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\assess\\self\\check\\organizationCheck.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\assess\\self\\check\\organizationCheck.vue", "mtime": 1756099891054}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\babel.config.js", "mtime": 1688548084091}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_info", "require", "_user", "_dept", "_vueTreeselect", "_interopRequireDefault", "name", "components", "Treeselect", "data", "loading", "showSearch", "rejectOpen", "total", "checkedTotal", "listToCheck", "listChecked", "title", "open", "queryParams", "pageNum", "pageSize", "workNo", "deptId", "assessDate", "status", "postType", "checkedQueryParams", "form", "id", "deptScore", "businessScore", "leaderScore", "organizationScore", "organizationScoreReason", "rules", "deptOptions", "openCheck", "checkInfo", "spanList", "toCheckLabel", "beAssessedList", "benefit", "userInfo", "benefitLinkFlag", "benefitDetail", "multipleSelection", "single", "multiple", "quickScoreDialogVisible", "batchQuickScoreForm", "score", "undefined", "ids", "batchQuickScoreRules", "required", "message", "trigger", "type", "batchQuickScoreOpen", "selectedRows", "computed", "canSubmitBatchScore", "length", "_iterator", "_createForOfIteratorHelper2", "default", "_step", "s", "n", "done", "row", "value", "quickScore", "err", "e", "f", "created", "getDefaultAssessDate", "getTreeselect", "getList", "getCheckedList", "methods", "now", "Date", "currentDay", "getDate", "targetDate", "getFullYear", "getMonth", "year", "month", "concat", "getBeAssessedList", "param", "_this", "listBeAssessed", "then", "res", "code", "for<PERSON>ach", "item", "_toConsumableArray2", "hrLateralAssessInfoList", "console", "log", "getSelfAssessUser", "_this2", "normalizer", "node", "children", "label", "deptName", "_this3", "listDept", "response", "handleTree", "_this4", "listInfo", "rows", "_this5", "cancel", "reset", "handleQuery", "reset<PERSON><PERSON>y", "resetForm", "handleCheckDetail", "_this6", "getInfo", "list", "JSON", "parse", "content", "handleSpanList", "userId", "checkSubmit", "_this7", "verify", "check", "$message", "rejectClick", "rejectCancel", "rejectReason", "rejectSubmit", "_this8", "$confirm", "confirmButtonText", "cancelButtonText", "reject", "catch", "_this9", "rejectInfo", "warning", "previousScore", "previousScoreName", "parseFloat", "selfScore", "handleListChange", "flag", "i", "push", "rowspan", "colspan", "objectSpanMethod", "_ref", "column", "rowIndex", "columnIndex", "category", "calScore", "Number", "categoryScore", "dePoints", "toFixed", "Math", "abs", "batchCalScore", "benefitScore", "handleSelectionChange", "selection", "map", "handleBatchQuickScore", "_this0", "$modal", "msgError", "batchWithBenefitByIds", "cancelBatchQuickScore", "submitBatchQuickScore", "_this1", "validationResult", "validateBatchQuickScore", "<PERSON><PERSON><PERSON><PERSON>", "submitData", "quickReason", "confirm", "batchQuickScore", "msgSuccess", "handleCheckedDetail", "_this10", "infoId", "error", "getHistoryRemarks", "remarks", "deptScoreReason", "trim", "businessScoreReason", "join"], "sources": ["src/views/assess/self/check/organizationCheck.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"80px\">\r\n      <el-row>\r\n        <el-form-item label=\"考核年月\" prop=\"assessDate\">\r\n          <el-date-picker\r\n            v-model=\"queryParams.assessDate\"\r\n            type=\"month\"\r\n            value-format=\"yyyy-M\"\r\n            format=\"yyyy 年 M 月\"\r\n            placeholder=\"选择考核年月\"\r\n            :clearable=\"false\">\r\n          </el-date-picker>\r\n        </el-form-item>\r\n        <el-form-item label=\"姓名\" prop=\"name\">\r\n          <el-input\r\n            v-model=\"queryParams.name\"\r\n            placeholder=\"请输入姓名\"\r\n            clearable\r\n            @keyup.enter.native=\"handleQuery\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"部门\" prop=\"deptId\">\r\n          <treeselect style=\"width: 200px;\" v-model=\"queryParams.deptId\" :multiple=\"false\" :options=\"deptOptions\" :normalizer=\"normalizer\" :disable-branch-nodes=\"true\" placeholder=\"请选择部门\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"岗位类型\" prop=\"postType\">\r\n          <el-select v-model=\"queryParams.postType\" placeholder=\"请选择岗位类型\" clearable style=\"width: 150px;\">\r\n            <el-option label=\"技术\" value=\"0\"></el-option>\r\n            <el-option label=\"行政\" value=\"1\"></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n          <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n        </el-form-item>\r\n      </el-row>\r\n    </el-form>\r\n\r\n    \r\n    <!-- 待评分列表 -->\r\n    <el-card class=\"box-card\" style=\"margin-bottom: 20px;\">\r\n      <div slot=\"header\" class=\"clearfix\">\r\n        <span style=\"font-size: 16px; font-weight: bold; color: #409EFF;\">\r\n          <i class=\"el-icon-s-order\"></i>\r\n          {{ toCheckLabel }}\r\n        </span>\r\n      </div>\r\n      \r\n      <el-row :gutter=\"10\" class=\"mb8\">\r\n        <el-col :span=\"1.5\">\r\n          <el-button\r\n            type=\"success\"\r\n            plain\r\n            icon=\"el-icon-edit\"\r\n            size=\"mini\"\r\n            @click=\"handleBatchQuickScore\"\r\n          >批量快速评分</el-button>\r\n        </el-col>\r\n        <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n      </el-row>\r\n        <el-table v-loading=\"loading\" :data=\"listToCheck\" @selection-change=\"handleSelectionChange\">\r\n          <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n          <el-table-column label=\"工号\" align=\"center\" prop=\"workNo\" width=\"120\"/>\r\n          <el-table-column label=\"姓名\" align=\"center\" prop=\"name\" width=\"120\"/>\r\n          <el-table-column label=\"部门\" align=\"center\" prop=\"deptName\" ></el-table-column>\r\n          <el-table-column label=\"岗位类型\" align=\"center\" prop=\"postType\" width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              <el-tag :type=\"scope.row.postType == '0' ? 'primary' : 'success'\" size=\"small\">\r\n                {{ scope.row.postType == '0' ? '技术' : '行政' }}\r\n              </el-tag>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"自评分\" align=\"center\" prop=\"selfScore\"></el-table-column>\r\n          <el-table-column label=\"部门领导评分\" align=\"center\" prop=\"deptScore\"></el-table-column>\r\n          <el-table-column label=\"事业部评分\" align=\"center\" prop=\"businessScore\">\r\n            <template slot-scope=\"scope\">\r\n              <span v-if=\"scope.row.businessScore\">{{ scope.row.businessScore }}</span>\r\n              <span v-else>无</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"是否100%挂钩公司效益\" align=\"center\" prop=\"benefitLinkFlag\">\r\n            <template slot-scope=\"scope\">\r\n              <el-tag :type=\"scope.row.benefitLinkFlag == 'Y' ? 'success' : 'info'\">{{ scope.row.benefitLinkFlag == \"Y\" ? \"是\" : \" 否\" }}</el-tag>\r\n            </template>\r\n          </el-table-column>\r\n          <!-- <el-table-column label=\"运改组织部评分\" align=\"center\" prop=\"organizationScore\">\r\n            <template slot-scope=\"scope\">\r\n              <el-input-number\r\n                v-model=\"scope.row.quickScore\"\r\n                :min=\"0\"\r\n                :max=\"100\"\r\n                size=\"mini\"\r\n                style=\"width: 120px\"\r\n                placeholder=\"请输入分数\">\r\n              </el-input-number>\r\n            </template>\r\n          </el-table-column> -->\r\n          <!-- <el-table-column label=\"加减分原因\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <el-input\r\n                v-model=\"scope.row.quickReason\"\r\n                type=\"textarea\"\r\n                :autosize=\"{ minRows: 1, maxRows: 4}\"\r\n                size=\"mini\"\r\n                style=\"width: 150px\"\r\n                placeholder=\"请输入加减分原因\">\r\n              </el-input>\r\n            </template>\r\n          </el-table-column> -->\r\n          <el-table-column label=\"备注\" align=\"center\" width=\"200\">\r\n            <template slot-scope=\"scope\">\r\n              <el-tooltip effect=\"dark\" placement=\"top\" :disabled=\"!getHistoryRemarks(scope.row)\">\r\n                <div slot=\"content\" style=\"max-width: 300px; white-space: pre-wrap;\">\r\n                  {{ getHistoryRemarks(scope.row) }}\r\n                </div>\r\n                <div class=\"history-remarks-cell\">\r\n                  {{ getHistoryRemarks(scope.row) || '暂无备注' }}\r\n                </div>\r\n              </el-tooltip>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"150\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                icon=\"el-icon-edit\"\r\n                @click=\"handleCheckDetail(scope.row)\"\r\n              >详细评分</el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      <pagination\r\n        v-show=\"total>0\"\r\n        :total=\"total\"\r\n        :page.sync=\"queryParams.pageNum\"\r\n        :limit.sync=\"queryParams.pageSize\"\r\n        @pagination=\"getList\"\r\n      />\r\n    </el-card>\r\n\r\n    <!-- 评分记录 -->\r\n    <el-card class=\"box-card\">\r\n      <div slot=\"header\" class=\"clearfix\">\r\n        <span style=\"font-size: 16px; font-weight: bold; color: #67C23A;\">\r\n          <i class=\"el-icon-document\"></i>\r\n          评分记录({{ checkedTotal }})\r\n        </span>\r\n      </div>\r\n      \r\n      <el-table v-loading=\"loading\" :data=\"listChecked\">\r\n        <el-table-column label=\"工号\" align=\"center\" prop=\"workNo\" width=\"120\"/>\r\n        <el-table-column label=\"姓名\" align=\"center\" prop=\"name\" width=\"120\"/>\r\n        <el-table-column label=\"部门\" align=\"center\" prop=\"deptName\" />\r\n        <el-table-column label=\"职务\" align=\"center\" prop=\"job\" width=\"150\"/>\r\n        <el-table-column label=\"岗位类型\" align=\"center\" prop=\"postType\" width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag :type=\"scope.row.postType == '0' ? 'primary' : 'success'\" size=\"small\">\r\n              {{ scope.row.postType == '0' ? '技术' : '行政' }}\r\n            </el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"评分类型\" align=\"center\" prop=\"type\" >\r\n          <template slot-scope=\"scope\">\r\n            <el-tag v-if=\"scope.row.type == '1'\" type=\"primary\" size=\"small\">部门领导评分</el-tag>\r\n            <el-tag v-if=\"scope.row.type == '2'\" type=\"warning\" size=\"small\">事业部领导评分</el-tag>\r\n            <el-tag v-if=\"scope.row.type == '3'\" type=\"success\" size=\"small\">运改组织部审核</el-tag>\r\n            <el-tag v-if=\"scope.row.type == '4'\" type=\"info\" size=\"small\">条线领导评分</el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"评分时间\" align=\"center\" prop=\"createTime\" width=\"160\"/>\r\n        <el-table-column label=\"评分\" align=\"center\" prop=\"score\" width=\"100\"/>\r\n        <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              icon=\"el-icon-view\"\r\n              @click=\"handleCheckedDetail(scope.row)\"\r\n            >查看详情</el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      \r\n      <pagination\r\n        v-show=\"checkedTotal>0\"\r\n        :total=\"checkedTotal\"\r\n        :page.sync=\"checkedQueryParams.pageNum\"\r\n        :limit.sync=\"checkedQueryParams.pageSize\"\r\n        @pagination=\"getCheckedList\"\r\n        style=\"margin-top: 20px;\"\r\n      />\r\n    </el-card>\r\n\r\n    <el-dialog\r\n      :visible.sync=\"open\"\r\n      fullscreen\r\n      class=\"assessment-detail-dialog\">\r\n      <div class=\"detail-container\">\r\n        <div class=\"detail-header\">\r\n          <h2 style=\"text-align: center; color: #303133; margin-bottom: 20px;\">\r\n            <i class=\"el-icon-document\"></i>\r\n            月度业绩考核表\r\n          </h2>\r\n          <el-card shadow=\"never\" style=\"margin-bottom: 20px;\">\r\n            <el-descriptions class=\"margin-top\" :column=\"3\" border>\r\n              <el-descriptions-item>\r\n                <template slot=\"label\">\r\n                  <i class=\"el-icon-user\"></i> 姓名\r\n                </template>\r\n                {{ checkInfo.name }}\r\n              </el-descriptions-item>\r\n              <el-descriptions-item>\r\n                <template slot=\"label\">\r\n                  <i class=\"el-icon-office-building\"></i> 部门\r\n                </template>\r\n                {{ checkInfo.deptName }}\r\n              </el-descriptions-item>\r\n              <el-descriptions-item>\r\n                <template slot=\"label\">\r\n                  <i class=\"el-icon-date\"></i> 考核年月\r\n                </template>\r\n                {{ checkInfo.assessDate }}\r\n              </el-descriptions-item>\r\n            </el-descriptions>\r\n          </el-card>\r\n        </div>\r\n        \r\n        <el-card shadow=\"never\" class=\"assessment-table-card\">\r\n          <div slot=\"header\" class=\"clearfix\">\r\n            <span style=\"font-size: 16px; font-weight: bold; color: #409EFF;\">\r\n              <i class=\"el-icon-s-data\"></i>\r\n              考核详情\r\n            </span>\r\n          </div>\r\n          <el-table v-loading=\"loading\" :data=\"checkInfo.list\"\r\n            :span-method=\"objectSpanMethod\" border stripe>\r\n            <el-table-column label=\"类型\" align=\"center\" prop=\"item\" width=\"120\"/>\r\n            <el-table-column label=\"指标\" align=\"center\" prop=\"category\" width=\"150\"/>\r\n            <el-table-column label=\"目标\" align=\"center\" prop=\"target\" width=\"180\"/>\r\n            <el-table-column label=\"评分标准\" align=\"center\" prop=\"standard\" />\r\n            <el-table-column label=\"完成实绩（若扣分，写明原因）\" align=\"center\" prop=\"performance\" >\r\n              <template slot-scope=\"scope\">\r\n                  <div style=\"display: flex\">\r\n                    <el-popover\r\n                      placement=\"left\"\r\n                      width=\"636\"\r\n                      trigger=\"click\"\r\n                      :ref=\"'popover' + scope.$index\">\r\n                      <el-table :data=\"beAssessedList\">\r\n                        <el-table-column width=\"150\" property=\"assessDeptName\" label=\"提出考核单位\"></el-table-column>\r\n                        <el-table-column width=\"300\" property=\"assessContent\" label=\"事项\"></el-table-column>\r\n                        <el-table-column width=\"80\" property=\"deductionOfPoint\" label=\"加减分\"></el-table-column>\r\n                      </el-table>\r\n                      <el-button slot=\"reference\" icon=\"el-icon-search\" size=\"small\"></el-button>\r\n                    </el-popover>\r\n                    <span style=\"margin-left: 10px;\">{{ scope.row.performance }}</span>\r\n                  </div>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"加减分\" align=\"center\" prop=\"dePoints\" width=\"150\" />\r\n            <el-table-column label=\"加减分理由\" align=\"center\" prop=\"pointsReason\" width=\"180\" />\r\n          </el-table>\r\n        </el-card>\r\n        \r\n        <el-card shadow=\"never\" class=\"signature-card\" style=\"margin-top: 20px;\">\r\n          <div slot=\"header\" class=\"clearfix\">\r\n            <span style=\"font-size: 16px; font-weight: bold; color: #67C23A;\">\r\n              <i class=\"el-icon-edit-outline\"></i>\r\n              评分记录\r\n            </span>\r\n          </div>\r\n          <el-form size=\"small\" :inline=\"false\" label-width=\"200px\" label-position=\"left\">\r\n            <!-- 自评分 -->\r\n            <el-form-item>\r\n              <template slot=\"label\">\r\n                <span style=\"color: #606266;\">\r\n                  自评分数 / 签名：\r\n                </span>\r\n              </template>\r\n              <div class=\"signature-content\">\r\n                <span class=\"score-text\">{{ checkInfo.selfScore }} 分</span>\r\n                <span class=\"separator\">/</span>\r\n                <span class=\"signature-name\">{{ checkInfo.name }}</span>\r\n              </div>\r\n            </el-form-item>\r\n            \r\n            <!-- 部门领导评分 -->\r\n            <el-form-item v-if=\"checkInfo.deptScore && checkInfo.deptUserName\">\r\n              <template slot=\"label\">\r\n                <span style=\"color: #606266;\">\r\n                  部门领导评分 / 签名：\r\n                </span>\r\n              </template>\r\n              <div class=\"signature-content\">\r\n                <span class=\"score-text\">{{ checkInfo.deptScore }} 分</span>\r\n                <span class=\"separator\">/</span>\r\n                <span class=\"signature-name\">{{ checkInfo.deptUserName }}</span>\r\n                <div v-if=\"checkInfo.deptScoreReason\" class=\"reason-text\">\r\n                  <span class=\"reason-label\">加减分理由：</span>\r\n                  <span class=\"reason-content\">{{ checkInfo.deptScoreReason }}</span>\r\n                </div>\r\n              </div>\r\n            </el-form-item>\r\n            \r\n            <!-- 事业部领导评分 -->\r\n            <el-form-item v-if=\"checkInfo.businessUserName && checkInfo.businessScore\">\r\n              <template slot=\"label\">\r\n                <span style=\"color: #606266;\">\r\n                  事业部领导评分 / 签名：\r\n                </span>\r\n              </template>\r\n              <div class=\"signature-content\">\r\n                <span class=\"score-text\">{{ checkInfo.businessScore }} 分</span>\r\n                <span class=\"separator\">/</span>\r\n                <span class=\"signature-name\">{{ checkInfo.businessUserName }}</span>\r\n                <div v-if=\"checkInfo.businessScoreReason\" class=\"reason-text\">\r\n                  <span class=\"reason-label\">加减分理由：</span>\r\n                  <span class=\"reason-content\">{{ checkInfo.businessScoreReason }}</span>\r\n                </div>\r\n              </div>\r\n            </el-form-item>\r\n            \r\n            <!-- 运改组织部评分 -->\r\n            <el-form-item v-if=\"checkInfo.organizationScore && checkInfo.organizationUserName\">\r\n              <template slot=\"label\">\r\n                <span style=\"color: #606266;\">\r\n                  运改组织部评分 / 签名：\r\n                </span>\r\n              </template>\r\n              <div class=\"signature-content\">\r\n                <span class=\"score-text\">{{ checkInfo.organizationScore }} 分</span>\r\n                <span class=\"separator\">/</span>\r\n                <span class=\"signature-name\">{{ checkInfo.organizationUserName }}</span>\r\n                <div v-if=\"checkInfo.organizationScoreReason\" class=\"reason-text\">\r\n                  <span class=\"reason-label\">加减分理由：</span>\r\n                  <span class=\"reason-content\">{{ checkInfo.organizationScoreReason }}</span>\r\n                </div>\r\n              </div>\r\n            </el-form-item>\r\n            \r\n            <!-- 公司效益信息 -->\r\n            <el-form-item label=\"是否100%挂钩公司效益：\">\r\n              <el-tag :type=\"userInfo.benefitLinkFlag == 'Y' ? 'success' : 'info'\">{{ userInfo.benefitLinkFlag == \"Y\" ? \"是\" : \" 否\" }}</el-tag>\r\n            </el-form-item>\r\n            \r\n            <!-- 当前状态评分输入 -->\r\n            <el-form-item v-if=\"checkInfo.status == '3'\" label=\"公司效益加减分：\">\r\n              <div style=\"display: flex; align-items: center; gap: 10px; margin-bottom: 10px;\">\r\n                <el-input-number v-model=\"benefit\" placeholder=\"请输入公司效益加减\" style=\"width: 200px;\" />\r\n                <span>分</span>\r\n                <el-button type=\"primary\" size=\"mini\" @click=\"calScore\">计 算</el-button>\r\n              </div>\r\n              <div v-if=\"benefitDetail\" class=\"benefit-detail\">\r\n                <i class=\"el-icon-info\"></i>\r\n                {{ benefitDetail }}\r\n              </div>\r\n            </el-form-item>\r\n            <el-form-item v-if=\"checkInfo.status == '3'\" label=\"运改部/组织部审核：\">\r\n              <div style=\"display: flex; align-items: center; gap: 10px;\">\r\n                <el-input-number v-model=\"form.organizationScore\" :min=\"0\" :max=\"100\" placeholder=\"请输入评分\" style=\"width: 150px;\" />\r\n                <span>分</span>\r\n              </div>\r\n            </el-form-item>\r\n            <el-form-item v-if=\"checkInfo.status == '3'\" label=\"运改组织部加减分理由：\">\r\n              <div style=\"display: flex;width: 500px;\">\r\n                <el-input\r\n                  v-model=\"form.organizationScoreReason\"\r\n                  type=\"textarea\"\r\n                  :autosize=\"{ minRows: 3, maxRows: 6}\"\r\n                  placeholder=\"请输入运改组织部加减分理由\"\r\n                  maxlength=\"500\"\r\n                  show-word-limit>\r\n                </el-input>\r\n              </div>\r\n            </el-form-item>\r\n          </el-form>\r\n        </el-card>\r\n        \r\n        <div class=\"dialog-footer\" style=\"text-align: center; margin-top: 30px; padding: 20px;\">\r\n          <el-button type=\"primary\" size=\"medium\" @click=\"checkSubmit\">\r\n            <i class=\"el-icon-check\"></i> 提 交\r\n          </el-button>\r\n          <el-button plain type=\"danger\" size=\"medium\" @click=\"rejectClick\">\r\n            <i class=\"el-icon-close\"></i> 退 回\r\n          </el-button>\r\n          <el-button plain type=\"info\" size=\"medium\" @click=\"cancel\">\r\n            <i class=\"el-icon-back\"></i> 取 消\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 驳回弹出框 -->\r\n    <el-dialog title=\"退回\" :visible.sync=\"rejectOpen\" append-to-body center width=\"40%\">\r\n        <el-form label-width=\"150px\">\r\n            <el-form-item label=\"退回原因:\">\r\n                <el-input type=\"textarea\"\r\n                :autosize=\"{ minRows: 5}\" \r\n                v-model=\"checkInfo.rejectReason\" \r\n                placeholder=\"请输入内容\"></el-input>\r\n            </el-form-item>\r\n        </el-form>\r\n        <div slot=\"footer\" class=\"dialog-footer\" style=\"text-align: center;\">\r\n            <el-button type=\"success\" plain @click=\"rejectSubmit\">提 交</el-button>\r\n            <el-button @click=\"rejectCancel\">取 消</el-button>\r\n        </div>\r\n    </el-dialog>\r\n\r\n      <!-- 批量快速评分对话框 -->\r\n      <el-dialog :title=\"'批量快速评分确认'\" :visible.sync=\"batchQuickScoreOpen\" width=\"1400px\" append-to-body>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <div class=\"benefit-score-container\" style=\"display: flex; align-items: center; justify-content: flex-start; margin-bottom: 8px;\">\r\n              <div class=\"benefit-input-group\" style=\"display: flex; align-items: center; height: 32px;\">\r\n                <span style=\"margin-right: 10px; white-space: nowrap; line-height: 32px;\">公司效益加减分:</span>\r\n                <el-input \r\n                  type=\"number\" \r\n                  v-model=\"benefit\" \r\n                  placeholder=\"请输入公司效益加减\" \r\n                  style=\"width: 180px; margin-right: 5px;\"\r\n                />\r\n                <span style=\"margin-right: 15px; line-height: 32px;\">分</span>\r\n                <el-button type=\"primary\" size=\"mini\" @click=\"batchCalScore\" style=\"height: 28px;\">计 算</el-button>\r\n              </div>\r\n            </div>\r\n          </el-col>\r\n        </el-row>\r\n        <el-alert\r\n          title=\"请确认以下人员的评分信息\"\r\n          type=\"warning\"\r\n          :closable=\"false\"\r\n          show-icon\r\n          class=\"mb20\"\r\n        />\r\n        <el-table :data=\"selectedRows\" size=\"small\" border>\r\n          <el-table-column label=\"工号\" align=\"center\" prop=\"workNo\" />\r\n          <el-table-column label=\"姓名\" align=\"center\" prop=\"name\" />\r\n          <el-table-column label=\"部门\" align=\"center\" prop=\"deptName\" />\r\n          <el-table-column label=\"岗位\" align=\"center\" prop=\"job\" />\r\n          <el-table-column label=\"岗位类型\" align=\"center\" prop=\"postType\" width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              <el-tag :type=\"scope.row.postType == '0' ? 'primary' : 'success'\" size=\"small\">\r\n                {{ scope.row.postType == '0' ? '技术' : '行政' }}\r\n              </el-tag>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"是否100%挂钩公司效益\" align=\"center\" prop=\"benefitLinkFlag\">\r\n            <template slot-scope=\"scope\">\r\n              <el-tag :type=\"scope.row.benefitLinkFlag == 'Y' ? 'success' : 'info'\">{{ scope.row.benefitLinkFlag == \"Y\" ? \"是\" : \" 否\" }}</el-tag>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"自评分\" align=\"center\" prop=\"selfScore\" width=\"90px\"/>\r\n          <el-table-column label=\"部门评分\" align=\"center\" prop=\"deptScore\" width=\"90px\">\r\n            <template slot-scope=\"scope\">\r\n              <span v-if=\"scope.row.deptScore\">{{ scope.row.deptScore }}</span>\r\n              <span v-else>-</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"事业部评分\" align=\"center\" prop=\"businessScore\" width=\"90px\">\r\n            <template slot-scope=\"scope\">\r\n              <span v-if=\"scope.row.businessScore\">{{ scope.row.businessScore }}</span>\r\n              <span v-else>-</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"效益加减分\" align=\"center\" prop=\"benefitScore\" width=\"90px\"/>\r\n          <el-table-column label=\"运改/组织部评分\" align=\"center\" prop=\"quickScore\" width=\"160px\">\r\n            <template slot-scope=\"scope\">\r\n              <!-- <span :class=\"{'text-red': !scope.row.quickScore}\">{{ scope.row.quickScore || '未计算' }}</span> -->\r\n               <el-input-number \r\n                v-model=\"scope.row.quickScore\" \r\n                :min=\"0\" \r\n                :max=\"100\" \r\n                size=\"mini\"\r\n                style=\"width: 120px\"\r\n                placeholder=\"请输入分数\">\r\n              </el-input-number>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"加减分理由\" align=\"center\" prop=\"quickReason\" width=\"180px\">\r\n            <template slot-scope=\"scope\">\r\n              <el-input\r\n                v-model=\"scope.row.quickReason\"\r\n                type=\"textarea\"\r\n                :autosize=\"{ minRows: 1, maxRows: 4}\"\r\n                size=\"mini\"\r\n                style=\"width: 150px\"\r\n                placeholder=\"请输入加减分原因\">\r\n              </el-input>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"备注\" align=\"center\" width=\"200\">\r\n            <template slot-scope=\"scope\">\r\n              <el-tooltip effect=\"dark\" placement=\"top\" :disabled=\"!getHistoryRemarks(scope.row)\">\r\n                <div slot=\"content\" style=\"max-width: 300px; white-space: pre-wrap;\">\r\n                  {{ getHistoryRemarks(scope.row) }}\r\n                </div>\r\n                <div class=\"batch-history-remarks-cell\">\r\n                  {{ getHistoryRemarks(scope.row) || '暂无备注' }}\r\n                </div>\r\n              </el-tooltip>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n        <div slot=\"footer\" class=\"dialog-footer\">\r\n          <el-button type=\"primary\" @click=\"submitBatchQuickScore\" :disabled=\"!canSubmitBatchScore\">确 定</el-button>\r\n          <el-button @click=\"cancelBatchQuickScore\">取 消</el-button>\r\n          </div>\r\n      </el-dialog>\r\n\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listInfo, listChecked, getInfo, check, listBeAssessed, rejectInfo, batchQuickScore, batchWithBenefitByIds} from \"@/api/assess/self/info\";\r\nimport { getSelfAssessUser} from \"@/api/assess/self/user\";\r\nimport { listDept } from \"@/api/assess/lateral/dept\";\r\nimport Treeselect from \"@riophae/vue-treeselect\";\r\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.css\";\r\n\r\nexport default {\r\n  name: \"OrganizationCheck\",\r\n  components: {\r\n    Treeselect\r\n  },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 退回原因输入框\r\n      rejectOpen:false,\r\n      // 总条数\r\n      total: 0,\r\n      checkedTotal: 0,\r\n      // 绩效考核-干部自评人员配置表格数据\r\n      listToCheck: [],\r\n      listChecked: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        workNo: null,\r\n        name:null,\r\n        deptId:null,\r\n        assessDate:null,\r\n        status:\"3\",\r\n        postType:null\r\n      },\r\n      // 评分记录查询参数\r\n      checkedQueryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        workNo: null,\r\n        name:null,\r\n        deptId:null,\r\n        assessDate:null,\r\n        postType:null\r\n      },\r\n      // 表单参数\r\n      form: {\r\n        id:null,\r\n        // 部门领导评分\r\n        deptScore:null,\r\n        // 事业部评分\r\n        businessScore:null,\r\n        // 条线领导评分\r\n        leaderScore:null,\r\n        // 运改组织部评分\r\n        organizationScore:null,\r\n        // 运改组织部加减分理由\r\n        organizationScoreReason:null,\r\n      },\r\n      // 表单校验\r\n      rules: {\r\n      },\r\n      deptOptions:[],\r\n      openCheck:false,\r\n      checkInfo:{},\r\n      // 合并单元格\r\n      spanList:[],\r\n      // 待评分标签\r\n      toCheckLabel:\"待评分(0)\",\r\n      // 横向被考评信息\r\n      beAssessedList:[],\r\n      benefit:null,  // 公司效益分\r\n      userInfo:{\r\n        benefitLinkFlag:null\r\n      },\r\n      benefitDetail:\"\",  // 效益详细\r\n      // 选中数组\r\n      multipleSelection: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 批量快速评分对话框显示状态\r\n      quickScoreDialogVisible: false,\r\n      // 批量快速评分表单参数\r\n      batchQuickScoreForm: {\r\n        score: undefined,\r\n        ids: []\r\n      },\r\n      // 批量快速评分表单验证规则\r\n      batchQuickScoreRules: {\r\n        score: [\r\n          { required: true, message: \"评分不能为空\", trigger: \"blur\" },\r\n          { type: 'number', message: \"评分必须为数字\", trigger: \"blur\" }\r\n        ]\r\n      },\r\n      // 批量快速评分对话框\r\n      batchQuickScoreOpen: false,\r\n      // 选中数组\r\n      ids: [],\r\n      // 选中的行数据\r\n      selectedRows: [],\r\n    };\r\n  },\r\n  computed: {\r\n    // 是否可以提交批量评分\r\n    canSubmitBatchScore() {\r\n      if (this.selectedRows.length === 0) return false;\r\n      \r\n      // 简单检查是否所有行都填写了评分\r\n      for (let row of this.selectedRows) {\r\n        if (!row.quickScore && row.quickScore !== 0) {\r\n          return false;\r\n        }\r\n      }\r\n      \r\n      return true;\r\n    }\r\n  },\r\n  created() {\r\n    this.queryParams.assessDate = this.getDefaultAssessDate()\r\n    this.checkedQueryParams.assessDate = this.getDefaultAssessDate()\r\n    // this.getSelfAssessUser();\r\n    // this.getCheckDeptList();\r\n    this.getTreeselect();\r\n    this.getList();\r\n    this.getCheckedList();\r\n  },\r\n  methods: {\r\n\r\n    // 获取默认考核日期\r\n    getDefaultAssessDate() {\r\n      const now = new Date();\r\n      const currentDay = now.getDate();\r\n\r\n      let targetDate;\r\n      if (currentDay < 10) {\r\n        // 当前日期小于10日，默认为上个月\r\n        targetDate = new Date(now.getFullYear(), now.getMonth() - 1, 1);\r\n      } else {\r\n        // 当前日期大于等于10日，默认为当月\r\n        targetDate = new Date(now.getFullYear(), now.getMonth(), 1);\r\n      }\r\n\r\n      // 格式化为 YYYY-M 格式\r\n      const year = targetDate.getFullYear();\r\n      const month = targetDate.getMonth() + 1;\r\n      return `${year}-${month}`;\r\n    },\r\n\r\n    // 获取被考核信息\r\n    getBeAssessedList(param){\r\n      listBeAssessed(param).then(res =>{\r\n        let beAssessedList = [];\r\n        if(res.code == 200){\r\n          if(res.data.length > 0){\r\n            res.data.forEach(item => {\r\n              beAssessedList = [...beAssessedList,...item.hrLateralAssessInfoList]\r\n            })\r\n            this.beAssessedList = beAssessedList;\r\n          }\r\n        }\r\n        console.log(beAssessedList)\r\n      })\r\n    },\r\n    // 获取被评分人员信息\r\n    getSelfAssessUser(param){\r\n      getSelfAssessUser(param).then(res =>{\r\n        if(res.code == 200){\r\n          this.userInfo = res.data;\r\n        }\r\n      })\r\n    },\r\n    /** 转换横向评价部门数据结构 */\r\n    normalizer(node) {\r\n      if (node.children && !node.children.length) {\r\n        delete node.children;\r\n      }\r\n      return {\r\n        id: node.deptId,\r\n        label: node.deptName,\r\n        children: node.children\r\n      };\r\n    },\r\n\t  /** 查询横向评价部门下拉树结构 */\r\n    getTreeselect() {\r\n      listDept().then(response => {\r\n        this.deptOptions = this.handleTree(response.data, \"deptId\", \"parentId\");\r\n      });\r\n    },\r\n    /** 查询绩效考核-干部自评待审核列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listInfo(this.queryParams).then(response => {\r\n        this.listToCheck = response.rows;\r\n        this.total = response.total;\r\n        this.toCheckLabel = `待评分(${response.total})`\r\n        this.loading = false;\r\n      });\r\n    },\r\n    /** 获取已审核列表 */\r\n    getCheckedList(){\r\n      this.loading = true;\r\n      listChecked(this.checkedQueryParams).then(res => {\r\n        this.listChecked = res.rows;\r\n        this.checkedTotal = res.total;\r\n        this.loading = false;\r\n      })\r\n    },\r\n\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        id: null,\r\n        organizationScore: null,\r\n        organizationScoreReason: null,\r\n      };\r\n      this.benefit = null;\r\n      this.benefitDetail = \"\";\r\n      this.userInfo = {\r\n        benefitLinkFlag:null\r\n      }\r\n      // this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.checkedQueryParams.pageNum = 1;\r\n      // 同步搜索条件\r\n      this.checkedQueryParams.name = this.queryParams.name;\r\n      this.checkedQueryParams.deptId = this.queryParams.deptId;\r\n      this.checkedQueryParams.assessDate = this.queryParams.assessDate;\r\n      this.checkedQueryParams.postType = this.queryParams.postType;\r\n      this.getList();\r\n      this.getCheckedList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n\r\n    // 审批详情\r\n    handleCheckDetail(row){\r\n      getInfo({id:row.id}).then(res => {\r\n        console.log(res);\r\n        this.reset();\r\n        if(res.code == 200){\r\n          this.checkInfo = res.data;\r\n          let list = JSON.parse(res.data.content);\r\n          this.handleSpanList(list);\r\n          let param ={\r\n            deptId:res.data.deptId,\r\n            assessDate:res.data.assessDate\r\n          }\r\n          this.getBeAssessedList(param);  // 获取横向评分被考核数据\r\n          this.getSelfAssessUser({id:res.data.userId});  // 获取用户信息\r\n          this.checkInfo.list = list;\r\n\r\n          // 初始化表单数据\r\n          this.form.organizationScore = res.data.organizationScore;\r\n          this.form.organizationScoreReason = res.data.organizationScoreReason;\r\n        }\r\n        this.open = true\r\n      })\r\n    },\r\n\r\n    // 审批提交\r\n    checkSubmit(){\r\n      if(this.verify()){\r\n        this.form.id = this.checkInfo.id;\r\n        this.form.status = this.checkInfo.status;\r\n        check(this.form).then(res => {\r\n          console.log(res)\r\n          if(res.code == 200){\r\n            this.$message({\r\n              type: 'success',\r\n              message: '提交成功!'\r\n            });\r\n            this.reset();\r\n            this.open = false;\r\n            this.getList();\r\n            this.getCheckedList();\r\n          }else{\r\n            this.$message({\r\n              type: 'warning',\r\n              message: '操作失败，无权限或当前审批状态不匹配'\r\n            });\r\n          }\r\n        })\r\n      }else{\r\n        this.$message({\r\n          type: 'warning',\r\n          message: '请填写评分'\r\n        });\r\n        return false;\r\n      }\r\n    },\r\n\r\n    // 退回点击事件\r\n    rejectClick(){\r\n      this.rejectOpen = true;\r\n    },\r\n\r\n    rejectCancel(){\r\n      this.checkInfo.rejectReason = null;\r\n      this.rejectOpen = false;\r\n    },\r\n\r\n    // 退回\r\n    rejectSubmit(){\r\n      if(!this.checkInfo.rejectReason){\r\n        this.$message({\r\n          type: 'warning',\r\n          message: '请填写退回原因'\r\n        });\r\n        return;\r\n      }\r\n      this.$confirm('确认后, 是否继续?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.reject();\r\n      }).catch(() => {\r\n          \r\n      });\r\n    },\r\n\r\n    reject(){\r\n      rejectInfo(this.checkInfo).then(res => {\r\n          this.reset();\r\n          this.checkInfo.rejectReason = null;\r\n          this.rejectOpen = false;\r\n          this.open = false;\r\n          this.getList();\r\n          this.getCheckedList();\r\n      })\r\n    },\r\n\r\n\r\n    // 数据验证\r\n    verify(){\r\n      if(!this.form.organizationScore) {\r\n        this.$message.warning('请输入运改组织部评分');\r\n        return false;\r\n      }\r\n\r\n      // 检查是否需要填写加减分理由\r\n      let previousScore = null;\r\n      let previousScoreName = '';\r\n\r\n      if (this.checkInfo.businessScore) {\r\n        previousScore = parseFloat(this.checkInfo.businessScore);\r\n        previousScoreName = '事业部评分';\r\n      } else if (this.checkInfo.deptScore) {\r\n        previousScore = parseFloat(this.checkInfo.deptScore);\r\n        previousScoreName = '部门领导评分';\r\n      } else if (this.checkInfo.selfScore) {\r\n        previousScore = parseFloat(this.checkInfo.selfScore);\r\n        previousScoreName = '自评分';\r\n      }\r\n\r\n      // 运改组织部评分与上一环节评分不一致时，加减分理由必填\r\n      if (previousScore !== null && parseFloat(this.form.organizationScore) !== previousScore && !this.form.organizationScoreReason) {\r\n        this.$message.warning(`运改组织部评分(${this.form.organizationScore}分)与${previousScoreName}(${previousScore}分)不一致，请填写加减分理由`);\r\n        return false;\r\n      }\r\n\r\n      return true;\r\n    },\r\n\r\n    handleListChange(type){\r\n      console.log(type)\r\n    },\r\n    // 处理列表\r\n    handleSpanList(data){\r\n      let spanList = [];\r\n      let flag = 0;\r\n      for(let i = 0; i < data.length; i++){\r\n        // 相同考核项合并\r\n        if(i == 0){\r\n          spanList.push({\r\n            rowspan: 1,\r\n            colspan: 1\r\n          })\r\n        }else{\r\n          if(data[i - 1].item == data[i].item){\r\n            spanList.push({\r\n              rowspan: 0,\r\n              colspan: 0\r\n            })\r\n            spanList[flag].rowspan += 1;\r\n          }else{\r\n            spanList.push({\r\n              rowspan: 1,\r\n              colspan: 1\r\n            })\r\n            flag = i;\r\n          }\r\n        }\r\n      }\r\n      this.spanList = spanList;\r\n    },\r\n\r\n    // 合并单元格方法\r\n    objectSpanMethod({ row, column, rowIndex, columnIndex }) {\r\n      // 第一列相同项合并\r\n      if (columnIndex === 0) {\r\n        return this.spanList[rowIndex];\r\n      }\r\n      // 类别无内容 合并\r\n      if(columnIndex === 1){\r\n        if(!row.category){\r\n          return {\r\n            rowspan: 0,\r\n            colspan: 0\r\n          }\r\n        }\r\n      }\r\n      if(columnIndex === 2){\r\n        if(!row.category){\r\n          return {\r\n            rowspan: 1,\r\n            colspan: 2\r\n          }\r\n        }\r\n      }\r\n    },\r\n    // 计算效益\r\n    calScore(){\r\n      if(this.benefit || this.benefit == 0){\r\n        let benefit = Number(this.benefit);\r\n\r\n        // 确定前一步评分：优先事业部评分，其次部门评分，最后自评分\r\n        let previousScore = 0;\r\n        let previousScoreName = '';\r\n        if (this.checkInfo.businessScore !== null && this.checkInfo.businessScore !== undefined && this.checkInfo.businessScore !== '') {\r\n          // 有事业部评分，以事业部评分为基础\r\n          previousScore = Number(this.checkInfo.businessScore);\r\n          previousScoreName = '事业部评分';\r\n        } else if (this.checkInfo.deptScore !== null && this.checkInfo.deptScore !== undefined && this.checkInfo.deptScore !== '') {\r\n          // 没有事业部评分，以部门评分为基础\r\n          previousScore = Number(this.checkInfo.deptScore);\r\n          previousScoreName = '部门评分';\r\n        } else {\r\n          // 都没有，以自评分为基础\r\n          previousScore = Number(this.checkInfo.selfScore);\r\n          previousScoreName = '自评分';\r\n        }\r\n\r\n        let categoryScore = 0;\r\n        if(this.userInfo.benefitLinkFlag == \"N\"){\r\n          this.checkInfo.list.forEach(row => {\r\n            console.log(row)\r\n            if(row.category == \"效益\"){\r\n              categoryScore += Number(row.dePoints);\r\n            }\r\n          })\r\n          this.form.organizationScore = parseFloat((previousScore + (benefit / 2) - (categoryScore / 2)).toFixed(1));\r\n          this.benefitDetail = `计算：${previousScoreName}(${previousScore}) ${benefit > 0 ? \"+\" : \"-\"} ${Math.abs(benefit / 2)} ${categoryScore > 0 ? \"-\" : \"+\"} ${Math.abs(categoryScore / 2)} = ${this.form.organizationScore}`\r\n        }else{\r\n          this.form.organizationScore = parseFloat((previousScore + benefit).toFixed(1));\r\n          this.benefitDetail = `计算：${previousScoreName}(${previousScore}) ${benefit > 0 ? \"+\" : \"-\"} ${Math.abs(benefit)} = ${this.form.organizationScore}`\r\n        }\r\n      }else{\r\n        this.$message({\r\n          type: 'warning',\r\n          message: '请填写公司效益加减分'\r\n        });\r\n        return;\r\n      }\r\n    },\r\n    // 批量计算\r\n    batchCalScore(){\r\n      if(this.benefit || this.benefit == 0){\r\n        let benefit = Number(this.benefit);\r\n        this.selectedRows.forEach(row => {\r\n          // 确定前一步评分：优先事业部评分，其次部门评分，最后自评分\r\n          let previousScore = 0;\r\n          if (row.businessScore !== null && row.businessScore !== undefined && row.businessScore !== '') {\r\n            // 有事业部评分，以事业部评分为基础\r\n            previousScore = Number(row.businessScore);\r\n          } else if (row.deptScore !== null && row.deptScore !== undefined && row.deptScore !== '') {\r\n            // 没有事业部评分，以部门评分为基础\r\n            previousScore = Number(row.deptScore);\r\n          } else {\r\n            // 都没有，以自评分为基础\r\n            previousScore = Number(row.selfScore);\r\n          }\r\n          \r\n          if(row.benefitLinkFlag == \"N\"){\r\n            row.quickScore = parseFloat((previousScore + (benefit / 2) - (Number(row.benefitScore) / 2)).toFixed(1));\r\n          }else{\r\n            row.quickScore = parseFloat((previousScore + benefit).toFixed(1));\r\n          }\r\n        })\r\n      }else{\r\n        this.$message({\r\n          type: 'warning',\r\n          message: '请填写公司效益加减分'\r\n        });\r\n        return;\r\n      }\r\n    },\r\n\r\n    /** 选择条数改变 */\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.id)\r\n      this.selectedRows = selection\r\n      this.single = selection.length !== 1\r\n      this.multiple = !selection.length\r\n    },\r\n\r\n    /** 批量快速评分按钮操作 */\r\n    handleBatchQuickScore() {\r\n      if (this.ids.length === 0) {\r\n        this.$modal.msgError(\"请选择需要评分的数据\");\r\n        return;\r\n      }\r\n      batchWithBenefitByIds(this.ids).then(res => {\r\n        if(res.code == 200){\r\n          this.selectedRows = res.data;\r\n          this.batchQuickScoreOpen = true;\r\n        }\r\n      })\r\n      // // 检查是否有未填写评分的记录\r\n      // const emptyScores = this.selectedRows.filter(row => !row.quickScore);\r\n      // if (emptyScores.length > 0) {\r\n      //   this.$modal.msgError(`有${emptyScores.length}条记录未填写快速评分，请先填写评分`);\r\n      //   return;\r\n      // }\r\n    },\r\n\r\n    /** 取消批量快速评分操作 */\r\n    cancelBatchQuickScore() {\r\n      this.batchQuickScoreOpen = false;\r\n    },\r\n\r\n    /** 提交批量快速评分 */\r\n    submitBatchQuickScore() {\r\n      // 验证评分一致性和理由必填\r\n      const validationResult = this.validateBatchQuickScore();\r\n      if (!validationResult.isValid) {\r\n        this.$modal.msgError(validationResult.message);\r\n        return;\r\n      }\r\n\r\n      // 准备提交数据\r\n      const submitData = this.selectedRows.map(row => ({\r\n        id: row.id,\r\n        quickScore: row.quickScore,\r\n        quickReason: row.quickReason\r\n      }));\r\n\r\n      this.$modal.confirm('是否确认提交选中人员的快速评分？').then(() => {\r\n        return batchQuickScore(submitData);\r\n      }).then(() => {\r\n        this.$modal.msgSuccess(\"批量评分成功\");\r\n        this.batchQuickScoreOpen = false;\r\n        this.getList();\r\n        this.getCheckedList();\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 验证批量快速评分 */\r\n    validateBatchQuickScore() {\r\n      for (let i = 0; i < this.selectedRows.length; i++) {\r\n        const row = this.selectedRows[i];\r\n        \r\n        // 检查是否填写了评分\r\n        if (!row.quickScore && row.quickScore !== 0) {\r\n          return {\r\n            isValid: false,\r\n            message: `第${i + 1}行 ${row.name} 未填写评分，请先填写评分`\r\n          };\r\n        }\r\n\r\n        // 运改组织部评分时的验证\r\n        if (row.status == '3') {\r\n          let previousScore = null;\r\n          let previousScoreName = '';\r\n          \r\n          // 判断上一环节评分\r\n          if (row.businessScore !== null && row.businessScore !== undefined && row.businessScore !== '') {\r\n            // 有事业部评分，以事业部评分为准\r\n            previousScore = parseFloat(row.businessScore);\r\n            previousScoreName = '事业部领导评分';\r\n          } else if (row.deptScore !== null && row.deptScore !== undefined && row.deptScore !== '') {\r\n            // 没有事业部评分，以部门评分为准\r\n            previousScore = parseFloat(row.deptScore);\r\n            previousScoreName = '部门领导评分';\r\n          } else {\r\n            // 都没有，以自评分为准\r\n            previousScore = parseFloat(row.selfScore);\r\n            previousScoreName = '自评分';\r\n          }\r\n          \r\n          // 运改组织部评分与上一环节评分不一致时，加减分理由必填\r\n          if (parseFloat(row.quickScore) !== previousScore && !row.quickReason) {\r\n            return {\r\n              isValid: false,\r\n              message: `第${i + 1}行 ${row.name} 运改组织部评分(${row.quickScore}分)与${previousScoreName}(${previousScore}分)不一致，请填写加减分理由`\r\n            };\r\n          }\r\n        }\r\n      }\r\n\r\n      return { isValid: true };\r\n    },\r\n\r\n    /** 查看评分记录详情 */\r\n    handleCheckedDetail(row) {\r\n      getInfo({id: row.infoId}).then(res => {\r\n        console.log(res);\r\n        this.reset();\r\n        if(res.code == 200){\r\n          this.checkInfo = res.data;\r\n          let list = JSON.parse(res.data.content);\r\n          this.handleSpanList(list);\r\n          let param ={\r\n            deptId:res.data.deptId,\r\n            assessDate:res.data.assessDate\r\n          }\r\n          this.getBeAssessedList(param);  // 获取横向评分被考核数据\r\n          this.getSelfAssessUser({id:res.data.userId});  // 获取用户信息\r\n          this.checkInfo.list = list;\r\n          this.open = true;\r\n        }\r\n      }).catch(error => {\r\n        this.$message.error('获取详情失败');\r\n      });\r\n    },\r\n\r\n    // 获取历史备注（各步骤加减分原因拼接）\r\n    getHistoryRemarks(row) {\r\n      const remarks = [];\r\n\r\n      // 部门领导加减分理由\r\n      if (row.deptScoreReason && row.deptScoreReason.trim()) {\r\n        remarks.push(`部门领导：${row.deptScoreReason.trim()}`);\r\n      }\r\n\r\n      // 事业部加减分理由\r\n      if (row.businessScoreReason && row.businessScoreReason.trim()) {\r\n        remarks.push(`事业部：${row.businessScoreReason.trim()}`);\r\n      }\r\n\r\n      // 运改部/组织部加减分理由\r\n      if (row.organizationScoreReason && row.organizationScoreReason.trim()) {\r\n        remarks.push(`运改部/组织部：${row.organizationScoreReason.trim()}`);\r\n      }\r\n\r\n      return remarks.join('\\n');\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.assessment-detail-dialog .detail-container {\r\n  padding: 20px;\r\n  background-color: #f5f7fa;\r\n  min-height: 100vh;\r\n}\r\n\r\n.assessment-detail-dialog .detail-header h2 {\r\n  background: linear-gradient(135deg, #409EFF, #67C23A);\r\n  background-clip: text;\r\n  -webkit-background-clip: text;\r\n  color: transparent;\r\n  font-weight: bold;\r\n}\r\n\r\n.assessment-detail-dialog .assessment-table-card {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.assessment-detail-dialog .signature-card {\r\n  background: #ffffff;\r\n}\r\n\r\n.signature-content {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.score-text {\r\n  font-weight: 500;\r\n  color: #303133;\r\n}\r\n\r\n.separator {\r\n  color: #909399;\r\n  margin: 0 4px;\r\n}\r\n\r\n.signature-name {\r\n  color: #303133;\r\n}\r\n\r\n.reason-text {\r\n  width: 100%;\r\n  margin-top: 8px;\r\n  padding: 8px 12px;\r\n  background-color: #f8f9fa;\r\n  border-left: 3px solid #409EFF;\r\n  border-radius: 4px;\r\n}\r\n\r\n.reason-label {\r\n  font-weight: 500;\r\n  color: #606266;\r\n  margin-right: 8px;\r\n}\r\n\r\n.reason-content {\r\n  color: #303133;\r\n  line-height: 1.6;\r\n}\r\n\r\n.benefit-detail {\r\n  color: #909399;\r\n  font-size: 13px;\r\n  padding: 8px 12px;\r\n  background-color: #f4f4f5;\r\n  border-radius: 4px;\r\n  border-left: 3px solid #e6a23c;\r\n}\r\n\r\n.dialog-footer {\r\n  border-top: 1px solid #e4e7ed;\r\n  background-color: #ffffff;\r\n  border-radius: 0 0 6px 6px;\r\n}\r\n\r\n.assessment-detail-dialog .el-card {\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.assessment-detail-dialog .el-descriptions {\r\n  background-color: #ffffff;\r\n}\r\n\r\n.assessment-detail-dialog .el-table {\r\n  border-radius: 6px;\r\n  overflow: hidden;\r\n}\r\n\r\n.text-red {\r\n  color: #F56C6C;\r\n}\r\n\r\n.history-remarks-cell {\r\n  max-width: 180px;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n  font-size: 12px;\r\n  color: #606266;\r\n  line-height: 1.4;\r\n  cursor: pointer;\r\n}\r\n\r\n.history-remarks-cell:hover {\r\n  color: #409EFF;\r\n}\r\n\r\n.batch-history-remarks-cell {\r\n  max-width: 180px;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n  font-size: 12px;\r\n  color: #606266;\r\n  line-height: 1.4;\r\n  cursor: pointer;\r\n}\r\n\r\n.batch-history-remarks-cell:hover {\r\n  color: #409EFF;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAkgBA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,KAAA,GAAAF,OAAA;AACA,IAAAG,cAAA,GAAAC,sBAAA,CAAAJ,OAAA;AACAA,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAK,IAAA;EACAC,UAAA;IACAC,UAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,UAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACAC,YAAA;MACA;MACAC,WAAA;MACAC,WAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,MAAA;QACAhB,IAAA;QACAiB,MAAA;QACAC,UAAA;QACAC,MAAA;QACAC,QAAA;MACA;MACA;MACAC,kBAAA;QACAP,OAAA;QACAC,QAAA;QACAC,MAAA;QACAhB,IAAA;QACAiB,MAAA;QACAC,UAAA;QACAE,QAAA;MACA;MACA;MACAE,IAAA;QACAC,EAAA;QACA;QACAC,SAAA;QACA;QACAC,aAAA;QACA;QACAC,WAAA;QACA;QACAC,iBAAA;QACA;QACAC,uBAAA;MACA;MACA;MACAC,KAAA,GACA;MACAC,WAAA;MACAC,SAAA;MACAC,SAAA;MACA;MACAC,QAAA;MACA;MACAC,YAAA;MACA;MACAC,cAAA;MACAC,OAAA;MAAA;MACAC,QAAA;QACAC,eAAA;MACA;MACAC,aAAA;MAAA;MACA;MACAC,iBAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,uBAAA;MACA;MACAC,mBAAA;QACAC,KAAA,EAAAC,SAAA;QACAC,GAAA;MACA;MACA;MACAC,oBAAA;QACAH,KAAA,GACA;UAAAI,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,IAAA;UAAAF,OAAA;UAAAC,OAAA;QAAA;MAEA;MACA;MACAE,mBAAA;MACA;MACAN,GAAA;MACA;MACAO,YAAA;IACA;EACA;EACAC,QAAA;IACA;IACAC,mBAAA,WAAAA,oBAAA;MACA,SAAAF,YAAA,CAAAG,MAAA;;MAEA;MAAA,IAAAC,SAAA,OAAAC,2BAAA,CAAAC,OAAA,EACA,KAAAN,YAAA;QAAAO,KAAA;MAAA;QAAA,KAAAH,SAAA,CAAAI,CAAA,MAAAD,KAAA,GAAAH,SAAA,CAAAK,CAAA,IAAAC,IAAA;UAAA,IAAAC,GAAA,GAAAJ,KAAA,CAAAK,KAAA;UACA,KAAAD,GAAA,CAAAE,UAAA,IAAAF,GAAA,CAAAE,UAAA;YACA;UACA;QACA;MAAA,SAAAC,GAAA;QAAAV,SAAA,CAAAW,CAAA,CAAAD,GAAA;MAAA;QAAAV,SAAA,CAAAY,CAAA;MAAA;MAEA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAA1D,WAAA,CAAAK,UAAA,QAAAsD,oBAAA;IACA,KAAAnD,kBAAA,CAAAH,UAAA,QAAAsD,oBAAA;IACA;IACA;IACA,KAAAC,aAAA;IACA,KAAAC,OAAA;IACA,KAAAC,cAAA;EACA;EACAC,OAAA;IAEA;IACAJ,oBAAA,WAAAA,qBAAA;MACA,IAAAK,GAAA,OAAAC,IAAA;MACA,IAAAC,UAAA,GAAAF,GAAA,CAAAG,OAAA;MAEA,IAAAC,UAAA;MACA,IAAAF,UAAA;QACA;QACAE,UAAA,OAAAH,IAAA,CAAAD,GAAA,CAAAK,WAAA,IAAAL,GAAA,CAAAM,QAAA;MACA;QACA;QACAF,UAAA,OAAAH,IAAA,CAAAD,GAAA,CAAAK,WAAA,IAAAL,GAAA,CAAAM,QAAA;MACA;;MAEA;MACA,IAAAC,IAAA,GAAAH,UAAA,CAAAC,WAAA;MACA,IAAAG,KAAA,GAAAJ,UAAA,CAAAE,QAAA;MACA,UAAAG,MAAA,CAAAF,IAAA,OAAAE,MAAA,CAAAD,KAAA;IACA;IAEA;IACAE,iBAAA,WAAAA,kBAAAC,KAAA;MAAA,IAAAC,KAAA;MACA,IAAAC,oBAAA,EAAAF,KAAA,EAAAG,IAAA,WAAAC,GAAA;QACA,IAAAzD,cAAA;QACA,IAAAyD,GAAA,CAAAC,IAAA;UACA,IAAAD,GAAA,CAAAzF,IAAA,CAAAsD,MAAA;YACAmC,GAAA,CAAAzF,IAAA,CAAA2F,OAAA,WAAAC,IAAA;cACA5D,cAAA,MAAAmD,MAAA,KAAAU,mBAAA,CAAApC,OAAA,EAAAzB,cAAA,OAAA6D,mBAAA,CAAApC,OAAA,EAAAmC,IAAA,CAAAE,uBAAA;YACA;YACAR,KAAA,CAAAtD,cAAA,GAAAA,cAAA;UACA;QACA;QACA+D,OAAA,CAAAC,GAAA,CAAAhE,cAAA;MACA;IACA;IACA;IACAiE,iBAAA,WAAAA,kBAAAZ,KAAA;MAAA,IAAAa,MAAA;MACA,IAAAD,uBAAA,EAAAZ,KAAA,EAAAG,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAQ,MAAA,CAAAhE,QAAA,GAAAuD,GAAA,CAAAzF,IAAA;QACA;MACA;IACA;IACA,mBACAmG,UAAA,WAAAA,WAAAC,IAAA;MACA,IAAAA,IAAA,CAAAC,QAAA,KAAAD,IAAA,CAAAC,QAAA,CAAA/C,MAAA;QACA,OAAA8C,IAAA,CAAAC,QAAA;MACA;MACA;QACAjF,EAAA,EAAAgF,IAAA,CAAAtF,MAAA;QACAwF,KAAA,EAAAF,IAAA,CAAAG,QAAA;QACAF,QAAA,EAAAD,IAAA,CAAAC;MACA;IACA;IACA,oBACA/B,aAAA,WAAAA,cAAA;MAAA,IAAAkC,MAAA;MACA,IAAAC,cAAA,IAAAjB,IAAA,WAAAkB,QAAA;QACAF,MAAA,CAAA7E,WAAA,GAAA6E,MAAA,CAAAG,UAAA,CAAAD,QAAA,CAAA1G,IAAA;MACA;IACA;IACA,uBACAuE,OAAA,WAAAA,QAAA;MAAA,IAAAqC,MAAA;MACA,KAAA3G,OAAA;MACA,IAAA4G,cAAA,OAAAnG,WAAA,EAAA8E,IAAA,WAAAkB,QAAA;QACAE,MAAA,CAAAtG,WAAA,GAAAoG,QAAA,CAAAI,IAAA;QACAF,MAAA,CAAAxG,KAAA,GAAAsG,QAAA,CAAAtG,KAAA;QACAwG,MAAA,CAAA7E,YAAA,yBAAAoD,MAAA,CAAAuB,QAAA,CAAAtG,KAAA;QACAwG,MAAA,CAAA3G,OAAA;MACA;IACA;IACA,cACAuE,cAAA,WAAAA,eAAA;MAAA,IAAAuC,MAAA;MACA,KAAA9G,OAAA;MACA,IAAAM,iBAAA,OAAAW,kBAAA,EAAAsE,IAAA,WAAAC,GAAA;QACAsB,MAAA,CAAAxG,WAAA,GAAAkF,GAAA,CAAAqB,IAAA;QACAC,MAAA,CAAA1G,YAAA,GAAAoF,GAAA,CAAArF,KAAA;QACA2G,MAAA,CAAA9G,OAAA;MACA;IACA;IAEA;IACA+G,MAAA,WAAAA,OAAA;MACA,KAAAvG,IAAA;MACA,KAAAwG,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAA9F,IAAA;QACAC,EAAA;QACAI,iBAAA;QACAC,uBAAA;MACA;MACA,KAAAQ,OAAA;MACA,KAAAG,aAAA;MACA,KAAAF,QAAA;QACAC,eAAA;MACA;MACA;IACA;IACA,aACA+E,WAAA,WAAAA,YAAA;MACA,KAAAxG,WAAA,CAAAC,OAAA;MACA,KAAAO,kBAAA,CAAAP,OAAA;MACA;MACA,KAAAO,kBAAA,CAAArB,IAAA,QAAAa,WAAA,CAAAb,IAAA;MACA,KAAAqB,kBAAA,CAAAJ,MAAA,QAAAJ,WAAA,CAAAI,MAAA;MACA,KAAAI,kBAAA,CAAAH,UAAA,QAAAL,WAAA,CAAAK,UAAA;MACA,KAAAG,kBAAA,CAAAD,QAAA,QAAAP,WAAA,CAAAO,QAAA;MACA,KAAAsD,OAAA;MACA,KAAAC,cAAA;IACA;IACA,aACA2C,UAAA,WAAAA,WAAA;MACA,KAAAC,SAAA;MACA,KAAAF,WAAA;IACA;IAEA;IACAG,iBAAA,WAAAA,kBAAAvD,GAAA;MAAA,IAAAwD,MAAA;MACA,IAAAC,aAAA;QAAAnG,EAAA,EAAA0C,GAAA,CAAA1C;MAAA,GAAAoE,IAAA,WAAAC,GAAA;QACAM,OAAA,CAAAC,GAAA,CAAAP,GAAA;QACA6B,MAAA,CAAAL,KAAA;QACA,IAAAxB,GAAA,CAAAC,IAAA;UACA4B,MAAA,CAAAzF,SAAA,GAAA4D,GAAA,CAAAzF,IAAA;UACA,IAAAwH,IAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAjC,GAAA,CAAAzF,IAAA,CAAA2H,OAAA;UACAL,MAAA,CAAAM,cAAA,CAAAJ,IAAA;UACA,IAAAnC,KAAA;YACAvE,MAAA,EAAA2E,GAAA,CAAAzF,IAAA,CAAAc,MAAA;YACAC,UAAA,EAAA0E,GAAA,CAAAzF,IAAA,CAAAe;UACA;UACAuG,MAAA,CAAAlC,iBAAA,CAAAC,KAAA;UACAiC,MAAA,CAAArB,iBAAA;YAAA7E,EAAA,EAAAqE,GAAA,CAAAzF,IAAA,CAAA6H;UAAA;UACAP,MAAA,CAAAzF,SAAA,CAAA2F,IAAA,GAAAA,IAAA;;UAEA;UACAF,MAAA,CAAAnG,IAAA,CAAAK,iBAAA,GAAAiE,GAAA,CAAAzF,IAAA,CAAAwB,iBAAA;UACA8F,MAAA,CAAAnG,IAAA,CAAAM,uBAAA,GAAAgE,GAAA,CAAAzF,IAAA,CAAAyB,uBAAA;QACA;QACA6F,MAAA,CAAA7G,IAAA;MACA;IACA;IAEA;IACAqH,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA,SAAAC,MAAA;QACA,KAAA7G,IAAA,CAAAC,EAAA,QAAAS,SAAA,CAAAT,EAAA;QACA,KAAAD,IAAA,CAAAH,MAAA,QAAAa,SAAA,CAAAb,MAAA;QACA,IAAAiH,WAAA,OAAA9G,IAAA,EAAAqE,IAAA,WAAAC,GAAA;UACAM,OAAA,CAAAC,GAAA,CAAAP,GAAA;UACA,IAAAA,GAAA,CAAAC,IAAA;YACAqC,MAAA,CAAAG,QAAA;cACAjF,IAAA;cACAF,OAAA;YACA;YACAgF,MAAA,CAAAd,KAAA;YACAc,MAAA,CAAAtH,IAAA;YACAsH,MAAA,CAAAxD,OAAA;YACAwD,MAAA,CAAAvD,cAAA;UACA;YACAuD,MAAA,CAAAG,QAAA;cACAjF,IAAA;cACAF,OAAA;YACA;UACA;QACA;MACA;QACA,KAAAmF,QAAA;UACAjF,IAAA;UACAF,OAAA;QACA;QACA;MACA;IACA;IAEA;IACAoF,WAAA,WAAAA,YAAA;MACA,KAAAhI,UAAA;IACA;IAEAiI,YAAA,WAAAA,aAAA;MACA,KAAAvG,SAAA,CAAAwG,YAAA;MACA,KAAAlI,UAAA;IACA;IAEA;IACAmI,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,UAAA1G,SAAA,CAAAwG,YAAA;QACA,KAAAH,QAAA;UACAjF,IAAA;UACAF,OAAA;QACA;QACA;MACA;MACA,KAAAyF,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAzF,IAAA;MACA,GAAAuC,IAAA;QACA+C,MAAA,CAAAI,MAAA;MACA,GAAAC,KAAA,cAEA;IACA;IAEAD,MAAA,WAAAA,OAAA;MAAA,IAAAE,MAAA;MACA,IAAAC,gBAAA,OAAAjH,SAAA,EAAA2D,IAAA,WAAAC,GAAA;QACAoD,MAAA,CAAA5B,KAAA;QACA4B,MAAA,CAAAhH,SAAA,CAAAwG,YAAA;QACAQ,MAAA,CAAA1I,UAAA;QACA0I,MAAA,CAAApI,IAAA;QACAoI,MAAA,CAAAtE,OAAA;QACAsE,MAAA,CAAArE,cAAA;MACA;IACA;IAGA;IACAwD,MAAA,WAAAA,OAAA;MACA,UAAA7G,IAAA,CAAAK,iBAAA;QACA,KAAA0G,QAAA,CAAAa,OAAA;QACA;MACA;;MAEA;MACA,IAAAC,aAAA;MACA,IAAAC,iBAAA;MAEA,SAAApH,SAAA,CAAAP,aAAA;QACA0H,aAAA,GAAAE,UAAA,MAAArH,SAAA,CAAAP,aAAA;QACA2H,iBAAA;MACA,gBAAApH,SAAA,CAAAR,SAAA;QACA2H,aAAA,GAAAE,UAAA,MAAArH,SAAA,CAAAR,SAAA;QACA4H,iBAAA;MACA,gBAAApH,SAAA,CAAAsH,SAAA;QACAH,aAAA,GAAAE,UAAA,MAAArH,SAAA,CAAAsH,SAAA;QACAF,iBAAA;MACA;;MAEA;MACA,IAAAD,aAAA,aAAAE,UAAA,MAAA/H,IAAA,CAAAK,iBAAA,MAAAwH,aAAA,UAAA7H,IAAA,CAAAM,uBAAA;QACA,KAAAyG,QAAA,CAAAa,OAAA,+CAAA5D,MAAA,MAAAhE,IAAA,CAAAK,iBAAA,mBAAA2D,MAAA,CAAA8D,iBAAA,OAAA9D,MAAA,CAAA6D,aAAA;QACA;MACA;MAEA;IACA;IAEAI,gBAAA,WAAAA,iBAAAnG,IAAA;MACA8C,OAAA,CAAAC,GAAA,CAAA/C,IAAA;IACA;IACA;IACA2E,cAAA,WAAAA,eAAA5H,IAAA;MACA,IAAA8B,QAAA;MACA,IAAAuH,IAAA;MACA,SAAAC,CAAA,MAAAA,CAAA,GAAAtJ,IAAA,CAAAsD,MAAA,EAAAgG,CAAA;QACA;QACA,IAAAA,CAAA;UACAxH,QAAA,CAAAyH,IAAA;YACAC,OAAA;YACAC,OAAA;UACA;QACA;UACA,IAAAzJ,IAAA,CAAAsJ,CAAA,MAAA1D,IAAA,IAAA5F,IAAA,CAAAsJ,CAAA,EAAA1D,IAAA;YACA9D,QAAA,CAAAyH,IAAA;cACAC,OAAA;cACAC,OAAA;YACA;YACA3H,QAAA,CAAAuH,IAAA,EAAAG,OAAA;UACA;YACA1H,QAAA,CAAAyH,IAAA;cACAC,OAAA;cACAC,OAAA;YACA;YACAJ,IAAA,GAAAC,CAAA;UACA;QACA;MACA;MACA,KAAAxH,QAAA,GAAAA,QAAA;IACA;IAEA;IACA4H,gBAAA,WAAAA,iBAAAC,IAAA;MAAA,IAAA7F,GAAA,GAAA6F,IAAA,CAAA7F,GAAA;QAAA8F,MAAA,GAAAD,IAAA,CAAAC,MAAA;QAAAC,QAAA,GAAAF,IAAA,CAAAE,QAAA;QAAAC,WAAA,GAAAH,IAAA,CAAAG,WAAA;MACA;MACA,IAAAA,WAAA;QACA,YAAAhI,QAAA,CAAA+H,QAAA;MACA;MACA;MACA,IAAAC,WAAA;QACA,KAAAhG,GAAA,CAAAiG,QAAA;UACA;YACAP,OAAA;YACAC,OAAA;UACA;QACA;MACA;MACA,IAAAK,WAAA;QACA,KAAAhG,GAAA,CAAAiG,QAAA;UACA;YACAP,OAAA;YACAC,OAAA;UACA;QACA;MACA;IACA;IACA;IACAO,QAAA,WAAAA,SAAA;MACA,SAAA/H,OAAA,SAAAA,OAAA;QACA,IAAAA,OAAA,GAAAgI,MAAA,MAAAhI,OAAA;;QAEA;QACA,IAAA+G,aAAA;QACA,IAAAC,iBAAA;QACA,SAAApH,SAAA,CAAAP,aAAA,kBAAAO,SAAA,CAAAP,aAAA,KAAAqB,SAAA,SAAAd,SAAA,CAAAP,aAAA;UACA;UACA0H,aAAA,GAAAiB,MAAA,MAAApI,SAAA,CAAAP,aAAA;UACA2H,iBAAA;QACA,gBAAApH,SAAA,CAAAR,SAAA,kBAAAQ,SAAA,CAAAR,SAAA,KAAAsB,SAAA,SAAAd,SAAA,CAAAR,SAAA;UACA;UACA2H,aAAA,GAAAiB,MAAA,MAAApI,SAAA,CAAAR,SAAA;UACA4H,iBAAA;QACA;UACA;UACAD,aAAA,GAAAiB,MAAA,MAAApI,SAAA,CAAAsH,SAAA;UACAF,iBAAA;QACA;QAEA,IAAAiB,aAAA;QACA,SAAAhI,QAAA,CAAAC,eAAA;UACA,KAAAN,SAAA,CAAA2F,IAAA,CAAA7B,OAAA,WAAA7B,GAAA;YACAiC,OAAA,CAAAC,GAAA,CAAAlC,GAAA;YACA,IAAAA,GAAA,CAAAiG,QAAA;cACAG,aAAA,IAAAD,MAAA,CAAAnG,GAAA,CAAAqG,QAAA;YACA;UACA;UACA,KAAAhJ,IAAA,CAAAK,iBAAA,GAAA0H,UAAA,EAAAF,aAAA,GAAA/G,OAAA,OAAAiI,aAAA,MAAAE,OAAA;UACA,KAAAhI,aAAA,wBAAA+C,MAAA,CAAA8D,iBAAA,OAAA9D,MAAA,CAAA6D,aAAA,QAAA7D,MAAA,CAAAlD,OAAA,uBAAAkD,MAAA,CAAAkF,IAAA,CAAAC,GAAA,CAAArI,OAAA,YAAAkD,MAAA,CAAA+E,aAAA,uBAAA/E,MAAA,CAAAkF,IAAA,CAAAC,GAAA,CAAAJ,aAAA,cAAA/E,MAAA,MAAAhE,IAAA,CAAAK,iBAAA;QACA;UACA,KAAAL,IAAA,CAAAK,iBAAA,GAAA0H,UAAA,EAAAF,aAAA,GAAA/G,OAAA,EAAAmI,OAAA;UACA,KAAAhI,aAAA,wBAAA+C,MAAA,CAAA8D,iBAAA,OAAA9D,MAAA,CAAA6D,aAAA,QAAA7D,MAAA,CAAAlD,OAAA,uBAAAkD,MAAA,CAAAkF,IAAA,CAAAC,GAAA,CAAArI,OAAA,UAAAkD,MAAA,MAAAhE,IAAA,CAAAK,iBAAA;QACA;MACA;QACA,KAAA0G,QAAA;UACAjF,IAAA;UACAF,OAAA;QACA;QACA;MACA;IACA;IACA;IACAwH,aAAA,WAAAA,cAAA;MACA,SAAAtI,OAAA,SAAAA,OAAA;QACA,IAAAA,OAAA,GAAAgI,MAAA,MAAAhI,OAAA;QACA,KAAAkB,YAAA,CAAAwC,OAAA,WAAA7B,GAAA;UACA;UACA,IAAAkF,aAAA;UACA,IAAAlF,GAAA,CAAAxC,aAAA,aAAAwC,GAAA,CAAAxC,aAAA,KAAAqB,SAAA,IAAAmB,GAAA,CAAAxC,aAAA;YACA;YACA0H,aAAA,GAAAiB,MAAA,CAAAnG,GAAA,CAAAxC,aAAA;UACA,WAAAwC,GAAA,CAAAzC,SAAA,aAAAyC,GAAA,CAAAzC,SAAA,KAAAsB,SAAA,IAAAmB,GAAA,CAAAzC,SAAA;YACA;YACA2H,aAAA,GAAAiB,MAAA,CAAAnG,GAAA,CAAAzC,SAAA;UACA;YACA;YACA2H,aAAA,GAAAiB,MAAA,CAAAnG,GAAA,CAAAqF,SAAA;UACA;UAEA,IAAArF,GAAA,CAAA3B,eAAA;YACA2B,GAAA,CAAAE,UAAA,GAAAkF,UAAA,EAAAF,aAAA,GAAA/G,OAAA,OAAAgI,MAAA,CAAAnG,GAAA,CAAA0G,YAAA,OAAAJ,OAAA;UACA;YACAtG,GAAA,CAAAE,UAAA,GAAAkF,UAAA,EAAAF,aAAA,GAAA/G,OAAA,EAAAmI,OAAA;UACA;QACA;MACA;QACA,KAAAlC,QAAA;UACAjF,IAAA;UACAF,OAAA;QACA;QACA;MACA;IACA;IAEA,aACA0H,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAA9H,GAAA,GAAA8H,SAAA,CAAAC,GAAA,WAAA/E,IAAA;QAAA,OAAAA,IAAA,CAAAxE,EAAA;MAAA;MACA,KAAA+B,YAAA,GAAAuH,SAAA;MACA,KAAApI,MAAA,GAAAoI,SAAA,CAAApH,MAAA;MACA,KAAAf,QAAA,IAAAmI,SAAA,CAAApH,MAAA;IACA;IAEA,iBACAsH,qBAAA,WAAAA,sBAAA;MAAA,IAAAC,MAAA;MACA,SAAAjI,GAAA,CAAAU,MAAA;QACA,KAAAwH,MAAA,CAAAC,QAAA;QACA;MACA;MACA,IAAAC,2BAAA,OAAApI,GAAA,EAAA4C,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAmF,MAAA,CAAA1H,YAAA,GAAAsC,GAAA,CAAAzF,IAAA;UACA6K,MAAA,CAAA3H,mBAAA;QACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IAEA,iBACA+H,qBAAA,WAAAA,sBAAA;MACA,KAAA/H,mBAAA;IACA;IAEA,eACAgI,qBAAA,WAAAA,sBAAA;MAAA,IAAAC,MAAA;MACA;MACA,IAAAC,gBAAA,QAAAC,uBAAA;MACA,KAAAD,gBAAA,CAAAE,OAAA;QACA,KAAAR,MAAA,CAAAC,QAAA,CAAAK,gBAAA,CAAArI,OAAA;QACA;MACA;;MAEA;MACA,IAAAwI,UAAA,QAAApI,YAAA,CAAAwH,GAAA,WAAA7G,GAAA;QAAA;UACA1C,EAAA,EAAA0C,GAAA,CAAA1C,EAAA;UACA4C,UAAA,EAAAF,GAAA,CAAAE,UAAA;UACAwH,WAAA,EAAA1H,GAAA,CAAA0H;QACA;MAAA;MAEA,KAAAV,MAAA,CAAAW,OAAA,qBAAAjG,IAAA;QACA,WAAAkG,qBAAA,EAAAH,UAAA;MACA,GAAA/F,IAAA;QACA2F,MAAA,CAAAL,MAAA,CAAAa,UAAA;QACAR,MAAA,CAAAjI,mBAAA;QACAiI,MAAA,CAAA5G,OAAA;QACA4G,MAAA,CAAA3G,cAAA;MACA,GAAAoE,KAAA;IACA;IAEA,eACAyC,uBAAA,WAAAA,wBAAA;MACA,SAAA/B,CAAA,MAAAA,CAAA,QAAAnG,YAAA,CAAAG,MAAA,EAAAgG,CAAA;QACA,IAAAxF,GAAA,QAAAX,YAAA,CAAAmG,CAAA;;QAEA;QACA,KAAAxF,GAAA,CAAAE,UAAA,IAAAF,GAAA,CAAAE,UAAA;UACA;YACAsH,OAAA;YACAvI,OAAA,WAAAoC,MAAA,CAAAmE,CAAA,iBAAAnE,MAAA,CAAArB,GAAA,CAAAjE,IAAA;UACA;QACA;;QAEA;QACA,IAAAiE,GAAA,CAAA9C,MAAA;UACA,IAAAgI,aAAA;UACA,IAAAC,iBAAA;;UAEA;UACA,IAAAnF,GAAA,CAAAxC,aAAA,aAAAwC,GAAA,CAAAxC,aAAA,KAAAqB,SAAA,IAAAmB,GAAA,CAAAxC,aAAA;YACA;YACA0H,aAAA,GAAAE,UAAA,CAAApF,GAAA,CAAAxC,aAAA;YACA2H,iBAAA;UACA,WAAAnF,GAAA,CAAAzC,SAAA,aAAAyC,GAAA,CAAAzC,SAAA,KAAAsB,SAAA,IAAAmB,GAAA,CAAAzC,SAAA;YACA;YACA2H,aAAA,GAAAE,UAAA,CAAApF,GAAA,CAAAzC,SAAA;YACA4H,iBAAA;UACA;YACA;YACAD,aAAA,GAAAE,UAAA,CAAApF,GAAA,CAAAqF,SAAA;YACAF,iBAAA;UACA;;UAEA;UACA,IAAAC,UAAA,CAAApF,GAAA,CAAAE,UAAA,MAAAgF,aAAA,KAAAlF,GAAA,CAAA0H,WAAA;YACA;cACAF,OAAA;cACAvI,OAAA,WAAAoC,MAAA,CAAAmE,CAAA,iBAAAnE,MAAA,CAAArB,GAAA,CAAAjE,IAAA,kDAAAsF,MAAA,CAAArB,GAAA,CAAAE,UAAA,mBAAAmB,MAAA,CAAA8D,iBAAA,OAAA9D,MAAA,CAAA6D,aAAA;YACA;UACA;QACA;MACA;MAEA;QAAAsC,OAAA;MAAA;IACA;IAEA,eACAM,mBAAA,WAAAA,oBAAA9H,GAAA;MAAA,IAAA+H,OAAA;MACA,IAAAtE,aAAA;QAAAnG,EAAA,EAAA0C,GAAA,CAAAgI;MAAA,GAAAtG,IAAA,WAAAC,GAAA;QACAM,OAAA,CAAAC,GAAA,CAAAP,GAAA;QACAoG,OAAA,CAAA5E,KAAA;QACA,IAAAxB,GAAA,CAAAC,IAAA;UACAmG,OAAA,CAAAhK,SAAA,GAAA4D,GAAA,CAAAzF,IAAA;UACA,IAAAwH,IAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAjC,GAAA,CAAAzF,IAAA,CAAA2H,OAAA;UACAkE,OAAA,CAAAjE,cAAA,CAAAJ,IAAA;UACA,IAAAnC,KAAA;YACAvE,MAAA,EAAA2E,GAAA,CAAAzF,IAAA,CAAAc,MAAA;YACAC,UAAA,EAAA0E,GAAA,CAAAzF,IAAA,CAAAe;UACA;UACA8K,OAAA,CAAAzG,iBAAA,CAAAC,KAAA;UACAwG,OAAA,CAAA5F,iBAAA;YAAA7E,EAAA,EAAAqE,GAAA,CAAAzF,IAAA,CAAA6H;UAAA;UACAgE,OAAA,CAAAhK,SAAA,CAAA2F,IAAA,GAAAA,IAAA;UACAqE,OAAA,CAAApL,IAAA;QACA;MACA,GAAAmI,KAAA,WAAAmD,KAAA;QACAF,OAAA,CAAA3D,QAAA,CAAA6D,KAAA;MACA;IACA;IAEA;IACAC,iBAAA,WAAAA,kBAAAlI,GAAA;MACA,IAAAmI,OAAA;;MAEA;MACA,IAAAnI,GAAA,CAAAoI,eAAA,IAAApI,GAAA,CAAAoI,eAAA,CAAAC,IAAA;QACAF,OAAA,CAAA1C,IAAA,kCAAApE,MAAA,CAAArB,GAAA,CAAAoI,eAAA,CAAAC,IAAA;MACA;;MAEA;MACA,IAAArI,GAAA,CAAAsI,mBAAA,IAAAtI,GAAA,CAAAsI,mBAAA,CAAAD,IAAA;QACAF,OAAA,CAAA1C,IAAA,4BAAApE,MAAA,CAAArB,GAAA,CAAAsI,mBAAA,CAAAD,IAAA;MACA;;MAEA;MACA,IAAArI,GAAA,CAAArC,uBAAA,IAAAqC,GAAA,CAAArC,uBAAA,CAAA0K,IAAA;QACAF,OAAA,CAAA1C,IAAA,+CAAApE,MAAA,CAAArB,GAAA,CAAArC,uBAAA,CAAA0K,IAAA;MACA;MAEA,OAAAF,OAAA,CAAAI,IAAA;IACA;EACA;AACA", "ignoreList": []}]}