package com.ruoyi.app.supply.service.impl;

import com.github.pagehelper.PageInfo;
import com.ruoyi.app.supply.domain.SupplyInfo;
import com.ruoyi.app.supply.mapper.SupplyInfoMapper;
import com.ruoyi.app.supply.service.ISupplyInfoService;
import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.constant.HttpStatus;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.DataSourceType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 供应商信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class SupplyInfoServiceImpl implements ISupplyInfoService {

    @Autowired
    private SupplyInfoMapper supplyInfoMapper;

    /**
     * 查询供应商信息
     *
     * @param supplyCode 供应商代码
     * @return 供应商信息
     */
    @Override
    @DataSource(value = DataSourceType.SUPPLY)
    public SupplyInfo selectSupplyInfoByCode(Integer supplyCode) {
        return supplyInfoMapper.selectSupplyInfoByCode(supplyCode);
    }

    /**
     * 查询供应商信息列表
     *
     * @param supplyInfo 供应商信息
     * @return 供应商信息集合
     */
    @Override
    @DataSource(value = DataSourceType.SUPPLY)
    public List<SupplyInfo> selectSupplyInfoList(SupplyInfo supplyInfo) {
        return supplyInfoMapper.selectSupplyInfoList(supplyInfo);
    }

    /**
     * 查询供应商信息分页列表
     *
     * @param supplyInfo 供应商信息
     * @return 供应商信息分页数据
     */
    @Override
    @DataSource(value = DataSourceType.SUPPLY)
    public TableDataInfo selectSupplyInfoTable(SupplyInfo supplyInfo) {
        List<SupplyInfo> list = supplyInfoMapper.selectSupplyInfoList(supplyInfo);
        long total = new PageInfo(list).getTotal();
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setMsg("查询成功");
        rspData.setRows(list);
        rspData.setTotal(total);
        return rspData;
    }

    /**
     * 新增供应商信息
     *
     * @param supplyInfo 供应商信息
     * @return 结果
     */
    @Override
    @DataSource(value = DataSourceType.SUPPLY)
    public int insertSupplyInfo(SupplyInfo supplyInfo) {
        return supplyInfoMapper.insertSupplyInfo(supplyInfo);
    }

    /**
     * 修改供应商信息
     *
     * @param supplyInfo 供应商信息
     * @return 结果
     */
    @Override
    @DataSource(value = DataSourceType.SUPPLY)
    public int updateSupplyInfo(SupplyInfo supplyInfo) {
        return supplyInfoMapper.updateSupplyInfo(supplyInfo);
    }

    /**
     * 批量删除供应商信息
     *
     * @param supplyCodes 需要删除的供应商代码主键集合
     * @return 结果
     */
    @Override
    @DataSource(value = DataSourceType.SUPPLY)
    public int deleteSupplyInfoByCodes(Integer[] supplyCodes) {
        return supplyInfoMapper.deleteSupplyInfoByCodes(supplyCodes);
    }

    /**
     * 删除供应商信息信息
     *
     * @param supplyCode 供应商代码主键
     * @return 结果
     */
    @Override
    @DataSource(value = DataSourceType.SUPPLY)
    public int deleteSupplyInfoByCode(Integer supplyCode) {
        return supplyInfoMapper.deleteSupplyInfoByCode(supplyCode);
    }
}
