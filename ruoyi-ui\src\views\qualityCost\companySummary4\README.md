# 公司汇总分厂数据处理功能说明

## 功能概述

本功能用于处理 `listCompanySummaryData` API 返回的分厂格式数据，自动识别并清理模拟数据，将有效数据正确赋值到表格中。

## 数据格式

### API返回数据格式
```javascript
{
  "一炼": {
    "1": {
      costCenterName: "一炼成本中心",
      costEx: 150000.50,      // 金额
      costTon: 1250.75,       // 吨位
      yearMonth: "202501"     // 年月
    },
    "2": {
      costCenterName: null,
      costEx: 0,
      costTon: 0,
      yearMonth: null
    }
    // ... 更多月份数据
  },
  "二炼": {
    // ... 二炼的数据
  }
}
```

### 模拟数据识别规则
以下情况会被识别为模拟数据并被自动删除：
- `costCenterName: null`
- `costEx: 0`
- `costTon: 0`
- `yearMonth: null`

## 功能特性

### 1. 自动数据格式检测
- 自动识别分厂数据格式
- 如果不是分厂格式，清空表格数据

### 2. 模拟数据清理
- 自动识别并删除所有模拟数据
- 只保留有效的数据记录

### 3. 表格数据转换
- 将分厂数据转换为表格友好的格式
- 按分厂组织数据行
- 按月份组织数据列

### 4. 数据格式化
- 金额显示为千分位格式（如：15.00万）
- 吨位显示为整数格式
- 无效数据显示为"-"

## 使用方法

### 1. 自动处理
当API返回的数据符合分厂格式时，系统会自动：
1. 检测数据格式
2. 清理模拟数据
3. 转换数据格式
4. 更新表格显示

### 2. 手动测试
点击"测试分厂数据"按钮可以：
1. 测试纯模拟数据的处理
2. 测试包含真实数据的处理
3. 查看控制台输出了解处理过程

## 表格显示

### 列结构
- **分厂**: 显示分厂名称
- **月份分组**: 1月到12月，每个月份包含金额和吨位两列

### 数据映射
- `costEx` → 对应月份的金额列（如：`januaryAmount`, `februaryAmount` 等）
- `costTon` → 对应月份的吨位列（如：`januaryPerTon`, `februaryPerTon` 等）
- 分厂名称 → 表格行标题（`company` 字段）
- 月份数字 → 表格列标题（1月对应 `january`，2月对应 `february` 等）

### 数据映射示例
```javascript
// 输入数据
{
  "一炼": {
    "1": { costEx: 150000.50, costTon: 1250.75 },
    "2": { costEx: 180000.25, costTon: 1350.50 }
  }
}

// 映射后的表格行数据
{
  company: "一炼",
  januaryAmount: 150000.50,    // 1月金额
  januaryPerTon: 1250.75,      // 1月吨位
  februaryAmount: 180000.25,   // 2月金额
  februaryPerTon: 1350.50,     // 2月吨位
  // ... 其他月份
}
```

## 示例

### 输入数据
```javascript
{
  "一炼": {
    "1": { costCenterName: "一炼成本中心", costEx: 150000.50, costTon: 1250.75, yearMonth: "202501" },
    "2": { costCenterName: null, costEx: 0, costTon: 0, yearMonth: null },
    "3": { costCenterName: "一炼成本中心", costEx: 165000.75, costTon: 1200.25, yearMonth: "202503" }
  }
}
```

### 处理后表格
| 分厂 | 一月金额 | 一月吨位 | 二月金额 | 二月吨位 | 三月金额 | 三月吨位 |
|------|----------|----------|----------|----------|----------|----------|
| 一炼 | 15.00万  | 1,251    | -        | -        | 16.50万  | 1,200    |

## 核心方法

### 1. `processFactoryData(rawData)`
主处理方法，负责：
- 检测数据格式
- 调用清理和转换方法
- 更新表格数据

### 2. `isFactoryDataFormat(data)`
格式检测方法，检查：
- 数据是否为对象格式
- 是否包含分厂名称作为key
- 分厂数据是否包含月份数据
- 月份数据是否包含必需字段

### 3. `cleanMockData(data)`
模拟数据清理方法：
- 遍历所有分厂和月份数据
- 识别并删除模拟数据
- 只保留有效数据

### 4. `convertFactoryDataToTable(factoryData)`
数据转换方法：
- 将分厂数据转换为扁平化的表格数据
- 保留所有原始字段信息

### 5. `updateTableWithFactoryData(tableData)`
表格更新方法：
- 将转换后的数据映射到表格格式
- 按分厂创建表格行
- 按月份填充数据
- 计算合计数据

## 注意事项

1. **数据格式要求**: 输入数据必须严格按照分厂格式组织
2. **模拟数据清理**: 所有模拟数据会被自动删除，不会显示在表格中
3. **性能考虑**: 大量数据时建议分批处理
4. **错误处理**: 如果数据格式不正确，会清空表格数据

## 测试功能

### 测试按钮
页面右上角有"测试分厂数据"按钮，点击后会：
1. 先测试纯模拟数据（应该清空表格）
2. 2秒后测试包含真实数据的数据（应该显示有效数据）

### 测试数据
- `testFactoryData`: 纯模拟数据，用于测试清理功能
- `testFactoryDataWithRealValues`: 包含真实数据的测试数据

## 扩展性

- 可以轻松添加新的数据格式检测规则
- 可以自定义模拟数据识别条件
- 可以修改表格显示格式
- 可以添加更多的数据处理逻辑 