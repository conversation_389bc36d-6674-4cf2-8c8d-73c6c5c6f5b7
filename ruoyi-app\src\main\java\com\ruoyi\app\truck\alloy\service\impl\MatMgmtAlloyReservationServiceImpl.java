package com.ruoyi.app.truck.alloy.service.impl;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.ruoyi.app.truck.alloy.domain.MatMgmtAlloyLog;
import com.ruoyi.app.truck.alloy.domain.MatMgmtAlloyReservation;
import com.ruoyi.app.truck.alloy.dto.AlloyStepDTO;
import com.ruoyi.app.truck.alloy.dto.AlloyVerifyDTO;
import com.ruoyi.app.truck.alloy.dto.AlloyAssignDTO;
import com.ruoyi.app.truck.alloy.enums.MatAlloyApprovalDept;
import com.ruoyi.app.truck.alloy.enums.MatElectrodeType;
import com.ruoyi.app.truck.alloy.enums.MatReservationStatus;
import com.ruoyi.app.truck.alloy.service.IMatMgmtAlloyAiSuoService;
import com.ruoyi.app.truck.alloy.mapper.MatMgmtAlloyLogMapper;
import com.ruoyi.app.truck.alloy.mapper.MatMgmtAlloyReservationMapper;
import com.ruoyi.app.truck.alloy.service.IMatMgmtAlloyReservationService;
import com.ruoyi.app.truck.common.domain.TruckImageDTO;
import com.ruoyi.app.truck.common.enums.VehicleEmissionStandardsEnum;
import com.ruoyi.app.truck.common.service.impl.TruckBlacklistServiceImpl;
import com.ruoyi.app.v1.domain.CompanyList;
import com.ruoyi.app.v1.domain.CompanyUser;
import com.ruoyi.app.v1.mapper.AppCommonV1Mapper;
import com.ruoyi.app.v1.mapper.CompanyListMapper;
import com.ruoyi.app.v1.mapper.CompanyUserMapper;
import com.ruoyi.app.v2.service.IOfficialUserService;
import com.ruoyi.app.visitor.vo.VisitorPhotoVo;
import com.ruoyi.common.constant.HttpStatus;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SnowFlakeUtil;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.framework.manager.AsyncManager;
import com.ruoyi.framework.web.service.TemplateMessageService;
import com.ruoyi.system.service.ISysRoleService;
import com.github.pagehelper.PageInfo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import static com.ruoyi.common.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;

/**
 * 物管合金车辆预约Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-15
 */
@Service
public class MatMgmtAlloyReservationServiceImpl implements IMatMgmtAlloyReservationService {

    private static final Logger log = LoggerFactory.getLogger(MatMgmtAlloyReservationServiceImpl.class);

    @Autowired
    private MatMgmtAlloyReservationMapper matMgmtAlloyReservationMapper;

    @Autowired
    private MatMgmtAlloyLogMapper matMgmtAlloyLogMapper;

    @Autowired
    private CompanyUserMapper companyUserMapper;

    @Autowired
    private CompanyListMapper companyListMapper;

    @Autowired
    private TruckBlacklistServiceImpl truckBlacklistService;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private ISysRoleService sysRoleService;

    @Autowired
    private TemplateMessageService templateMessageService;

    @Autowired
    private IMatMgmtAlloyAiSuoService matMgmtAlloyAiSuoService;

    @Autowired
    private AppCommonV1Mapper commonMapper;

    @Autowired
    private IOfficialUserService officialUserService;

    // 合金相关管理角色常量
    private static final List<String> ALLOY_ADMIN_ROLES = Arrays.asList(
            "matMgmt.alloy.purchase",    // 合金-采购中心
            "matMgmt.alloy.commerce", //合金-商务部
            "matMgmt.alloy.security.signIn",   // 合金-保卫部签到员
            "matMgmt.alloy.security.entrance",      // 合金-保卫部进出扫码
            "matMgmt.alloy.assign",    // 合金-物管合金分配
            "matMgmt.alloy.electrodeAssign",    // 合金-物管电极分配
            "matMgmt.alloy.infrom",  // 合金-分配通知人
            "matMgmt.alloy.confirm",  // 合金-物管扫码确认
            "matMgmt.alloy.matManager" //合金-管理员
    );

    // 合金-客商预约
    public static final String RESERVE_ROLE_KEY = "matMgmt.alloy.reserve";
    // 合金-采购中心
    public static final String PURCHASE_ROLE_KEY = "matMgmt.alloy.purchase";
    // 合金-商务部
    public static final String COMMERCE_ROLE_KEY = "matMgmt.alloy.commerce";
    // 合金-扫码签到
    public static final String SIGN_IN_ROLE_KEY = "matMgmt.alloy.security.signIn";
    // 合金-门卫扫码
    public static final String ENTRANCE_ROLE_KEY = "matMgmt.alloy.security.entrance";
    // 合金-物管合金分配
    public static final String ASSIGN_ROLE_KEY = "matMgmt.alloy.assign";
    // 合金-物管电极分配
    public static final String ELECTRODE_ASSIGN_ROLE_KEY = "matMgmt.alloy.electrodeAssign";
    // 合金-分配通知
    public static final String INFORM_ROLE_KEY = "matMgmt.alloy.infrom";
    // 合金-物管确认
    public static final String CONFIRM_ROLE_KEY = "matMgmt.alloy.confirm";
    // 合金-管理员
    public static final String MAT_MANAGER_ROLE_KEY = "matMgmt.alloy.matManager";

    /**
     * 查询物管合金车辆预约详情
     *
     * @param reservationNo 预约编号
     * @return 物管合金车辆预约
     */
    @Override
    public MatMgmtAlloyReservation detail(String reservationNo) {
        MatMgmtAlloyReservation matMgmtAlloyReservation = matMgmtAlloyReservationMapper.selectMatMgmtAlloyReservationByReservationNo(reservationNo);

        List<MatMgmtAlloyLog> matMgmtAlloyLogs = matMgmtAlloyLogMapper.selectMatMgmtAlloyLogByReservationNo(reservationNo);
        matMgmtAlloyReservation.setMatMgmtAlloyLogs(matMgmtAlloyLogs);

        return matMgmtAlloyReservation;
    }

    /**
     * 查询物管合金车辆预约
     *
     * @param id 物管合金车辆预约ID
     * @return 物管合金车辆预约
     */
    @Override
    public MatMgmtAlloyReservation selectMatMgmtAlloyReservationById(Long id) {
        return matMgmtAlloyReservationMapper.selectMatMgmtAlloyReservationById(id);
    }

    /**
     * 查询物管合金车辆预约列表
     *
     * @param matMgmtAlloyReservation 物管合金车辆预约
     * @return 物管合金车辆预约
     */
    @Override
    public List<MatMgmtAlloyReservation> selectMatMgmtAlloyReservationList(MatMgmtAlloyReservation matMgmtAlloyReservation) {
        return matMgmtAlloyReservationMapper.selectMatMgmtAlloyReservationList(matMgmtAlloyReservation);
    }

    /**
     * 新增物管合金车辆预约
     *
     * @param matMgmtAlloyReservation 物管合金车辆预约
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertMatMgmtAlloyReservation(MatMgmtAlloyReservation matMgmtAlloyReservation) {
        matMgmtAlloyReservation.setCreateTime(DateUtils.getNowDate());
        return matMgmtAlloyReservationMapper.insertMatMgmtAlloyReservation(matMgmtAlloyReservation);
    }

    /**
     * 修改物管合金车辆预约
     *
     * @param matMgmtAlloyReservation 物管合金车辆预约
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateMatMgmtAlloyReservation(MatMgmtAlloyReservation matMgmtAlloyReservation) {
        matMgmtAlloyReservation.setUpdateTime(DateUtils.getNowDate());
        return matMgmtAlloyReservationMapper.updateMatMgmtAlloyReservation(matMgmtAlloyReservation);
    }

    /**
     * 修改物管合金车辆预约状态
     *
     * @param matMgmtAlloyReservation 物管合金车辆预约
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateMatMgmtAlloyReservationStatus(MatMgmtAlloyReservation matMgmtAlloyReservation) {
        matMgmtAlloyReservation.setUpdateTime(DateUtils.getNowDate());
        return matMgmtAlloyReservationMapper.updateMatMgmtAlloyReservationStatus(matMgmtAlloyReservation);
    }

    /**
     * 批量删除物管合金车辆预约
     *
     * @param ids 需要删除的物管合金车辆预约ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteMatMgmtAlloyReservationByIds(Long[] ids) {
        return matMgmtAlloyReservationMapper.deleteMatMgmtAlloyReservationByIds(ids);
    }

    /**
     * 删除物管合金车辆预约信息
     *
     * @param id 物管合金车辆预约ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteMatMgmtAlloyReservationById(Long id) {
        return matMgmtAlloyReservationMapper.deleteMatMgmtAlloyReservationById(id);
    }

    /**
     * 新增合金预约（业务方法）
     *
     * @param matMgmtAlloyReservation 物管合金车辆预约
     * @param openId                  用户OpenId
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertMatMgmtAlloyReservationBusiness(MatMgmtAlloyReservation matMgmtAlloyReservation, String openId) {
        try {

            // 0. 判断车辆是否在黑名单中
            if (truckBlacklistService.isCarNumberInBlacklist(matMgmtAlloyReservation.getCarNo())) {
                throw new RuntimeException("黑名单车辆禁止预约");
            }

            // 1. 获取用户信息和公司信息（使用缓存提升性能）
            if (StringUtils.isBlank(openId)) {
                log.error("合金预约新增请求失败 - 无法获取用户OpenId");
                throw new RuntimeException("用户信息异常，请重新登录");
            }

            CompanyUser userInfo = getUserInfoWithCache(openId);
            if (userInfo == null || StringUtils.isBlank(userInfo.getCompanyId())) {
                log.error("合金预约新增请求失败 - 无法获取用户公司信息，OpenId：{}", openId);
                throw new RuntimeException("用户信息异常，请重新登录");
            }

            String companyId = userInfo.getCompanyId();

            // 2. 获取公司名称
            String companyName = "";
            try {
                CompanyList companyInfo = companyListMapper.selectCompanyListByCompanyId(companyId);
                if (companyInfo != null) {
                    companyName = companyInfo.getCompanyName();
                }
            } catch (Exception e) {
                log.warn("获取公司名称失败，公司ID：{}，将使用空值", companyId, e);
            }

            // 3. 生成预约编号
            String reservationNo = SnowFlakeUtil.getAlloyReservationNoSnowFlakeId();
            // 4. 设置预约信息
            this.handleDriverInfo(matMgmtAlloyReservation);
            matMgmtAlloyReservation.setReservationNo(reservationNo);
            // 推送流水号和预约编号一致
            matMgmtAlloyReservation.setPushNo(reservationNo);
            // 判断是合金还是电极
            Integer alloyCode = matMgmtAlloyReservation.getAlloyValue();
            if (alloyCode != null) {
                if (alloyCode == 200) {
                    // 电极
                    matMgmtAlloyReservation.setDeliveryCategoryCode("2");
                } else {
                    // 合金
                    matMgmtAlloyReservation.setDeliveryCategoryCode("1");
                }
            }
            matMgmtAlloyReservation.setStatus("1"); // 1-待审核
            matMgmtAlloyReservation.setApplyCompanyId(companyId);
            matMgmtAlloyReservation.setApplyCompanyName(companyName);
            matMgmtAlloyReservation.setSupplierSalesPhone(userInfo.getPhone());
            matMgmtAlloyReservation.setOperatorOpenid(openId);
            matMgmtAlloyReservation.setDelFlag("0"); // 0-未删除
            matMgmtAlloyReservation.setCreateBy(openId);
            matMgmtAlloyReservation.setCreateTime(DateUtils.getNowDate());

            // 5. 插入预约数据
            int result = matMgmtAlloyReservationMapper.insertMatMgmtAlloyReservation(matMgmtAlloyReservation);

            if (result > 0) {
                // 6. 异步插入日志
                String logInfo = companyName + "新增供货申请";
                insertLog(reservationNo, logInfo);

                // 7. 调用通知模块，根据业务部门判断推送对象
                String path = "packageC/pages/matAlloy/approve/list";
                String deptCode = matMgmtAlloyReservation.getApprovalDept();
                if (StringUtils.isNotBlank(deptCode)) {
                    if ("1".equals(deptCode)) {
                        //提交采购中心
                        this.sendApproveMessage(PURCHASE_ROLE_KEY, reservationNo, "您有合金预约单待审批", path);
                    }
                    if ("2".equals(deptCode)) {
                        this.sendApproveMessage(COMMERCE_ROLE_KEY, reservationNo, "您有合金预约单待审批", path);
                    }
                }

                log.info("合金预约新增成功 - 预约编号：{}，公司：{}，OpenId：{}", reservationNo, companyName, openId);
                return result;
            } else {
                log.error("合金预约新增失败 - 数据库插入失败，公司：{}，OpenId：{}", companyName, openId);
                throw new RuntimeException("预约申请提交失败，请稍后重试");
            }

        } catch (RuntimeException e) {
            throw new RuntimeException("预约异常");
        } catch (Exception e) {
            log.error("合金预约新增请求发生异常", e);
            throw new RuntimeException("系统异常，请稍后重试");
        }
    }

    /**
     * 插入日志（通用方法）
     *
     * @param reservationNo 预约编号
     * @param info          日志信息
     */
    public void insertLog(String reservationNo, String info) {
        try {
            MatMgmtAlloyLog alloyLog = new MatMgmtAlloyLog();
            alloyLog.setReservationNo(reservationNo);
            alloyLog.setInfo(info);
            alloyLog.setCreateTime(DateUtils.getNowDate());
            alloyLog.setCreateBy("system");
            matMgmtAlloyLogMapper.insertMatMgmtAlloyLog(alloyLog);
        } catch (Exception e) {
            log.error("合金预约日志插入失败 - 预约编号：{}，日志信息：{}", reservationNo, info, e);
        }
    }

    // 向指定角色人员发送审核推送消息
    private void sendApproveMessage(String roleKey, String reservationNo, String msg, String path) {
        //获取指定角色的工号
        List<SysUser> sysUserList = sysRoleService.selectSysUserListByRoleKey(roleKey);
        List<String> workNos = sysUserList.stream().map(SysUser::getUserName).collect(Collectors.toList());
        String type = "合金预约单";
        String time = DateUtils.dateTimeNow("yyyy-MM-dd HH:mm:ss");
        for (String workNo : workNos) {
            this.sendToDoMessage(time, type, msg, workNo, path);
        }
    }

    // 发送微信通知
    private void sendToDoMessage(String time, String type, String message, String workNo, String path) {
        AsyncManager.me().execute(new TimerTask() {
            @Override
            public void run() {
                String appOpenId = commonMapper.selectOpenIdByWorkNo(workNo);
                if (StringUtils.isNotBlank(appOpenId)) {
                    String openId = officialUserService.getOpenIdsByOpenId(appOpenId);
                    if (StringUtils.isNotBlank(openId)) {
                        templateMessageService.sendToDoMessage(time, type, message, openId, path);
                    }
                }
            }
        });
    }

    // 根据openid发送微信推送
    private void sendOfficialMessageByOpenId(String time, String type, String message, String appOpenId, String path) {
        AsyncManager.me().execute(new TimerTask() {
            @Override
            public void run() {
                String openId = officialUserService.getOpenIdsByOpenId(appOpenId);
                if (StringUtils.isNotBlank(openId)) {
                    templateMessageService.sendToDoMessage(time, type, message, openId, path);
                }
            }
        });
    }

    /**
     * 获取用户公司信息（带缓存）
     * 缓存key格式：alloy:userInfo:{openId}
     * 缓存时间：10分钟
     *
     * @param openId 用户openId
     * @return 公司用户信息
     */
    private CompanyUser getUserInfoWithCache(String openId) {
        if (openId == null || openId.trim().isEmpty()) {
            log.warn("获取用户信息失败 - OpenId为空");
            return null;
        }

        String cacheKey = "alloy:userInfo:" + openId;

        // 先从缓存获取
        CompanyUser cachedUserInfo = redisCache.getCacheObject(cacheKey);
        if (cachedUserInfo != null) {
            log.debug("从缓存获取用户信息成功 - OpenId：{}, CompanyId：{}", openId, cachedUserInfo.getCompanyId());
            return cachedUserInfo;
        }

        // 缓存没有，查询数据库
        log.debug("缓存未命中，查询数据库 - OpenId：{}", openId);
        CompanyUser userInfo = companyUserMapper.getSingleCompanyUserByOpenId(openId);

        if (userInfo != null && userInfo.getCompanyId() != null) {
            // 存入缓存，10分钟过期
            redisCache.setCacheObject(cacheKey, userInfo, 10, TimeUnit.MINUTES);
            log.debug("用户信息已缓存 - OpenId：{}, CompanyId：{}", openId, userInfo.getCompanyId());
        } else {
            log.warn("查询用户信息失败或数据异常 - OpenId：{}", openId);
        }

        return userInfo;
    }

    private void handleDriverInfo(MatMgmtAlloyReservation reservation) {
        if (reservation.getDriverFaceImg() != null) {
            reservation.setDriverFaceImg(this.toImgList(reservation.getDriverFaceImg()));
        }
        if (reservation.getDriverLicenseImgs() != null) {
            reservation.setDriverLicenseImgs(this.toImgList(reservation.getDriverLicenseImgs()));
        }
        if (reservation.getDrivingLicenseImg() != null) {
            reservation.setDrivingLicenseImg(this.toImgList(reservation.getDrivingLicenseImg()));
        }
    }

    private String toImgList(String strList) {
        List<TruckImageDTO> truckImageDTOList = new ArrayList<TruckImageDTO>();
        if (StringUtils.isNotBlank(strList)) {
            String[] strArr = strList.split(",");
            for (String str : strArr) {
                TruckImageDTO truckImageDTO = new TruckImageDTO();
                truckImageDTO.setUrl(str);
                truckImageDTOList.add(truckImageDTO);
            }
        }
        return JSON.toJSONString(truckImageDTOList);
    }

    /**
     * 判断用户是否具有合金管理角色
     *
     * @param workNo 工号
     * @return 是否具有管理角色
     */
    @Override
    public boolean hasAlloyAdminRole(String workNo) {
        if (StringUtils.isBlank(workNo)) {
            return false;
        }
        try {
            boolean hasRole = sysRoleService.selectRoleExistByUserName(workNo, ALLOY_ADMIN_ROLES);
            log.debug("角色判断结果 - 工号：{}，是否具有管理角色：{}", workNo, hasRole);
            return hasRole;
        } catch (Exception e) {
            log.error("判断用户角色时发生异常 - 工号：{}", workNo, e);
            return false;
        }
    }

    /**
     * 查询物管合金车辆预约列表（返回TableDataInfo）
     *
     * @param matMgmtAlloyReservation 物管合金车辆预约
     * @return TableDataInfo
     */
    @Override
    public TableDataInfo selectMatMgmtAlloyReservationTableList(MatMgmtAlloyReservation matMgmtAlloyReservation) {
        TableDataInfo rspData = new TableDataInfo();
        try {
            // 1. 查询原始列表
            // 日期处理
            if (StringUtils.isNotBlank(matMgmtAlloyReservation.getQueryEndTime())) {
                matMgmtAlloyReservation.setQueryEndTime(matMgmtAlloyReservation.getQueryEndTime() + " 23:59:59");
            }
            List<MatMgmtAlloyReservation> list = matMgmtAlloyReservationMapper.selectMatMgmtAlloyReservationList(matMgmtAlloyReservation);
            long total = new PageInfo(list).getTotal();

            // 2. 转换为展示格式
            List<Map<String, Object>> result = new ArrayList<>();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            SimpleDateFormat dateSdf = new SimpleDateFormat("yyyy-MM-dd");

            for (MatMgmtAlloyReservation item : list) {
                Map<String, Object> map = new HashMap<>();
                map.put("id", item.getId());
                map.put("reservationNo", item.getReservationNo());
                map.put("dept", MatAlloyApprovalDept.getNameByCode(item.getApprovalDept()));
                map.put("status", item.getStatus());
                map.put("statusDesc", MatReservationStatus.getNameByCode(item.getStatus()));
                map.put("applyCompanyId", item.getApplyCompanyId());
                map.put("applyCompanyName", item.getApplyCompanyName());
                map.put("supplierSalesName", item.getSupplierSalesName());
                map.put("supplierSalesPhone", item.getSupplierSalesPhone());
                map.put("carNo", item.getCarNo());
                map.put("alloyLabel", item.getAlloyLabel());
                map.put("alloyType", item.getAlloyType());
                map.put("alloyValue", item.getAlloyValue());
                map.put("electrodeType", MatElectrodeType.getNameByCode(item.getElectrodeType()));
                map.put("expectedDeliveryTime", item.getExpectedDeliveryTime() != null ? dateSdf.format(item.getExpectedDeliveryTime()) : "");
                map.put("estimatedWeight", item.getEstimatedWeight());
                map.put("driverName", item.getDriverName());
                map.put("driverPhone", item.getDriverMobile() != null ? item.getDriverMobile() : "");
                map.put("createTime", item.getCreateTime() != null ? sdf.format(item.getCreateTime()) : "");
                map.put("checkinTime", item.getCheckinTime() != null ? sdf.format(item.getCheckinTime()) : "");
                result.add(map);
            }

            // 3. 设置返回数据
            rspData.setTotal(total);
            rspData.setRows(result);
            rspData.setCode(HttpStatus.SUCCESS);
            rspData.setMsg("查询成功");

        } catch (Exception e) {
            log.error("查询合金预约列表发生异常", e);
            rspData.setTotal(0);
            rspData.setRows(new ArrayList<>());
            rspData.setCode(HttpStatus.ERROR);
            rspData.setMsg("查询失败");
        }

        return rspData;
    }

    /**
     * 获取预约详情
     *
     * @param reservationNo 预约编号
     * @param workNo        工号
     * @return 预约详情Map
     */
    @Override
    public Map<String, Object> getReservationDetail(String reservationNo, String workNo) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 1. 根据预约编号查询预约信息
            MatMgmtAlloyReservation query = new MatMgmtAlloyReservation();
            query.setReservationNo(reservationNo);
            List<MatMgmtAlloyReservation> list = matMgmtAlloyReservationMapper.selectMatMgmtAlloyReservationList(query);

            if (list == null || list.isEmpty()) {
                log.warn("未找到预约信息 - 预约编号：{}", reservationNo);
                result.put("success", false);
                result.put("message", "未找到预约信息");
                return result;
            }

            MatMgmtAlloyReservation reservation = list.get(0);

            // 2. 基本信息转换
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            SimpleDateFormat dateSdf = new SimpleDateFormat("yyyy-MM-dd");

            result.put("id", reservation.getId());
            result.put("reservationNo", reservation.getReservationNo());
            result.put("status", reservation.getStatus());
            result.put("statusDesc", MatReservationStatus.getNameByCode(reservation.getStatus()));
            result.put("approvalDeptName", MatAlloyApprovalDept.getNameByCode(reservation.getApprovalDept()));
            result.put("applyCompanyId", reservation.getApplyCompanyId());
            result.put("applyCompanyName", reservation.getApplyCompanyName());
            result.put("supplierSalesName", reservation.getSupplierSalesName());
            result.put("supplierSalesPhone", reservation.getSupplierSalesPhone());
            result.put("carNo", reservation.getCarNo());
            result.put("alloyValue", reservation.getAlloyValue());
            result.put("alloyLabel", reservation.getAlloyLabel());
            result.put("alloyType", reservation.getAlloyType());
            result.put("electrodeType", MatElectrodeType.getNameByCode(reservation.getElectrodeType()));
            result.put("expectedDeliveryTime", reservation.getExpectedDeliveryTime() != null ? dateSdf.format(reservation.getExpectedDeliveryTime()) : "");
            result.put("estimatedWeight", reservation.getEstimatedWeight());
            result.put("enterDoor", reservation.getEnterDoor() == null ? "" : reservation.getEnterDoor());
            result.put("effectiveStartTime", reservation.getEffectiveStartTime() != null ? sdf.format(reservation.getEffectiveStartTime()) : "");
            result.put("effectiveEndTime", reservation.getEffectiveEndTime() != null ? sdf.format(reservation.getEffectiveEndTime()) : "");
            result.put("driverName", reservation.getDriverName());
            result.put("driverMobile", reservation.getDriverMobile());
            result.put("driverFaceImg", JSONArray.parseArray(reservation.getDriverFaceImg(), VisitorPhotoVo.class));
            result.put("driverLicenseImgs", JSONArray.parseArray(reservation.getDriverLicenseImgs(), VisitorPhotoVo.class));
            result.put("drivingLicenseImg", JSONArray.parseArray(reservation.getDrivingLicenseImg(), VisitorPhotoVo.class));
            result.put("vehicleEmissionStandardsDesc", VehicleEmissionStandardsEnum.getInfoByCode(reservation.getVehicleEmissionStandards()));
            result.put("createTime", reservation.getCreateTime() != null ? sdf.format(reservation.getCreateTime()) : "");
            result.put("updateTime", reservation.getUpdateTime() != null ? sdf.format(reservation.getUpdateTime()) : "");
            result.put("remark", reservation.getRemark());

            // 3. 记录日志
            List<AlloyStepDTO> alloyReservationDTOList = this.getAlloyLog(reservationNo);

            result.put("logSteps", alloyReservationDTOList);

            // 是否展示二维码
            if (StringUtils.isBlank(workNo)) {
                // 客商展示二维码
                result.put("showQRCode", true);
            } else {
                // 内部用户隐藏二维码
                result.put("showQRCode", false);
            }

            // 4. 权限判断 - 是否可取消
            boolean canCancel = false;
            if (StringUtils.isBlank(workNo)) {
                // 外部用户：状态为1(待审核)和2(审核通过)时可取消
                if ("1".equals(reservation.getStatus()) || "2".equals(reservation.getStatus())) {
                    canCancel = true;
                }
            }
            result.put("canCancel", canCancel);

            // 5. 权限判断 - 是否可审核
            boolean canApprove = false;
            if (StringUtils.isNotBlank(workNo)) {
                // 判断是否为审核角色
                boolean isPurchaseCenter = sysRoleService.selectRoleExistByUserName(workNo, Arrays.asList(PURCHASE_ROLE_KEY, COMMERCE_ROLE_KEY));
                if (isPurchaseCenter && ("1".equals(reservation.getStatus()) || "22".equals(reservation.getStatus()))) {
                    canApprove = true;
                }
            }
            result.put("canApprove", canApprove);

            // 6. 权限判断 - 是否可分配
            boolean canAssign = false;
            if (StringUtils.isNotBlank(workNo)) {
                // 判断是否为物管分配入厂角色
                boolean isAssign = sysRoleService.selectRoleExistByUserName(workNo, Arrays.asList(ASSIGN_ROLE_KEY, ELECTRODE_ASSIGN_ROLE_KEY));
                if (isAssign && ("3".equals(reservation.getStatus()))) {
                    canAssign = true;
                }
            }
            result.put("canAssign", canAssign);

            // 7. 权限判断 - 是否可重新分配
            boolean canReassign = false;
            if (StringUtils.isNotBlank(workNo)) {
                // 判断是否为物管分配入厂角色
                boolean isAssign = sysRoleService.selectRoleExistByUserName(workNo, Arrays.asList(ASSIGN_ROLE_KEY, ELECTRODE_ASSIGN_ROLE_KEY));
                if (isAssign && ("4".equals(reservation.getStatus()))) {
                    canReassign = true;
                }
            }
            result.put("canReassign", canReassign);

            // 8. 权限判断 - 是否可签到
            boolean canCheckIn = false;
            if (StringUtils.isNotBlank(workNo)) {
                // 判断是否为保卫部签到角色
                boolean hasSignInRole = sysRoleService.selectRoleExistByUserName(workNo, Collections.singletonList(SIGN_IN_ROLE_KEY));
                if (hasSignInRole && "2".equals(reservation.getStatus())) {
                    canCheckIn = true;
                }
            }
            result.put("canCheckIn", canCheckIn);

            // 9. 权限判断 - 是否可门卫扫码
            boolean canEntranceGuard = false;
            if (StringUtils.isNotBlank(workNo)) {
                // 判断是否为门卫扫码角色
                boolean hasEntranceRole = sysRoleService.selectRoleExistByUserName(workNo, Collections.singletonList(ENTRANCE_ROLE_KEY));
                // 只有enterDoor存在且为"1"（前往安全村）且状态为4或6时才可门卫扫码
                String enterDoor = reservation.getEnterDoor();
                if (hasEntranceRole && StringUtils.isNotBlank(enterDoor) && "1".equals(enterDoor) &&
                        ("4".equals(reservation.getStatus()) || "6".equals(reservation.getStatus()))) {
                    canEntranceGuard = true;
                }
            }
            result.put("canEntranceGuard", canEntranceGuard);

            // 10. 权限判断 - 是否可物管确认
            boolean canMatConfirm = false;
            if (StringUtils.isNotBlank(workNo)) {
                // 判断是否为物管确认角色
                boolean isConfirm = sysRoleService.selectRoleExistByUserName(workNo, Collections.singletonList(CONFIRM_ROLE_KEY));
                // 只有enterDoor存在且为"1"（前往安全村）且状态为5（已进厂）时才可物管确认
                String enterDoor = reservation.getEnterDoor();
                if (isConfirm && StringUtils.isNotBlank(enterDoor) && "1".equals(enterDoor) && "5".equals(reservation.getStatus())) {
                    canMatConfirm = true;
                }
            }
            result.put("canMatConfirm", canMatConfirm);

            result.put("success", true);
            result.put("message", "查询成功");

            log.info("获取预约详情成功 - 预约编号：{}，工号：{}", reservationNo, workNo);

        } catch (Exception e) {
            log.error("获取预约详情发生异常 - 预约编号：{}，工号：{}", reservationNo, workNo, e);
            result.put("success", false);
            result.put("message", "查询失败");
        }

        return result;
    }

    private List<AlloyStepDTO> getAlloyLog(String reservationNo) {
        List<AlloyStepDTO> stepDTOList = new ArrayList<>();

        MatMgmtAlloyLog condition = new MatMgmtAlloyLog();
        condition.setReservationNo(reservationNo);
        List<MatMgmtAlloyLog> reservationLogs = matMgmtAlloyLogMapper.selectMatMgmtAlloyLogList(condition);
        if (CollectionUtils.isEmpty(reservationLogs)) {
            return stepDTOList;
        }
        reservationLogs = reservationLogs.stream().sorted(Comparator.comparing(MatMgmtAlloyLog::getCreateTime).reversed()).collect(Collectors.toList());
        for (MatMgmtAlloyLog alloyLog : reservationLogs) {
            AlloyStepDTO alloyStepDTO = new AlloyStepDTO();
            alloyStepDTO.setText(DateUtils.parseDateToStr(YYYY_MM_DD_HH_MM_SS, alloyLog.getCreateTime()));
            alloyStepDTO.setDesc(alloyLog.getInfo());

            stepDTOList.add(alloyStepDTO);
        }

        return stepDTOList;
    }

    /**
     * 审核预约
     *
     * @param verifyDTO 审核DTO
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int approveReservation(AlloyVerifyDTO verifyDTO) {
        try {
            // 1. 参数验证
            if (verifyDTO == null) {
                log.error("审核预约失败 - 审核参数为空");
                throw new RuntimeException("审核参数不能为空");
            }

            String reservationNo = verifyDTO.getReservationNo();
            Boolean approveFlag = verifyDTO.getApproveFlag();
            String workNo = verifyDTO.getWorkNo();

            // 2. 权限验证 - 检查是否为采购中心角色
            boolean isPurchaseCenter = sysRoleService.selectRoleExistByUserName(workNo, Arrays.asList(PURCHASE_ROLE_KEY, COMMERCE_ROLE_KEY));
            if (!isPurchaseCenter) {
                throw new RuntimeException("您没有审核权限");
            }

            // 3. 查询预约信息
            MatMgmtAlloyReservation query = new MatMgmtAlloyReservation();
            query.setReservationNo(reservationNo);
            List<MatMgmtAlloyReservation> list = matMgmtAlloyReservationMapper.selectMatMgmtAlloyReservationList(query);

            if (list == null || list.isEmpty()) {
                throw new RuntimeException("未找到预约信息");
            }

            MatMgmtAlloyReservation reservation = list.get(0);

            // 4. 状态验证 - 只有待审核(1)和待取消(22)可以审核
            if (!"1".equals(reservation.getStatus()) && !"22".equals(reservation.getStatus())) {
                throw new RuntimeException("当前非待审核状态");
            }

            // 5. 更新预约状态和审核信息
            MatMgmtAlloyReservation updateReservation = new MatMgmtAlloyReservation();
            updateReservation.setId(reservation.getId());
            // 判断待审核(1)还是待取消(22)
            if ("1".equals(reservation.getStatus())) {
                if (approveFlag) {
                    updateReservation.setStatus("2");
                } else {
                    updateReservation.setStatus("21"); // 2-申请通过，21-审核驳回
                }
            } else {
                if (approveFlag) {
                    updateReservation.setStatus("23");
                } else {
                    updateReservation.setStatus("21"); // 23-取消成功，21-审核驳回
                }
            }
            updateReservation.setBusinessApprover(workNo);
            updateReservation.setBusinessApproveReason(verifyDTO.getReason());
            updateReservation.setBusinessApproveTime(DateUtils.getNowDate());
//            updateReservation.setUpdateTime(DateUtils.getNowDate());
            int result = matMgmtAlloyReservationMapper.updateMatMgmtAlloyReservation(updateReservation);

            String approvalStatus = updateReservation.getStatus();
            String message = "";
            if ("2".equals(approvalStatus)) {
                //审核通过通知保安签到
//                String path = "packageC/pages/matAlloy/list/list";
//                this.sendApproveMessage(SIGN_IN_ROLE_KEY, reservationNo, "合金车辆待签到", path);
            } else if ("23".equals(approvalStatus)) {
                message = "取消成功";
            } else {
                if (reservation.getStatus().equals("1")) {
                    message = "您的申请被驳回";
                } else {
                    message = "您的取消申请被驳回";
                }
            }

            // 7.通知申请人
            String path = "packageC/pages/matAlloy/list/list";
            String type = "合金预约单";
            String time = DateUtils.dateTimeNow("yyyy-MM-dd HH:mm:ss");
            this.sendOfficialMessageByOpenId(time, type, message, reservation.getOperatorOpenid(), path);

            if (result > 0) {
                // 6. 异步插入日志
                String action = approveFlag ? "通过" : "驳回";
                String workName = verifyDTO.getWorkName();
                String logInfo = MatAlloyApprovalDept.getNameByCode(reservation.getApprovalDept()) + " " + workName + action + "申请";

                // 如果有审核建议，添加到日志中
                if (StringUtils.isNotBlank(verifyDTO.getReason())) {
                    logInfo += "，审核建议：" + verifyDTO.getReason();
                }

                insertLog(reservationNo, logInfo);
                return result;
            } else {
                throw new RuntimeException("审核失败");
            }

        } catch (RuntimeException e) {
            throw new RuntimeException("审核异常");
        } catch (Exception e) {
            throw new RuntimeException("系统异常");
        }
    }

    /**
     * 取消申请
     *
     * @param reservationNo 预约编号
     * @param openId        用户OpenId
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int cancelReservation(String reservationNo, String openId) {
        try {
            // 1. 查询预约信息
            MatMgmtAlloyReservation query = new MatMgmtAlloyReservation();
            query.setReservationNo(reservationNo);
            List<MatMgmtAlloyReservation> list = matMgmtAlloyReservationMapper.selectMatMgmtAlloyReservationList(query);

            if (list == null || list.isEmpty()) {
                log.error("取消申请失败 - 未找到预约信息，预约编号：{}", reservationNo);
                throw new RuntimeException("未找到预约信息");
            }

            MatMgmtAlloyReservation reservation = list.get(0);

            // 2. 状态验证 - 只有状态为1(待审核)和2(审核通过)才能取消
            if (!"1".equals(reservation.getStatus()) && !"2".equals(reservation.getStatus())) {
                log.error("取消申请失败 - 预约状态不允许取消，当前状态：{}，预约编号：{}", reservation.getStatus(), reservationNo);
                throw new RuntimeException("当前预约状态不允许取消");
            }

            // 3. 更新预约状态为22(待取消)
            MatMgmtAlloyReservation updateReservation = new MatMgmtAlloyReservation();
            updateReservation.setId(reservation.getId());
            updateReservation.setStatus("22"); // 22-待取消
            updateReservation.setUpdateBy(openId);
            updateReservation.setUpdateTime(DateUtils.getNowDate());

            int result = matMgmtAlloyReservationMapper.updateMatMgmtAlloyReservation(updateReservation);

            if (result > 0) {
                // 4. 异步插入日志
                String logInfo = reservation.getApplyCompanyName() + "取消申请";
                insertLog(reservationNo, logInfo);

                // 5. 调用通知模块
                String path = "packageC/pages/matAlloy/approve/list";
                String deptCode = reservation.getApprovalDept();
                if (StringUtils.isNotBlank(deptCode)) {
                    if ("1".equals(deptCode)) {
                        //提交采购中心
                        this.sendApproveMessage(PURCHASE_ROLE_KEY, reservationNo, "合金预约单申请取消", path);
                    }
                    if ("2".equals(deptCode)) {
                        this.sendApproveMessage(COMMERCE_ROLE_KEY, reservationNo, "合金预约单申请取消", path);
                    }
                }

                return result;
            } else {
                throw new RuntimeException("取消申请失败，请稍后重试");
            }

        } catch (RuntimeException e) {
            throw new RuntimeException("取消申请失败");
        } catch (Exception e) {
            log.error("取消申请发生异常 - 预约编号：{}，OpenId：{}", reservationNo, openId, e);
            throw new RuntimeException("系统异常，请稍后重试");
        }
    }

    /**
     * 合金扫码操作（无确认动作，暂不使用）
     *
     * @param reservationNo 预约编号
     * @param scanAction    扫码动作（checkIn-签到，entranceGuard-门卫扫码）
     * @param workNo        操作人工号
     * @param workName      操作人姓名
     * @return 结果Map
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> alloyScanCode(String reservationNo, String scanAction, String workNo, String workName) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 1. 查询预约信息
            MatMgmtAlloyReservation query = new MatMgmtAlloyReservation();
            query.setReservationNo(reservationNo);
            List<MatMgmtAlloyReservation> list = matMgmtAlloyReservationMapper.selectMatMgmtAlloyReservationList(query);

            if (list == null || list.isEmpty()) {
                log.error("扫码操作失败 - 未找到预约信息，预约编号：{}", reservationNo);
                result.put("success", false);
                result.put("message", "未找到预约信息");
                return result;
            }

            MatMgmtAlloyReservation reservation = list.get(0);
            String currentStatus = reservation.getStatus();

            // 2. 根据扫码动作处理不同逻辑
            switch (scanAction) {
                case "checkIn":
                    return handleCheckInScan(reservation, reservationNo, currentStatus, workNo, workName);
                case "entranceGuard":
                    return handleEntranceGuardScan(reservation, reservationNo, currentStatus, workNo, workName);
                default:
                    log.error("扫码操作失败 - 不支持的扫码动作：{}", scanAction);
                    result.put("success", false);
                    result.put("message", "不支持的扫码动作");
                    return result;
            }

        } catch (Exception e) {
            log.error("扫码操作发生异常 - 预约编号：{}，扫码动作：{}", reservationNo, scanAction, e);
            result.put("success", false);
            result.put("message", "系统异常，请稍后重试");
            return result;
        }
    }

    /**
     * 处理签到扫码逻辑
     */
    private Map<String, Object> handleCheckInScan(MatMgmtAlloyReservation reservation, String reservationNo, String currentStatus, String workNo, String workName) {
        Map<String, Object> result = new HashMap<>();

        if ("3".equals(currentStatus)) {
            // 状态为3，已签到，不修改状态
            log.info("签到扫码 - 已签到状态，预约编号：{}", reservationNo);
            result.put("success", true);
            result.put("message", "已签到");
            return result;
        } else if ("2".equals(currentStatus)) {
            // 状态为2，可以签到
            // 更新预约状态为3(已签到)
            MatMgmtAlloyReservation updateReservation = new MatMgmtAlloyReservation();
            updateReservation.setId(reservation.getId());
            updateReservation.setStatus("3"); // 3-已签到
            updateReservation.setCheckinTime(DateUtils.getNowDate()); // 设置签到时间
            updateReservation.setUpdateTime(DateUtils.getNowDate());

            int updateResult = matMgmtAlloyReservationMapper.updateMatMgmtAlloyReservation(updateReservation);

            if (updateResult > 0) {
                // 异步插入日志 - 显示操作人姓名和车辆信息
                String operatorName = StringUtils.isNotBlank(workName) ? workName : "未知操作人";
                String vehicleInfo = reservation.getCarNo() != null ? reservation.getCarNo() : "未知车辆";
                String logInfo = "治安保卫部 " + operatorName + "扫码签到，" + vehicleInfo + "签到成功";
                insertLog(reservationNo, logInfo);

                log.info("签到扫码成功 - 预约编号：{}", reservationNo);
                result.put("success", true);
                result.put("message", "签到成功");
                return result;
            } else {
                log.error("签到扫码失败 - 数据库更新失败，预约编号：{}", reservationNo);
                result.put("success", false);
                result.put("message", "签到失败，请稍后重试");
                return result;
            }
        } else {
            // 其他状态不允许签到
            log.warn("签到扫码失败 - 状态不允许签到，当前状态：{}，预约编号：{}", currentStatus, reservationNo);
            result.put("success", false);
            result.put("message", "非签到环节");
            return result;
        }
    }

    /**
     * 处理门卫扫码逻辑
     */
    private Map<String, Object> handleEntranceGuardScan(MatMgmtAlloyReservation reservation, String reservationNo, String currentStatus, String workNo, String workName) {
        Map<String, Object> result = new HashMap<>();

        if ("5".equals(currentStatus)) {
            // 状态为5，已允许入厂，不修改状态
            log.info("门卫扫码 - 已允许入厂状态，预约编号：{}", reservationNo);
            result.put("success", true);
            result.put("message", "已允许入厂");
            return result;
        } else if ("7".equals(currentStatus)) {
            // 状态为7，已允许出厂，不修改状态
            log.info("门卫扫码 - 已允许出厂状态，预约编号：{}", reservationNo);
            result.put("success", true);
            result.put("message", "已允许出厂");
            return result;
        } else if ("4".equals(currentStatus)) {
            // 状态4 -> 状态5，允许入厂
            MatMgmtAlloyReservation updateReservation = new MatMgmtAlloyReservation();
            updateReservation.setId(reservation.getId());
            updateReservation.setStatus("5");
            updateReservation.setActualEarliestTime(DateUtils.getNowDate());
            updateReservation.setUpdateTime(DateUtils.getNowDate());

            int updateResult = matMgmtAlloyReservationMapper.updateMatMgmtAlloyReservation(updateReservation);

            if (updateResult > 0) {
                // 异步插入日志 - 门卫扫码入厂，包含检查项目
                String operatorName = StringUtils.isNotBlank(workName) ? workName : "未知操作人";
                String logInfo = "治安保卫部 " + operatorName + "门卫扫码，" + reservation.getCarNo() + "入厂";
                insertLog(reservationNo, logInfo);
                // 入厂检查项
                String checkItems = "\"汽车前板、汽车左右侧、汽车底板、顶板、箱顶、汽车内外门、汽车底架外部及下方、驾驶室\"已检查";
                insertLog(reservationNo, checkItems);

                log.info("门卫扫码成功 - 预约编号：{}，操作：车辆入厂", reservationNo);
                result.put("success", true);
                result.put("message", "允许入厂");
                return result;
            } else {
                log.error("门卫扫码失败 - 数据库更新失败，预约编号：{}", reservationNo);
                result.put("success", false);
                result.put("message", "操作失败，请稍后重试");
                return result;
            }
        } else if ("6".equals(currentStatus)) {
            // 状态6 -> 状态7，允许出厂
            MatMgmtAlloyReservation updateReservation = new MatMgmtAlloyReservation();
            updateReservation.setId(reservation.getId());
            updateReservation.setStatus("7");
            updateReservation.setActualLatestTime(DateUtils.getNowDate());
            updateReservation.setUpdateTime(DateUtils.getNowDate());

            int updateResult = matMgmtAlloyReservationMapper.updateMatMgmtAlloyReservation(updateReservation);

            if (updateResult > 0) {
                // 异步插入日志 - 门卫扫码出厂
                String operatorName = StringUtils.isNotBlank(workName) ? workName : "未知操作人";
                String logInfo = "治安保卫部 " + operatorName + "门卫扫码，" + reservation.getCarNo() + "出厂";
                insertLog(reservationNo, logInfo);

                log.info("门卫扫码成功 - 预约编号：{}，操作：车辆出厂", reservationNo);
                result.put("success", true);
                result.put("message", "允许出厂");
                return result;
            } else {
                log.error("门卫扫码失败 - 数据库更新失败，预约编号：{}", reservationNo);
                result.put("success", false);
                result.put("message", "操作失败，请稍后重试");
                return result;
            }
        } else {
            // 非门卫扫码环节
            log.warn("门卫扫码失败 - 状态不允许门卫扫码，当前状态：{}，预约编号：{}", currentStatus, reservationNo);
            result.put("success", false);
            result.put("message", "非门卫扫码环节");
            return result;
        }
    }

    /**
     * 物管分配
     *
     * @param assignDTO 分配参数DTO
     * @param workNo    操作人工号
     * @param workName  操作人姓名
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int materialAssign(AlloyAssignDTO assignDTO, String workNo, String workName) {
        try {
            // 1. 参数验证
            if (assignDTO == null) {
                log.error("物管分配失败 - 分配参数为空");
                throw new RuntimeException("分配参数不能为空");
            }

            String reservationNo = assignDTO.getReservationNo();
            if (StringUtils.isBlank(reservationNo)) {
                log.error("物管分配失败 - 预约编号为空");
                throw new RuntimeException("预约编号不能为空");
            }

            Date effectiveStartTime = assignDTO.getEffectiveStartTime();
            Date effectiveEndTime = assignDTO.getEffectiveEndTime();
            if (effectiveStartTime == null || effectiveEndTime == null) {
                log.error("物管分配失败 - 有效时间为空");
                throw new RuntimeException("有效时间不能为空");
            }

            String enterDoor = assignDTO.getEnterDoor();
            if (enterDoor == null) {
                log.error("物管分配失败 - 进门方式为空");
                throw new RuntimeException("进门方式不能为空");
            }

            if (StringUtils.isBlank(workNo)) {
                log.error("物管分配失败 - 操作人工号为空");
                throw new RuntimeException("操作人信息异常");
            }

            // 2. 查询预约信息
            MatMgmtAlloyReservation query = new MatMgmtAlloyReservation();
            query.setReservationNo(reservationNo);
            List<MatMgmtAlloyReservation> list = matMgmtAlloyReservationMapper.selectMatMgmtAlloyReservationList(query);

            if (list == null || list.isEmpty()) {
                log.error("物管分配失败 - 未找到预约信息，预约编号：{}", reservationNo);
                throw new RuntimeException("未找到预约信息");
            }

            MatMgmtAlloyReservation reservation = list.get(0);
            String currentStatus = reservation.getStatus();

            // 3. 状态验证 - 只有状态为3(已签到)才能物管分配
            if (!"3".equals(currentStatus)) {
                log.warn("物管分配失败 - 状态不允许分配，当前状态：{}，预约编号：{}", currentStatus, reservationNo);
                throw new RuntimeException("非物管分配入厂阶段");
            }

            // 4. 更新预约状态为4(已分配)
            MatMgmtAlloyReservation updateReservation = new MatMgmtAlloyReservation();
            updateReservation.setId(reservation.getId());
            updateReservation.setStatus("4"); // 4-已分配
            updateReservation.setEffectiveStartTime(effectiveStartTime);
            updateReservation.setEffectiveEndTime(effectiveEndTime);
            updateReservation.setEnterDoor(enterDoor);
            updateReservation.setUpdateBy(workNo);
            updateReservation.setUpdateTime(DateUtils.getNowDate());

            int result = matMgmtAlloyReservationMapper.updateMatMgmtAlloyReservation(updateReservation);

            if (result > 0) {
                // 5. 插入日志
                String operatorName = StringUtils.isNotBlank(workName) ? workName : "未知操作人";
                String vehicleInfo = reservation.getCarNo() != null ? reservation.getCarNo() : "未知车辆";
                String logInfo = "物管部 " + operatorName + "分配车辆" + vehicleInfo + "入厂";
                insertLog(reservationNo, logInfo);

                // 6. 如果enterDoor为2，推送进厂数据到爱索
                if ("2".equals(enterDoor)) {
                    reservation.setBusinessApprover(workName);
                    reservation.setStatus("4");
                    reservation.setEffectiveStartTime(effectiveStartTime);
                    reservation.setEffectiveEndTime(effectiveEndTime);
                    reservation.setEnterDoor(enterDoor);
                    reservation.setUpdateBy(workNo);
                    reservation.setUpdateTime(DateUtils.getNowDate());
                    pushEnterFactoryData(reservationNo, reservation);
                }
                // 7. 调用通知模块，通知分配通知人
                String path = "packageC/pages/matAlloy/assign/index";
                this.sendApproveMessage(INFORM_ROLE_KEY, reservationNo, "合金预约单分配完成", path);

                log.info("物管分配成功 - 预约编号：{}，操作人：{}，车辆：{}，进门方式：{}", reservationNo, operatorName, vehicleInfo, enterDoor);
                return result;
            } else {
                log.error("物管分配失败 - 数据库更新失败，预约编号：{}，工号：{}", reservationNo, workNo);
                throw new RuntimeException("分配失败，请稍后重试");
            }

        } catch (RuntimeException e) {
            throw e;
        } catch (Exception e) {
            throw new RuntimeException("系统异常，请稍后重试");
        }
    }

    /**
     * 重新分配
     *
     * @param assignDTO 分配参数DTO
     * @param workNo    操作人工号
     * @param workName  操作人姓名
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int materialReassign(AlloyAssignDTO assignDTO, String workNo, String workName) {
        try {
            // 1. 参数验证
            if (assignDTO == null) {
                log.error("重新分配失败 - 分配参数为空");
                throw new RuntimeException("分配参数不能为空");
            }

            String reservationNo = assignDTO.getReservationNo();
            if (StringUtils.isBlank(reservationNo)) {
                log.error("重新分配失败 - 预约编号为空");
                throw new RuntimeException("预约编号不能为空");
            }

            Date effectiveStartTime = assignDTO.getEffectiveStartTime();
            Date effectiveEndTime = assignDTO.getEffectiveEndTime();
            if (effectiveStartTime == null || effectiveEndTime == null) {
                log.error("重新分配失败 - 有效时间为空");
                throw new RuntimeException("有效时间不能为空");
            }

            String enterDoor = assignDTO.getEnterDoor();
            if (enterDoor == null) {
                log.error("重新分配失败 - 进门方式为空");
                throw new RuntimeException("进门方式不能为空");
            }

            if (StringUtils.isBlank(workNo)) {
                log.error("重新分配失败 - 操作人工号为空");
                throw new RuntimeException("操作人信息异常");
            }

            // 2. 查询预约信息
            MatMgmtAlloyReservation query = new MatMgmtAlloyReservation();
            query.setReservationNo(reservationNo);
            List<MatMgmtAlloyReservation> list = matMgmtAlloyReservationMapper.selectMatMgmtAlloyReservationList(query);

            if (list == null || list.isEmpty()) {
                log.error("重新分配失败 - 未找到预约信息，预约编号：{}", reservationNo);
                throw new RuntimeException("未找到预约信息");
            }

            MatMgmtAlloyReservation reservation = list.get(0);
            String currentStatus = reservation.getStatus();
            String originalEnterDoor = reservation.getEnterDoor();

            // 3. 状态验证 - 只有状态为4(待入厂)才能重新分配
            if (StringUtils.isBlank(originalEnterDoor)) {
                throw new RuntimeException("当前阶段尚未分配");
            } else {
                if (!"4".equals(currentStatus)) {
                    log.warn("重新分配失败 - 状态不允许分配，当前状态：{}，预约编号：{}", currentStatus, reservationNo);
                    throw new RuntimeException("当前阶段不可重新分配");
                }

                // 4. 如果originalEnterDoor为2，删除推送进厂数据到爱索
                if ("2".equals(originalEnterDoor)) {
                    pushDeleteEnterFactoryData(reservationNo, reservation);
                    log.info("删除推送信息 - 预约编号：{}，操作人：{}，车辆：{}，进门方式：{}", reservationNo, originalEnterDoor);
                }
            }

            // 5. 更新分配信息
            MatMgmtAlloyReservation updateReservation = new MatMgmtAlloyReservation();
            updateReservation.setId(reservation.getId());
            updateReservation.setStatus("4"); // 4-已分配
            updateReservation.setEffectiveStartTime(effectiveStartTime);
            updateReservation.setEffectiveEndTime(effectiveEndTime);
            updateReservation.setEnterDoor(enterDoor);
            updateReservation.setUpdateBy(workNo);
            updateReservation.setUpdateTime(DateUtils.getNowDate());

            //重新分配到三号门重新生成pushNo
            String pushNo = " ";
            if ("2".equals(enterDoor)) {
                pushNo = SnowFlakeUtil.getAlloyPushNoSnowFlakeId();
                updateReservation.setPushNo(pushNo);
                log.info("重新生成推送号：{}", pushNo);
            }

            int result = matMgmtAlloyReservationMapper.updateMatMgmtAlloyReservation(updateReservation);

            if (result > 0) {
                // 6. 插入日志
                String operatorName = StringUtils.isNotBlank(workName) ? workName : "未知操作人";
                String vehicleInfo = reservation.getCarNo() != null ? reservation.getCarNo() : "未知车辆";
                String logInfo = "物管部 " + operatorName + "重新分配车辆" + vehicleInfo + "入厂";
                insertLog(reservationNo, logInfo);

                // 7. 如果enterDoor为2，推送进厂数据到爱索
                if ("2".equals(enterDoor)) {
                    reservation.setPushNo(pushNo);
                    reservation.setBusinessApprover(workName);
                    reservation.setStatus("4");
                    reservation.setEffectiveStartTime(effectiveStartTime);
                    reservation.setEffectiveEndTime(effectiveEndTime);
                    reservation.setEnterDoor(enterDoor);
                    reservation.setUpdateBy(workNo);
                    reservation.setUpdateTime(DateUtils.getNowDate());
                    pushEnterFactoryData(reservationNo, reservation);
                }
                // 8. 调用通知模块，通知分配通知人
                String path = "packageC/pages/matAlloy/assign/index";
                this.sendApproveMessage(INFORM_ROLE_KEY, reservationNo, "合金预约单已重新分配", path);

                log.info("物管重新分配成功 - 预约编号：{}，操作人：{}，车辆：{}，进门方式：{}", reservationNo, operatorName, vehicleInfo, enterDoor);
                return result;
            } else {
                log.error("物管重新分配失败 - 数据库更新失败，预约编号：{}，工号：{}", reservationNo, workNo);
                throw new RuntimeException("重新分配失败，请稍后重试");
            }

        } catch (RuntimeException e) {
            throw e;
        } catch (Exception e) {
            throw new RuntimeException("系统异常，请稍后重试");
        }
    }

    /**
     * 推送进厂数据
     *
     * @param reservationNo 预约编号
     * @param reservation   预约信息
     */
    private void pushEnterFactoryData(String reservationNo, MatMgmtAlloyReservation reservation) {
        try {
            log.info("开始推送进厂数据 - 预约编号：{}，车辆：{}", reservationNo, reservation.getCarNo());

            // 调用爱索推送接口
            matMgmtAlloyAiSuoService.pushTruckInfo(reservation);

            log.info("推送进厂数据成功 - 预约编号：{}，车辆：{}", reservationNo, reservation.getCarNo());

        } catch (Exception e) {
            log.error("推送进厂数据失败 - 预约编号：{}，车辆：{}", reservationNo, reservation.getCarNo(), e);
            // 推送失败抛出异常
            throw new RuntimeException("推送进厂数据失败");
        }
    }

    /**
     * 删除推送进厂数据
     *
     * @param reservationNo 预约编号
     * @param reservation   预约信息
     */
    private void pushDeleteEnterFactoryData(String reservationNo, MatMgmtAlloyReservation reservation) {
        try {
            log.info("开始删除推送进厂数据 - 预约编号：{}，车辆：{}", reservationNo, reservation.getCarNo());

            // 调用爱索推送接口
            matMgmtAlloyAiSuoService.pushDeleteTruckInfo(reservation);

            log.info("删除推送进厂数据成功 - 预约编号：{}，车辆：{}", reservationNo, reservation.getCarNo());

        } catch (Exception e) {
            log.error("删除推送进厂数据失败 - 预约编号：{}，车辆：{}", reservationNo, reservation.getCarNo(), e);
            // 推送失败抛出异常
            throw new RuntimeException("删除推送进厂数据失败");
        }
    }

    /**
     * 物管确认
     *
     * @param reservationNo 预约编号
     * @param workNo        操作人工号
     * @param workName      操作人姓名
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int materialConfirm(String reservationNo, String workNo, String workName) {
        try {
            // 1. 参数验证
            if (StringUtils.isBlank(reservationNo)) {
                log.error("物管确认失败 - 预约编号为空");
                throw new RuntimeException("预约编号不能为空");
            }

            if (StringUtils.isBlank(workNo)) {
                log.error("物管确认失败 - 操作人工号为空");
                throw new RuntimeException("操作人信息异常");
            }

            // 2. 权限验证 - 检查是否有物管部确认权限
            boolean hasConfirmInRole = sysRoleService.selectRoleExistByUserName(workNo, Collections.singletonList(CONFIRM_ROLE_KEY));
            if (!hasConfirmInRole) {
                log.error("签到失败 - 用户无签到权限，工号：{}", workNo);
                throw new RuntimeException("您没有签到权限");
            }

            // 2. 查询预约信息
            MatMgmtAlloyReservation query = new MatMgmtAlloyReservation();
            query.setReservationNo(reservationNo);
            List<MatMgmtAlloyReservation> list = matMgmtAlloyReservationMapper.selectMatMgmtAlloyReservationList(query);

            if (list == null || list.isEmpty()) {
                log.error("物管确认失败 - 未找到预约信息，预约编号：{}", reservationNo);
                throw new RuntimeException("未找到预约信息");
            }

            MatMgmtAlloyReservation reservation = list.get(0);
            String currentStatus = reservation.getStatus(); //状态
            String endoor = reservation.getEnterDoor(); //入厂大门

            if (StringUtils.isBlank(endoor)) {
                throw new RuntimeException("未分配至安全村，非物管确认环节");
            } else {
                if (!"1".equals(endoor)) {
                    throw new RuntimeException("未分配至安全村，非物管确认环节");
                }
            }

            // 3. 状态验证 - 只有状态为5(已入厂)才进行物管确认
            if (!"5".equals(currentStatus)) {
                log.warn("物管确认失败 - 状态不允许确认，当前状态：{}，预约编号：{}", currentStatus, reservationNo);
                throw new RuntimeException("非物管确认环节");
            }

            // 4. 更新预约状态为6(已确认)
            MatMgmtAlloyReservation updateReservation = new MatMgmtAlloyReservation();
            updateReservation.setId(reservation.getId());
            updateReservation.setStatus("6"); // 6-已确认
            updateReservation.setUpdateBy(workNo);
            updateReservation.setUpdateTime(DateUtils.getNowDate());

            int result = matMgmtAlloyReservationMapper.updateMatMgmtAlloyReservation(updateReservation);

            if (result > 0) {
                // 5. 异步插入日志
                String operatorName = StringUtils.isNotBlank(workName) ? workName : "未知操作人";
                String logInfo = "物管部 " + operatorName + "确认";
                insertLog(reservationNo, logInfo);

                log.info("物管确认成功 - 预约编号：{}，操作人：{}", reservationNo, operatorName);
                return result;
            } else {
                log.error("物管确认失败 - 数据库更新失败，预约编号：{}，工号：{}", reservationNo, workNo);
                throw new RuntimeException("确认失败，请稍后重试");
            }

        } catch (RuntimeException e) {
            throw e;
        } catch (Exception e) {
            log.error("物管确认发生异常 - 预约编号：{}，工号：{}", reservationNo, workNo, e);
            throw new RuntimeException("系统异常，请稍后重试");
        }
    }

    /**
     * 签到
     *
     * @param reservationNo 预约编号
     * @param workNo        操作人工号
     * @param workName      操作人姓名
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int checkIn(String reservationNo, String workNo, String workName) {
        try {
            // 1. 参数验证
            if (StringUtils.isBlank(reservationNo)) {
                log.error("签到失败 - 预约编号为空");
                throw new RuntimeException("预约编号不能为空");
            }

            if (StringUtils.isBlank(workNo)) {
                log.error("签到失败 - 操作人工号为空");
                throw new RuntimeException("操作人信息异常");
            }

            // 2. 权限验证 - 检查是否有保卫部签到权限
            boolean hasSignInRole = sysRoleService.selectRoleExistByUserName(workNo, Collections.singletonList(SIGN_IN_ROLE_KEY));
            if (!hasSignInRole) {
                log.error("签到失败 - 用户无签到权限，工号：{}", workNo);
                throw new RuntimeException("您没有签到权限");
            }

            // 3. 查询预约信息
            MatMgmtAlloyReservation query = new MatMgmtAlloyReservation();
            query.setReservationNo(reservationNo);
            List<MatMgmtAlloyReservation> list = matMgmtAlloyReservationMapper.selectMatMgmtAlloyReservationList(query);

            if (list == null || list.isEmpty()) {
                log.error("签到失败 - 未找到预约信息，预约编号：{}", reservationNo);
                throw new RuntimeException("未找到预约信息");
            }

            MatMgmtAlloyReservation reservation = list.get(0);
            String currentStatus = reservation.getStatus();

            // 4. 状态验证 - 只有状态为2(审核通过)才能签到
            if (!"2".equals(currentStatus)) {
                log.warn("签到失败 - 状态不允许签到，当前状态：{}，预约编号：{}", currentStatus, reservationNo);
                throw new RuntimeException("非签到环节");
            }

            // 5. 更新预约状态为3(已签到)
            MatMgmtAlloyReservation updateReservation = new MatMgmtAlloyReservation();
            updateReservation.setId(reservation.getId());
            updateReservation.setStatus("3"); // 3-已签到
            updateReservation.setCheckinTime(DateUtils.getNowDate()); // 设置签到时间
            updateReservation.setUpdateBy(workNo);
            updateReservation.setUpdateTime(DateUtils.getNowDate());

            int result = matMgmtAlloyReservationMapper.updateMatMgmtAlloyReservation(updateReservation);

            if (result > 0) {
                // 6. 异步插入日志
                String operatorName = StringUtils.isNotBlank(workName) ? workName : "未知操作人";
                String vehicleInfo = reservation.getCarNo() != null ? reservation.getCarNo() : "未知车辆";
                String logInfo = "治安保卫部 " + operatorName + "扫码签到，车辆" + vehicleInfo + "签到成功";
                insertLog(reservationNo, logInfo);

                // 7. 调用通知模块，通知物管分配人员
                String path = "packageC/pages/matAlloy/assign/index";
                String deliveryCategoryCode = reservation.getDeliveryCategoryCode();
                if (StringUtils.isNotBlank(deliveryCategoryCode)) {
                    if ("1".equals(deliveryCategoryCode)) {
                        // 合金
                        this.sendApproveMessage(ASSIGN_ROLE_KEY, reservationNo, "您有合金预约单待分配", path);
                    }
                    if ("2".equals(deliveryCategoryCode)) {
                        // 电极
                        this.sendApproveMessage(ELECTRODE_ASSIGN_ROLE_KEY, reservationNo, "您有合金预约单待分配", path);
                    }
                }

                log.info("签到成功 - 预约编号：{}，操作人：{}，车辆：{}", reservationNo, operatorName, vehicleInfo);
                return result;
            } else {
                log.error("签到失败 - 数据库更新失败，预约编号：{}，工号：{}", reservationNo, workNo);
                throw new RuntimeException("签到失败，请稍后重试");
            }

        } catch (RuntimeException e) {
            throw e;
        } catch (Exception e) {
            log.error("签到发生异常 - 预约编号：{}，工号：{}", reservationNo, workNo, e);
            throw new RuntimeException("系统异常，请稍后重试");
        }
    }

    /**
     * 门卫确认通行（安全村）
     *
     * @param reservationNo 预约编号
     * @param workNo        操作人工号
     * @param workName      操作人姓名
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int entranceGuardScan(String reservationNo, String workNo, String workName) {
        try {
            // 1. 参数验证
            if (StringUtils.isBlank(reservationNo)) {
                log.error("门卫扫码失败 - 预约编号为空");
                throw new RuntimeException("预约编号不能为空");
            }

            if (StringUtils.isBlank(workNo)) {
                log.error("门卫扫码失败 - 操作人工号为空");
                throw new RuntimeException("操作人信息异常");
            }

            // 2. 权限验证 - 检查是否有门卫扫码权限
            boolean hasEntranceRole = sysRoleService.selectRoleExistByUserName(workNo, Collections.singletonList(ENTRANCE_ROLE_KEY));
            if (!hasEntranceRole) {
                log.error("门卫扫码失败 - 用户无门卫扫码权限，工号：{}", workNo);
                throw new RuntimeException("您没有确认通行权限");
            }

            // 3. 查询预约信息
            MatMgmtAlloyReservation query = new MatMgmtAlloyReservation();
            query.setReservationNo(reservationNo);
            List<MatMgmtAlloyReservation> list = matMgmtAlloyReservationMapper.selectMatMgmtAlloyReservationList(query);

            if (list == null || list.isEmpty()) {
                log.error("门卫扫码失败 - 未找到预约信息，预约编号：{}", reservationNo);
                throw new RuntimeException("未找到预约信息");
            }

            MatMgmtAlloyReservation reservation = list.get(0);
            String currentStatus = reservation.getStatus();
            String enterDoor = reservation.getEnterDoor();

            // 4. 进门方式验证 - 只有enterDoor存在且为"1"才需要门卫扫码
            if (StringUtils.isBlank(enterDoor) || !"1".equals(enterDoor)) {
                log.warn("门卫扫码失败 - 进门方式不需要门卫扫码，enterDoor：{}，预约编号：{}", enterDoor, reservationNo);
                throw new RuntimeException("该预约无需门卫确认通行");
            }

            // 5. 状态验证和处理
            String newStatus = null;
            String logAction = null;

            if ("4".equals(currentStatus)) {
                // 状态4 -> 状态5，允许入厂
                newStatus = "5";
                logAction = "车辆入厂";
            } else if ("6".equals(currentStatus)) {
                // 状态6 -> 状态7，允许出厂
                newStatus = "7";
                logAction = "车辆出厂";
            } else {
                log.warn("门卫扫码失败 - 状态不允许门卫扫码，当前状态：{}，预约编号：{}", currentStatus, reservationNo);
                throw new RuntimeException("非门卫确认通行环节");
            }

            // 6. 更新预约状态
            MatMgmtAlloyReservation updateReservation = new MatMgmtAlloyReservation();
            updateReservation.setId(reservation.getId());
            updateReservation.setStatus(newStatus);
            updateReservation.setUpdateBy(workNo);
            updateReservation.setUpdateTime(DateUtils.getNowDate());

            int result = matMgmtAlloyReservationMapper.updateMatMgmtAlloyReservation(updateReservation);

            if (result > 0) {
                // 7. 异步插入日志
                String operatorName = StringUtils.isNotBlank(workName) ? workName : "未知操作人";
                String logInfo = "治安保卫部 " + operatorName + "门卫扫码，确认通行" + logAction;

                // 如果是入厂，添加检查项目
                if ("车辆入厂".equals(logAction)) {
                    String checkItems = "\"汽车前板、汽车左右侧、汽车底板、顶板、箱顶、汽车内外门、汽车底架外部及下方、驾驶室\"已检查";
                    logInfo += "，" + checkItems;
                }

                insertLog(reservationNo, logInfo);

                log.info("门卫扫码成功 - 预约编号：{}，操作人：{}，操作：{}", reservationNo, operatorName, logAction);
                return result;
            } else {
                log.error("门卫扫码失败 - 数据库更新失败，预约编号：{}，工号：{}", reservationNo, workNo);
                throw new RuntimeException("确认通行异常");
            }

        } catch (RuntimeException e) {
            throw new RuntimeException("确认异常，请稍后重试");
        } catch (Exception e) {
            log.error("门卫扫码发生异常 - 预约编号：{}，工号：{}", reservationNo, workNo, e);
            throw new RuntimeException("系统异常，请稍后重试");
        }
    }

    /**
     * 扫码检查（只检查状态，不修改数据）
     *
     * @param reservationNo 预约编号
     * @param scanAction    扫码动作（checkIn-签到，entranceGuard-门卫扫码，matConfirm-物管确认）
     * @param workNo        操作人工号
     * @param workName      操作人姓名
     * @return 结果Map
     */
    @Override
    public Map<String, Object> scanCheck(String reservationNo, String scanAction, String workNo, String workName) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 1. 参数验证
            if (StringUtils.isBlank(reservationNo)) {
                log.error("扫码检查失败 - 预约编号为空");
                result.put("success", false);
                result.put("message", "预约编号不能为空");
                return result;
            }

            if (StringUtils.isBlank(scanAction)) {
                log.error("扫码检查失败 - 扫码动作为空");
                result.put("success", false);
                result.put("message", "扫码动作不能为空");
                return result;
            }

            // 2. 查询预约信息
            MatMgmtAlloyReservation query = new MatMgmtAlloyReservation();
            query.setReservationNo(reservationNo);
            List<MatMgmtAlloyReservation> list = matMgmtAlloyReservationMapper.selectMatMgmtAlloyReservationList(query);

            if (list == null || list.isEmpty()) {
                log.error("扫码检查失败 - 未找到预约信息，预约编号：{}", reservationNo);
                result.put("success", false);
                result.put("message", "未找到预约信息");
                return result;
            }

            MatMgmtAlloyReservation reservation = list.get(0);
            String currentStatus = reservation.getStatus();
            String enterDoor = reservation.getEnterDoor();

            // 3. 根据扫码动作检查不同逻辑
            switch (scanAction) {
                case "checkIn":
                    return checkSignInStatus(reservation, reservationNo, currentStatus, workNo);
                case "entranceGuard":
                    return checkEntranceGuardStatus(reservation, reservationNo, currentStatus, enterDoor, workNo);
                case "matConfirm":
                    return checkMatConfirmStatus(reservation, reservationNo, currentStatus, enterDoor, workNo);
                default:
                    log.error("扫码检查失败 - 不支持的扫码动作：{}", scanAction);
                    result.put("success", false);
                    result.put("message", "不支持的扫码动作");
                    return result;
            }

        } catch (Exception e) {
            log.error("扫码检查发生异常 - 预约编号：{}，扫码动作：{}", reservationNo, scanAction, e);
            result.put("success", false);
            result.put("message", "系统异常，请稍后重试");
            return result;
        }
    }

    /**
     * 检查签到状态
     */
    private Map<String, Object> checkSignInStatus(MatMgmtAlloyReservation reservation, String reservationNo, String currentStatus, String workNo) {
        Map<String, Object> result = new HashMap<>();

        // 权限验证
        if (StringUtils.isNotBlank(workNo)) {
            boolean hasSignInRole = sysRoleService.selectRoleExistByUserName(workNo, Arrays.asList(SIGN_IN_ROLE_KEY));
            if (!hasSignInRole) {
                result.put("success", false);
                result.put("message", "您没有签到权限");
                return result;
            }
        }

        if ("2".equals(currentStatus)) {
            result.put("success", true);
            result.put("message", "请签到");
        } else if ("3".equals(currentStatus)) {
            result.put("success", true);
            result.put("message", "已签到");
        } else {
            result.put("success", false);
            result.put("message", "非签到环节");
        }

        return result;
    }

    /**
     * 检查门卫扫码状态
     */
    private Map<String, Object> checkEntranceGuardStatus(MatMgmtAlloyReservation reservation, String reservationNo, String currentStatus, String enterDoor, String workNo) {
        Map<String, Object> result = new HashMap<>();

        // 权限验证
        if (StringUtils.isNotBlank(workNo)) {
            boolean hasEntranceRole = sysRoleService.selectRoleExistByUserName(workNo, Arrays.asList(ENTRANCE_ROLE_KEY));
            if (!hasEntranceRole) {
                result.put("success", false);
                result.put("message", "您没有门卫扫码权限");
                return result;
            }
        }

        // 进门方式验证
        int status = Integer.parseInt(currentStatus);
        if (status > 3 && status < 8 && (StringUtils.isBlank(enterDoor) || !"1".equals(enterDoor))) {
            result.put("success", false);
            result.put("message", "三号门无需门卫扫码");
            return result;
        }

        if ("4".equals(currentStatus)) {
            result.put("success", true);
            result.put("message", "请确认入厂");
        } else if ("5".equals(currentStatus)) {
            result.put("success", true);
            result.put("message", "已允许入厂");
        } else if ("6".equals(currentStatus)) {
            result.put("success", true);
            result.put("message", "请确认出厂");
        } else if ("7".equals(currentStatus)) {
            result.put("success", true);
            result.put("message", "已允许出厂");
        } else {
            result.put("success", false);
            result.put("message", "非门卫扫码环节");
        }

        return result;
    }

    /**
     * 检查物管确认状态
     */
    private Map<String, Object> checkMatConfirmStatus(MatMgmtAlloyReservation reservation, String reservationNo, String currentStatus, String enterDoor, String workNo) {
        Map<String, Object> result = new HashMap<>();

        // 权限验证
        if (StringUtils.isNotBlank(workNo)) {
            boolean hasConfirmRole = sysRoleService.selectRoleExistByUserName(workNo, Arrays.asList(CONFIRM_ROLE_KEY));
            if (!hasConfirmRole) {
                result.put("success", false);
                result.put("message", "您没有物管确认权限");
                return result;
            }
        }

        // 进门方式验证
        int status = Integer.parseInt(currentStatus);
        if (status > 4 && status < 7 && (StringUtils.isBlank(enterDoor) || !"1".equals(enterDoor))) {
            result.put("success", false);
            result.put("message", "三号门无需物管确认");
            return result;
        }

        if ("5".equals(currentStatus)) {
            result.put("success", true);
            result.put("message", "请物管确认");
        } else if ("6".equals(currentStatus)) {
            result.put("success", true);
            result.put("message", "已物管确认");
        } else {
            result.put("success", false);
            result.put("message", "非物管确认环节");
        }

        return result;
    }

    public static void main(String[] args) {
        System.out.println(SnowFlakeUtil.getAlloyReservationNoSnowFlakeId());
    }
}
