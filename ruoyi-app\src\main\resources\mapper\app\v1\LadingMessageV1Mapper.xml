<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.app.v1.mapper.LadingMessageV1Mapper">

    <resultMap type="LadingMessageV1" id="ladingMessageResult">
        <result property="recCreateTime" column="rec_create_time"/>
        <result property="stockName" column="stock_name"/>
        <result property="consignUserCode" column="consign_user_code"/>
        <result property="consignUserName" column="consign_user_name"/>
        <result property="billOfLadingNo" column="bill_of_lading_no"/>
        <result property="planWt" column="plan_wt"/>
        <result property="finishWt" column="finish_wt"/>
        <result property="orderInPerson" column="order_in_person"/>
        <result property="delivyPlanStatus" column="delivy_plan_status"/>
        <result property="planEndTime" column="plan_end_time"/>
        <result property="delivyPlanType" column="delivy_plan_type"/>
        <result property="billRemark" column="bill_remark"/>
    </resultMap>

    <select id="selectOperatorStrvalue" resultType="string">
        select strvalue from table(select xcc1.GET_SALE_WT(#{operatorNo}) from dual)
    </select>

    <select id="selectBCOperatorStrvalue" resultType="string">
        select strvalue from table(select xcb1.GET_SALE_WT_X(#{operatorNo}) from dual)
    </select>

    <select id="selectLadingMessageResult"  resultMap="ladingMessageResult">
        select
        t.rec_create_time,  --提单转库单创建时间 不显示
        t.stock_name,  --提货库区 显示
        t.consign_user_code, --提货客户编号，转库单显示转向库区编号 不显示
        t.consign_user_name, --提货客户名称 显示
        t.bill_of_lading_no, --提单号 显示
        t.plan_wt,--计划重量 显示
        t.finish_wt,--完成重量 不显示
        t.order_in_person,--对应销售员 不显示
        t.delivy_plan_status,  --提单状态 不显示
        t.plan_end_time  ,--提单截止时间 显示
        t.delivy_plan_type, --提单转库单标记 t 提单，z转库单 不显示
        t.bill_remark
        from xcc1.T0101 t
        where 1=1
        and TO_DATE(SUBSTR(t.rec_create_time, 1, 8), 'YYYYMMDD') >= TRUNC(SYSDATE) - 14
        and t.order_in_person in
        <foreach collection="strValue" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="consignUserName != null  and consignUserName != ''">and t.consign_user_name like concat(concat('%',#{consignUserName}), '%')</if>
        order by t.rec_create_time desc
    </select>

    <resultMap type="LadingList" id="ladingListResult">
        <result property="billOfLadingNo" column="BILL_OF_LADING_NO"/>
        <result property="sgSign" column="SG_SIGN"/>
        <result property="sgStd" column="SG_STD"/>
        <result property="guige" column="GUIGE"/>
        <result property="planWt" column="PLAN_WT"/>
        <result property="finishWt" column="FINISH_WT"/>
        <result property="orderNoErp" column="ORDER_NO_ERP"/>
        <result property="unFinishWt" column="UN_FINISH_WT"/>
    </resultMap>

    <select id="selectLadingList" parameterType="String" resultMap="ladingListResult">
        --T0102 提单具体信息
            select
            BILL_OF_LADING_NO,  --提单号 显示
            SG_SIGN,  --钢种 显示
            SG_STD,  --标准 显示
            GUIGE,  --规格 显示
            PLAN_WT,  --计划量 显示
            FINISH_WT,  --完成量 显示
            ORDER_NO_ERP, --合同号 显示
            UN_FINISH_WT --未完成量 显示
            from xcc1.T0102  t
--             where t.bill_of_lading_no = '18A3203160501';
            where t.bill_of_lading_no = #{billOfLadingNo}
    </select>

    <resultMap type="DriverLading" id="driverLadingResult">
        <result property="billOfLadingNo" column="bill_of_lading_no"/>
        <result property="delivyNo" column="delivy_no"/>
        <result property="vehicleNo" column="vehicle_no"/>
        <result property="carryCompanyName" column="carry_company_name"/>
        <result property="delivyTime" column="delivy_time"/>
        <result property="driverMobileNo" column="driver_mobile_no"/>
        <result property="driverName" column="driver_name"/>
        <result property="reservation" column="Reservation"/>
    </resultMap>

    <select id="selectDriverLading" parameterType="String" resultMap="driverLadingResult">
            select
            t.bill_of_lading_no,--提单号 不显示
            t.delivy_no, --提货号 不显示
            t.vehicle_no,--车号 不显示
            t.carry_company_name, --运输委托单号 不显示
            t.delivy_time,--预约时间 不显示
            t.driver_mobile_no,--驾驶员手机号 不显示
            driver_name,--驾驶员 不显示
            t.Reservation--预约信息合集 显示
            from  xcc1.T0103 t
            where t.bill_of_lading_no = #{billOfLadingNo}
    </select>

    <select id="selectConsignUserNameResult" parameterType="LadingMessageV1" resultMap="ladingMessageResult">
        select
        t.consign_user_code, --提货客户编号，转库单显示转向库区编号 不显示
        t.consign_user_name --提货客户名称 显示
        from xcc1.T0101 t
        where 1=1
        and t.order_in_person in
        <foreach collection="strValue" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>
