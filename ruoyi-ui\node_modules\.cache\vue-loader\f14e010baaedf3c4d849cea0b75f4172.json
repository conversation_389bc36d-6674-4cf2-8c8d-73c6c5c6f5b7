{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\assess\\self\\collect\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\assess\\self\\collect\\index.vue", "mtime": 1756099891056}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBsaXN0SW5mbywgZ2V0SW5mbywgZXhwb3J0SW5mbywgZXhwb3J0RGV0YWlsLCBleHBvcnRUZWNobmljYWxQZXJmb3JtYW5jZVN1bW1hcnksIGV4cG9ydEFkbWluaXN0cmF0aXZlUGVyZm9ybWFuY2VTdW1tYXJ5IH0gZnJvbSAiQC9hcGkvYXNzZXNzL3NlbGYvaW5mbyI7DQppbXBvcnQgeyBnZXRUb2tlbiB9IGZyb20gIkAvdXRpbHMvYXV0aCI7DQppbXBvcnQgeyBkb3duTG9hZFppcCB9IGZyb20gIkAvdXRpbHMvemlwZG93bmxvYWQiOw0KaW1wb3J0IHsgbGlzdERlcHQgfSBmcm9tICJAL2FwaS9hc3Nlc3MvbGF0ZXJhbC9kZXB0IjsNCmltcG9ydCBUcmVlc2VsZWN0IGZyb20gIkByaW9waGFlL3Z1ZS10cmVlc2VsZWN0IjsNCmltcG9ydCAiQHJpb3BoYWUvdnVlLXRyZWVzZWxlY3QvZGlzdC92dWUtdHJlZXNlbGVjdC5jc3MiOw0KDQpleHBvcnQgZGVmYXVsdCB7DQogICAgbmFtZTogIlNlbGZBc3Nlc3NDb2xsZWN0IiwNCiAgICBjb21wb25lbnRzOiB7DQogICAgICAgIFRyZWVzZWxlY3QNCiAgICB9LA0KICAgIGRhdGEoKSB7DQogICAgICByZXR1cm4gew0KICAgICAgICAvLyDpga7nvanlsYINCiAgICAgICAgbG9hZGluZzogdHJ1ZSwNCiAgICAgICAgLy8g5pi+56S65pCc57Si5p2h5Lu2DQogICAgICAgIHNob3dTZWFyY2g6IHRydWUsDQogICAgICAgIC8vIOaAu+adoeaVsA0KICAgICAgICB0b3RhbDogMCwNCiAgICAgICAgLy8g57up5pWI6ICD5qC4LeW5sumDqOiHquivhOS6uuWRmOmFjee9ruihqOagvOaVsOaNrg0KICAgICAgICBsaXN0SW5mbzogW10sDQogICAgICAgIC8vIOW8ueWHuuWxguagh+mimA0KICAgICAgICB0aXRsZTogIiIsDQogICAgICAgIC8vIOaYr+WQpuaYvuekuuW8ueWHuuWxgg0KICAgICAgICBvcGVuOiBmYWxzZSwNCiAgICAgICAgLy8g5p+l6K+i5Y+C5pWwDQogICAgICAgIHF1ZXJ5UGFyYW1zOiB7DQogICAgICAgICAgcGFnZU51bTogMSwNCiAgICAgICAgICBwYWdlU2l6ZTogMTAsDQogICAgICAgICAgd29ya05vOiBudWxsLA0KICAgICAgICAgIG5hbWU6bnVsbCwNCiAgICAgICAgICBkZXB0SWQ6bnVsbCwNCiAgICAgICAgICBhc3Nlc3NEYXRlOm51bGwsDQogICAgICAgICAgc3RhdHVzOm51bGwNCiAgICAgICAgfSwNCiAgICAgICAgLy8g6KGo5Y2V5Y+C5pWwDQogICAgICAgIGZvcm06IHsNCiAgICAgICAgICBpZDpudWxsLA0KICAgICAgICAgIC8vIOmDqOmXqOmihuWvvOivhOWIhg0KICAgICAgICAgIGRlcHRTY29yZTpudWxsLA0KICAgICAgICAgIC8vIOS6i+S4mumDqOivhOWIhg0KICAgICAgICAgIGJ1c2luZXNzU2NvcmU6bnVsbCwNCiAgICAgICAgICAvLyDmnaHnur/pooblr7zor4TliIYNCiAgICAgICAgICBsZWFkZXJTY29yZTpudWxsLA0KICAgICAgICB9LA0KICAgICAgICAvLyDooajljZXmoKHpqowNCiAgICAgICAgcnVsZXM6IHsNCiAgICAgICAgfSwNCiAgICAgICAgZGVwdE9wdGlvbnM6W10sDQogICAgICAgIG9wZW5DaGVjazpmYWxzZSwNCiAgICAgICAgY2hlY2tJbmZvOnsNCiAgICAgICAgICBuYW1lOm51bGwsDQogICAgICAgICAgYXNzZXNzRGF0ZTpudWxsLA0KICAgICAgICAgIGRlcHROYW1lOm51bGwsDQogICAgICAgICAgbGlzdDpbXQ0KICAgICAgICB9LA0KICAgICAgICAvLyDlkIjlubbljZXlhYPmoLwNCiAgICAgICAgc3Bhbkxpc3Q6W10sDQogICAgICAgIC8vIOWvvOWFpeWPguaVsA0KICAgICAgICB1cGxvYWQ6IHsNCiAgICAgICAgICAvLyDmmK/lkKbnpoHnlKjkuIrkvKANCiAgICAgICAgICBpc1VwbG9hZGluZzogZmFsc2UsDQogICAgICAgICAgLy8g6K6+572u5LiK5Lyg55qE6K+35rGC5aS06YOoDQogICAgICAgICAgaGVhZGVyczogeyBBdXRob3JpemF0aW9uOiAnQmVhcmVyICcgKyBnZXRUb2tlbigpIH0sDQogICAgICAgICAgLy8g5LiK5Lyg55qE5Zyw5Z2ADQogICAgICAgICAgdXJsOiBwcm9jZXNzLmVudi5WVUVfQVBQX0JBU0VfQVBJICsgIi93ZWIvc2VsZkFzc2Vzcy9pbmZvL2ltcG9ydEZpbmFsU2NvcmUiLA0KICAgICAgICB9LA0KICAgICAgfTsNCiAgICB9LA0KICAgIGNyZWF0ZWQoKSB7DQogICAgICAgIHRoaXMucXVlcnlQYXJhbXMuYXNzZXNzRGF0ZSA9IHRoaXMuZ2V0RGVmYXVsdEFzc2Vzc0RhdGUoKQ0KICAgICAgICAvLyB0aGlzLmdldFNlbGZBc3Nlc3NVc2VyKCk7DQogICAgICAgIC8vIHRoaXMuZ2V0Q2hlY2tEZXB0TGlzdCgpOw0KICAgICAgICB0aGlzLmdldFRyZWVzZWxlY3QoKTsNCiAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgfSwNCiAgICBtZXRob2RzOiB7DQoNCiAgICAgICAgLy8g6I635Y+W6buY6K6k6ICD5qC45pel5pyfDQogICAgICAgIGdldERlZmF1bHRBc3Nlc3NEYXRlKCkgew0KICAgICAgICAgICAgY29uc3Qgbm93ID0gbmV3IERhdGUoKTsNCiAgICAgICAgICAgIGNvbnN0IGN1cnJlbnREYXkgPSBub3cuZ2V0RGF0ZSgpOw0KDQogICAgICAgICAgICBsZXQgdGFyZ2V0RGF0ZTsNCiAgICAgICAgICAgIGlmIChjdXJyZW50RGF5IDwgMTApIHsNCiAgICAgICAgICAgICAgICAvLyDlvZPliY3ml6XmnJ/lsI/kuo4xMOaXpe+8jOm7mOiupOS4uuS4iuS4quaciA0KICAgICAgICAgICAgICAgIHRhcmdldERhdGUgPSBuZXcgRGF0ZShub3cuZ2V0RnVsbFllYXIoKSwgbm93LmdldE1vbnRoKCkgLSAxLCAxKTsNCiAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgICAgLy8g5b2T5YmN5pel5pyf5aSn5LqO562J5LqOMTDml6XvvIzpu5jorqTkuLrlvZPmnIgNCiAgICAgICAgICAgICAgICB0YXJnZXREYXRlID0gbmV3IERhdGUobm93LmdldEZ1bGxZZWFyKCksIG5vdy5nZXRNb250aCgpLCAxKTsNCiAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgLy8g5qC85byP5YyW5Li6IFlZWVktTSDmoLzlvI8NCiAgICAgICAgICAgIGNvbnN0IHllYXIgPSB0YXJnZXREYXRlLmdldEZ1bGxZZWFyKCk7DQogICAgICAgICAgICBjb25zdCBtb250aCA9IHRhcmdldERhdGUuZ2V0TW9udGgoKSArIDE7DQogICAgICAgICAgICByZXR1cm4gYCR7eWVhcn0tJHttb250aH1gOw0KICAgICAgICB9LA0KDQogICAgICAgIC8vIOiOt+WPlumDqOmXqOS/oeaBrw0KICAgICAgICAvKiog5p+l6K+i5qiq5ZCR6K+E5Lu36YOo6Zeo5LiL5ouJ5qCR57uT5p6EICovDQogICAgICAgIGdldFRyZWVzZWxlY3QoKSB7DQogICAgICAgICAgICBsaXN0RGVwdCgpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICAgICAgICAgIHRoaXMuZGVwdE9wdGlvbnMgPSB0aGlzLmhhbmRsZVRyZWUocmVzcG9uc2UuZGF0YSwgImRlcHRJZCIsICJwYXJlbnRJZCIpOw0KICAgICAgICAgICAgfSk7DQogICAgICAgIH0sDQogICAgICAgIC8qKiDovazmjaLmqKrlkJHor4Tku7fpg6jpl6jmlbDmja7nu5PmnoQgKi8NCiAgICAgICAgbm9ybWFsaXplcihub2RlKSB7DQogICAgICAgICAgICBpZiAobm9kZS5jaGlsZHJlbiAmJiAhbm9kZS5jaGlsZHJlbi5sZW5ndGgpIHsNCiAgICAgICAgICAgICAgICBkZWxldGUgbm9kZS5jaGlsZHJlbjsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICAgIHJldHVybiB7DQogICAgICAgICAgICAgICAgaWQ6IG5vZGUuZGVwdElkLA0KICAgICAgICAgICAgICAgIGxhYmVsOiBub2RlLmRlcHROYW1lLA0KICAgICAgICAgICAgICAgIGNoaWxkcmVuOiBub2RlLmNoaWxkcmVuDQogICAgICAgICAgICB9Ow0KICAgICAgICB9LA0KICAgICAgICAvKiog5p+l6K+i57up5pWI6ICD5qC4LeW5sumDqOiHquivhOW+heWuoeaguOWIl+ihqCAqLw0KICAgICAgICBnZXRMaXN0KCkgew0KICAgICAgICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsNCiAgICAgICAgICAgIGxpc3RJbmZvKHRoaXMucXVlcnlQYXJhbXMpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICAgICAgICAgIHRoaXMubGlzdEluZm8gPSByZXNwb25zZS5yb3dzOw0KICAgICAgICAgICAgICAgIHRoaXMudG90YWwgPSByZXNwb25zZS50b3RhbDsNCiAgICAgICAgICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgICAgIH0pOw0KICAgICAgICB9LA0KICAgICAgICAvLyDlj5bmtojmjInpkq4NCiAgICAgICAgY2FuY2VsKCkgew0KICAgICAgICAgICAgdGhpcy5vcGVuID0gZmFsc2U7DQogICAgICAgICAgICB0aGlzLnJlc2V0KCk7DQogICAgICAgIH0sDQogICAgICAgIC8vIOihqOWNlemHjee9rg0KICAgICAgICByZXNldCgpIHsNCiAgICAgICAgICAgIHRoaXMuZm9ybSA9IHsNCiAgICAgICAgICAgICAgICBpZDogbnVsbCwNCiAgICAgICAgICAgICAgICBkZXB0U2NvcmU6IG51bGwsDQogICAgICAgICAgICAgICAgYnVzaW5lc3NTY29yZTogbnVsbCwNCiAgICAgICAgICAgICAgICBsZWFkZXJTY29yZTogbnVsbCwNCiAgICAgICAgICAgIH07DQogICAgICAgICAgICAvLyB0aGlzLnJlc2V0Rm9ybSgiZm9ybSIpOw0KICAgICAgICB9LA0KICAgICAgICAvKiog5pCc57Si5oyJ6ZKu5pON5L2cICovDQogICAgICAgIGhhbmRsZVF1ZXJ5KCkgew0KICAgICAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMTsNCiAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICB9LA0KICAgICAgICAvKiog6YeN572u5oyJ6ZKu5pON5L2cICovDQogICAgICAgIHJlc2V0UXVlcnkoKSB7DQogICAgICAgICAgICB0aGlzLnJlc2V0Rm9ybSgicXVlcnlGb3JtIik7DQogICAgICAgICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCk7DQogICAgICAgIH0sDQogICAgICAgIC8vIOWuoeaJueivpuaDhQ0KICAgICAgICBoYW5kbGVEZXRhaWwocm93KXsNCiAgICAgICAgICAgIGdldEluZm8oe2lkOnJvdy5pZH0pLnRoZW4ocmVzID0+IHsNCiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZyhyZXMpOw0KICAgICAgICAgICAgICAgIGlmKHJlcy5jb2RlID09IDIwMCl7DQogICAgICAgICAgICAgICAgICAgIHRoaXMuY2hlY2tJbmZvID0gcmVzLmRhdGE7DQogICAgICAgICAgICAgICAgICAgIGxldCBsaXN0ID0gSlNPTi5wYXJzZShyZXMuZGF0YS5jb250ZW50KTsNCiAgICAgICAgICAgICAgICAgICAgdGhpcy5oYW5kbGVTcGFuTGlzdChsaXN0KTsNCiAgICAgICAgICAgICAgICAgICAgdGhpcy5jaGVja0luZm8ubGlzdCA9IGxpc3Q7DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIHRoaXMub3BlbiA9IHRydWUNCiAgICAgICAgICAgIH0pDQogICAgICAgIH0sDQoNCiAgICAgICAgLy8g5aSE55CG5YiX6KGoDQogICAgICAgIGhhbmRsZVNwYW5MaXN0KGRhdGEpew0KICAgICAgICAgICAgbGV0IHNwYW5MaXN0ID0gW107DQogICAgICAgICAgICBsZXQgZmxhZyA9IDA7DQogICAgICAgICAgICBmb3IobGV0IGkgPSAwOyBpIDwgZGF0YS5sZW5ndGg7IGkrKyl7DQogICAgICAgICAgICAvLyDnm7jlkIzogIPmoLjpobnlkIjlubYNCiAgICAgICAgICAgIGlmKGkgPT0gMCl7DQogICAgICAgICAgICAgICAgc3Bhbkxpc3QucHVzaCh7DQogICAgICAgICAgICAgICAgcm93c3BhbjogMSwNCiAgICAgICAgICAgICAgICBjb2xzcGFuOiAxDQogICAgICAgICAgICAgICAgfSkNCiAgICAgICAgICAgIH1lbHNlew0KICAgICAgICAgICAgICAgIGlmKGRhdGFbaSAtIDFdLml0ZW0gPT0gZGF0YVtpXS5pdGVtKXsNCiAgICAgICAgICAgICAgICBzcGFuTGlzdC5wdXNoKHsNCiAgICAgICAgICAgICAgICAgICAgcm93c3BhbjogMCwNCiAgICAgICAgICAgICAgICAgICAgY29sc3BhbjogMA0KICAgICAgICAgICAgICAgIH0pDQogICAgICAgICAgICAgICAgc3Bhbkxpc3RbZmxhZ10ucm93c3BhbiArPSAxOw0KICAgICAgICAgICAgICAgIH1lbHNlew0KICAgICAgICAgICAgICAgIHNwYW5MaXN0LnB1c2goew0KICAgICAgICAgICAgICAgICAgICByb3dzcGFuOiAxLA0KICAgICAgICAgICAgICAgICAgICBjb2xzcGFuOiAxDQogICAgICAgICAgICAgICAgfSkNCiAgICAgICAgICAgICAgICBmbGFnID0gaTsNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9DQogICAgICAgICAgICB9DQogICAgICAgICAgICB0aGlzLnNwYW5MaXN0ID0gc3Bhbkxpc3Q7DQogICAgICAgIH0sDQoNCiAgICAgICAgLy8g5ZCI5bm25Y2V5YWD5qC85pa55rOVDQogICAgICAgIG9iamVjdFNwYW5NZXRob2QoeyByb3csIGNvbHVtbiwgcm93SW5kZXgsIGNvbHVtbkluZGV4IH0pIHsNCiAgICAgICAgICAgIC8vIOesrOS4gOWIl+ebuOWQjOmhueWQiOW5tg0KICAgICAgICAgICAgaWYgKGNvbHVtbkluZGV4ID09PSAwKSB7DQogICAgICAgICAgICByZXR1cm4gdGhpcy5zcGFuTGlzdFtyb3dJbmRleF07DQogICAgICAgICAgICB9DQogICAgICAgICAgICAvLyDnsbvliKvml6DlhoXlrrkg5ZCI5bm2DQogICAgICAgICAgICBpZihjb2x1bW5JbmRleCA9PT0gMSl7DQogICAgICAgICAgICBpZighcm93LmNhdGVnb3J5KXsNCiAgICAgICAgICAgICAgICByZXR1cm4gew0KICAgICAgICAgICAgICAgIHJvd3NwYW46IDAsDQogICAgICAgICAgICAgICAgY29sc3BhbjogMA0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0NCiAgICAgICAgICAgIGlmKGNvbHVtbkluZGV4ID09PSAyKXsNCiAgICAgICAgICAgIGlmKCFyb3cuY2F0ZWdvcnkpew0KICAgICAgICAgICAgICAgIHJldHVybiB7DQogICAgICAgICAgICAgICAgcm93c3BhbjogMSwNCiAgICAgICAgICAgICAgICBjb2xzcGFuOiAyDQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfQ0KICAgICAgICB9LA0KDQogICAgICAgIC8vIOWvvOWHuuaMiemSrueCueWHu+S6i+S7tg0KICAgICAgICBoYW5kbGVFeHBvcnQoKXsNCiAgICAgICAgICBleHBvcnRJbmZvKHsuLi50aGlzLnF1ZXJ5UGFyYW1zfSkudGhlbihyZXMgPT4gew0KICAgICAgICAgICAgY29uc29sZS5sb2cocmVzKQ0KICAgICAgICAgICAgdGhpcy5kb3dubG9hZChyZXMubXNnLCLmnIjluqbnu6nmlYjogIPmoLjmsYfmgLvooagueGxzeCIpDQogICAgICAgICAgfSkNCiAgICAgICAgfSwNCg0KICAgICAgICAvLyDor6bnu4blr7zlh7oNCiAgICAgICAgaGFuZGxlRXhwb3J0RGV0YWlsKCl7DQogICAgICAgICAgZXhwb3J0RGV0YWlsKHtpZDp0aGlzLmNoZWNrSW5mby5pZH0pLnRoZW4ocmVzID0+IHsNCiAgICAgICAgICAgIGNvbnNvbGUubG9nKHJlcykNCiAgICAgICAgICAgIHRoaXMuZG93bmxvYWQocmVzLm1zZywi5pyI5bqm57up5pWI6ICD5qC46KGoLnhsc3giKQ0KICAgICAgICAgIH0pDQogICAgICAgIH0sDQoNCiAgICAgICAgaGFuZGxlQmF0Y2hFeHBvcnQoKXsNCiAgICAgICAgICBkb3duTG9hZFppcCgiL3dlYi9zZWxmQXNzZXNzL2luZm8vYmF0Y2hFeHBvcnREZXRhaWw/YXNzZXNzRGF0ZT0iICsgdGhpcy5xdWVyeVBhcmFtcy5hc3Nlc3NEYXRlLCAicnVveWkiKTsNCiAgICAgICAgfSwNCg0KICAgICAgICAvLyDlr7zlh7rmioDmnK/luo/liJfkuJrnu6nmsYfmgLvooagNCiAgICAgICAgaGFuZGxlRXhwb3J0VGVjaG5pY2FsU3VtbWFyeSgpew0KICAgICAgICAgIGlmICghdGhpcy5xdWVyeVBhcmFtcy5hc3Nlc3NEYXRlKSB7DQogICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcigi6K+36YCJ5oup6ICD5qC45bm05pyIIik7DQogICAgICAgICAgICByZXR1cm47DQogICAgICAgICAgfQ0KICAgICAgICAgIGV4cG9ydFRlY2huaWNhbFBlcmZvcm1hbmNlU3VtbWFyeSh7YXNzZXNzRGF0ZTogdGhpcy5xdWVyeVBhcmFtcy5hc3Nlc3NEYXRlfSkudGhlbihyZXMgPT4gew0KICAgICAgICAgICAgY29uc29sZS5sb2cocmVzKQ0KICAgICAgICAgICAgdGhpcy5kb3dubG9hZChyZXMubXNnLCLmioDmnK/luo/liJfkuJrnu6nmsYfmgLvooagueGxzeCIpDQogICAgICAgICAgfSkuY2F0Y2goZXJyb3IgPT4gew0KICAgICAgICAgICAgY29uc29sZS5lcnJvcigi5a+85Ye65aSx6LSl77yaIiwgZXJyb3IpOw0KICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoIuWvvOWHuuWksei0pe+8jOivt+eojeWQjumHjeivlSIpOw0KICAgICAgICAgIH0pDQogICAgICAgIH0sDQoNCiAgICAgICAgLy8g5a+85Ye66KGM5pS/5bqP5YiX5Lia57up5rGH5oC76KGoDQogICAgICAgIGhhbmRsZUV4cG9ydEFkbWluaXN0cmF0aXZlU3VtbWFyeSgpew0KICAgICAgICAgIGlmICghdGhpcy5xdWVyeVBhcmFtcy5hc3Nlc3NEYXRlKSB7DQogICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcigi6K+36YCJ5oup6ICD5qC45bm05pyIIik7DQogICAgICAgICAgICByZXR1cm47DQogICAgICAgICAgfQ0KICAgICAgICAgIGV4cG9ydEFkbWluaXN0cmF0aXZlUGVyZm9ybWFuY2VTdW1tYXJ5KHthc3Nlc3NEYXRlOiB0aGlzLnF1ZXJ5UGFyYW1zLmFzc2Vzc0RhdGV9KS50aGVuKHJlcyA9PiB7DQogICAgICAgICAgICBjb25zb2xlLmxvZyhyZXMpDQogICAgICAgICAgICB0aGlzLmRvd25sb2FkKHJlcy5tc2csIuihjOaUv+W6j+WIl+S4mue7qeaxh+aAu+ihqC54bHN4IikNCiAgICAgICAgICB9KS5jYXRjaChlcnJvciA9PiB7DQogICAgICAgICAgICBjb25zb2xlLmVycm9yKCLlr7zlh7rlpLHotKXvvJoiLCBlcnJvcik7DQogICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcigi5a+85Ye65aSx6LSl77yM6K+356iN5ZCO6YeN6K+VIik7DQogICAgICAgICAgfSkNCiAgICAgICAgfSwNCg0KICAgICAgICAvLyDlr7zlhaXnm7jlhbMNCiAgICAgICAgaGFuZGxlRmlsZVVwbG9hZFByb2dyZXNzKCl7DQogICAgICAgIHRoaXMudXBsb2FkLmlzVXBsb2FkaW5nID0gdHJ1ZTsNCiAgICAgICAgfSwNCiAgICAgICAgaGFuZGxlRmlsZVN1Y2Nlc3MocmVzcG9uc2Upew0KICAgICAgICAgICAgdGhpcy51cGxvYWQuaXNVcGxvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgICAgIGNvbnNvbGUubG9nKHJlc3BvbnNlKQ0KICAgICAgICAgICAgLy8gdGhpcy5oYW5kbGVRdWVyeSgpOw0KICAgICAgICAgICAgLy8gdGhpcy5pbXBvcnRSZXMgPSByZXNwb25zZS5kYXRhOw0KICAgICAgICAgICAgLy8gdGhpcy5vcGVuSW1wb3J0UmVzID0gdHJ1ZTsNCiAgICAgICAgfSwNCiAgICB9DQp9Ow0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmPA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/assess/self/collect", "sourcesContent": ["<template>\r\n    <div class=\"app-container\">\r\n      <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"80px\">\r\n        <el-row>\r\n          <el-form-item label=\"考核年月\" prop=\"assessDate\">\r\n            <el-date-picker\r\n              v-model=\"queryParams.assessDate\"\r\n              type=\"month\"\r\n              value-format=\"yyyy-M\"\r\n              format=\"yyyy 年 M 月\"\r\n              placeholder=\"选择考核年月\"\r\n              :clearable=\"false\">\r\n            </el-date-picker>\r\n          </el-form-item>\r\n          <el-form-item label=\"部门\" prop=\"deptId\">\r\n            <treeselect style=\"width: 200px;\" v-model=\"queryParams.deptId\" :multiple=\"false\" :options=\"deptOptions\" :normalizer=\"normalizer\" :disable-branch-nodes=\"true\" placeholder=\"请选择部门\" />\r\n          </el-form-item>\r\n          <el-form-item label=\"工号\" prop=\"name\">\r\n            <el-input\r\n              v-model=\"queryParams.workNo\"\r\n              placeholder=\"请输入工号\"\r\n              clearable\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"姓名\" prop=\"name\">\r\n            <el-input\r\n              v-model=\"queryParams.name\"\r\n              placeholder=\"请输入姓名\"\r\n              clearable\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"状态\" prop=\"status\">\r\n            <el-select v-model=\"queryParams.status\" placeholder=\"请选择状态\" clearable style=\"width: 200px;\">\r\n              <el-option label=\"未提交/退回\" value=\"0\"></el-option>\r\n              <el-option label=\"部门领导评分\" value=\"1\"></el-option>\r\n              <el-option label=\"事业部评分\" value=\"2\"></el-option>\r\n              <el-option label=\"运改部/组织部审核\" value=\"3\"></el-option>\r\n              <el-option label=\"总经理部评分\" value=\"4\"></el-option>\r\n              <el-option label=\"已完成\" value=\"5\"></el-option>\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-row>\r\n      </el-form>\r\n\r\n      <el-row :gutter=\"10\" class=\"mb8\">\r\n        <el-col :span=\"1.5\">\r\n          <el-button\r\n            type=\"primary\"\r\n            plain\r\n            icon=\"el-icon-download\"\r\n            size=\"small\"\r\n            @click=\"handleExport\"\r\n          >导出列表</el-button>\r\n        </el-col>\r\n        <el-col :span=\"1.5\">\r\n          <el-button\r\n            type=\"warning\"\r\n            plain\r\n            icon=\"el-icon-download\"\r\n            size=\"small\"\r\n            @click=\"handleBatchExport\"\r\n          >批量导出(zip)</el-button>\r\n        </el-col>\r\n        <el-col :span=\"1.5\">\r\n          <el-button\r\n            type=\"info\"\r\n            plain\r\n            icon=\"el-icon-download\"\r\n            size=\"small\"\r\n            @click=\"handleExportTechnicalSummary\"\r\n          >导出技术序列业绩汇总表</el-button>\r\n        </el-col>\r\n        <el-col :span=\"1.5\">\r\n          <el-button\r\n            type=\"success\"\r\n            plain\r\n            icon=\"el-icon-download\"\r\n            size=\"small\"\r\n            @click=\"handleExportAdministrativeSummary\"\r\n          >导出行政序列业绩汇总表</el-button>\r\n        </el-col>\r\n        <el-col :span=\"1.5\">\r\n          <el-upload\r\n          accept=\".xlsx, .xls\"\r\n          :headers=\"upload.headers\"\r\n          :disabled=\"upload.isUploading\"\r\n          :action=\"upload.url\"\r\n          :show-file-list=\"false\"\r\n          :multiple=\"false\"\r\n          :on-progress=\"handleFileUploadProgress\"\r\n          :on-success=\"handleFileSuccess\">\r\n              <el-button size=\"small\" type=\"success\" plain icon=\"el-icon-download\">导入最终分数</el-button>\r\n          </el-upload>\r\n          <!-- <el-button\r\n            type=\"success\"\r\n            plain\r\n            icon=\"el-icon-upload2\"\r\n            size=\"small\"\r\n            @click=\"handleImportFinalScore\"\r\n          >导入最终分数</el-button> -->\r\n        </el-col>\r\n        <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n      </el-row>\r\n\r\n      <el-table v-loading=\"loading\" :data=\"listInfo\">\r\n        <el-table-column label=\"工号\" align=\"center\" prop=\"workNo\" width=\"120\"/>\r\n        <el-table-column label=\"姓名\" align=\"center\" prop=\"name\" width=\"120\"/>\r\n        <el-table-column label=\"部门\" align=\"center\" prop=\"deptName\" ></el-table-column>\r\n        <el-table-column label=\"自评分\" align=\"center\" prop=\"selfScore\" ></el-table-column>\r\n        <el-table-column label=\"部门领导评分\" align=\"center\" prop=\"selfScore\" >\r\n          <template slot-scope=\"scope\">\r\n            {{ scope.row.deptScore ? scope.row.deptScore : \"\"}}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"事业部评分\" align=\"center\" prop=\"selfScore\" >\r\n          <template slot-scope=\"scope\">\r\n            {{ scope.row.businessScore ? scope.row.businessScore : \"\"}}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"运改组织部审核\" align=\"center\" prop=\"selfScore\" >\r\n          <template slot-scope=\"scope\">\r\n            {{ scope.row.organizationScore ? scope.row.organizationScore : \"\"}}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"总经理部评分\" align=\"center\" prop=\"selfScore\" >\r\n          <template slot-scope=\"scope\">\r\n            {{ scope.row.leaderScore ? scope.row.leaderScore : \"\"}}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"最终分数\" align=\"center\" prop=\"finalScore\" >\r\n          <template slot-scope=\"scope\">\r\n            {{ scope.row.finalScore ? scope.row.finalScore : \"\"}}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"状态\" align=\"center\" prop=\"status\" >\r\n          <template slot-scope=\"scope\">\r\n            <el-tag v-if=\"scope.row.status == '0' && scope.row.rejectReason\" type=\"danger\">退 回</el-tag>\r\n            <el-tag v-if=\"scope.row.status == '0' && !scope.row.rejectReason\" type=\"info\">未提交</el-tag>\r\n            <el-tag v-if=\"scope.row.status == '1'\" type=\"warning\">部门领导评分</el-tag>\r\n            <el-tag v-if=\"scope.row.status == '2'\" type=\"warning\">事业部评分</el-tag>\r\n            <el-tag v-if=\"scope.row.status == '3'\" type=\"warning\">运改部/组织部审核</el-tag>\r\n            <el-tag v-if=\"scope.row.status == '4'\" type=\"warning\">总经理部评分</el-tag>\r\n            <el-tag v-if=\"scope.row.status == '5'\" type=\"success\">已完成</el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"150\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              icon=\"el-icon-edit\"\r\n              @click=\"handleDetail(scope.row)\"\r\n            >详情</el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <pagination\r\n        v-show=\"total>0\"\r\n        :total=\"total\"\r\n        :page.sync=\"queryParams.pageNum\"\r\n        :limit.sync=\"queryParams.pageSize\"\r\n        @pagination=\"getList\"\r\n      />\r\n\r\n      <el-dialog\r\n        :visible.sync=\"open\"\r\n        fullscreen>\r\n        <h3 style=\"text-align: center;\">月度业绩考核表</h3>\r\n          <el-descriptions class=\"margin-top\" :column=\"3\">\r\n            <el-descriptions-item>\r\n              <template slot=\"label\">\r\n                姓名\r\n              </template>\r\n              {{ checkInfo.name }}\r\n            </el-descriptions-item>\r\n            <el-descriptions-item>\r\n              <template slot=\"label\">\r\n                部门\r\n              </template>\r\n              {{ checkInfo.deptName }}\r\n            </el-descriptions-item>\r\n            <el-descriptions-item>\r\n              <template slot=\"label\">\r\n                考核年月\r\n              </template>\r\n              {{ checkInfo.assessDate }}\r\n            </el-descriptions-item>\r\n          </el-descriptions>\r\n\r\n          <el-row :gutter=\"10\" class=\"mb8\">\r\n            <el-col :span=\"1.5\">\r\n              <el-button\r\n                type=\"primary\"\r\n                plain\r\n                icon=\"el-icon-plus\"\r\n                size=\"small\"\r\n                @click=\"handleExportDetail\"\r\n              >导出</el-button>\r\n            </el-col>\r\n          </el-row>\r\n          <el-table v-loading=\"loading\" :data=\"checkInfo.list\"\r\n            :span-method=\"objectSpanMethod\" border>\r\n            <el-table-column label=\"类型\" align=\"center\" prop=\"item\" width=\"120\"/>\r\n            <el-table-column label=\"指标\" align=\"center\" prop=\"category\" width=\"150\"/>\r\n            <el-table-column label=\"目标\" align=\"center\" prop=\"target\" width=\"180\" />\r\n            <el-table-column label=\"评分标准\" align=\"center\" prop=\"standard\" />\r\n            <el-table-column label=\"完成实绩（若扣分，写明原因）\" align=\"center\" prop=\"performance\" />\r\n            <el-table-column label=\"加减分\" align=\"center\" prop=\"dePoints\" width=\"150\" />\r\n            <el-table-column label=\"加减分原因\" align=\"center\" prop=\"pointsReason\" width=\"180\" />\r\n          </el-table>\r\n          <el-form size=\"small\" :inline=\"false\" label-width=\"200px\" style=\"margin-top: 10px;\">\r\n            <el-form-item label=\"自评分数 / 签名：\">\r\n              <span >{{ checkInfo.selfScore + \" 分 / \" + checkInfo.name }}</span>\r\n            </el-form-item>\r\n            <el-form-item v-if=\"checkInfo.deptScore && checkInfo.deptUserName\" label=\"部门领导评分 / 签名：\">\r\n              <span >{{ checkInfo.deptScore + \" 分 / \" + checkInfo.deptUserName }}</span>\r\n            </el-form-item>\r\n            <el-form-item v-if=\"checkInfo.businessUserName && checkInfo.businessScore\" label=\"事业部领导评分 / 签名：\">\r\n              <span>{{ checkInfo.businessScore + \" 分 / \" + checkInfo.businessUserName }}</span>\r\n            </el-form-item>\r\n            <el-form-item v-if=\"checkInfo.organizationScore && checkInfo.organizationUserName\" label=\"运改组织部审核：\">\r\n              <span >{{ checkInfo.organizationScore + \" 分 / \" + checkInfo.organizationUserName }}</span>\r\n            </el-form-item>\r\n            <el-form-item v-if=\"checkInfo.leaderScore && checkInfo.leaderName\" label=\"总经理部领导评分 / 签名：\">\r\n              <span >{{ checkInfo.leaderScore + \" 分 / \" + checkInfo.leaderName }}</span>\r\n            </el-form-item>\r\n          </el-form>\r\n          <div style=\"text-align: center;\">\r\n            <el-button plain type=\"info\" @click=\"cancel\">返 回</el-button>\r\n          </div>\r\n      </el-dialog>\r\n\r\n    </div>\r\n  </template>\r\n\r\n<script>\r\nimport { listInfo, getInfo, exportInfo, exportDetail, exportTechnicalPerformanceSummary, exportAdministrativePerformanceSummary } from \"@/api/assess/self/info\";\r\nimport { getToken } from \"@/utils/auth\";\r\nimport { downLoadZip } from \"@/utils/zipdownload\";\r\nimport { listDept } from \"@/api/assess/lateral/dept\";\r\nimport Treeselect from \"@riophae/vue-treeselect\";\r\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.css\";\r\n\r\nexport default {\r\n    name: \"SelfAssessCollect\",\r\n    components: {\r\n        Treeselect\r\n    },\r\n    data() {\r\n      return {\r\n        // 遮罩层\r\n        loading: true,\r\n        // 显示搜索条件\r\n        showSearch: true,\r\n        // 总条数\r\n        total: 0,\r\n        // 绩效考核-干部自评人员配置表格数据\r\n        listInfo: [],\r\n        // 弹出层标题\r\n        title: \"\",\r\n        // 是否显示弹出层\r\n        open: false,\r\n        // 查询参数\r\n        queryParams: {\r\n          pageNum: 1,\r\n          pageSize: 10,\r\n          workNo: null,\r\n          name:null,\r\n          deptId:null,\r\n          assessDate:null,\r\n          status:null\r\n        },\r\n        // 表单参数\r\n        form: {\r\n          id:null,\r\n          // 部门领导评分\r\n          deptScore:null,\r\n          // 事业部评分\r\n          businessScore:null,\r\n          // 条线领导评分\r\n          leaderScore:null,\r\n        },\r\n        // 表单校验\r\n        rules: {\r\n        },\r\n        deptOptions:[],\r\n        openCheck:false,\r\n        checkInfo:{\r\n          name:null,\r\n          assessDate:null,\r\n          deptName:null,\r\n          list:[]\r\n        },\r\n        // 合并单元格\r\n        spanList:[],\r\n        // 导入参数\r\n        upload: {\r\n          // 是否禁用上传\r\n          isUploading: false,\r\n          // 设置上传的请求头部\r\n          headers: { Authorization: 'Bearer ' + getToken() },\r\n          // 上传的地址\r\n          url: process.env.VUE_APP_BASE_API + \"/web/selfAssess/info/importFinalScore\",\r\n        },\r\n      };\r\n    },\r\n    created() {\r\n        this.queryParams.assessDate = this.getDefaultAssessDate()\r\n        // this.getSelfAssessUser();\r\n        // this.getCheckDeptList();\r\n        this.getTreeselect();\r\n        this.getList();\r\n    },\r\n    methods: {\r\n\r\n        // 获取默认考核日期\r\n        getDefaultAssessDate() {\r\n            const now = new Date();\r\n            const currentDay = now.getDate();\r\n\r\n            let targetDate;\r\n            if (currentDay < 10) {\r\n                // 当前日期小于10日，默认为上个月\r\n                targetDate = new Date(now.getFullYear(), now.getMonth() - 1, 1);\r\n            } else {\r\n                // 当前日期大于等于10日，默认为当月\r\n                targetDate = new Date(now.getFullYear(), now.getMonth(), 1);\r\n            }\r\n\r\n            // 格式化为 YYYY-M 格式\r\n            const year = targetDate.getFullYear();\r\n            const month = targetDate.getMonth() + 1;\r\n            return `${year}-${month}`;\r\n        },\r\n\r\n        // 获取部门信息\r\n        /** 查询横向评价部门下拉树结构 */\r\n        getTreeselect() {\r\n            listDept().then(response => {\r\n                this.deptOptions = this.handleTree(response.data, \"deptId\", \"parentId\");\r\n            });\r\n        },\r\n        /** 转换横向评价部门数据结构 */\r\n        normalizer(node) {\r\n            if (node.children && !node.children.length) {\r\n                delete node.children;\r\n            }\r\n            return {\r\n                id: node.deptId,\r\n                label: node.deptName,\r\n                children: node.children\r\n            };\r\n        },\r\n        /** 查询绩效考核-干部自评待审核列表 */\r\n        getList() {\r\n            this.loading = true;\r\n            listInfo(this.queryParams).then(response => {\r\n                this.listInfo = response.rows;\r\n                this.total = response.total;\r\n                this.loading = false;\r\n            });\r\n        },\r\n        // 取消按钮\r\n        cancel() {\r\n            this.open = false;\r\n            this.reset();\r\n        },\r\n        // 表单重置\r\n        reset() {\r\n            this.form = {\r\n                id: null,\r\n                deptScore: null,\r\n                businessScore: null,\r\n                leaderScore: null,\r\n            };\r\n            // this.resetForm(\"form\");\r\n        },\r\n        /** 搜索按钮操作 */\r\n        handleQuery() {\r\n            this.queryParams.pageNum = 1;\r\n            this.getList();\r\n        },\r\n        /** 重置按钮操作 */\r\n        resetQuery() {\r\n            this.resetForm(\"queryForm\");\r\n            this.handleQuery();\r\n        },\r\n        // 审批详情\r\n        handleDetail(row){\r\n            getInfo({id:row.id}).then(res => {\r\n                console.log(res);\r\n                if(res.code == 200){\r\n                    this.checkInfo = res.data;\r\n                    let list = JSON.parse(res.data.content);\r\n                    this.handleSpanList(list);\r\n                    this.checkInfo.list = list;\r\n                }\r\n                this.open = true\r\n            })\r\n        },\r\n\r\n        // 处理列表\r\n        handleSpanList(data){\r\n            let spanList = [];\r\n            let flag = 0;\r\n            for(let i = 0; i < data.length; i++){\r\n            // 相同考核项合并\r\n            if(i == 0){\r\n                spanList.push({\r\n                rowspan: 1,\r\n                colspan: 1\r\n                })\r\n            }else{\r\n                if(data[i - 1].item == data[i].item){\r\n                spanList.push({\r\n                    rowspan: 0,\r\n                    colspan: 0\r\n                })\r\n                spanList[flag].rowspan += 1;\r\n                }else{\r\n                spanList.push({\r\n                    rowspan: 1,\r\n                    colspan: 1\r\n                })\r\n                flag = i;\r\n                }\r\n            }\r\n            }\r\n            this.spanList = spanList;\r\n        },\r\n\r\n        // 合并单元格方法\r\n        objectSpanMethod({ row, column, rowIndex, columnIndex }) {\r\n            // 第一列相同项合并\r\n            if (columnIndex === 0) {\r\n            return this.spanList[rowIndex];\r\n            }\r\n            // 类别无内容 合并\r\n            if(columnIndex === 1){\r\n            if(!row.category){\r\n                return {\r\n                rowspan: 0,\r\n                colspan: 0\r\n                }\r\n            }\r\n            }\r\n            if(columnIndex === 2){\r\n            if(!row.category){\r\n                return {\r\n                rowspan: 1,\r\n                colspan: 2\r\n                }\r\n            }\r\n            }\r\n        },\r\n\r\n        // 导出按钮点击事件\r\n        handleExport(){\r\n          exportInfo({...this.queryParams}).then(res => {\r\n            console.log(res)\r\n            this.download(res.msg,\"月度绩效考核汇总表.xlsx\")\r\n          })\r\n        },\r\n\r\n        // 详细导出\r\n        handleExportDetail(){\r\n          exportDetail({id:this.checkInfo.id}).then(res => {\r\n            console.log(res)\r\n            this.download(res.msg,\"月度绩效考核表.xlsx\")\r\n          })\r\n        },\r\n\r\n        handleBatchExport(){\r\n          downLoadZip(\"/web/selfAssess/info/batchExportDetail?assessDate=\" + this.queryParams.assessDate, \"ruoyi\");\r\n        },\r\n\r\n        // 导出技术序列业绩汇总表\r\n        handleExportTechnicalSummary(){\r\n          if (!this.queryParams.assessDate) {\r\n            this.$modal.msgError(\"请选择考核年月\");\r\n            return;\r\n          }\r\n          exportTechnicalPerformanceSummary({assessDate: this.queryParams.assessDate}).then(res => {\r\n            console.log(res)\r\n            this.download(res.msg,\"技术序列业绩汇总表.xlsx\")\r\n          }).catch(error => {\r\n            console.error(\"导出失败：\", error);\r\n            this.$modal.msgError(\"导出失败，请稍后重试\");\r\n          })\r\n        },\r\n\r\n        // 导出行政序列业绩汇总表\r\n        handleExportAdministrativeSummary(){\r\n          if (!this.queryParams.assessDate) {\r\n            this.$modal.msgError(\"请选择考核年月\");\r\n            return;\r\n          }\r\n          exportAdministrativePerformanceSummary({assessDate: this.queryParams.assessDate}).then(res => {\r\n            console.log(res)\r\n            this.download(res.msg,\"行政序列业绩汇总表.xlsx\")\r\n          }).catch(error => {\r\n            console.error(\"导出失败：\", error);\r\n            this.$modal.msgError(\"导出失败，请稍后重试\");\r\n          })\r\n        },\r\n\r\n        // 导入相关\r\n        handleFileUploadProgress(){\r\n        this.upload.isUploading = true;\r\n        },\r\n        handleFileSuccess(response){\r\n            this.upload.isUploading = false;\r\n            console.log(response)\r\n            // this.handleQuery();\r\n            // this.importRes = response.data;\r\n            // this.openImportRes = true;\r\n        },\r\n    }\r\n};\r\n</script>\r\n"]}]}