package com.ruoyi.web.controller.supply;

import com.ruoyi.app.supply.domain.SupplyInfo;
import com.ruoyi.app.supply.service.ISupplyInfoService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 供应商信息Controller
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@RestController
@RequestMapping("/web/supply/info")
public class SupplyInfoController extends BaseController {
    
    @Autowired
    private ISupplyInfoService supplyInfoService;

    /**
     * 查询供应商信息列表
     */
    @GetMapping("/list")
    public TableDataInfo list(SupplyInfo supplyInfo) {
        startPage();
        List<SupplyInfo> list = supplyInfoService.selectSupplyInfoList(supplyInfo);
        return getDataTable(list);
    }

    /**
     * 根据供应商代码查询供应商信息
     */
    @GetMapping(value = "/getByCode/{supplyCode}")
    public AjaxResult getByCode(@PathVariable("supplyCode") Integer supplyCode) {
        return AjaxResult.success(supplyInfoService.selectSupplyInfoByCode(supplyCode));
    }

    /**
     * 查询供应商信息详细
     */
    @GetMapping(value = "/{supplyCode}")
    public AjaxResult getInfo(@PathVariable("supplyCode") Integer supplyCode) {
        return AjaxResult.success(supplyInfoService.selectSupplyInfoByCode(supplyCode));
    }

    /**
     * 新增供应商信息
     */
    @Log(title = "供应商信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SupplyInfo supplyInfo) {
        return toAjax(supplyInfoService.insertSupplyInfo(supplyInfo));
    }

    /**
     * 修改供应商信息
     */
    @Log(title = "供应商信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SupplyInfo supplyInfo) {
        return toAjax(supplyInfoService.updateSupplyInfo(supplyInfo));
    }

    /**
     * 删除供应商信息
     */
    @Log(title = "供应商信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{supplyCodes}")
    public AjaxResult remove(@PathVariable Integer[] supplyCodes) {
        return toAjax(supplyInfoService.deleteSupplyInfoByCodes(supplyCodes));
    }

    /**
     * 导出供应商信息列表
     */
    @Log(title = "供应商信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public AjaxResult export(SupplyInfo supplyInfo) {
        List<SupplyInfo> list = supplyInfoService.selectSupplyInfoList(supplyInfo);
        ExcelUtil<SupplyInfo> util = new ExcelUtil<SupplyInfo>(SupplyInfo.class);
        return util.exportExcel(list, "供应商信息数据");
    }
}
