package com.ruoyi.app.leave.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 出门证部门（厂内单位）对象 leave_department
 * 
 * <AUTHOR>
 * @date 2025-03-13
 */
public class LeaveDepartment extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 状态 */
    @Excel(name = "状态")
    private Long validFlag;

    /** 库房编码 */
    @Excel(name = "库房编码")
    private String storeCode;

    /** 库房名称 */
    @Excel(name = "库房名称")
    private String storeName;

    /** 拼音头缩写 */
    @Excel(name = "拼音头缩写")
    private String queryWord;

    /** 原料库0，港口库为1，车站库2；3-投料库，4-完工库，5-销售库 */
    @Excel(name = "原料库0，港口库为1，车站库2；3-投料库，4-完工库，5-销售库")
    private Long type;

    /** 单位名称 */
    @Excel(name = "单位名称")
    private String unitName;

    /** 库房位置 */
    @Excel(name = "库房位置")
    private String position;

    /** 最小限制 */
    @Excel(name = "最小限制")
    private Long lowerLimit;

    /** 最大限制 */
    @Excel(name = "最大限制")
    private Long upLimit;

    /** 备注信息 */
    @Excel(name = "备注信息")
    private String memo;

    /** 上级单位编码 */
    @Excel(name = "上级单位编码")
    private String fStoreCode;

    /** 上级单位名称 */
    @Excel(name = "上级单位名称")
    private String fStoreName;

    /** 单位层级 */
    @Excel(name = "单位层级")
    private Long levelName;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setValidFlag(Long validFlag) 
    {
        this.validFlag = validFlag;
    }

    public Long getValidFlag() 
    {
        return validFlag;
    }
    public void setStoreCode(String storeCode) 
    {
        this.storeCode = storeCode;
    }

    public String getStoreCode() 
    {
        return storeCode;
    }
    public void setStoreName(String storeName) 
    {
        this.storeName = storeName;
    }

    public String getStoreName() 
    {
        return storeName;
    }
    public void setQueryWord(String queryWord) 
    {
        this.queryWord = queryWord;
    }

    public String getQueryWord() 
    {
        return queryWord;
    }
    public void setType(Long type) 
    {
        this.type = type;
    }

    public Long getType() 
    {
        return type;
    }
    public void setUnitName(String unitName) 
    {
        this.unitName = unitName;
    }

    public String getUnitName() 
    {
        return unitName;
    }
    public void setPosition(String position) 
    {
        this.position = position;
    }

    public String getPosition() 
    {
        return position;
    }
    public void setLowerLimit(Long lowerLimit) 
    {
        this.lowerLimit = lowerLimit;
    }

    public Long getLowerLimit() 
    {
        return lowerLimit;
    }
    public void setUpLimit(Long upLimit) 
    {
        this.upLimit = upLimit;
    }

    public Long getUpLimit() 
    {
        return upLimit;
    }
    public void setMemo(String memo) 
    {
        this.memo = memo;
    }

    public String getMemo() 
    {
        return memo;
    }
    public void setfStoreCode(String fStoreCode) 
    {
        this.fStoreCode = fStoreCode;
    }

    public String getfStoreCode() 
    {
        return fStoreCode;
    }
    public void setfStoreName(String fStoreName) 
    {
        this.fStoreName = fStoreName;
    }

    public String getfStoreName() 
    {
        return fStoreName;
    }
    public void setLevelName(Long levelName) 
    {
        this.levelName = levelName;
    }

    public Long getLevelName() 
    {
        return levelName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("validFlag", getValidFlag())
            .append("storeCode", getStoreCode())
            .append("storeName", getStoreName())
            .append("queryWord", getQueryWord())
            .append("type", getType())
            .append("unitName", getUnitName())
            .append("position", getPosition())
            .append("lowerLimit", getLowerLimit())
            .append("upLimit", getUpLimit())
            .append("memo", getMemo())
            .append("fStoreCode", getfStoreCode())
            .append("fStoreName", getfStoreName())
            .append("levelName", getLevelName())
            .append("createTime", getCreateTime())
            .append("createBy", getCreateBy())
            .append("updateTime", getUpdateTime())
            .append("updateBy", getUpdateBy())
            .toString();
    }
}
