package com.ruoyi.app.leave.controller;

import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.app.leave.domain.LeavePlanMaterial;
import com.ruoyi.app.leave.service.ILeavePlanMaterialService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 出门证计划申请物资Controller
 * 
 * <AUTHOR>
 * @date 2025-03-13
 */
@RestController
@RequestMapping("/leave/planMaterial")
public class LeavePlanMaterialController extends BaseController
{
    @Autowired
    private ILeavePlanMaterialService leavePlanMaterialService;

    /**
     * 查询出门证计划申请物资列表
     */
    @PreAuthorize("@ss.hasPermi('leave:material:list')")
    @GetMapping("/list")
    public TableDataInfo list(LeavePlanMaterial leavePlanMaterial)
    {
        startPage();
        List<LeavePlanMaterial> list = leavePlanMaterialService.selectLeavePlanMaterialList(leavePlanMaterial);
        return getDataTable(list);
    }

    /**
     * 导出出门证计划申请物资列表
     */
    @PreAuthorize("@ss.hasPermi('leave:material:export')")
    @Log(title = "出门证计划申请物资", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(LeavePlanMaterial leavePlanMaterial)
    {
        List<LeavePlanMaterial> list = leavePlanMaterialService.selectLeavePlanMaterialList(leavePlanMaterial);
        ExcelUtil<LeavePlanMaterial> util = new ExcelUtil<LeavePlanMaterial>(LeavePlanMaterial.class);
        return util.exportExcel(list, "material");
    }

    /**
     * 获取出门证计划申请物资详细信息
     */
    @PreAuthorize("@ss.hasPermi('leave:material:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(leavePlanMaterialService.selectLeavePlanMaterialById(id));
    }

    /**
     * 新增出门证计划申请物资
     */
    @PreAuthorize("@ss.hasPermi('leave:material:add')")
    @Log(title = "出门证计划申请物资", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody LeavePlanMaterial leavePlanMaterial)
    {
        return toAjax(leavePlanMaterialService.insertLeavePlanMaterial(leavePlanMaterial));
    }

    /**
     * 修改出门证计划申请物资
     */
    @PreAuthorize("@ss.hasPermi('leave:material:edit')")
    @Log(title = "出门证计划申请物资", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody LeavePlanMaterial leavePlanMaterial)
    {
        return toAjax(leavePlanMaterialService.updateLeavePlanMaterial(leavePlanMaterial));
    }

    /**
     * 删除出门证计划申请物资
     */
    @PreAuthorize("@ss.hasPermi('leave:material:remove')")
    @Log(title = "出门证计划申请物资", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(leavePlanMaterialService.deleteLeavePlanMaterialByIds(ids));
    }
}
