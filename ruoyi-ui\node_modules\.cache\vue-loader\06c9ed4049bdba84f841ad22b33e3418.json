{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\dataReport\\form\\admin.vue?vue&type=style&index=0&id=5f88274b&lang=less", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\dataReport\\form\\admin.vue", "mtime": 1756099891060}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\@vue\\cli-service\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KLnYtbW9kYWwgew0KICBkaXNwbGF5OiBub25lOw0KfQ0K"}, {"version": 3, "sources": ["admin.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAusCA;AACA;AACA", "file": "admin.vue", "sourceRoot": "src/views/dataReport/form", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form\r\n      :model=\"queryParams\"\r\n      ref=\"queryForm\"\r\n      :inline=\"true\"\r\n      label-width=\"68px\"\r\n    >\r\n      <el-form-item label=\"扎口部门\" prop=\"deptCode\">\r\n        <el-cascader\r\n          ref=\"cascaderHandle\"\r\n          :options=\"deptList\"\r\n          clearable\r\n          filterable\r\n          v-model=\"queryParams.deptCode\"\r\n          :props=\"{ expandTrigger: 'hover', emitPath: false,checkStrictly: true }\"\r\n          :show-all-levels=\"false\"\r\n          @change=\"handleQueryDept\"\r\n        >\r\n        <span\r\n              slot-scope=\"{ node, data }\"\r\n              style=\"margin-left: -10px; padding-left: 10px; display: block\"\r\n              @click=\"clickNode($event, node)\"\r\n              >{{ data.label }}</span\r\n            >\r\n        </el-cascader>\r\n      </el-form-item>\r\n      <el-form-item label=\"报表名称\" prop=\"dimensionalityName\">\r\n        <el-input\r\n          v-model=\"queryParams.dimensionalityName\"\r\n          placeholder=\"请输入名称\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"填报时间\">\r\n                <el-date-picker\r\n                  v-model=\"queryParams.fcDate\"\r\n                  value-format=\"yyyy-MM-dd\"\r\n                  type=\"date\"\r\n                  @change=\"handleDateChange\"\r\n                  placeholder=\"选择日期\">\r\n                </el-date-picker>\r\n      </el-form-item>\r\n      <!-- <el-form-item label=\"是否在用启用\" prop=\"isUse\">\r\n        <el-select v-model=\"queryParams.isUse\" placeholder=\"请选择\">\r\n          <el-option label=\"启用\" value=\"1\"></el-option>\r\n          <el-option label=\"停用\" value=\"0\"></el-option>\r\n        </el-select>\r\n      </el-form-item> -->\r\n      <el-form-item>\r\n        <el-button\r\n          type=\"cyan\"\r\n          icon=\"el-icon-search\"\r\n          size=\"mini\"\r\n          @click=\"handleQuery\"\r\n          >搜索</el-button\r\n        >\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\"\r\n          >重置</el-button\r\n        >\r\n      </el-form-item>\r\n    </el-form>\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          icon=\"el-icon-plus\"\r\n          size=\"mini\"\r\n          @click=\"handleAdd\"\r\n          >新建报表</el-button\r\n        >\r\n      </el-col>\r\n    </el-row>\r\n    <el-table v-loading=\"loading\" :data=\"rootList\" border>\r\n      <el-table-column label=\"扎口部门\" align=\"center\" prop=\"deptName\" width=\"240\"/>\r\n      <!-- <el-table-column\r\n        label=\"扎口部门及人员\"\r\n        align=\"center\"\r\n        class-name=\"small-padding fixed-width\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              @click=\"handleaAdminList(scope.row)\"\r\n              >{{scope.row.deptName}}</el-button\r\n            >\r\n        </template>\r\n      </el-table-column> -->\r\n      <!-- <el-table-column label=\"报表名称\" align=\"center\" prop=\"dimensionalityName\"/> -->\r\n      <el-table-column\r\n        label=\"报表名称\"\r\n        align=\"center\"\r\n        class-name=\"small-padding fixed-width\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              @click=\"handleAnswer(scope.row)\"\r\n              >{{scope.row.dimensionalityName}}</el-button\r\n            >\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"当期完成率\" align=\"center\" prop=\"countRate\" width=\"160\"/>\r\n      <el-table-column label=\"当期应填数量\" align=\"center\" prop=\"shouldCount\" width=\"160\" />\r\n      <!-- <el-table-column\r\n        label=\"当期应填数量\"\r\n        align=\"center\"\r\n        class-name=\"small-padding fixed-width\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              @click=\"handlefill(scope.row)\"\r\n              >{{scope.row.shouldCount}}</el-button\r\n            >\r\n        </template>\r\n      </el-table-column> -->\r\n\r\n      <el-table-column\r\n        label=\"当期未填数量\"\r\n        align=\"center\"\r\n        class-name=\"small-padding fixed-width\"\r\n        width=\"160\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              @click=\"handlefill(scope.row)\"\r\n              >{{scope.row.notCount}}</el-button\r\n            >\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <!-- <el-table-column label=\"当期应填数量\" align=\"center\" prop=\"\"  @cell-click=\"handleDetail(scope.row)\"/>\r\n      <el-table-column label=\"当期未填数量\" align=\"center\" prop=\"hasCount\"/> -->\r\n      \r\n      <!-- <el-table-column label=\"是否在用\" align=\"center\" prop=\"isUse\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag\r\n            style=\"margin-left: 10px\"\r\n            :type=\"scope.row.isUse == '1'? 'success' : 'danger'\"\r\n            >{{ scope.row.isUse == \"1\" ? \"启用\" : \"停用\" }}</el-tag\r\n          >\r\n        </template>\r\n      </el-table-column> -->\r\n      <el-table-column\r\n        label=\"操作\"\r\n        align=\"center\"\r\n        class-name=\"small-padding fixed-width\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            v-if=\"scope.row.ruleType != '5' \"\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleDetail(scope.row)\"\r\n            >维度管理</el-button\r\n          >\r\n          <el-button\r\n            v-if=\"scope.row.ruleType != '5' && scope.row.ruleType != '0'\"\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleDeadLine(scope.row)\"\r\n            >截止日期</el-button\r\n          >\r\n          <el-button\r\n            v-if=\"\r\n                  scope.row.ruleType == '1' ||\r\n                  scope.row.ruleType == '3' ||\r\n                  scope.row.ruleType == '4'\r\n                 \"\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"toUpdateUsers(scope.row)\"\r\n            >分配权限</el-button\r\n          >\r\n          <el-button\r\n              v-if=\"\r\n                  scope.row.ruleType == '0' ||\r\n                  scope.row.ruleType == '2' ||\r\n                  scope.row.ruleType == '4' ||\r\n                  scope.row.ruleType == '5'\r\n                 \"\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleExport(scope.row)\"\r\n            >导出数据</el-button>\r\n            <el-button\r\n              v-if=\"\r\n                  (scope.row.ruleType == '0' ||\r\n                  scope.row.ruleType == '2' ||\r\n                  scope.row.ruleType == '4' ||\r\n                  scope.row.ruleType == '5') &&\r\n                  aloneList(scope.row.dimensionalityName)\r\n                 \"\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleSpecial(scope.row)\"\r\n            >单周期数据导出</el-button>\r\n            <el-button\r\n              v-if=\"\r\n                  (scope.row.ruleType == '0' ||\r\n                  scope.row.ruleType == '2' ||\r\n                  scope.row.ruleType == '4' ||\r\n                  scope.row.ruleType == '5') &&\r\n                  containsSubstring('工装',scope.row.dimensionalityName)\r\n                 \"\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleMouth(scope.row)\"\r\n            >规整化数据导出</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination\r\n      v-show=\"total > 0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      :pageSizes=\"pageSizes\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <el-drawer\r\n      title=\"详情\"\r\n      :visible.sync=\"drawer\"\r\n      direction=\"rtl\"\r\n      size=\"80%\"\r\n      :before-close=\"handleClose\"\r\n    >\r\n      <tree-view :node=\"detail\" @refreshData=\"getDetail\"></tree-view>\r\n    </el-drawer>\r\n    <el-dialog title=\"选择导出范围\" :visible.sync=\"exportOpen\" width=\"400px\" append-to-body destroy-on-close>\r\n      <span>数据日期范围：</span>\r\n        <el-date-picker\r\n          v-model=\"dateValue\"\r\n          type=\"daterange\"\r\n          range-separator=\"至\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          @change=\"onDateChange\">\r\n        </el-date-picker>        \r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <div class=\"el-upload__tip\" slot=\"tip\">\r\n          <el-checkbox v-model=\"noteShow\" />是否在导出内容中展示指标修正历史\r\n        </div>\r\n      <el-button type=\"success\" @click=\"exportDataPreview\">预 览</el-button>\r\n      <el-button type=\"primary\" @click=\"exportData\">导 出</el-button>\r\n      <!-- <el-button @click=\"exportOpen = false\">取 消</el-button> -->\r\n    </div>\r\n    </el-dialog>\r\n\r\n    \r\n    <el-dialog title=\"选择导出范围\" :visible.sync=\"mouthImportOpen\" width=\"400px\" append-to-body destroy-on-close>\r\n      <span>数据日期范围：</span>\r\n        <el-date-picker\r\n          v-model=\"dateValue\"\r\n          type=\"daterange\"\r\n          range-separator=\"至\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          @change=\"onDateChange\">\r\n        </el-date-picker>        \r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n      <el-button type=\"primary\" @click=\"exportMouthDataPreview\">预 览</el-button>\r\n      <el-button type=\"primary\" @click=\"exportMouthData\">导 出</el-button>\r\n      <!-- <el-button @click=\"mouthImportOpen = false\">取 消</el-button> -->\r\n    </div>\r\n    </el-dialog>\r\n\r\n    <el-dialog title=\"选择导出范围\" :visible.sync=\"newOpen\" width=\"400px\" append-to-body destroy-on-close>\r\n      <el-form ref=\"form\" :model=\"form\" label-width=\"80px\">\r\n        <el-form-item label=\"名称\" prop=\"dimensionalityName\">\r\n          <el-input\r\n            v-model=\"form.dimensionalityName\"\r\n            placeholder=\"请输入名称\"\r\n            clearable\r\n            size=\"small\"\r\n            @keyup.enter.native=\"handleQuery\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"扎口部门\" prop=\"deptCode\">\r\n          <el-cascader\r\n            :options=\"deptList\"\r\n            clearable\r\n            v-model=\"form.deptCode\"\r\n            :props=\"{ expandTrigger: 'hover', emitPath: false,checkStrictly: true }\"\r\n            :show-all-levels=\"false\"\r\n          >\r\n          </el-cascader>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"addClick\">确 定</el-button>\r\n        <el-button @click=\"newOpen = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <el-dialog :title=\"adminTitle\" :visible.sync=\"adminOpen\" width=\"1000px\" append-to-body>\r\n      <el-table v-loading=\"loading\" :data=\"userList\">\r\n        <el-table-column label=\"用户工号\" align=\"center\" prop=\"workNo\" />\r\n        <el-table-column label=\"用户姓名\" align=\"center\" prop=\"userName\" />\r\n        <!-- <el-table-column label=\"用户姓名\" align=\"center\" prop=\"userName\" /> -->\r\n      </el-table>\r\n    </el-dialog>\r\n\r\n    <el-dialog :title=\"deadlineTitle\" :visible.sync=\"deadlineOpen\" width=\"800px\" append-to-body>\r\n      <el-form ref=\"deadlineForm\" :model=\"deadlineForm\" label-width=\"160px\">\r\n        <el-form-item label=\"截止日期开关\" prop=\"deadlineSwitch\">\r\n          <el-switch\r\n            v-model=\"deadlineForm.deadlineSwitch\"\r\n            active-color=\"#13ce66\"\r\n            inactive-color=\"#ff4949\"\r\n            active-value=\"1\"\r\n            inactive-value=\"0\"\r\n            >\r\n          </el-switch>\r\n        </el-form-item>\r\n        <el-form-item v-if=\" deadlineForm.deadlineSwitch == '1' \" label=\"截止日期\" prop=\"deadlineDate\">\r\n          <el-input type=\"text\"\r\n                    v-model=\"deadlineForm.deadlineDate\" \r\n                    placeholder=\"截止日期格式为(年/月/日)\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item v-if=\" deadlineForm.deadlineSwitch == '1' \"  label=\"邮件通知开关\" prop=\"mailSwitch\">\r\n          <el-switch\r\n            v-model=\"deadlineForm.mailSwitch\"\r\n            active-color=\"#13ce66\"\r\n            inactive-color=\"#ff4949\"\r\n            active-value=\"1\"\r\n            inactive-value=\"0\"\r\n            >\r\n          </el-switch>\r\n        </el-form-item>\r\n        <el-form-item v-if=\" deadlineForm.mailSwitch == '1' \" label=\"通知时间\" prop=\"deadlineDate\">\r\n          <el-input type=\"text\"\r\n                    v-model=\"deadlineForm.countdown\" \r\n                    placeholder=\"设置在截止日期前几天进行通知\"></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- <el-dialog\r\n      title=\"单周期报表导出\"\r\n      :visible.sync=\"SpecialImportOpen\"\r\n      width=\"400px\"\r\n      append-to-body\r\n      destroy-on-close\r\n    >\r\n      <span>选择导出时间：</span>\r\n      <el-date-picker\r\n            v-model=\"specialFcDate\"\r\n            value-format=\"yyyy-MM-dd\"\r\n            type=\"date\"\r\n            :default-value=\"new Date()\"\r\n            placeholder=\"选择时间\"\r\n          >\r\n      </el-date-picker>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"downloadTemplateSpecial\">数据下载</el-button>\r\n      </div>\r\n    </el-dialog> -->\r\n\r\n    <el-dialog title=\"单周期报表导出\" :visible.sync=\"SpecialImportOpen\" width=\"400px\" append-to-body destroy-on-close>\r\n      <span>选择导出时间：</span>\r\n      <el-date-picker\r\n            v-model=\"specialFcDate\"\r\n            value-format=\"yyyy-MM-dd\"\r\n            type=\"date\"\r\n            :default-value=\"new Date()\"\r\n            placeholder=\"选择时间\"\r\n          >\r\n      </el-date-picker>     \r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n      <el-button type=\"success\" @click=\"downloadTemplateSpecialPreview\">数据预览</el-button>\r\n      <el-button type=\"primary\" @click=\"downloadTemplateSpecial\">数据下载</el-button>\r\n    </div>\r\n    </el-dialog>\r\n\r\n    <el-dialog  title=\"数据预览\"  :visible.sync=\"searchopen\" width=\"1800px\" >\r\n        <div class=\"test\">\r\n          <vue-office-excel\r\n              :src=\"customBlobContent\"\r\n              style=\"height: 100vh;\"\r\n          />\r\n        </div>\r\n    </el-dialog>\r\n\r\n  </div>\r\n</template>\r\n  \r\n  <script>\r\nimport TreeView from \"@/components/TreeView\";\r\nimport {\r\n  rootListDimensionality,\r\n  getRootListById,\r\n  addDimensionality,\r\n  getStatusListWithadmin\r\n} from \"@/api/tYjy/dimensionality\";\r\n\r\nimport {\r\n  listPermission,\r\n} from \"@/api/tYjy/dimensionalitypermission\";\r\n\r\nimport {\r\n  deadlinebranch,\r\n  updateForm,\r\n} from \"@/api/tYjy/form\";\r\n\r\nimport { listDept } from \"@/api/tYjy/dept\";\r\nimport axios from \"axios\";\r\nimport * as xlsx from 'xlsx';\r\nexport default {\r\n  name: \"Dimensionality\",\r\n  components: {\r\n    TreeView,\r\n  },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      newOpen:false,\r\n      SpecialImportOpen:false,\r\n      mouthImportOpen:false,\r\n      searchopen:false,\r\n      total:0,\r\n      pageSizes:[20,50,100],\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 20,\r\n        dimensionalityName: null,\r\n        isUse: null,\r\n      },\r\n      rootList: [],\r\n      detail: {},\r\n      rootId:null,\r\n      drawer:false,\r\n      query:{\r\n        startDate:null,\r\n        endDate:null,\r\n        rootId:null,\r\n        title:null,\r\n      },\r\n      exportOpen:false,\r\n      deadlineOpen:false,\r\n      deadlineTitle:\"批量修改截止日期\",\r\n      deadlineForm:\r\n      {\r\n        dimensionalityPath:null\r\n      },\r\n      dateValue:null,\r\n      deptList: [],\r\n      form:{},\r\n      userList:[],\r\n      adminOpen:false,\r\n      adminTitle:\"管理员名单\",\r\n      specialFcDate:null,\r\n      dimensionalityName:null,\r\n      dimensionalityId:null,\r\n      noteShow:false //是否展示指标\r\n\r\n\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n    this.getDept();\r\n  },\r\n  methods: {\r\n    clickNode($event, node) {\r\n      $event.target.parentElement.parentElement.firstElementChild.click();\r\n    },\r\n    getList() {\r\n      this.loading = true;\r\n      getStatusListWithadmin(this.queryParams).then((res) => {\r\n        this.rootList = res.rows;\r\n        for(let i=0;i<this.rootList.length;i++)\r\n        {\r\n          if(this.rootList[i].id==273 || this.rootList[i].id==840 || this.rootList[i].id==873 || this.rootList[i].id==1077 || this.rootList[i].id==1059 || this.containsSubstring('安全责任工资',this.rootList[i].dimensionalityName))\r\n          {\r\n            this.rootList[i].showboot=1\r\n          }\r\n          else\r\n          {\r\n            this.rootList[i].showboot=0\r\n          }\r\n          if(this.containsSubstring('工装',this.rootList[i].dimensionalityName))\r\n          {\r\n            this.rootList[i].showMouth=1\r\n          }\r\n          else\r\n          {\r\n            this.rootList[i].showMouth=0\r\n          }\r\n        }\r\n        this.total = res.total;\r\n        this.loading = false;\r\n      });\r\n      // rootListDimensionality(this.queryParams).then((res) => {\r\n      //   this.rootList = res.rows;\r\n      //   this.total = res.total;\r\n      //   this.loading = false;\r\n      // });\r\n    },\r\n    getDept() {\r\n      listDept().then((res) => {\r\n        this.deptList = res.rows[0].children;\r\n        console.log(res);\r\n        for(let i=0;i<this.deptList.length;i++)\r\n        {\r\n          this.dealdeptList(this.deptList[i],0)\r\n        }\r\n      });\r\n    },\r\n    dealdeptList(row,count)\r\n    {\r\n       row.value=row.path\r\n       row.label=row.deptName\r\n       if(row.children.length>0 && count<1)\r\n       {\r\n          for(let i=0;i<row.children.length;i++)\r\n          {\r\n            this.dealdeptList(row.children[i],count+1)\r\n          }\r\n       }\r\n       else\r\n       {\r\n          row.children=null\r\n       }\r\n    },\r\n    handleQueryDept() {\r\n      this.$refs.cascaderHandle.dropDownVisible = false;\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n\r\n\r\n    handleAdd() {\r\n      this.newOpen=true\r\n      // let that = this;\r\n      // this.$prompt(\"请输入名称\", \"提示\", {\r\n      //   confirmButtonText: \"确定\",\r\n      //   cancelButtonText: \"取消\",\r\n      // })\r\n      //   .then(({ value }) => {\r\n      //     let form = {};\r\n      //     form.dimensionalityName = value;\r\n      //     addDimensionality(form).then((res) => {\r\n      //       that.getList();\r\n      //     });\r\n      //   })\r\n      //   .catch(() => {\r\n      //     that.$message({\r\n      //       type: \"info\",\r\n      //       message: \"取消操作\",\r\n      //     });\r\n      //   });\r\n    },\r\n    handleDetail(row){\r\n      this.rootId = row.id;\r\n      this.rootRuleType = row.ruleType;\r\n      this.getDetail();\r\n      this.drawer = true;\r\n    },\r\n    handleDeadLine(row){\r\n      this.deadlineForm={dimensionalityPath:null}\r\n      this.deadlineForm.dimensionalityPath=row.path\r\n      this.deadlineOpen=true\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      if(this.deadlineForm.deadlineSwitch==1)\r\n      {\r\n        if(this.deadlineForm.deadlineDate==null)\r\n        {\r\n          this.$modal.msgError(\"截止日期不能为空\");\r\n          return\r\n        }\r\n        let deadlineDateCheck=this.deadlineForm.deadlineDate.split(\"/\")\r\n        if(deadlineDateCheck.length!=3)\r\n        {\r\n          this.$modal.msgError(\"截止日期格式不正确，正确格式是 年/月/日 \");\r\n          return\r\n        }\r\n        if(!/^-?(0|([1-9]?\\d)|100)$/.test(deadlineDateCheck[0]))\r\n        {\r\n          this.$modal.msgError(\"截止日期中年应是在-100到100之间的整数\");\r\n          return\r\n        }\r\n        if(!/^-?(0|([0]?\\d)|11|12)$/.test(deadlineDateCheck[1]))\r\n        {\r\n          this.$modal.msgError(\"截止日期中月应是在-12到12之间的整数\");\r\n          return\r\n        }\r\n        if(!/^-?(0|([1-2]?\\d)|31|30)$/.test(deadlineDateCheck[2]))\r\n        {\r\n          this.$modal.msgError(\"截止日期中日应是在-31到31之间的整数\");\r\n          return\r\n        }\r\n      }\r\n      deadlinebranch(this.deadlineForm).then((response) => \r\n      {\r\n        this.msgSuccess(\"批量修改截止日期成功\");\r\n        this.deadlineOpen = false;\r\n      });\r\n    },\r\n\r\n    cancel() {\r\n      this.deadlineOpen = false;\r\n    },\r\n\r\n    getDetail(){\r\n    getRootListById({id : this.rootId,ruleType:this.rootRuleType}).then(res => {\r\n        this.detail = res.data;\r\n        if(this.detail == null || this.detail == undefined)this.detail = {}\r\n        console.log(this.detail)\r\n        this.$forceUpdate();\r\n      });\r\n    },\r\n    handleClose(){\r\n      this.drawer = false;\r\n      this.getList();\r\n      this.$forceUpdate();\r\n    },\r\n    handleExport(row){\r\n      this.query.rootId  = row.id;\r\n      this.query.title = row.dimensionalityName;\r\n      this.clickChangeTime();\r\n      this.exportOpen = true;\r\n    },\r\n\r\n    addClick() {\r\n      // this.form.deptId=parseInt(this.form.deptId.split(\",\")[-1])\r\n      addDimensionality(this.form).then((res) => {\r\n        this.newOpen = false;\r\n        this.getList();\r\n        this.form={};\r\n      });\r\n    },\r\n    exportData() {\r\n      if (\r\n        this.query.startDate == null ||\r\n        this.query.startDate == \"\"||\r\n        this.query.endDate == null||\r\n        this.query.endDate == \"\"\r\n      ) {\r\n        this.$notify.error({\r\n          title: \"错误\",\r\n          message: \"导出前请先输入开始结束时间\",\r\n        });\r\n        return;\r\n      }\r\n      this.query.noteShow=this.noteShow\r\n      this.downloadFile(\r\n        \"/web/TYjy/dimensionality/exportStatistics\",\r\n        {\r\n          ...this.query,\r\n        },\r\n        \"(\" +\r\n          this.query.startDate +\r\n          \"-\" +\r\n          this.query.endDate +\r\n          \")\" +\r\n          this.query.title +\r\n          `.xlsx`\r\n      );\r\n    },\r\n    exportDataPreview()\r\n    {\r\n      if (\r\n        this.query.startDate == null ||\r\n        this.query.startDate == \"\"||\r\n        this.query.endDate == null||\r\n        this.query.endDate == \"\"\r\n      ) {\r\n        this.$notify.error({\r\n          title: \"错误\",\r\n          message: \"导出前请先输入开始结束时间\",\r\n        });\r\n        return;\r\n      }\r\n      this.query.noteShow=this.noteShow\r\n      this.downloadXlsx(\r\n        \"/web/TYjy/dimensionality/exportStatistics\",\r\n        {\r\n          ...this.query,\r\n        },\r\n        this.dimensionalityName+\"(\" +this.specialFcDate+\r\n          \")\" +\r\n          `数据.xlsx`\r\n      ).then((blob) => {\r\n        let reader = new FileReader();\r\n        reader.readAsArrayBuffer(blob);\r\n        reader.onload = (evt) => {\r\n          this.customBlobContent=reader.result;\r\n          let ints = new Uint8Array(evt.target.result); //要使用读取的内容，所以将读取内容转化成Uint8Array\r\n          ints = ints.slice(0, blob.size);\r\n          let workBook = xlsx.read(ints, { type: \"array\" });\r\n          let sheetNames = workBook.SheetNames;\r\n          let sheetName = sheetNames[0];\r\n          let workSheet = workBook.Sheets[sheetName];\r\n          //获取Excle内容，并将空内容用空值保存\r\n          let excelTable = xlsx.utils.sheet_to_json(workSheet);\r\n          // 获取Excel头部\r\n          let tableThead = Array.from(Object.keys(excelTable[0])).map(\r\n            (item) => {\r\n              return item\r\n            }\r\n          );\r\n          this.excelData = excelTable;\r\n          this.exceltitle=tableThead\r\n          this.excelHtml= excelTable\r\n          this.searchopen = true;\r\n        }\r\n      });\r\n    },\r\n    exportMouthDataPreview(){\r\n      if (\r\n        this.query.startDate == null ||\r\n        this.query.startDate == \"\"||\r\n        this.query.endDate == null||\r\n        this.query.endDate == \"\"\r\n      ) {\r\n        this.$notify.error({\r\n          title: \"错误\",\r\n          message: \"导出前请先输入开始结束时间\",\r\n        });\r\n        return;\r\n      }\r\n      this.query.rootId = this.dimensionalityId\r\n      this.query.type=\"1\"\r\n      this.downloadXlsx(\r\n          \"/web/TYjy/answer/exportEverymouth\",\r\n          {\r\n            ...this.query,\r\n          },\r\n          this.dimensionalityName+\"(\" +this.specialFcDate+\r\n            \")\" +\r\n            `数据.xlsx`\r\n        ).then((blob) => {\r\n          let reader = new FileReader();\r\n          reader.readAsArrayBuffer(blob);\r\n          \r\n          reader.onload = (evt) => {\r\n            this.customBlobContent=reader.result;\r\n            let ints = new Uint8Array(evt.target.result); //要使用读取的内容，所以将读取内容转化成Uint8Array\r\n            ints = ints.slice(0, blob.size);\r\n            let workBook = xlsx.read(ints, { type: \"array\" });\r\n            let sheetNames = workBook.SheetNames;\r\n            let sheetName = sheetNames[0];\r\n            let workSheet = workBook.Sheets[sheetName];\r\n            //获取Excle内容，并将空内容用空值保存\r\n            let excelTable = xlsx.utils.sheet_to_json(workSheet);\r\n            // 获取Excel头部\r\n            let tableThead = Array.from(Object.keys(excelTable[0])).map(\r\n              (item) => {\r\n                return item\r\n              }\r\n            );\r\n            this.excelData = excelTable;\r\n            this.exceltitle=tableThead\r\n            this.excelHtml= excelTable\r\n            this.searchopen = true;\r\n          }\r\n        });\r\n    },\r\n    exportMouthData(){\r\n      if (\r\n        this.query.startDate == null ||\r\n        this.query.startDate == \"\"||\r\n        this.query.endDate == null||\r\n        this.query.endDate == \"\"\r\n      ) {\r\n        this.$notify.error({\r\n          title: \"错误\",\r\n          message: \"导出前请先输入开始结束时间\",\r\n        });\r\n        return;\r\n      }\r\n      this.query.rootId = this.dimensionalityId\r\n      this.query.type=\"1\"\r\n      this.downloadFile(\r\n        \"/web/TYjy/answer/exportEverymouth\",\r\n        {\r\n          ...this.query,\r\n        },\r\n        \"(\" +\r\n          this.query.startDate +\r\n          \"-\" +\r\n          this.query.endDate +\r\n          \")\" +\r\n          this.dimensionalityName +\r\n          `.xlsx`\r\n      );\r\n    },\r\n    onDateChange(){\r\n      console.log(this.dateValue)\r\n      if(this.dateValue != null && this.dateValue != \"\"){\r\n        this.query.startDate = this.dateValue[0] ;\r\n        this.query.endDate = this.dateValue[1];\r\n      }else{\r\n        this.query.startDate = \"\";\r\n        this.query.endDate = \"\";\r\n      }\r\n    },\r\n    toUpdateUsers(row){\r\n      const dimensionalityId = row.id;\r\n      this.$router.push(\"/dataReport/dimensionality-auth/dimensionalityPermission/\" + dimensionalityId);\r\n      // this.$router.go(0)\r\n    },\r\n    handleDateChange() {\r\n      this.getList();\r\n    },\r\n    handleaAdminList(row){\r\n\r\n      const dimensionalityId = row.id;\r\n      listPermission({dimensionalityId:dimensionalityId}).then((response) => {\r\n        this.userList = response.rows;\r\n        // this.total = response.total;\r\n        // this.loading = false;\r\n        this.adminOpen = true;\r\n      });\r\n\r\n      // const fcDate = this.queryParams.fcDate;\r\n      // const dimensionalityName = row.dimensionalityName;\r\n      // this.$router.push({ path: '/dataReport/adminfill-auth/adminfillstatus', query: { dimensionalityId:dimensionalityId, fcDate:fcDate,dimensionalityName:dimensionalityName} });\r\n    },\r\n    handlefill(row){\r\n      // const dimensionalityId = row.id;\r\n      // this.$router.push(\"/dataReport/adminfill-auth/adminfillstatus/\" + dimensionalityId);\r\n      const dimensionalityId = row.id;\r\n      const fcDate = this.queryParams.fcDate;\r\n      const dimensionalityName = row.dimensionalityName;\r\n      this.$router.push({ path: '/dataReport/adminfill-auth/adminfillstatus/'+ dimensionalityId, query: { dimensionalityId:dimensionalityId, fcDate:fcDate,dimensionalityName:dimensionalityName} });\r\n    },\r\n    handleAnswer(row){\r\n      // const dimensionalityId = row.id;\r\n      // this.$router.push(\"/dataReport/adminfill-auth/adminfillstatus/\" + dimensionalityId);\r\n      const dimensionalityId = row.id;\r\n      const fcDate = this.queryParams.fcDate;\r\n      const dimensionalityName= row.dimensionalityName;\r\n      this.$router.push({ path: '/dataReport/answerShow-auth/answerShow/'+ dimensionalityId, query: { dimensionalityId:dimensionalityId, fcDate:fcDate,dimensionalityName:dimensionalityName} });\r\n    },\r\n\r\n  handleSpecial(row){\r\n      // this.query.rootId  = row.id;\r\n      this.dimensionalityName = row.dimensionalityName;\r\n      this.dimensionalityId=row.id;\r\n      this.SpecialImportOpen = true;\r\n      \r\n    },\r\n  handleMouth(row){\r\n      // this.query.rootId  = row.id;\r\n      this.dimensionalityName = row.dimensionalityName;\r\n      this.dimensionalityId=row.id;\r\n      this.clickChangeTime();\r\n      this.mouthImportOpen = true;\r\n    },\r\n  downloadTemplateSpecialPreview(){\r\n    if (this.specialFcDate == null ) {\r\n        this.specialFcDate= this.queryParams.fcDate\r\n      }\r\n\r\n      // if (this.specialFcDate == null ) {\r\n      //   this.$notify.error({\r\n      //     title: \"错误\",\r\n      //     message: \"未选择时间\",\r\n      //   });\r\n      //   return;\r\n      // }\r\n      let queryImport={}\r\n      queryImport.rootId = this.dimensionalityId\r\n      queryImport.fcDate = this.specialFcDate\r\n      queryImport.type=\"1\"\r\n      let url=\"\"\r\n      if(this.dimensionalityName=='研究院目标指标一览')\r\n      {\r\n        url=\"/web/TYjy/answer/exportWithTemplate\"\r\n      }\r\n      else\r\n      {\r\n        url=\"/web/TYjy/answer/exportTemplateSpecial\"\r\n      }\r\n      this.downloadXlsx(\r\n          url,\r\n          {\r\n            ...queryImport,\r\n          },\r\n          this.dimensionalityName+\"(\" +this.specialFcDate+\r\n            \")\" +\r\n            `数据.xlsx`\r\n        ).then((blob) => {\r\n          let reader = new FileReader();\r\n          reader.readAsArrayBuffer(blob);\r\n          \r\n          reader.onload = (evt) => {\r\n            this.customBlobContent=reader.result;\r\n            let ints = new Uint8Array(evt.target.result); //要使用读取的内容，所以将读取内容转化成Uint8Array\r\n            ints = ints.slice(0, blob.size);\r\n            let workBook = xlsx.read(ints, { type: \"array\" });\r\n            let sheetNames = workBook.SheetNames;\r\n            let sheetName = sheetNames[0];\r\n            let workSheet = workBook.Sheets[sheetName];\r\n            //获取Excle内容，并将空内容用空值保存\r\n            let excelTable = xlsx.utils.sheet_to_json(workSheet);\r\n            // 获取Excel头部\r\n            let tableThead = Array.from(Object.keys(excelTable[0])).map(\r\n              (item) => {\r\n                return item\r\n              }\r\n            );\r\n            this.excelData = excelTable;\r\n            this.exceltitle=tableThead\r\n            this.excelHtml= excelTable\r\n            this.searchopen = true;\r\n          }\r\n        });\r\n  },\r\n  downloadTemplateSpecial(){\r\n      if (this.specialFcDate == null ) {\r\n        this.specialFcDate= this.queryParams.fcDate\r\n      }\r\n\r\n      // if (this.specialFcDate == null ) {\r\n      //   this.$notify.error({\r\n      //     title: \"错误\",\r\n      //     message: \"未选择时间\",\r\n      //   });\r\n      //   return;\r\n      // }\r\n      let queryImport={}\r\n      queryImport.rootId = this.dimensionalityId\r\n      queryImport.fcDate = this.specialFcDate\r\n      queryImport.type=\"1\"\r\n      let url=\"\"\r\n      if(this.dimensionalityName=='研究院目标指标一览')\r\n      {\r\n        url=\"/web/TYjy/answer/exportWithTemplate\"\r\n      }\r\n      else\r\n      {\r\n        url=\"/web/TYjy/answer/exportTemplateSpecial\"\r\n      }\r\n      this.downloadFile(\r\n        url,\r\n        {\r\n          ...queryImport,\r\n        },\r\n        this.dimensionalityName+\"(\" +this.specialFcDate+\r\n          \")\" +\r\n          `数据.xlsx`\r\n      );\r\n    },\r\n\r\n    //此处为按钮判断管理\r\n    containsSubstring(substring, string) \r\n    {\r\n      return string.includes(substring);\r\n    },\r\n    //支持预览和导出\r\n    mouthCheck(string)\r\n    {\r\n      if(string== '六化指标')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '工装上线验收合格率统计')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '2025年经济责任制技经指标')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '研究院技经提升指标跟踪')\r\n      {\r\n        return true;\r\n      }\r\n      return false;\r\n    },\r\n    //支持单周期导出\r\n    aloneList(string) {\r\n      if(string== '气体结算月报')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '高炉、转炉煤气月报表')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '天然气消耗月报表')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '蒸汽消耗月报表')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '电量月报表')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '特板事业部2025年经济责任制奖罚汇总')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '研究院目标指标一览')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '安全责任工资考核表')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '安全责任工资考核汇总')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '水处理水量报表')\r\n      {\r\n        return true;\r\n      }\r\n      return false;\r\n    },\r\n\r\n    //数据预览模块处理\r\n    handlePreview() {\r\n      let queryImport={}\r\n      queryImport.rootId = this.queryParams.dimensionalityId\r\n      queryImport.fcDate = this.queryParams.fcDate\r\n      queryImport.type=\"1\"\r\n      if(this.dimensionalityName=='研究院目标指标一览')\r\n      {\r\n          this.downloadXlsx(\r\n          \"/web/TYjy/answer/exportWithTemplate\",\r\n          {\r\n            ...queryImport,\r\n          },\r\n          this.dimensionalityName+\"(\" +this.specialFcDate+\r\n            \")\" +\r\n            `数据.xlsx`\r\n        ).then((blob) => {\r\n          let reader = new FileReader();\r\n          reader.readAsArrayBuffer(blob);\r\n          \r\n          reader.onload = (evt) => {\r\n            this.customBlobContent=reader.result;\r\n            let ints = new Uint8Array(evt.target.result); //要使用读取的内容，所以将读取内容转化成Uint8Array\r\n            ints = ints.slice(0, blob.size);\r\n            let workBook = xlsx.read(ints, { type: \"array\" });\r\n            let sheetNames = workBook.SheetNames;\r\n            let sheetName = sheetNames[0];\r\n            let workSheet = workBook.Sheets[sheetName];\r\n            //获取Excle内容，并将空内容用空值保存\r\n            let excelTable = xlsx.utils.sheet_to_json(workSheet);\r\n            // 获取Excel头部\r\n            let tableThead = Array.from(Object.keys(excelTable[0])).map(\r\n              (item) => {\r\n                return item\r\n              }\r\n            );\r\n            this.excelData = excelTable;\r\n            this.exceltitle=tableThead\r\n            this.excelHtml= excelTable\r\n            this.searchopen = true;\r\n          }\r\n        });\r\n      }\r\n      else\r\n      {\r\n        this.downloadXlsx(\r\n        \"/web/TYjy/answer/exportTemplateSpecial\",\r\n        {\r\n          ...queryImport,\r\n        },\r\n        this.dimensionalityName+\"(\" +this.specialFcDate+\r\n          \")\" +\r\n          `数据.xlsx`\r\n      ).then((blob) => {\r\n        let reader = new FileReader();\r\n        reader.readAsArrayBuffer(blob);\r\n        reader.onload = (evt) => {\r\n          this.customBlobContent=reader.result;\r\n          let ints = new Uint8Array(evt.target.result); //要使用读取的内容，所以将读取内容转化成Uint8Array\r\n          ints = ints.slice(0, blob.size);\r\n          let workBook = xlsx.read(ints, { type: \"array\" });\r\n          let sheetNames = workBook.SheetNames;\r\n          let sheetName = sheetNames[0];\r\n          let workSheet = workBook.Sheets[sheetName];\r\n          //获取Excle内容，并将空内容用空值保存\r\n          let excelTable = xlsx.utils.sheet_to_json(workSheet);\r\n          // 获取Excel头部\r\n          let tableThead = Array.from(Object.keys(excelTable[0])).map(\r\n            (item) => {\r\n              return item\r\n            }\r\n          );\r\n          this.excelData = excelTable;\r\n          this.exceltitle=tableThead\r\n          this.excelHtml= excelTable\r\n          this.searchopen = true;\r\n        }\r\n      });\r\n      }\r\n    },\r\n    // 时间段预览\r\n    handlePreview1() {\r\n      // let queryImport={}\r\n      // queryImport.rootId = this.queryParams.dimensionalityId\r\n      // queryImport.fcDate = this.queryParams.fcDate\r\n      // queryImport.type=\"1\"\r\n        if (\r\n        this.queryImport.startDate == null ||\r\n        this.queryImport.startDate == \"\"||\r\n        this.queryImport.endDate == null||\r\n        this.queryImport.endDate == \"\"\r\n      ) {\r\n        this.$notify.error({\r\n          title: \"错误\",\r\n          message: \"导出前请先输入开始结束时间\",\r\n        });\r\n        return;\r\n      }\r\n      this.queryImport.rootId = this.queryParams.dimensionalityId\r\n      this.queryImport.type=\"1\"\r\n      this.downloadXlsx(\r\n        \"/web/TYjy/answer/exportTemplateNomral\",\r\n        {\r\n          ...this.queryImport,\r\n        },\r\n        this.dimensionalityName+\"(\" +this.specialFcDate+\r\n          \")\" +\r\n          `数据.xlsx`\r\n      ).then((blob) => {\r\n        let reader = new FileReader();\r\n        reader.readAsArrayBuffer(blob);\r\n        \r\n        reader.onload = (evt) => {\r\n          this.customBlobContent=reader.result;\r\n          let ints = new Uint8Array(evt.target.result); //要使用读取的内容，所以将读取内容转化成Uint8Array\r\n          ints = ints.slice(0, blob.size);\r\n          let workBook = xlsx.read(ints, { type: \"array\" });\r\n          let sheetNames = workBook.SheetNames;\r\n          let sheetName = sheetNames[0];\r\n          let workSheet = workBook.Sheets[sheetName];\r\n          //获取Excle内容，并将空内容用空值保存\r\n          let excelTable = xlsx.utils.sheet_to_json(workSheet);\r\n          // 获取Excel头部\r\n          let tableThead = Array.from(Object.keys(excelTable[0])).map(\r\n            (item) => {\r\n              return item\r\n            }\r\n          );\r\n          this.excelData = excelTable;\r\n          this.exceltitle=tableThead\r\n          this.excelHtml= excelTable\r\n          this.searchopen = true;\r\n        }\r\n      });\r\n    },\r\n\r\n    clickChangeTime()\r\n    {\r\n         let now =new Date();\r\n         this.query.startDate=this.getFirstOfYear(now);\r\n         this.query.endDate=this.getFirstOfMonth(now);\r\n         this.dateValue=[];\r\n         this.dateValue.push(this.query.startDate);\r\n         this.dateValue.push(this.query.endDate);\r\n    },\r\n    // 获取时间的优化处理\r\n    getFirstOfYear(now)\r\n    {\r\n      let  firstDayOfYear = new Date(now.getFullYear(), 0, 1);\r\n      return this.formatDate(firstDayOfYear);\r\n    },\r\n    getFirstOfMonth(now)\r\n    {\r\n      let firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);\r\n      return this.formatDate(firstDayOfMonth);\r\n    },\r\n    // 日期格式化函数（转为 yyyy-MM-dd）\r\n    formatDate(date) \r\n    {\r\n      const year = date.getFullYear();\r\n      const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从0开始需+1\r\n      const day = String(date.getDate()).padStart(2, '0');\r\n      return `${year}-${month}-${day}`;\r\n    },\r\n\r\n  },\r\n};\r\n</script>\r\n<style lang=\"less\">\r\n.v-modal {\r\n  display: none;\r\n}\r\n</style>\r\n  "]}]}