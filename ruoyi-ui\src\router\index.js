import Vue from 'vue'
import Router from 'vue-router'

Vue.use(Router)

/* Layout */
import Layout from '@/layout'
import ParentView from '@/components/ParentView';

/**
 * Note: 路由配置项
 *
 * hidden: true                   // 当设置 true 的时候该路由不会再侧边栏出现 如401，login等页面，或者如一些编辑页面/edit/1
 * alwaysShow: true               // 当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式--如组件页面
 *                                // 只有一个时，会将那个子路由当做根路由显示在侧边栏--如引导页面
 *                                // 若你想不管路由下面的 children 声明的个数都显示你的根路由
 *                                // 你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，一直显示根路由
 * redirect: noRedirect           // 当设置 noRedirect 的时候该路由在面包屑导航中不可被点击
 * name:'router-name'             // 设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题
 * meta : {
    noCache: true                // 如果设置为true，则不会被 <keep-alive> 缓存(默认 false)
    title: 'title'               // 设置该路由在侧边栏和面包屑中展示的名字
    icon: 'svg-name'             // 设置该路由的图标，对应路径src/assets/icons/svg
    breadcrumb: false            // 如果设置为false，则不会在breadcrumb面包屑中显示
  }
 */

// 公共路由
export const constantRoutes = [{
    path: '/redirect',
    component: Layout,
    hidden: true,
    children: [{
      path: '/redirect/:path(.*)',
      component: (resolve) => require(['@/views/redirect'], resolve)
    }]
  },
  {
    path: '/login',
    component: (resolve) => require(['@/views/login'], resolve),
    hidden: true
  },
  {
    path: '/404',
    component: (resolve) => require(['@/views/error/404'], resolve),
    hidden: true
  },
  {
    path: '/401',
    component: (resolve) => require(['@/views/error/401'], resolve),
    hidden: true
  },
  {
    path: '/bill/:billId(.*)',
    component: (resolve) => require(['@/views/lading/bill/index'], resolve),
    hidden: true
  },
  {
    path: '/tdgcb04/:supplyNo(.*)',
    component: (resolve) => require(['@/views/dgcb/supplier/supplyDetail/index'], resolve),
    hidden: true
  },
  {
    path: '/bigScreenCommon',
    component: (resolve) => require(['@/views/bigScreen/common'], resolve),
    hidden: true,
    meta: {
      title: '大屏跳转',
    }
  },
  {
    path: '/bigScreen/tsdd',
    component: (resolve) => require(['@/views/bigScreen/tsdd/index'], resolve),
    hidden: true,
    meta: {
      title: '铁水调度展示大屏',
    }
  },
  {
    path: '/wgbPoints/screen',
    component: (resolve) => require(['@/views/wgbPoints/screen/index'], resolve),
    hidden: true,
    meta: {
      title: '积分管理数据大屏',
    }
  },
  {
    path: '/resetPas',
    component: (resolve) => require(['@/views/resetPas'], resolve),
    hidden: true,
    meta: {
      noCache: true ,
      title: '密码重置',
    }
  },
  {
    path: '/codeLogin',
    component: (resolve) => require(['@/views/login/codeLogin'], resolve),
    hidden: true
  },
  {
    path: '/webView/:url(.*)',
    component: (resolve) => require(['@/views/webView'], resolve),
    hidden: true
  },

  {
    path: '',
    component: Layout,
    redirect: 'index',
    children: [{
      path: 'index',
      component: (resolve) => require(['@/views/index'], resolve),
      name: '首页',
      meta: {
        title: '首页',
        icon: 'dashboard',
        noCache: true,
        affix: true
      }
    }]
  },
  {
    path: '/user',
    component: Layout,
    hidden: true,
    redirect: 'noredirect',
    children: [{
      path: 'profile',
      component: (resolve) => require(['@/views/system/user/profile/index'], resolve),
      name: 'Profile',
      meta: {
        title: '个人中心',
        icon: 'user'
      }
    }]
  },
  {
    path: '/dict',
    component: Layout,
    hidden: true,
    children: [{
      path: 'type/data/:dictId(\\d+)',
      component: (resolve) => require(['@/views/system/dict/data'], resolve),
      name: 'Data',
      meta: {
        title: '字典数据',
        icon: ''
      }
    }]
  },
  {
    path: '/job',
    component: Layout,
    hidden: true,
    children: [{
      path: 'log',
      component: (resolve) => require(['@/views/monitor/job/log'], resolve),
      name: 'JobLog',
      meta: {
        title: '调度日志'
      }
    }]
  },
  {
    path: '/gen',
    component: Layout,
    hidden: true,
    children: [{
      path: 'edit/:tableId(\\d+)',
      component: (resolve) => require(['@/views/tool/gen/editTable'], resolve),
      name: 'GenEdit',
      meta: {
        title: '修改生成配置'
      }
    }]
  },
  {
    path: '/cater/order',
    component: Layout,
    hidden: true,
    children: [{
      path: 'list/:orderDate/:canteenId/:locationId/:timeCode',
      component: (resolve) => require(['@/views/catering/order/index'], resolve),
      name: 'CaterOrder',
      meta: {
        title: '订单管理',
        activeMenu: '/cater/order'
      }
    }, ]
  },
  {
    path: '/energySteel',
    component: Layout,
    hidden: true,
    children: [{
      path: 'projectDict/data/:dictId(\\d+)',
      component: (resolve) => require(['@/views/energySteel/projectDictData/index'], resolve),
      name: 'projectDictData',
      meta: {
        title: '字典数据',
        icon: ''
      }
    }]
  },
  {
    path: '/dgcb/addSupplyInfo',
    component: (resolve) => require(['@/views/dgcb/supplier/addSupplyInfo/index'], resolve),
    hidden: true,
    // children: [{
    //   path: 'addSupplyInfo',
    //   component: (resolve) => require(['@/views/dgcb/supplier/addSupplyInfo/index'], resolve),
    //   name: 'addSupplyInfo',
    //   meta: {
    //     title: '新增供货清单',
    //     icon: ''
    //   }
    // }]
  },


  {
    // 吨钢承包页面路由
    path: '/dgcb',
    component: Layout,
    hidden: true,
    children: [{
      path: 'supplyInfo/:supplyNo(\\d+)',
      component: (resolve) => require(['@/views/dgcb/supplier/supplyInfo/index'], resolve),
      name: 'supplyInfo',
      meta: {
        title: '供货清单详情',
        icon: ''
      }
    }, {
      // 返修单填报
      path: 'repair/report',
      component: (resolve) => require(['@/views/dgcb/repair/report/index'], resolve),
      name: 'repairReport',
      meta: {
        title: '返修单填报',
        icon: ''
      }
    }, {
      // 返修单详情
      path: 'repair/detail/:type/:repairId(\\d+)',
      component: (resolve) => require(['@/views/dgcb/repair/detail/index'], resolve),
      name: 'repairDetail',
      meta: {
        title: '返修单详情',
        icon: ''
      }
    }, {
      // 供应商返修单详情
      path: 'supplier/repair/detail/:repairId(\\d+)',
      component: (resolve) => require(['@/views/dgcb/supplier/repair/detail'], resolve),
      name: 'suppRepairDetail',
      meta: {
        title: '返修单详情',
        icon: ''
      }
    }, {
      // 供应商新增返厂单
      path: 'supplier/repair/addReturn/:repairNo',
      component: (resolve) => require(['@/views/dgcb/supplier/repair/addReturn'], resolve),
      name: 'supplierAddReturn',
      meta: {
        title: '新增返厂单',
        icon: ''
      }
    }, {
      // 供应商返厂单详情
      path: 'supplier/repair/return/detail/:returnId(\\d+)',
      component: (resolve) => require(['@/views/dgcb/supplier/repair/returnDetail'], resolve),
      name: 'supplierReturnDetail',
      meta: {
        title: '返厂单详情',
        icon: ''
      }
    }]
  },
  {
    path: '/dgcb/contract',
    component: Layout,
    hidden: true,
    children: [{
      path: 'info/:contractNo',
      component: (resolve) => require(['@/views/dgcb/contract/detail/info'], resolve),
      name: 'ContractInfo',
      meta: {
        title: '合同信息',
        icon: ''
      }
    }]
  },
  // {
  //   path: '/dgcb/repair',
  //   component: Layout,
  //   hidden: true,
  //   children: [{
  //     path: 'info/:repairNo',
  //     component: (resolve) => require(['@/views/dgcb/repair/detail/info'], resolve),
  //     name: 'RepairInfo',
  //     meta: {
  //       title: '返修单信息',
  //       icon: ''
  //     }
  //   }]
  // },
  {
    path: '/dgcb/inventory',
    component: Layout,
    hidden: true,
    children: [{
      path: 'record/:itemNo/:itemNum',
      component: (resolve) => require(['@/views/dgcb/inventory/record/index'], resolve),
      name: 'inventoryRecord',
      meta: {
        title: '库存日志',
        icon: ''
      }
    }]
  },
  {
    path: '/truck/scrapSteel',
    component: Layout,
    hidden: true,
    children: [{
      path: 'detail/:ticketNo',
      component: (resolve) => require(['@/views/truck/scrapSteel/detail'], resolve),
      name: 'scrapSteelDetail',
      meta: {
        title: '废钢预约单详情',
        icon: ''
      }
    }, {
      path: '/truck/common/ticket/detail:ticketNo',
      component: (resolve) => require(['@/views/truck/common/ticket/detail'], resolve),
      name: 'truckUnifyTicketDetail',
      meta: {
        title: '货车预约单详情',
        icon: ''
      }
    }]
  },
  {
    path: '/truck/alloy',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'detail/:reservationNo',
        component: (resolve) => require(['@/views/truck/alloy/detail'], resolve),
        name: 'alloyOrderDetail',
        meta: {
          title: '合金预约单详情',
          icon: ''
        }
      }
    ]
  },
  //车辆进出厂
  {
    path: '/vehicle',
    component: Layout,
    hidden: true,
    children: [{
      path: 'passport/detail/:flowNo',
      component: (resolve) => require(['@/views/vehicle/passport/detail/index'], resolve),
      name: 'passportDetail',
      meta: {
        title: '通行证详情',
        icon: ''
      }
    }, {
      path: 'parkingFlow/detail/:id',
      component: (resolve) => require(['@/views/vehicle/parkingFlow/detail/index'], resolve),
      name: 'ParkingFlowDetail',
      meta: {
        title: '停车证详情',
        icon: ''
      }
    }, {
      path: 'passportApply/list/:passportNo',
      component: (resolve) => require(['@/views/vehicle/passport/index'], resolve),
      name: 'PassportApplyList',
      meta: {
        title: '通行证申请列表',
        icon: ''
      }
    }]
  },
  {
    path: '/eventTrack/event',
    component: Layout,
    hidden: true,
    children: [{
      path: 'eventDept/:eventNo',
      component: (resolve) => require(['@/views/eventTrack/event/eventDept'], resolve),
      name: 'eventDept',
      meta: {
        title: '事件详情',
        icon: ''
      }
    }, {
      path: 'eventItems/:eventNo/:deptName',
      component: (resolve) => require(['@/views/eventTrack/event/eventItems'], resolve),
      name: 'eventItems',
      meta: {
        title: '事件记录',
        icon: ''
      }
    }]
  },
  {
    path: '/eventTrack/toDo',
    component: Layout,
    hidden: true,
    children: [{
      path: 'detail/:eventNo/:deptName',
      component: (resolve) => require(['@/views/eventTrack/toDo/detail'], resolve),
      name: 'toDoDetail',
      meta: {
        title: '待办详情',
        icon: ''
      }
    }]
  },
  {
    path: '/apprentice',
    component: Layout,
    hidden: true,
    children: [{
      path: 'monthRecord/detail/:id',
      component: (resolve) => require(['@/views/apprentice/monthRecord/detail'], resolve),
      name: 'monthRecordDetail',
      meta: {
        title: '月度跟踪详情',
        icon: ''
      }
    }]
  }, {
    path: '/workFlow',
    component: Layout,
    hidden: true,
    children: [{
      path: 'detail/index/:id',
      component: (resolve) => require(['@/views/workFlow/detail/index'], resolve),
      name: 'workFlowDetail',
      meta: {
        title: '流程定义详情',
        icon: ''
      }
    }]
  },
  {
    path: '/quitSingle/info',
    component: Layout,
    hidden: true,
    children: [{
      path: 'detail/:id',
      component: (resolve) => require(['@/views/quitSingle/info/detail'], resolve),
      name: 'qsInfo',
      meta: {
        title: '个人信息',
        icon: ''
      }
    }]
  },
  {
    path: '/driverReserve/info',
    component: Layout,
    hidden: true,
    children: [{
      path: 'detail/:flowNo',
      component: (resolve) => require(['@/views/driverReserve/detail'], resolve),
      name: 'billInfo',
      meta: {
        title: '提单信息',
        icon: ''
      }
    }]
  },
  {
    // 图纸库详情页面路由
    path: '/drawing',
    component: Layout,
    hidden: true,
    children: [{
      path: 'detail/:id',
      component: (resolve) => require(['@/views/drawing/detail'], resolve),
      name: 'drawingDetail',
      meta: {
        title: '图纸库详情',
        icon: ''
      }
    }, {
      path: 'printDetail/:batchId(\\d+)',
      component: (resolve) => require(['@/views/drawing/printDetail/index'], resolve),
      name: 'drawingPrintDetail',
      meta: {
        title: '图纸库合并打印详情',
        icon: ''
      }
    },{
      path: 'technicalDetail/:id',
      component: (resolve) => require(['@/views/drawing/technicalDetail/index'], resolve),
      name: 'drawingTechnicalDetail',
      meta: {
        title: '技术协议详情',
        icon: ''
      }
    }]
  },
  {
    // 图纸库详情页面路由
    path: '/violate',
    component: Layout,
    hidden: true,
    children: [{
      path: 'detail/:ticketNo',
      component: (resolve) => require(['@/views/violate/detail'], resolve),
      name: 'violateTicketDetail',
      meta: {
        title: '违章罚款详情',
        icon: ''
      }
    }]
  },
  // 汽吊详情页
  {
    path: '/crane',
    component: Layout,
    hidden: true,
    children: [{
      path: 'info/detail/:applyNo',
      component: (resolve) => require(['@/views/crane/info/detail'], resolve),
      name: 'craneDetail',
      meta: {
        title: '汽吊用车详情',
        icon: ''
      }
    }]
  },
  //访客
  {
    path: '/visitor',
    component: Layout,
    hidden: true,
    children: [{
      path: 'dept-assign/user/:deptId',
      component: (resolve) => require(['@/views/visitor/dept/assignUser'], resolve),
      name: 'userDept',
      meta: {
        title: '分配用户',
        icon: ''
      }
    }, {
      path: 'list/detail/:flowNo',
      component: (resolve) => require(['@/views/visitor/list/detail'], resolve),
      name: 'visitorDetail',
      meta: {
        title: '访客详情',
        icon: ''
      }
    }]
  }
]

// 动态路由，基于用户权限动态去加载
export const dynamicRoutes = [{
    path: '/system/user-auth',
    component: Layout,
    hidden: true,
    permissions: ['system:user:edit'],
    children: [{
      path: 'role/:userId(\\d+)',
      component: () => import('@/views/system/user/authRole'),
      name: 'AuthRole',
      meta: {
        title: '分配角色',
        activeMenu: '/system/user'
      }
    }]
  },
  {
    path: '/system/role-auth',
    component: Layout,
    hidden: true,
    permissions: ['system:role:edit'],
    children: [{
      path: 'user/:roleId(\\d+)',
      component: () => import('@/views/system/role/authUser'),
      name: 'AuthUser',
      meta: {
        title: '分配用户',
        activeMenu: '/system/role'
      }
    }, ]
  },
  {
    path: '/dataReport/dept-auth',
    component: Layout,
    hidden: true,
    permissions: ['dataReport:dept:list'],
    children: [{
      path: 'deptUser/:deptId(\\d+)',
      component: () => import('@/views/dataReport/dept/deptUser'),
      name: 'DeptUser',
      meta: {
        title: '分配用户',
        activeMenu: '/dataReport/dataReportDept'
      },

    } ]
  },
  {
    path: '/dataReport/dimensionality-auth',
    component: Layout,
    hidden: true,
    permissions: ['dataReport:form:list'],
    children: [{
      path: 'dimensionalityPermission/:dimensionalityId(\\d+)',
      component: () => import('@/views/dataReport/form/dimensionalityPermission'),
      name: 'DimensionalityPermission',
      meta: {
        title: '分配用户',
        activeMenu: '/dataReport/dimensionalityPermission'
      },
    } ]
  },

  {
    path: '/dataReport/adminfill-auth',
    component: Layout,
    hidden: true,
    permissions: ['dataReport:form:list'],
    children: [{
      path: 'adminfillstatus/:dimensionalityId(\\d+)',
      component: () => import('@/views/dataReport/form/adminfillstatus'),
      name: 'adminfillstatus',
      query: '{"buMenID": buMenID, "gongZhongID": gongZhongID, "xianLuSuoID": xianLuSuoID}',
      meta: {
        title: '未填报详情',
        activeMenu: '/dataReport/adminfillstatus'
      },
    } ]
  },
  {
    path: '/dataReport/submitfill-auth',
    component: Layout,
    hidden: true,
    permissions: ['dataReport:form:list'],
    children: [{
      path: 'submitfillstatus/:dimensionalityId(\\d+)',
      component: () => import('@/views/dataReport/form/submitfillstatus'),
      name: 'submitfillstatus',
      query: '{"buMenID": buMenID, "gongZhongID": gongZhongID, "xianLuSuoID": xianLuSuoID}',
      meta: {
        title: '未填报详情',
        activeMenu: '/dataReport/submitfillstatus'
      },
    } ]
  },
  {
    path: '/dataReport/answerForm-auth',
    component: Layout,
    hidden: true,
    permissions: ['dataReport:form:list'],
    children: [{
      path: 'answerForm/:dimensionalityId(\\d+)',
      component: () => import('@/views/dataReport/answer/answerForm'),
      name: 'answerForm',
      query: '{"buMenID": buMenID, "gongZhongID": gongZhongID, "xianLuSuoID": xianLuSuoID}',
      meta: {
        title: '数据填报',
        activeMenu: '/dataReport/answerForm'
      },
    } ]
  },
  {
    path: '/dataReport/answerShow-auth',
    component: Layout,
    hidden: true,
    permissions: ['dataReport:form:list'],
    children: [{
      path: 'answerShow/:dimensionalityId(\\d+)',
      component: () => import('@/views/dataReport/answer/answerShow'),
      name: 'answerShow',
      query: '{"buMenID": buMenID, "gongZhongID": gongZhongID, "xianLuSuoID": xianLuSuoID}',
      meta: {
        title: '数据填报',
        activeMenu: '/dataReport/answerShow'
      },
    } ]
  },

  {
    path: '/teamSolution/deptDetail-auth',
    component: Layout,
    hidden: true,
    permissions: ['teamSolution:dept:situation'],
    children: [{
      path: 'deptDetail',
      component: () => import('@/views/teamSolution/deptDetail'),
      name: 'deptDetail',
      query: '{"buMenID": buMenID, "gongZhongID": gongZhongID, "xianLuSuoID": xianLuSuoID}',
      meta: {
        title: '分厂班组详情',
        activeMenu: '/teamSolution//deptDetail'
      },
    } ]
  },

]

export default new Router({
  mode: 'history', // 去掉url中的#
  scrollBehavior: () => ({
    y: 0
  }),
  routes: constantRoutes
})
