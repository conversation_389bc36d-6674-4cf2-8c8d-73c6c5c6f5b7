package com.ruoyi.app.leave.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 出门证厂外客户对象 leave_customer
 * 
 * <AUTHOR>
 * @date 2025-03-13
 */
public class LeaveCustomer extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 状态位0作废1正常2领卡单位 */
    @Excel(name = "状态位0作废1正常2领卡单位")
    private Integer validFlag;

    /** 客户编码 */
    @Excel(name = "客户编码")
    private String customerCode;

    /** 客户名称 */
    @Excel(name = "客户名称")
    private String customerName;

    /** 拼音头 */
    @Excel(name = "拼音头")
    private String queryWord;

    /** erp编码 */
    @Excel(name = "erp编码")
    private String erpCode;

    /** 电话 */
    @Excel(name = "电话")
    private String tele;

    /** 客户地址 */
    @Excel(name = "客户地址")
    private String address;

    /** 备注 */
    @Excel(name = "备注")
    private String memo;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setValidFlag(Integer validFlag) 
    {
        this.validFlag = validFlag;
    }

    public Integer getValidFlag() 
    {
        return validFlag;
    }
    public void setCustomerCode(String customerCode) 
    {
        this.customerCode = customerCode;
    }

    public String getCustomerCode() 
    {
        return customerCode;
    }
    public void setCustomerName(String customerName) 
    {
        this.customerName = customerName;
    }

    public String getCustomerName() 
    {
        return customerName;
    }
    public void setQueryWord(String queryWord) 
    {
        this.queryWord = queryWord;
    }

    public String getQueryWord() 
    {
        return queryWord;
    }
    public void setErpCode(String erpCode) 
    {
        this.erpCode = erpCode;
    }

    public String getErpCode() 
    {
        return erpCode;
    }
    public void setTele(String tele) 
    {
        this.tele = tele;
    }

    public String getTele() 
    {
        return tele;
    }
    public void setAddress(String address) 
    {
        this.address = address;
    }

    public String getAddress() 
    {
        return address;
    }
    public void setMemo(String memo) 
    {
        this.memo = memo;
    }

    public String getMemo() 
    {
        return memo;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("validFlag", getValidFlag())
            .append("customerCode", getCustomerCode())
            .append("customerName", getCustomerName())
            .append("queryWord", getQueryWord())
            .append("erpCode", getErpCode())
            .append("tele", getTele())
            .append("address", getAddress())
            .append("memo", getMemo())
            .append("createTime", getCreateTime())
            .append("createBy", getCreateBy())
            .append("updateTime", getUpdateTime())
            .append("updateBy", getUpdateBy())
            .toString();
    }
}
