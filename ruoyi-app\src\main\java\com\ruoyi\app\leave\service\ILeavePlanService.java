package com.ruoyi.app.leave.service;

import java.util.List;
import com.ruoyi.app.leave.domain.LeavePlan;

/**
 * 出门证计划申请Service接口
 * 
 * <AUTHOR>
 * @date 2025-03-13
 */
public interface ILeavePlanService 
{
    /**
     * 查询出门证计划申请
     * 
     * @param id 出门证计划申请ID
     * @return 出门证计划申请
     */
    public LeavePlan selectLeavePlanById(Long id);

    /**
     * 查询出门证计划申请列表
     * 
     * @param leavePlan 出门证计划申请
     * @return 出门证计划申请集合
     */
    public List<LeavePlan> selectLeavePlanList(LeavePlan leavePlan);

    /**
     * 新增出门证计划申请
     * 
     * @param leavePlan 出门证计划申请
     * @return 结果
     */
    public int insertLeavePlan(LeavePlan leavePlan);

    /**
     * 修改出门证计划申请
     * 
     * @param leavePlan 出门证计划申请
     * @return 结果
     */
    public int updateLeavePlan(LeavePlan leavePlan);

    /**
     * 批量删除出门证计划申请
     * 
     * @param ids 需要删除的出门证计划申请ID
     * @return 结果
     */
    public int deleteLeavePlanByIds(Long[] ids);

    /**
     * 删除出门证计划申请信息
     * 
     * @param id 出门证计划申请ID
     * @return 结果
     */
    public int deleteLeavePlanById(Long id);
}
