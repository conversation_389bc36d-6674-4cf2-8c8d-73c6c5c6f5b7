{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\dataReport\\answer\\answerShow.vue?vue&type=template&id=8df067d6&scoped=true", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\dataReport\\answer\\answerShow.vue", "mtime": 1756099891059}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}