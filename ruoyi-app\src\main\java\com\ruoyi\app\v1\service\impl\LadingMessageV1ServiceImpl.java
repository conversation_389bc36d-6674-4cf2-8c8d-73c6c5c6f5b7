package com.ruoyi.app.v1.service.impl;

import com.ruoyi.app.v1.domain.DriverLading;
import com.ruoyi.app.v1.domain.LadingList;
import com.ruoyi.app.v1.domain.LadingMessageV1;
import com.ruoyi.app.v1.domain.LadingSubscribe;
import com.ruoyi.app.v1.mapper.LadingMessageV1Mapper;
import com.ruoyi.app.v1.mapper.LadingSubscribeMapper;
import com.ruoyi.app.v1.service.ILadingMessageV1Service;
import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.common.utils.file.FileUploadUtils;
import com.sun.image.codec.jpeg.JPEGCodec;
import com.sun.image.codec.jpeg.JPEGImageEncoder;

import jdk.internal.org.xml.sax.InputSource;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringEscapeUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.w3c.dom.Document;
import org.xhtmlrenderer.swing.Java2DRenderer;
import org.xml.sax.SAXException;


import javax.imageio.ImageIO;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;
import java.awt.image.BufferedImage;
import java.io.*;
import java.util.*;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
public class LadingMessageV1ServiceImpl implements ILadingMessageV1Service
{
    @Autowired
    private LadingMessageV1Mapper ladingMessageV1Mapper;

    @Autowired
    private LadingSubscribeMapper ladingSubscribeMapper;


    private static final Logger log = LoggerFactory.getLogger(LadingMessageV1ServiceImpl.class);
    /**
     * 销售员工号权限(棒线)
     *
     * @return
     */
    @Override
    public List<String> getStrValue(String operatorNo) {
        List<String> strValues = ladingMessageV1Mapper.selectOperatorStrvalue(operatorNo);
        List<String> bcStrValues = ladingMessageV1Mapper.selectBCOperatorStrvalue(operatorNo);
        return Stream.concat(strValues.stream(), bcStrValues.stream())
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 查询提单列表
     *
     * @param ladingMessage 权限列表
     * @return 提单
     */
    public List<Map<String, Object>> selectLadingMessageResult(LadingMessageV1 ladingMessage) {
        List<LadingMessageV1> list = ladingMessageV1Mapper.selectLadingMessageResult(ladingMessage);

        //选择未预约的
        if(Optional.ofNullable(ladingMessage.getSelectAll()).isPresent()){
            if (ladingMessage.getSelectAll().equals("0")) {
                LadingSubscribe billQuery = new LadingSubscribe();
                billQuery.setFilterOneMonth(true);
                List<LadingSubscribe> billList = ladingSubscribeMapper.selectLadingSubscribeList(billQuery);
                Set<String> billNos = billList.stream()
                        .map(LadingSubscribe::getBillOfLadingNo)
                        .collect(Collectors.toSet());
                CollectionUtils.filter(list, x -> !billNos.contains(x.getBillOfLadingNo()));
            }
        }

        List<Map<String, Object>> result = new ArrayList<Map<String, Object>>();
        for (LadingMessageV1 item : list) {
            Map<String, Object> map = new HashMap<>();
            map.put("stockName", item.getStockName() == null ? "" : item.getStockName());
            map.put("consignUserCode", item.getConsignUserCode());
            map.put("consignUserName", item.getConsignUserName() == null ? "" : item.getConsignUserName());
            map.put("billOfLadingNo", item.getBillOfLadingNo() == null ? "" : item.getBillOfLadingNo());
            map.put("planWt", item.getPlanWt() == null ? "" : item.getPlanWt());
            map.put("planEndTime", item.getPlanEndTime() == null ? "" : item.getPlanEndTime());
            map.put("billRemark", item.getBillRemark() == null ? "" : item.getBillRemark());
            result.add(map);
        }
        return result;
    }

    /**
     * 查询提单列表
     *
     * @param billOfLadingNo 提单
     * @return 提单
     */
    @DataSource(value = DataSourceType.XCC1)
    public List<Map<String, Object>> selectLadingList(LadingList billOfLadingNo) { return ladingMessageV1Mapper.selectLadingList(billOfLadingNo); }

    /**
     * 查询提单列表
     *
     * @param billOfLadingNo 提单
     * @return 提单
     */
    @DataSource(value = DataSourceType.XCC1)
    public List<Map<String, Object>> selectDriverLading(DriverLading billOfLadingNo) { return ladingMessageV1Mapper.selectDriverLading(billOfLadingNo); }

    /**
     * 查询客商列表
     *
     * @param ladingMessage 权限列表
     * @return 提单
     */
    @DataSource(value = DataSourceType.XCC1)
    public List<Map<String, Object>> selectConsignUserNameResult(LadingMessageV1 ladingMessage) { return ladingMessageV1Mapper.selectConsignUserNameResult(ladingMessage); }


//    @PostConstruct
//    public void graphicsGeneration(){
//        try {
//            // 加载自定义字体文件
//            Resource resource = new ClassPathResource("fonts/wqy-zenhei/wqy-zenhei.ttc");
//            InputStream fis = resource.getInputStream();
//            Font customFont = Font.createFont(Font.TRUETYPE_FONT, fis);
//            // 注册字体
//            GraphicsEnvironment ge = GraphicsEnvironment.getLocalGraphicsEnvironment();
//            ge.registerFont(customFont);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }



    @Override
    public String getUrl(List<Map> list) {
        String url="";

        try {
            String text = "";
            for(Map map : list){
                text += map.get("sgSign") + "&#160;&#160;" + map.get("guige") + "&#160;&#160;" + map.get("planWt")+"吨<br/>";
            }
            log.info("图片转文字：开始");
            // 使用 DocumentBuilder 将 HTML 内容解析为 Document 对象
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document doc = builder.parse(new ByteArrayInputStream(("<html>\n" +
                    "\n" +
                    "<body>\n" +
                    "  <div style='width:100%;height: 74px;'>\n" +
                    "    <img width=\"100%\" height=\"74\" src=\"data:image/png;base64,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\" />\n" +
                    "  </div>\n" +
                    "  <text style='font-size:28px'>"+text+"</text>\n" +
                    "</body>\n" +
                    "</html>").getBytes()));
            Java2DRenderer renderer = new Java2DRenderer(doc,500,400);
            BufferedImage image = renderer.getImage();
            log.info("图片转文字：获取图片结束"+image.toString());
            url = createImage(image);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("图片转文字错误：",e);
        }
        return url;

    }

    public static String createImage(BufferedImage image) throws IOException {
        String url = "";
        try {
            String path = "/usr/local/ruoyi/tmpPic/";
            String name = getImageName()+".jpg";
            log.info("图片转文字：开始写文件");
            File newFileDir = new File(path+name);
            //如果不存在 则创建
            if (!newFileDir.getParentFile().exists())
            {
                newFileDir.getParentFile().mkdirs();
            }
            if (!newFileDir.exists())
            {
                newFileDir.createNewFile();
            }
            log.info("图片转文字：文件创建完毕"+newFileDir.getAbsolutePath());
//            FileOutputStream fos = new FileOutputStream(newFileDir);
//            log.info("图片转文字：fos长度"+fos.toString().length());
//            BufferedOutputStream bos = new BufferedOutputStream(fos);
//            log.info("图片转文字：bos长度"+bos.toString().length());
//            JPEGImageEncoder encoder = JPEGCodec.createJPEGEncoder(bos);
//            log.info("图片转文字：encoder长度"+encoder.toString().length());
//            encoder.encode(image);
//            log.info("图片转文字：encoder长度"+encoder.toString().length());
            ImageIO.write(image, "jpg", newFileDir);
//            bos.close();
//            fos.close();
            log.info("图片转文字：输出到文件完毕");
            FileInputStream is = new FileInputStream(newFileDir);
            log.info("图片转文字：开始上传文件"+is.toString().length());
            MultipartFile multipartFile = new MockMultipartFile(newFileDir.getName(),name,"jpg", is);
            // 上传并返回新文件名称
            String fileName = FileUploadUtils.uploadMinio(multipartFile, "xcPic");
            log.info("图片转文字：上传结束");
             url = "https://ydxt.citicsteel.com:8099/minio/" + fileName;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("图片转文字错误：",e);
        } finally {
            return url;
        }
    }

    /** 图片名生成 **/
    public static String getImageName() {
        //取当前时间的长整形值包含毫秒
        long millis = System.currentTimeMillis();
        //加上三位随机数
        Random random = new Random();
        int end3 = random.nextInt(999);
        //如果不足三位前面补0
        String str = millis + String.format("%03d", end3);
        return str;
    }

}
