{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\leave\\plan\\edit.vue?vue&type=template&id=7971f6a4&scoped=true", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\leave\\plan\\edit.vue", "mtime": 1756084866763}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}