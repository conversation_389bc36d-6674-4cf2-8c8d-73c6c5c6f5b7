<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.app.leave.mapper.LeavePlanMapper">
    
    <resultMap type="LeavePlan" id="LeavePlanResult">
        <result property="id"    column="id"    />
        <result property="applyNo"    column="apply_no"    />
        <result property="planNo"    column="plan_no"    />
        <result property="planType"    column="plan_type"    />
        <result property="businessCategory"    column="business_category"    />
        <result property="measureFlag"    column="measure_flag"    />
        <result property="plannedAmount"    column="planned_amount"    />
        <result property="receiveCompany"    column="receive_company"    />
        <result property="receiveCompanyCode"    column="receive_company_code"    />
        <result property="targetCompany"    column="target_company"    />
        <result property="targetCompanyCode"    column="target_company_code"    />
        <result property="sourceCompany"    column="source_company"    />
        <result property="sourceCompanyCode"    column="source_company_code"    />
        <result property="planReturnTime"    column="plan_return_time"    />
        <result property="realReturnTime"    column="real_return_time"    />
        <result property="monitor"    column="monitor"    />
        <result property="specialManager"    column="special_manager"    />
        <result property="expireTime"    column="expire_time"    />
        <result property="reason"    column="reason"    />
        <result property="itemType"    column="item_type"    />
        <result property="planStatus"    column="plan_status"    />
        <result property="applyTime"    column="apply_time"    />
        <result property="applyWorkNo"    column="apply_work_no"    />
        <result property="factoryApproveTime"    column="factory_approve_time"    />
        <result property="factoryApproveWorkNo"    column="factory_approve_work_no"    />
        <result property="factoryApproveFlag"    column="factory_approve_flag"    />
        <result property="factoryApproveContent"    column="factory_approve_content"    />
        <result property="factorySecApproveFlag"    column="factory_sec_approve_flag"    />
        <result property="factorySecApproveTime"    column="factory_sec_approve_time"    />
        <result property="factorySecApproveWorkNo"    column="factory_sec_approve_work_no"    />
        <result property="factorySecApproveContent"    column="factory_sec_approve_content"    />
        <result property="centerApproveTime"    column="center_approve_time"    />
        <result property="centerApproveWorkNo"    column="center_approve_work_no"    />
        <result property="centerApproveFlag"    column="center_approve_flag"    />
        <result property="centerApproveContent"    column="center_approve_content"    />
        <result property="applyFileUrl"    column="apply_file_url"    />
        <result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="selectLeavePlanVo">
        select id, apply_no, plan_no, plan_type, business_category, measure_flag, planned_amount, receive_company, receive_company_code, target_company, target_company_code, source_company, source_company_code, plan_return_time, real_return_time, monitor, special_manager, expire_time, reason, item_type, plan_status, apply_time, apply_work_no, factory_approve_time, factory_approve_work_no, factory_approve_flag, factory_approve_content, factory_sec_approve_flag, factory_sec_approve_time, factory_sec_approve_work_no, factory_sec_approve_content, center_approve_time, center_approve_work_no, center_approve_flag, center_approve_content, apply_file_url, remark, create_time, create_by, update_time, update_by from leave_plan
    </sql>

    <select id="selectLeavePlanList" parameterType="LeavePlan" resultMap="LeavePlanResult">
        <include refid="selectLeavePlanVo"/>
        <where>  
            <if test="planNo != null  and planNo != ''"> and plan_no = #{planNo}</if>
            <if test="planType != null "> and plan_type = #{planType}</if>
            <if test="businessCategory != null "> and business_category = #{businessCategory}</if>
            <if test="measureFlag != null "> and measure_flag = #{measureFlag}</if>
            <if test="plannedAmount != null "> and planned_amount = #{plannedAmount}</if>
            <if test="receiveCompany != null  and receiveCompany != ''"> and receive_company = #{receiveCompany}</if>
            <if test="receiveCompanyCode != null  and receiveCompanyCode != ''"> and receive_company_code = #{receiveCompanyCode}</if>
            <if test="targetCompany != null  and targetCompany != ''"> and target_company = #{targetCompany}</if>
            <if test="targetCompanyCode != null  and targetCompanyCode != ''"> and target_company_code = #{targetCompanyCode}</if>
            <if test="sourceCompany != null  and sourceCompany != ''"> and source_company = #{sourceCompany}</if>
            <if test="sourceCompanyCode != null  and sourceCompanyCode != ''"> and source_company_code = #{sourceCompanyCode}</if>
            <if test="planReturnTime != null "> and plan_return_time = #{planReturnTime}</if>
            <if test="realReturnTime != null "> and real_return_time = #{realReturnTime}</if>
            <if test="monitor != null  and monitor != ''"> and monitor = #{monitor}</if>
            <if test="specialManager != null  and specialManager != ''"> and special_manager = #{specialManager}</if>
            <if test="expireTime != null "> and expire_time = #{expireTime}</if>
            <if test="reason != null  and reason != ''"> and reason = #{reason}</if>
            <if test="itemType != null "> and item_type = #{itemType}</if>
            <if test="planStatus != null "> and plan_status = #{planStatus}</if>
            <if test="applyTime != null "> and apply_time = #{applyTime}</if>
            <if test="applyWorkNo != null  and applyWorkNo != ''"> and apply_work_no = #{applyWorkNo}</if>
            <if test="factoryApproveTime != null "> and factory_approve_time = #{factoryApproveTime}</if>
            <if test="factoryApproveWorkNo != null  and factoryApproveWorkNo != ''"> and factory_approve_work_no = #{factoryApproveWorkNo}</if>
            <if test="factoryApproveFlag != null "> and factory_approve_flag = #{factoryApproveFlag}</if>
            <if test="factoryApproveContent != null  and factoryApproveContent != ''"> and factory_approve_content = #{factoryApproveContent}</if>
            <if test="factorySecApproveFlag != null "> and factory_sec_approve_flag = #{factorySecApproveFlag}</if>
            <if test="factorySecApproveTime != null "> and factory_sec_approve_time = #{factorySecApproveTime}</if>
            <if test="factorySecApproveWorkNo != null  and factorySecApproveWorkNo != ''"> and factory_sec_approve_work_no = #{factorySecApproveWorkNo}</if>
            <if test="factorySecApproveContent != null  and factorySecApproveContent != ''"> and factory_sec_approve_content = #{factorySecApproveContent}</if>
            <if test="centerApproveTime != null "> and center_approve_time = #{centerApproveTime}</if>
            <if test="centerApproveWorkNo != null  and centerApproveWorkNo != ''"> and center_approve_work_no = #{centerApproveWorkNo}</if>
            <if test="centerApproveFlag != null "> and center_approve_flag = #{centerApproveFlag}</if>
            <if test="centerApproveContent != null  and centerApproveContent != ''"> and center_approve_content = #{centerApproveContent}</if>
            <if test="applyFileUrl != null  and applyFileUrl != ''"> and apply_file_url = #{applyFileUrl}</if>
        </where>
    </select>
    
    <select id="selectLeavePlanById" parameterType="Long" resultMap="LeavePlanResult">
        <include refid="selectLeavePlanVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertLeavePlan" parameterType="LeavePlan" useGeneratedKeys="true" keyProperty="id">
        insert into leave_plan
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="applyNo != null">apply_no,</if>
            <if test="planNo != null">plan_no,</if>
            <if test="planType != null">plan_type,</if>
            <if test="businessCategory != null">business_category,</if>
            <if test="measureFlag != null">measure_flag,</if>
            <if test="plannedAmount != null">planned_amount,</if>
            <if test="receiveCompany != null">receive_company,</if>
            <if test="receiveCompanyCode != null">receive_company_code,</if>
            <if test="targetCompany != null">target_company,</if>
            <if test="targetCompanyCode != null">target_company_code,</if>
            <if test="sourceCompany != null">source_company,</if>
            <if test="sourceCompanyCode != null">source_company_code,</if>
            <if test="planReturnTime != null">plan_return_time,</if>
            <if test="realReturnTime != null">real_return_time,</if>
            <if test="monitor != null">monitor,</if>
            <if test="specialManager != null">special_manager,</if>
            <if test="expireTime != null">expire_time,</if>
            <if test="reason != null">reason,</if>
            <if test="itemType != null">item_type,</if>
            <if test="planStatus != null">plan_status,</if>
            <if test="applyTime != null">apply_time,</if>
            <if test="applyWorkNo != null">apply_work_no,</if>
            <if test="factoryApproveTime != null">factory_approve_time,</if>
            <if test="factoryApproveWorkNo != null">factory_approve_work_no,</if>
            <if test="factoryApproveFlag != null">factory_approve_flag,</if>
            <if test="factoryApproveContent != null">factory_approve_content,</if>
            <if test="factorySecApproveFlag != null">factory_sec_approve_flag,</if>
            <if test="factorySecApproveTime != null">factory_sec_approve_time,</if>
            <if test="factorySecApproveWorkNo != null">factory_sec_approve_work_no,</if>
            <if test="factorySecApproveContent != null">factory_sec_approve_content,</if>
            <if test="centerApproveTime != null">center_approve_time,</if>
            <if test="centerApproveWorkNo != null">center_approve_work_no,</if>
            <if test="centerApproveFlag != null">center_approve_flag,</if>
            <if test="centerApproveContent != null">center_approve_content,</if>
            <if test="applyFileUrl != null">apply_file_url,</if>
            <if test="remark != null">remark,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="applyNo != null">#{applyNo},</if>
            <if test="planNo != null">#{planNo},</if>
            <if test="planType != null">#{planType},</if>
            <if test="businessCategory != null">#{businessCategory},</if>
            <if test="measureFlag != null">#{measureFlag},</if>
            <if test="plannedAmount != null">#{plannedAmount},</if>
            <if test="receiveCompany != null">#{receiveCompany},</if>
            <if test="receiveCompanyCode != null">#{receiveCompanyCode},</if>
            <if test="targetCompany != null">#{targetCompany},</if>
            <if test="targetCompanyCode != null">#{targetCompanyCode},</if>
            <if test="sourceCompany != null">#{sourceCompany},</if>
            <if test="sourceCompanyCode != null">#{sourceCompanyCode},</if>
            <if test="planReturnTime != null">#{planReturnTime},</if>
            <if test="realReturnTime != null">#{realReturnTime},</if>
            <if test="monitor != null">#{monitor},</if>
            <if test="specialManager != null">#{specialManager},</if>
            <if test="expireTime != null">#{expireTime},</if>
            <if test="reason != null">#{reason},</if>
            <if test="itemType != null">#{itemType},</if>
            <if test="planStatus != null">#{planStatus},</if>
            <if test="applyTime != null">#{applyTime},</if>
            <if test="applyWorkNo != null">#{applyWorkNo},</if>
            <if test="factoryApproveTime != null">#{factoryApproveTime},</if>
            <if test="factoryApproveWorkNo != null">#{factoryApproveWorkNo},</if>
            <if test="factoryApproveFlag != null">#{factoryApproveFlag},</if>
            <if test="factoryApproveContent != null">#{factoryApproveContent},</if>
            <if test="factorySecApproveFlag != null">#{factorySecApproveFlag},</if>
            <if test="factorySecApproveTime != null">#{factorySecApproveTime},</if>
            <if test="factorySecApproveWorkNo != null">#{factorySecApproveWorkNo},</if>
            <if test="factorySecApproveContent != null">#{factorySecApproveContent},</if>
            <if test="centerApproveTime != null">#{centerApproveTime},</if>
            <if test="centerApproveWorkNo != null">#{centerApproveWorkNo},</if>
            <if test="centerApproveFlag != null">#{centerApproveFlag},</if>
            <if test="centerApproveContent != null">#{centerApproveContent},</if>
            <if test="applyFileUrl != null">#{applyFileUrl},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
         </trim>
    </insert>

    <update id="updateLeavePlan" parameterType="LeavePlan">
        update leave_plan
        <trim prefix="SET" suffixOverrides=",">
            <if test="applyNo != null">apply_no = #{applyNo},</if>
            <if test="planNo != null">plan_no = #{planNo},</if>
            <if test="planType != null">plan_type = #{planType},</if>
            <if test="businessCategory != null">business_category = #{businessCategory},</if>
            <if test="measureFlag != null">measure_flag = #{measureFlag},</if>
            <if test="plannedAmount != null">planned_amount = #{plannedAmount},</if>
            <if test="receiveCompany != null">receive_company = #{receiveCompany},</if>
            <if test="receiveCompanyCode != null">receive_company_code = #{receiveCompanyCode},</if>
            <if test="targetCompany != null">target_company = #{targetCompany},</if>
            <if test="targetCompanyCode != null">target_company_code = #{targetCompanyCode},</if>
            <if test="sourceCompany != null">source_company = #{sourceCompany},</if>
            <if test="sourceCompanyCode != null">source_company_code = #{sourceCompanyCode},</if>
            <if test="planReturnTime != null">plan_return_time = #{planReturnTime},</if>
            <if test="realReturnTime != null">real_return_time = #{realReturnTime},</if>
            <if test="monitor != null">monitor = #{monitor},</if>
            <if test="specialManager != null">special_manager = #{specialManager},</if>
            <if test="expireTime != null">expire_time = #{expireTime},</if>
            <if test="reason != null">reason = #{reason},</if>
            <if test="itemType != null">item_type = #{itemType},</if>
            <if test="planStatus != null">plan_status = #{planStatus},</if>
            <if test="applyTime != null">apply_time = #{applyTime},</if>
            <if test="applyWorkNo != null">apply_work_no = #{applyWorkNo},</if>
            <if test="factoryApproveTime != null">factory_approve_time = #{factoryApproveTime},</if>
            <if test="factoryApproveWorkNo != null">factory_approve_work_no = #{factoryApproveWorkNo},</if>
            <if test="factoryApproveFlag != null">factory_approve_flag = #{factoryApproveFlag},</if>
            <if test="factoryApproveContent != null">factory_approve_content = #{factoryApproveContent},</if>
            <if test="factorySecApproveFlag != null">factory_sec_approve_flag = #{factorySecApproveFlag},</if>
            <if test="factorySecApproveTime != null">factory_sec_approve_time = #{factorySecApproveTime},</if>
            <if test="factorySecApproveWorkNo != null">factory_sec_approve_work_no = #{factorySecApproveWorkNo},</if>
            <if test="factorySecApproveContent != null">factory_sec_approve_content = #{factorySecApproveContent},</if>
            <if test="centerApproveTime != null">center_approve_time = #{centerApproveTime},</if>
            <if test="centerApproveWorkNo != null">center_approve_work_no = #{centerApproveWorkNo},</if>
            <if test="centerApproveFlag != null">center_approve_flag = #{centerApproveFlag},</if>
            <if test="centerApproveContent != null">center_approve_content = #{centerApproveContent},</if>
            <if test="applyFileUrl != null">apply_file_url = #{applyFileUrl},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteLeavePlanById" parameterType="Long">
        delete from leave_plan where id = #{id}
    </delete>

    <delete id="deleteLeavePlanByIds" parameterType="String">
        delete from leave_plan where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
</mapper>