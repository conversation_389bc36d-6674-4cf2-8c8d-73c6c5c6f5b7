package com.ruoyi.app.leave.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.app.leave.mapper.LeaveTaskMapper;
import com.ruoyi.app.leave.domain.LeaveTask;
import com.ruoyi.app.leave.service.ILeaveTaskService;

/**
 * 出门证任务Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-03-13
 */
@Service
public class LeaveTaskServiceImpl implements ILeaveTaskService 
{
    @Autowired
    private LeaveTaskMapper leaveTaskMapper;

    /**
     * 查询出门证任务
     * 
     * @param id 出门证任务ID
     * @return 出门证任务
     */
    @Override
    public LeaveTask selectLeaveTaskById(Long id)
    {
        return leaveTaskMapper.selectLeaveTaskById(id);
    }

    /**
     * 查询出门证任务列表
     * 
     * @param leaveTask 出门证任务
     * @return 出门证任务
     */
    @Override
    public List<LeaveTask> selectLeaveTaskList(LeaveTask leaveTask)
    {
        return leaveTaskMapper.selectLeaveTaskList(leaveTask);
    }

    /**
     * 新增出门证任务
     * 
     * @param leaveTask 出门证任务
     * @return 结果
     */
    @Override
    public int insertLeaveTask(LeaveTask leaveTask)
    {
        leaveTask.setCreateTime(DateUtils.getNowDate());
        return leaveTaskMapper.insertLeaveTask(leaveTask);
    }

    /**
     * 修改出门证任务
     * 
     * @param leaveTask 出门证任务
     * @return 结果
     */
    @Override
    public int updateLeaveTask(LeaveTask leaveTask)
    {
        leaveTask.setUpdateTime(DateUtils.getNowDate());
        return leaveTaskMapper.updateLeaveTask(leaveTask);
    }

    /**
     * 批量删除出门证任务
     * 
     * @param ids 需要删除的出门证任务ID
     * @return 结果
     */
    @Override
    public int deleteLeaveTaskByIds(Long[] ids)
    {
        return leaveTaskMapper.deleteLeaveTaskByIds(ids);
    }

    /**
     * 删除出门证任务信息
     * 
     * @param id 出门证任务ID
     * @return 结果
     */
    @Override
    public int deleteLeaveTaskById(Long id)
    {
        return leaveTaskMapper.deleteLeaveTaskById(id);
    }
}
