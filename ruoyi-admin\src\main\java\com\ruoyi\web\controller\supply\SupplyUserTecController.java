package com.ruoyi.web.controller.supply;

import java.io.*;
import java.util.List;
import java.util.Map;

import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.data.Pictures;
import com.jcraft.jsch.JSchException;
import com.jcraft.jsch.SftpException;
import com.ruoyi.app.supply.domain.SupplyUserFac;
import com.ruoyi.app.supply.domain.SupplyUserFile;
import com.ruoyi.app.supply.service.ISupplyUserFacService;
import com.ruoyi.app.supply.service.ISupplyUserFileService;
import com.ruoyi.common.ftp.SFtpUtil;
import com.ruoyi.common.utils.JSONChangeUtil;
import com.ruoyi.common.utils.StringUtils;
import fr.opensagres.poi.xwpf.converter.pdf.PdfConverter;
import fr.opensagres.poi.xwpf.converter.pdf.PdfOptions;
import org.apache.http.entity.ContentType;


import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.app.supply.domain.SupplyUserTec;
import com.ruoyi.app.supply.service.ISupplyUserTecService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

import static com.google.common.io.Files.getFileExtension;
import static com.ruoyi.app.templateFile.util.TemplateFileUtil.getNetUrlHttps;

/**
 * 三级教育卡Controller
 *
 * <AUTHOR>
 * @date 2023-3-27
 */
@RestController
@RequestMapping("/web/supply/usertec")
public class SupplyUserTecController extends BaseController {
    @Autowired
    private ISupplyUserTecService supplyUserTecService;

    /**
     * 查询三级教育卡列表
     */
    @GetMapping("/get/list")
    public TableDataInfo getList(SupplyUserTec supplyUserTec) {
        startPage();
        List<SupplyUserTec> list = supplyUserTecService.selectSupplyUserTecList(supplyUserTec);
        return getDataTable(list);
    }

    /**
     * 导出三级教育卡列表
     */
    @Log(title = "三级教育卡", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public AjaxResult export(SupplyUserTec supplyUserTec) {
        List<SupplyUserTec> list = supplyUserTecService.selectSupplyUserTecList(supplyUserTec);
        ExcelUtil<SupplyUserTec> util = new ExcelUtil<SupplyUserTec>(SupplyUserTec.class);
        return util.exportExcel(list, "三级教育卡数据");
    }

    /**
     * 获取三级教育卡详细信息
     */
    @GetMapping(value = "/get/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return success(supplyUserTecService.selectSupplyUserTecById(id));
    }

    /**
     * 新增三级教育卡
     */
    @Log(title = "三级教育卡", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public AjaxResult add(@RequestBody SupplyUserTec supplyUserTec) {
        return toAjax(supplyUserTecService.insertSupplyUserTec(supplyUserTec));
    }
    @Autowired
    private ISupplyUserFacService supplyUserFacService;
    @Autowired
    private ISupplyUserFileService supplyUserFileService;

    public static void convertDocxToPdf(File wordFile, File pdfFile) throws Exception {
        // 1. 加载Word文档
        try (FileInputStream in = new FileInputStream(wordFile);
             XWPFDocument document = new XWPFDocument(in)) {

            // 2. 创建PDF选项
            PdfOptions options = PdfOptions.create();

            // 3. 执行转换
            try (OutputStream out = new FileOutputStream(pdfFile)) {
                PdfConverter.getInstance().convert(document, out, options);
            }
        }
    }
    /**
     * 修改三级教育卡
     */
    @Log(title = "三级教育卡", businessType = BusinessType.UPDATE)
    @PutMapping("/update")
    public AjaxResult update(@RequestBody SupplyUserTec supplyUserTec) throws Exception {
        int res = supplyUserTecService.updateSupplyUserTec(supplyUserTec);
        SupplyUserTec old = supplyUserTecService.selectSupplyUserTecById(supplyUserTec.getId());
        if(supplyUserTec.getState().equals("1")&&(!old.getIsFile().equals("1"))){
            File template = getNetUrlHttps("https://ydxt.citicsteel.com:8099/minio/xctg/template/2025/08/14/53d955bd-9453-4914-9719-b9e74db09dc6.docx");
            SupplyUserTec etc = supplyUserTecService.selectSupplyUserTecById(supplyUserTec.getId());
            Integer userid = etc.getUserId();
            Map dataMap = JSONChangeUtil.toMapWithJackson(etc);

            dataMap.put("firEducatedUrl", Pictures.ofUrl(etc.getFirEducatedUrl())
                    .size(80, 30).create());
            dataMap.put("firEducatorUrl", Pictures.ofUrl(etc.getFirEducatorUrl())
                    .size(80, 30).create());
            dataMap.put("secEducatedUrl", Pictures.ofUrl(etc.getSecEducatedUrl())
                    .size(80, 30).create());
            dataMap.put("secEducatorUrl", Pictures.ofUrl(etc.getSecEducatorUrl())
                    .size(80, 30).create());
            dataMap.put("thiEducatedUrl", Pictures.ofUrl(etc.getThiEducatedUrl())
                    .size(80, 30).create());
            dataMap.put("thiEducatorUrl", Pictures.ofUrl(etc.getThiEducatorUrl())
                    .size(80, 30).create());

            XWPFTemplate xwpfTemplate = XWPFTemplate.compile(template).render(dataMap);
            String fileName = etc.getUserName()+"_三级教育卡.docx";
            File wordFile = new File(fileName);
//            File pdfFile = new File(etc.getUserName()+"_三级教育卡.pdf");

            try {
                xwpfTemplate.writeToFile(fileName);
//                convertDocxToPdf(wordFile, pdfFile);
                FileInputStream input = new FileInputStream(wordFile);
                MultipartFile newFile = new MockMultipartFile("file", fileName, ContentType.APPLICATION_OCTET_STREAM.toString(), input);


                // 生成当前日期字符串（不补零）
                java.time.LocalDate currentDate = java.time.LocalDate.now();
                String dateStr = currentDate.getYear() + "-" + currentDate.getMonthValue() + "-" + currentDate.getDayOfMonth(); // yyyy-M-d

                // 生成时间戳
                java.time.LocalDateTime now = java.time.LocalDateTime.now();
                String timestamp = now.format(java.time.format.DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS"));

                // 生成文件名：Docx-yyyyMMddHHmmssSSSXXX_三级教育卡.docx
                String uniqueFileName = "Docx-" + timestamp +  ".docx";

                // 构建完整路径：使用更简单的目录结构
                String remotePath = "File/Supply/Doc/" + dateStr + "/" + uniqueFileName;

                // 创建 SFtpUtil 实例并连接
                SFtpUtil sftpUtil = new SFtpUtil();
                sftpUtil.connect();

                try {
                    // 上传文件到 SFTP 服务器
                    InputStream inputStream = newFile.getInputStream();
                    sftpUtil.upload(inputStream, "/" + remotePath);

                    // 根据userid查询SupplyUserFac，获取userfacid和userdeptname
                    SupplyUserFac userFac = null;
                    if (userid != null) {
                        SupplyUserFac query = new SupplyUserFac();
                        query.setUserId(userid);
                        List<SupplyUserFac> userFacList = supplyUserFacService.selectSupplyUserFacList(query);
                        if (!userFacList.isEmpty()) {
                            userFac = userFacList.get(0);
                        }
                    }

                    // 创建 SupplyUserFile 记录
                    SupplyUserFile supplyUserFile = new SupplyUserFile();
                    supplyUserFile.setUserid(etc.getUserId());
                    supplyUserFile.setUsercode(etc.getUserCode());
                    supplyUserFile.setUsername(etc.getUserName());
                    supplyUserFile.setSupplycode(etc.getSupplyCode());
                    supplyUserFile.setSupplyname(etc.getSupplyName());
                    supplyUserFile.setIdcard(etc.getIdcard());

                    supplyUserFile.setFilename(fileName);
                    supplyUserFile.setFilesource(remotePath);
                    supplyUserFile.setFiletype(1); // 设置为1
                    supplyUserFile.setFormat(getFileExtension(uniqueFileName));
                    supplyUserFile.setState(1); // 设置为1

                    // 如果查询到SupplyUserFac，填充userfacid和userdeptname
                    if (userFac != null) {
                        supplyUserFile.setUserfacid(userFac.getId());
                        // 如果前端没有传入userdeptname，则使用SupplyUserFac中的userDeptName
                    }

                    // 保存到数据库
                    int result = supplyUserFileService.insertSupplyUserFile(supplyUserFile);

                    if (result > 0) {
                        etc.setIsFile("1");
                        supplyUserTecService.updateSupplyUserTec(etc);
                    }

                } finally {
                    // 断开 SFTP 连接
                    sftpUtil.disconnect();
                }
            } catch (IOException | JSchException | SftpException e) {
                throw new RuntimeException(e);
            }finally {
                wordFile.delete();
//                pdfFile.delete();
            }
        }

        return toAjax(res);
    }

    /**
     * 删除三级教育卡
     */
    @Log(title = "三级教育卡", businessType = BusinessType.DELETE)
    @DeleteMapping("/delete/{ids}")
    public AjaxResult delete(@PathVariable Integer[] ids) {
        return toAjax(supplyUserTecService.deleteSupplyUserTecByIds(ids));
    }
} 