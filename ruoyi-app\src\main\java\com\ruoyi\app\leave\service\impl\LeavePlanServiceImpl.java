package com.ruoyi.app.leave.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.app.leave.mapper.LeavePlanMapper;
import com.ruoyi.app.leave.domain.LeavePlan;
import com.ruoyi.app.leave.service.ILeavePlanService;

/**
 * 出门证计划申请Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-03-13
 */
@Service
public class LeavePlanServiceImpl implements ILeavePlanService 
{
    @Autowired
    private LeavePlanMapper leavePlanMapper;

    /**
     * 查询出门证计划申请
     * 
     * @param id 出门证计划申请ID
     * @return 出门证计划申请
     */
    @Override
    public LeavePlan selectLeavePlanById(Long id)
    {
        return leavePlanMapper.selectLeavePlanById(id);
    }

    /**
     * 查询出门证计划申请列表
     * 
     * @param leavePlan 出门证计划申请
     * @return 出门证计划申请
     */
    @Override
    public List<LeavePlan> selectLeavePlanList(LeavePlan leavePlan)
    {
        return leavePlanMapper.selectLeavePlanList(leavePlan);
    }

    /**
     * 新增出门证计划申请
     * 
     * @param leavePlan 出门证计划申请
     * @return 结果
     */
    @Override
    public int insertLeavePlan(LeavePlan leavePlan)
    {
        leavePlan.setCreateTime(DateUtils.getNowDate());
        return leavePlanMapper.insertLeavePlan(leavePlan);
    }

    /**
     * 修改出门证计划申请
     * 
     * @param leavePlan 出门证计划申请
     * @return 结果
     */
    @Override
    public int updateLeavePlan(LeavePlan leavePlan)
    {
        leavePlan.setUpdateTime(DateUtils.getNowDate());
        return leavePlanMapper.updateLeavePlan(leavePlan);
    }

    /**
     * 批量删除出门证计划申请
     * 
     * @param ids 需要删除的出门证计划申请ID
     * @return 结果
     */
    @Override
    public int deleteLeavePlanByIds(Long[] ids)
    {
        return leavePlanMapper.deleteLeavePlanByIds(ids);
    }

    /**
     * 删除出门证计划申请信息
     * 
     * @param id 出门证计划申请ID
     * @return 结果
     */
    @Override
    public int deleteLeavePlanById(Long id)
    {
        return leavePlanMapper.deleteLeavePlanById(id);
    }
}
