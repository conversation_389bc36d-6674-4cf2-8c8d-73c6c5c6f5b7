{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\suppPunishment\\punishmentBasis-module.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\suppPunishment\\punishmentBasis-module.vue", "mtime": 1756099891103}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJQdW5pc2htZW50QmFzaXNEaWFsb2ciLA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICAvLyDmmK/lkKbmmL7npLrlvLnlh7rlsYINCiAgICAgIHZpc2libGU6IGZhbHNlLA0KICAgICAgLy8g6YCJ5Lit55qE5L6d5o2u57G75Z6LDQogICAgICBzZWxlY3RlZEJhc2lzVHlwZTogJycsDQogICAgICAvLyDotKjph4/lvILorq7ljZXlj7cNCiAgICAgIHF1YWxpdHlOdW1iZXI6ICcnLA0KICAgICAgLy8g5Yi25bqm5ZCN56ewDQogICAgICBzeXN0ZW1OYW1lOiAnJywNCiAgICAgIC8vIOaKpeWRiuWQjeensA0KICAgICAgcmVwb3J0TmFtZTogJycsDQogICAgICAvLyDlt6Hmo4DlpITnvZrljZXlj7cNCiAgICAgIGluc3BlY3Rpb25OdW1iZXI6ICcnLA0KICAgICAgLy8g5a6J566h5aSE572a5Y2V5Y+3DQogICAgICBzYWZldHlOdW1iZXI6ICcnLA0KICAgICAgLy8g5L6d5o2u5YaF5a65DQogICAgICBiYXNpc0NvbnRlbnQ6ICcnLA0KICAgICAgLy8g6aKE6KeI5paH5pysDQogICAgICBwcmV2aWV3VGV4dDogJycNCiAgICB9Ow0KICB9LA0KICBtZXRob2RzOiB7DQogICAgLyoqIOaYvuekuuW8ueeqlyAqLw0KICAgIHNob3coY3VycmVudFZhbHVlID0gJycpIHsNCiAgICAgIHRoaXMudmlzaWJsZSA9IHRydWU7DQogICAgICB0aGlzLnBhcnNlQ3VycmVudFZhbHVlKGN1cnJlbnRWYWx1ZSk7DQogICAgICB0aGlzLnVwZGF0ZVByZXZpZXcoKTsNCiAgICAgIC8vIOehruS/neW8ueeql+WujOWFqOaJk+W8gOWQjuWGjei/m+ihjOWFtuS7luaTjeS9nA0KICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICBjb25zb2xlLmxvZygn5by556qX5bey5pi+56S677yM5b2T5YmN5pWw5o2u77yaJywgew0KICAgICAgICAgIHNlbGVjdGVkQmFzaXNUeXBlOiB0aGlzLnNlbGVjdGVkQmFzaXNUeXBlLA0KICAgICAgICAgIHF1YWxpdHlOdW1iZXI6IHRoaXMucXVhbGl0eU51bWJlciwNCiAgICAgICAgICBzeXN0ZW1OYW1lOiB0aGlzLnN5c3RlbU5hbWUsDQogICAgICAgICAgcmVwb3J0TmFtZTogdGhpcy5yZXBvcnROYW1lLA0KICAgICAgICAgIGluc3BlY3Rpb25OdW1iZXI6IHRoaXMuaW5zcGVjdGlvbk51bWJlciwNCiAgICAgICAgICBzYWZldHlOdW1iZXI6IHRoaXMuc2FmZXR5TnVtYmVyLA0KICAgICAgICAgIGJhc2lzQ29udGVudDogdGhpcy5iYXNpc0NvbnRlbnQNCiAgICAgICAgfSk7DQogICAgICB9KTsNCiAgICB9LA0KICAgIA0KICAgIC8qKiDpmpDol4/lvLnnqpcgKi8NCiAgICBoaWRlKCkgew0KICAgICAgdGhpcy52aXNpYmxlID0gZmFsc2U7DQogICAgfSwNCiAgICANCiAgICAvKiog5YWz6Zet5by556qXICovDQogICAgaGFuZGxlQ2xvc2UoKSB7DQogICAgICB0aGlzLnZpc2libGUgPSBmYWxzZTsNCiAgICAgIHRoaXMucmVzZXQoKTsNCiAgICB9LA0KICAgIA0KICAgIC8qKiDph43nva7mlbDmja4gKi8NCiAgICByZXNldCgpIHsNCiAgICAgIHRoaXMuc2VsZWN0ZWRCYXNpc1R5cGUgPSAnJzsNCiAgICAgIHRoaXMucXVhbGl0eU51bWJlciA9ICcnOw0KICAgICAgdGhpcy5zeXN0ZW1OYW1lID0gJyc7DQogICAgICB0aGlzLnJlcG9ydE5hbWUgPSAnJzsNCiAgICAgIHRoaXMuaW5zcGVjdGlvbk51bWJlciA9ICcnOw0KICAgICAgdGhpcy5zYWZldHlOdW1iZXIgPSAnJzsNCiAgICAgIHRoaXMuYmFzaXNDb250ZW50ID0gJyc7DQogICAgICB0aGlzLnByZXZpZXdUZXh0ID0gJyc7DQogICAgfSwNCiAgICANCiAgICAvKiog6Kej5p6Q5b2T5YmN5YC8ICovDQogICAgcGFyc2VDdXJyZW50VmFsdWUodmFsdWUpIHsNCiAgICAgIGlmICghdmFsdWUpIHsNCiAgICAgICAgdGhpcy5yZXNldCgpOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQogICAgICANCiAgICAgIC8vIOWwneivleino+aekOeOsOacieeahOS+neaNruWGheWuuQ0KICAgICAgaWYgKHZhbHVlLmluY2x1ZGVzKCfotKjph4/lvILorq7ljZXlj7fvvJonKSkgew0KICAgICAgICB0aGlzLnNlbGVjdGVkQmFzaXNUeXBlID0gJ3F1YWxpdHknOw0KICAgICAgICBjb25zdCBtYXRjaCA9IHZhbHVlLm1hdGNoKC/otKjph4/lvILorq7ljZXlj7fvvJooW17vvJtcbl0qKS8pOw0KICAgICAgICBpZiAobWF0Y2gpIHsNCiAgICAgICAgICB0aGlzLnF1YWxpdHlOdW1iZXIgPSBtYXRjaFsxXS50cmltKCk7DQogICAgICAgIH0NCiAgICAgIH0gZWxzZSBpZiAodmFsdWUuaW5jbHVkZXMoJ+WItuW6puWQjeensO+8micpKSB7DQogICAgICAgIHRoaXMuc2VsZWN0ZWRCYXNpc1R5cGUgPSAnc3lzdGVtJzsNCiAgICAgICAgY29uc3QgbWF0Y2ggPSB2YWx1ZS5tYXRjaCgv5Yi25bqm5ZCN56ew77yaKFte77ybXG5dKikvKTsNCiAgICAgICAgaWYgKG1hdGNoKSB7DQogICAgICAgICAgdGhpcy5zeXN0ZW1OYW1lID0gbWF0Y2hbMV0udHJpbSgpOw0KICAgICAgICB9DQogICAgICB9IGVsc2UgaWYgKHZhbHVlLmluY2x1ZGVzKCfmiqXlkYrvvJonKSkgew0KICAgICAgICB0aGlzLnNlbGVjdGVkQmFzaXNUeXBlID0gJ3JlcG9ydCc7DQogICAgICAgIGNvbnN0IG1hdGNoID0gdmFsdWUubWF0Y2goL+aKpeWRiu+8mihbXu+8m1xuXSopLyk7DQogICAgICAgIGlmIChtYXRjaCkgew0KICAgICAgICAgIHRoaXMucmVwb3J0TmFtZSA9IG1hdGNoWzFdLnRyaW0oKTsNCiAgICAgICAgfQ0KICAgICAgfSBlbHNlIGlmICh2YWx1ZS5pbmNsdWRlcygn5beh5qOA5aSE572a5Y2V5Y+377yaJykpIHsNCiAgICAgICAgdGhpcy5zZWxlY3RlZEJhc2lzVHlwZSA9ICdpbnNwZWN0aW9uJzsNCiAgICAgICAgY29uc3QgbWF0Y2ggPSB2YWx1ZS5tYXRjaCgv5beh5qOA5aSE572a5Y2V5Y+377yaKFte77ybXG5dKikvKTsNCiAgICAgICAgaWYgKG1hdGNoKSB7DQogICAgICAgICAgdGhpcy5pbnNwZWN0aW9uTnVtYmVyID0gbWF0Y2hbMV0udHJpbSgpOw0KICAgICAgICB9DQogICAgICB9IGVsc2UgaWYgKHZhbHVlLmluY2x1ZGVzKCflronnrqHlpITnvZrljZXlj7fvvJonKSkgew0KICAgICAgICB0aGlzLnNlbGVjdGVkQmFzaXNUeXBlID0gJ3NhZmV0eSc7DQogICAgICAgIGNvbnN0IG1hdGNoID0gdmFsdWUubWF0Y2goL+WuieeuoeWkhOe9muWNleWPt++8mihbXu+8m1xuXSopLyk7DQogICAgICAgIGlmIChtYXRjaCkgew0KICAgICAgICAgIHRoaXMuc2FmZXR5TnVtYmVyID0gbWF0Y2hbMV0udHJpbSgpOw0KICAgICAgICB9DQogICAgICB9DQogICAgICANCiAgICAgIC8vIOino+aekOS+neaNruWGheWuuQ0KICAgICAgY29uc3QgY29udGVudE1hdGNoID0gdmFsdWUubWF0Y2goL+S+neaNruWGheWuue+8mihbXl0qKS8pOw0KICAgICAgaWYgKGNvbnRlbnRNYXRjaCkgew0KICAgICAgICB0aGlzLmJhc2lzQ29udGVudCA9IGNvbnRlbnRNYXRjaFsxXS50cmltKCk7DQogICAgICB9IGVsc2Ugew0KICAgICAgICAvLyDlpoLmnpzmsqHmnInmib7liLDkvp3mja7lhoXlrrnmoIfor4bvvIzlsIbmlbTkuKrlhoXlrrnkvZzkuLrkvp3mja7lhoXlrrkNCiAgICAgICAgdGhpcy5iYXNpc0NvbnRlbnQgPSB2YWx1ZTsNCiAgICAgIH0NCiAgICB9LA0KICAgIA0KICAgIC8qKiDkvp3mja7nsbvlnovlj5jljJYgKi8NCiAgICBoYW5kbGVCYXNpc1R5cGVDaGFuZ2UoKSB7DQogICAgICB0aGlzLnVwZGF0ZVByZXZpZXcoKTsNCiAgICB9LA0KICAgIA0KICAgIC8qKiDmm7TmlrDkvp3mja7mlofmnKwgKi8NCiAgICB1cGRhdGVCYXNpc1RleHQoKSB7DQogICAgICB0aGlzLnVwZGF0ZVByZXZpZXcoKTsNCiAgICB9LA0KDQogICAgLyoqIOeCueWHu+i0qOmHj+W8guiuruWNleWPt+i+k+WFpeahhuaXtuiHquWKqOmAieS4rSAqLw0KICAgIGNoZWNrUXVhbGl0eSgpIHsNCiAgICAgIGlmICh0aGlzLnNlbGVjdGVkQmFzaXNUeXBlICE9PSAncXVhbGl0eScpIHsNCiAgICAgICAgdGhpcy5zZWxlY3RlZEJhc2lzVHlwZSA9ICdxdWFsaXR5JzsNCiAgICAgICAgdGhpcy51cGRhdGVQcmV2aWV3KCk7DQogICAgICB9DQogICAgfSwNCg0KICAgIC8qKiDngrnlh7vliLbluqblkI3np7DovpPlhaXmoYbml7boh6rliqjpgInkuK0gKi8NCiAgICBjaGVja1N5c3RlbSgpIHsNCiAgICAgIGlmICh0aGlzLnNlbGVjdGVkQmFzaXNUeXBlICE9PSAnc3lzdGVtJykgew0KICAgICAgICB0aGlzLnNlbGVjdGVkQmFzaXNUeXBlID0gJ3N5c3RlbSc7DQogICAgICAgIHRoaXMudXBkYXRlUHJldmlldygpOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvKiog54K55Ye75oql5ZGK6L6T5YWl5qGG5pe26Ieq5Yqo6YCJ5LitICovDQogICAgY2hlY2tSZXBvcnQoKSB7DQogICAgICBpZiAodGhpcy5zZWxlY3RlZEJhc2lzVHlwZSAhPT0gJ3JlcG9ydCcpIHsNCiAgICAgICAgdGhpcy5zZWxlY3RlZEJhc2lzVHlwZSA9ICdyZXBvcnQnOw0KICAgICAgICB0aGlzLnVwZGF0ZVByZXZpZXcoKTsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLyoqIOeCueWHu+W3oeajgOWkhOe9muWNleWPt+i+k+WFpeahhuaXtuiHquWKqOmAieS4rSAqLw0KICAgIGNoZWNrSW5zcGVjdGlvbigpIHsNCiAgICAgIGlmICh0aGlzLnNlbGVjdGVkQmFzaXNUeXBlICE9PSAnaW5zcGVjdGlvbicpIHsNCiAgICAgICAgdGhpcy5zZWxlY3RlZEJhc2lzVHlwZSA9ICdpbnNwZWN0aW9uJzsNCiAgICAgICAgdGhpcy51cGRhdGVQcmV2aWV3KCk7DQogICAgICB9DQogICAgfSwNCg0KICAgIC8qKiDngrnlh7vlronnrqHlpITnvZrljZXlj7fovpPlhaXmoYbml7boh6rliqjpgInkuK0gKi8NCiAgICBjaGVja1NhZmV0eSgpIHsNCiAgICAgIGlmICh0aGlzLnNlbGVjdGVkQmFzaXNUeXBlICE9PSAnc2FmZXR5Jykgew0KICAgICAgICB0aGlzLnNlbGVjdGVkQmFzaXNUeXBlID0gJ3NhZmV0eSc7DQogICAgICAgIHRoaXMudXBkYXRlUHJldmlldygpOw0KICAgICAgfQ0KICAgIH0sDQogICAgDQogICAgLyoqIOabtOaWsOmihOiniCAqLw0KICAgIHVwZGF0ZVByZXZpZXcoKSB7DQogICAgICBjb25zdCBwYXJ0cyA9IFtdOw0KDQogICAgICAvLyDmt7vliqDpgInkuK3nmoTkvp3mja7nsbvlnovkv6Hmga8NCiAgICAgIGlmICh0aGlzLnNlbGVjdGVkQmFzaXNUeXBlID09PSAncXVhbGl0eScgJiYgdGhpcy5xdWFsaXR5TnVtYmVyKSB7DQogICAgICAgIHBhcnRzLnB1c2goYOi0qOmHj+W8guiuruWNleWPt++8miR7dGhpcy5xdWFsaXR5TnVtYmVyfWApOw0KICAgICAgfSBlbHNlIGlmICh0aGlzLnNlbGVjdGVkQmFzaXNUeXBlID09PSAnc3lzdGVtJyAmJiB0aGlzLnN5c3RlbU5hbWUpIHsNCiAgICAgICAgcGFydHMucHVzaChg5Yi25bqm5ZCN56ew77yaJHt0aGlzLnN5c3RlbU5hbWV9YCk7DQogICAgICB9IGVsc2UgaWYgKHRoaXMuc2VsZWN0ZWRCYXNpc1R5cGUgPT09ICdyZXBvcnQnICYmIHRoaXMucmVwb3J0TmFtZSkgew0KICAgICAgICBwYXJ0cy5wdXNoKGDmiqXlkYrvvJoke3RoaXMucmVwb3J0TmFtZX1gKTsNCiAgICAgIH0gZWxzZSBpZiAodGhpcy5zZWxlY3RlZEJhc2lzVHlwZSA9PT0gJ2luc3BlY3Rpb24nICYmIHRoaXMuaW5zcGVjdGlvbk51bWJlcikgew0KICAgICAgICBwYXJ0cy5wdXNoKGDlt6Hmo4DlpITnvZrljZXlj7fvvJoke3RoaXMuaW5zcGVjdGlvbk51bWJlcn1gKTsNCiAgICAgIH0gZWxzZSBpZiAodGhpcy5zZWxlY3RlZEJhc2lzVHlwZSA9PT0gJ3NhZmV0eScgJiYgdGhpcy5zYWZldHlOdW1iZXIpIHsNCiAgICAgICAgcGFydHMucHVzaChg5a6J566h5aSE572a5Y2V5Y+377yaJHt0aGlzLnNhZmV0eU51bWJlcn1gKTsNCiAgICAgIH0NCg0KICAgICAgLy8g5re75Yqg5L6d5o2u5YaF5a65DQogICAgICBpZiAodGhpcy5iYXNpc0NvbnRlbnQpIHsNCiAgICAgICAgcGFydHMucHVzaChg5L6d5o2u5YaF5a6577yaJHt0aGlzLmJhc2lzQ29udGVudH1gKTsNCiAgICAgIH0NCg0KICAgICAgdGhpcy5wcmV2aWV3VGV4dCA9IHBhcnRzLmpvaW4oJ++8mycpOw0KDQogICAgICBjb25zb2xlLmxvZygn6aKE6KeI5pu05paw77yaJywgew0KICAgICAgICBzZWxlY3RlZEJhc2lzVHlwZTogdGhpcy5zZWxlY3RlZEJhc2lzVHlwZSwNCiAgICAgICAgcXVhbGl0eU51bWJlcjogdGhpcy5xdWFsaXR5TnVtYmVyLA0KICAgICAgICBzeXN0ZW1OYW1lOiB0aGlzLnN5c3RlbU5hbWUsDQogICAgICAgIHJlcG9ydE5hbWU6IHRoaXMucmVwb3J0TmFtZSwNCiAgICAgICAgaW5zcGVjdGlvbk51bWJlcjogdGhpcy5pbnNwZWN0aW9uTnVtYmVyLA0KICAgICAgICBzYWZldHlOdW1iZXI6IHRoaXMuc2FmZXR5TnVtYmVyLA0KICAgICAgICBiYXNpc0NvbnRlbnQ6IHRoaXMuYmFzaXNDb250ZW50LA0KICAgICAgICBwcmV2aWV3VGV4dDogdGhpcy5wcmV2aWV3VGV4dA0KICAgICAgfSk7DQogICAgfSwNCiAgICANCiAgICAvKiog56Gu6K6k6YCJ5oupICovDQogICAgaGFuZGxlQ29uZmlybSgpIHsNCiAgICAgIHRoaXMudXBkYXRlUHJldmlldygpOw0KDQogICAgICAvLyDpqozor4HmmK/lkKbloavlhpnkuoblv4XopoHkv6Hmga8NCiAgICAgIGlmICghdGhpcy5zZWxlY3RlZEJhc2lzVHlwZSkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+ivt+mAieaLqeS+neaNruexu+WeiycpOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQoNCiAgICAgIGlmICh0aGlzLnNlbGVjdGVkQmFzaXNUeXBlID09PSAncXVhbGl0eScgJiYgIXRoaXMucXVhbGl0eU51bWJlcikgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+ivt+i+k+WFpei0qOmHj+W8guiuruWNleWPtycpOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQoNCiAgICAgIGlmICh0aGlzLnNlbGVjdGVkQmFzaXNUeXBlID09PSAnc3lzdGVtJyAmJiAhdGhpcy5zeXN0ZW1OYW1lKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn6K+36L6T5YWl5Yi25bqm5ZCN56ewJyk7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCg0KICAgICAgaWYgKHRoaXMuc2VsZWN0ZWRCYXNpc1R5cGUgPT09ICdyZXBvcnQnICYmICF0aGlzLnJlcG9ydE5hbWUpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfor7fovpPlhaXmlofku7bmiqXmibnljZXlj7cnKTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KDQogICAgICBpZiAodGhpcy5zZWxlY3RlZEJhc2lzVHlwZSA9PT0gJ2luc3BlY3Rpb24nICYmICF0aGlzLmluc3BlY3Rpb25OdW1iZXIpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfor7fovpPlhaXlt6Hmo4DlpITnvZrljZXlj7cnKTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KDQogICAgICBpZiAodGhpcy5zZWxlY3RlZEJhc2lzVHlwZSA9PT0gJ3NhZmV0eScgJiYgIXRoaXMuc2FmZXR5TnVtYmVyKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn6K+36L6T5YWl5a6J566h5aSE572a5Y2V5Y+3Jyk7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCg0KICAgICAgaWYgKCF0aGlzLmJhc2lzQ29udGVudCkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+ivt+i+k+WFpeS+neaNruWGheWuuScpOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQoNCiAgICAgIHRoaXMuJGVtaXQoJ3NlbGVjdCcsIHRoaXMucHJldmlld1RleHQpOw0KICAgICAgdGhpcy5oYW5kbGVDbG9zZSgpOw0KICAgIH0NCiAgfQ0KfTsNCg=="}, {"version": 3, "sources": ["punishmentBasis-module.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "punishmentBasis-module.vue", "sourceRoot": "src/views/suppPunishment", "sourcesContent": ["<template>\r\n  <el-dialog\r\n    title=\"处罚依据选择\"\r\n    :visible.sync=\"visible\"\r\n    width=\"500px\"\r\n    top=\"5vh\"\r\n    append-to-body\r\n    @close=\"handleClose\"\r\n    :close-on-click-modal=\"false\"\r\n    custom-class=\"basis-dialog\"\r\n  >\r\n    <div class=\"basis-dialog-content\">\r\n      <!-- 处罚依据选项 -->\r\n      <div class=\"basis-options\">\r\n        <h4 class=\"section-title\"><span class=\"required-mark\">*</span>选择依据类型：</h4>\r\n        <el-radio-group v-model=\"selectedBasisType\" @change=\"handleBasisTypeChange\">\r\n          <!-- 质量异议单号 -->\r\n          <div class=\"basis-item\">\r\n            <div class=\"basis-row\">\r\n              <div class=\"radio-wrapper\">\r\n                <el-radio label=\"quality\" @change=\"handleBasisTypeChange\">质量异议单号</el-radio>\r\n              </div>\r\n              <div class=\"input-wrapper\">\r\n                <el-input\r\n                  v-model=\"qualityNumber\"\r\n                  placeholder=\"请输入质量异议单号\"\r\n                  class=\"aligned-input\"\r\n                  @input=\"updateBasisText\"\r\n                  @focus=\"checkQuality\"\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 制度名称 -->\r\n          <div class=\"basis-item\">\r\n            <div class=\"basis-row\">\r\n              <div class=\"radio-wrapper\">\r\n                <el-radio label=\"system\" @change=\"handleBasisTypeChange\">制度名称</el-radio>\r\n              </div>\r\n              <div class=\"input-wrapper\">\r\n                <el-input\r\n                  v-model=\"systemName\"\r\n                  placeholder=\"请输入制度名称\"\r\n                  class=\"aligned-input\"\r\n                  @input=\"updateBasisText\"\r\n                  @focus=\"checkSystem\"\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 文件报批单号 -->\r\n          <div class=\"basis-item\">\r\n            <div class=\"basis-row\">\r\n              <div class=\"radio-wrapper\">\r\n                <el-radio label=\"report\" @change=\"handleBasisTypeChange\">文件报批单号</el-radio>\r\n              </div>\r\n              <div class=\"input-wrapper\">\r\n                <el-input\r\n                  v-model=\"reportName\"\r\n                  placeholder=\"请输入文件报批单号\"\r\n                  class=\"aligned-input\"\r\n                  @input=\"updateBasisText\"\r\n                  @focus=\"checkReport\"\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 巡检处罚单号 -->\r\n          <div class=\"basis-item\">\r\n            <div class=\"basis-row\">\r\n              <div class=\"radio-wrapper\">\r\n                <el-radio label=\"inspection\" @change=\"handleBasisTypeChange\">巡检处罚单号</el-radio>\r\n              </div>\r\n              <div class=\"input-wrapper\">\r\n                <el-input\r\n                  v-model=\"inspectionNumber\"\r\n                  placeholder=\"请输入巡检处罚单号\"\r\n                  class=\"aligned-input\"\r\n                  @input=\"updateBasisText\"\r\n                  @focus=\"checkInspection\"\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 安管处罚单号 -->\r\n          <div class=\"basis-item\">\r\n            <div class=\"basis-row\">\r\n              <div class=\"radio-wrapper\">\r\n                <el-radio label=\"safety\" @change=\"handleBasisTypeChange\">安管处罚单号</el-radio>\r\n              </div>\r\n              <div class=\"input-wrapper\">\r\n                <el-input\r\n                  v-model=\"safetyNumber\"\r\n                  placeholder=\"请输入安管处罚单号\"\r\n                  class=\"aligned-input\"\r\n                  @input=\"updateBasisText\"\r\n                  @focus=\"checkSafety\"\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-radio-group>\r\n      </div>\r\n\r\n      <!-- 依据内容 -->\r\n      <div class=\"basis-content\">\r\n        <h4 class=\"section-title\"><span class=\"required-mark\">*</span>依据内容：</h4>\r\n        <div class=\"content-wrapper\">\r\n          <el-input\r\n            v-model=\"basisContent\"\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n            placeholder=\"请输入依据内容\"\r\n            @input=\"updateBasisText\"\r\n            class=\"content-textarea\"\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n    </div>\r\n\r\n    <!-- 底部按钮 -->\r\n    <div slot=\"footer\" class=\"dialog-footer\">\r\n      <div class=\"footer-buttons\">\r\n        <el-button type=\"primary\" @click=\"handleConfirm\">确 定</el-button>\r\n        <el-button @click=\"handleClose\">取 消</el-button>\r\n      </div>\r\n    </div>\r\n  </el-dialog>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: \"PunishmentBasisDialog\",\r\n  data() {\r\n    return {\r\n      // 是否显示弹出层\r\n      visible: false,\r\n      // 选中的依据类型\r\n      selectedBasisType: '',\r\n      // 质量异议单号\r\n      qualityNumber: '',\r\n      // 制度名称\r\n      systemName: '',\r\n      // 报告名称\r\n      reportName: '',\r\n      // 巡检处罚单号\r\n      inspectionNumber: '',\r\n      // 安管处罚单号\r\n      safetyNumber: '',\r\n      // 依据内容\r\n      basisContent: '',\r\n      // 预览文本\r\n      previewText: ''\r\n    };\r\n  },\r\n  methods: {\r\n    /** 显示弹窗 */\r\n    show(currentValue = '') {\r\n      this.visible = true;\r\n      this.parseCurrentValue(currentValue);\r\n      this.updatePreview();\r\n      // 确保弹窗完全打开后再进行其他操作\r\n      this.$nextTick(() => {\r\n        console.log('弹窗已显示，当前数据：', {\r\n          selectedBasisType: this.selectedBasisType,\r\n          qualityNumber: this.qualityNumber,\r\n          systemName: this.systemName,\r\n          reportName: this.reportName,\r\n          inspectionNumber: this.inspectionNumber,\r\n          safetyNumber: this.safetyNumber,\r\n          basisContent: this.basisContent\r\n        });\r\n      });\r\n    },\r\n    \r\n    /** 隐藏弹窗 */\r\n    hide() {\r\n      this.visible = false;\r\n    },\r\n    \r\n    /** 关闭弹窗 */\r\n    handleClose() {\r\n      this.visible = false;\r\n      this.reset();\r\n    },\r\n    \r\n    /** 重置数据 */\r\n    reset() {\r\n      this.selectedBasisType = '';\r\n      this.qualityNumber = '';\r\n      this.systemName = '';\r\n      this.reportName = '';\r\n      this.inspectionNumber = '';\r\n      this.safetyNumber = '';\r\n      this.basisContent = '';\r\n      this.previewText = '';\r\n    },\r\n    \r\n    /** 解析当前值 */\r\n    parseCurrentValue(value) {\r\n      if (!value) {\r\n        this.reset();\r\n        return;\r\n      }\r\n      \r\n      // 尝试解析现有的依据内容\r\n      if (value.includes('质量异议单号：')) {\r\n        this.selectedBasisType = 'quality';\r\n        const match = value.match(/质量异议单号：([^；\\n]*)/);\r\n        if (match) {\r\n          this.qualityNumber = match[1].trim();\r\n        }\r\n      } else if (value.includes('制度名称：')) {\r\n        this.selectedBasisType = 'system';\r\n        const match = value.match(/制度名称：([^；\\n]*)/);\r\n        if (match) {\r\n          this.systemName = match[1].trim();\r\n        }\r\n      } else if (value.includes('报告：')) {\r\n        this.selectedBasisType = 'report';\r\n        const match = value.match(/报告：([^；\\n]*)/);\r\n        if (match) {\r\n          this.reportName = match[1].trim();\r\n        }\r\n      } else if (value.includes('巡检处罚单号：')) {\r\n        this.selectedBasisType = 'inspection';\r\n        const match = value.match(/巡检处罚单号：([^；\\n]*)/);\r\n        if (match) {\r\n          this.inspectionNumber = match[1].trim();\r\n        }\r\n      } else if (value.includes('安管处罚单号：')) {\r\n        this.selectedBasisType = 'safety';\r\n        const match = value.match(/安管处罚单号：([^；\\n]*)/);\r\n        if (match) {\r\n          this.safetyNumber = match[1].trim();\r\n        }\r\n      }\r\n      \r\n      // 解析依据内容\r\n      const contentMatch = value.match(/依据内容：([^]*)/);\r\n      if (contentMatch) {\r\n        this.basisContent = contentMatch[1].trim();\r\n      } else {\r\n        // 如果没有找到依据内容标识，将整个内容作为依据内容\r\n        this.basisContent = value;\r\n      }\r\n    },\r\n    \r\n    /** 依据类型变化 */\r\n    handleBasisTypeChange() {\r\n      this.updatePreview();\r\n    },\r\n    \r\n    /** 更新依据文本 */\r\n    updateBasisText() {\r\n      this.updatePreview();\r\n    },\r\n\r\n    /** 点击质量异议单号输入框时自动选中 */\r\n    checkQuality() {\r\n      if (this.selectedBasisType !== 'quality') {\r\n        this.selectedBasisType = 'quality';\r\n        this.updatePreview();\r\n      }\r\n    },\r\n\r\n    /** 点击制度名称输入框时自动选中 */\r\n    checkSystem() {\r\n      if (this.selectedBasisType !== 'system') {\r\n        this.selectedBasisType = 'system';\r\n        this.updatePreview();\r\n      }\r\n    },\r\n\r\n    /** 点击报告输入框时自动选中 */\r\n    checkReport() {\r\n      if (this.selectedBasisType !== 'report') {\r\n        this.selectedBasisType = 'report';\r\n        this.updatePreview();\r\n      }\r\n    },\r\n\r\n    /** 点击巡检处罚单号输入框时自动选中 */\r\n    checkInspection() {\r\n      if (this.selectedBasisType !== 'inspection') {\r\n        this.selectedBasisType = 'inspection';\r\n        this.updatePreview();\r\n      }\r\n    },\r\n\r\n    /** 点击安管处罚单号输入框时自动选中 */\r\n    checkSafety() {\r\n      if (this.selectedBasisType !== 'safety') {\r\n        this.selectedBasisType = 'safety';\r\n        this.updatePreview();\r\n      }\r\n    },\r\n    \r\n    /** 更新预览 */\r\n    updatePreview() {\r\n      const parts = [];\r\n\r\n      // 添加选中的依据类型信息\r\n      if (this.selectedBasisType === 'quality' && this.qualityNumber) {\r\n        parts.push(`质量异议单号：${this.qualityNumber}`);\r\n      } else if (this.selectedBasisType === 'system' && this.systemName) {\r\n        parts.push(`制度名称：${this.systemName}`);\r\n      } else if (this.selectedBasisType === 'report' && this.reportName) {\r\n        parts.push(`报告：${this.reportName}`);\r\n      } else if (this.selectedBasisType === 'inspection' && this.inspectionNumber) {\r\n        parts.push(`巡检处罚单号：${this.inspectionNumber}`);\r\n      } else if (this.selectedBasisType === 'safety' && this.safetyNumber) {\r\n        parts.push(`安管处罚单号：${this.safetyNumber}`);\r\n      }\r\n\r\n      // 添加依据内容\r\n      if (this.basisContent) {\r\n        parts.push(`依据内容：${this.basisContent}`);\r\n      }\r\n\r\n      this.previewText = parts.join('；');\r\n\r\n      console.log('预览更新：', {\r\n        selectedBasisType: this.selectedBasisType,\r\n        qualityNumber: this.qualityNumber,\r\n        systemName: this.systemName,\r\n        reportName: this.reportName,\r\n        inspectionNumber: this.inspectionNumber,\r\n        safetyNumber: this.safetyNumber,\r\n        basisContent: this.basisContent,\r\n        previewText: this.previewText\r\n      });\r\n    },\r\n    \r\n    /** 确认选择 */\r\n    handleConfirm() {\r\n      this.updatePreview();\r\n\r\n      // 验证是否填写了必要信息\r\n      if (!this.selectedBasisType) {\r\n        this.$message.warning('请选择依据类型');\r\n        return;\r\n      }\r\n\r\n      if (this.selectedBasisType === 'quality' && !this.qualityNumber) {\r\n        this.$message.warning('请输入质量异议单号');\r\n        return;\r\n      }\r\n\r\n      if (this.selectedBasisType === 'system' && !this.systemName) {\r\n        this.$message.warning('请输入制度名称');\r\n        return;\r\n      }\r\n\r\n      if (this.selectedBasisType === 'report' && !this.reportName) {\r\n        this.$message.warning('请输入文件报批单号');\r\n        return;\r\n      }\r\n\r\n      if (this.selectedBasisType === 'inspection' && !this.inspectionNumber) {\r\n        this.$message.warning('请输入巡检处罚单号');\r\n        return;\r\n      }\r\n\r\n      if (this.selectedBasisType === 'safety' && !this.safetyNumber) {\r\n        this.$message.warning('请输入安管处罚单号');\r\n        return;\r\n      }\r\n\r\n      if (!this.basisContent) {\r\n        this.$message.warning('请输入依据内容');\r\n        return;\r\n      }\r\n\r\n      this.$emit('select', this.previewText);\r\n      this.handleClose();\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n/* 弹窗内容容器 */\r\n.basis-dialog-content {\r\n  padding: 10px 0;\r\n}\r\n\r\n/* 章节标题样式 */\r\n.section-title {\r\n  font-size: 16px;\r\n  color: #303133;\r\n  margin: 0 0 15px 0;\r\n  font-weight: 600;\r\n}\r\n\r\n/* 顶级标题样式（选择依据类型） */\r\n.basis-options .section-title {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n/* 处罚依据选项样式 */\r\n.basis-options {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.basis-item {\r\n  margin-bottom: 15px;\r\n  padding: 0;\r\n}\r\n\r\n/* 新的行布局 */\r\n.basis-row {\r\n  display: flex;\r\n  align-items: center;\r\n  width: 100%;\r\n  min-height: 36px;\r\n}\r\n\r\n.radio-wrapper {\r\n  width: 120px;\r\n  flex-shrink: 0;\r\n  display: flex;\r\n  align-items: center;\r\n  height: 36px;\r\n}\r\n\r\n.input-wrapper {\r\n  width: calc(100% - 135px);\r\n  margin-left: 14px;\r\n}\r\n\r\n.aligned-input {\r\n  width: 100%;\r\n}\r\n\r\n.aligned-input ::v-deep .el-input__inner {\r\n  height: 36px;\r\n  line-height: 36px;\r\n  border-radius: 4px;\r\n  font-size: 14px;\r\n}\r\n\r\n/* 单选框对齐样式 */\r\n.radio-wrapper ::v-deep .el-radio {\r\n  height: 36px;\r\n  display: flex;\r\n  align-items: center;\r\n  margin: 0;\r\n}\r\n\r\n.radio-wrapper ::v-deep .el-radio__input {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-right: 8px;\r\n}\r\n\r\n.radio-wrapper ::v-deep .el-radio__inner {\r\n  width: 16px;\r\n  height: 16px;\r\n}\r\n\r\n.radio-wrapper ::v-deep .el-radio__label {\r\n  font-size: 14px;\r\n  line-height: 36px;\r\n  padding-left: 8px;\r\n  color: #606266;\r\n}\r\n\r\n/* 依据内容区域样式 */\r\n.basis-content {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.basis-content .section-title {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.content-wrapper {\r\n  padding: 0;\r\n}\r\n\r\n.content-textarea {\r\n  width: 100%;\r\n}\r\n\r\n.content-textarea ::v-deep .el-textarea__inner {\r\n  border-radius: 4px;\r\n  font-family: inherit;\r\n  font-size: 14px;\r\n  line-height: 1.5;\r\n  min-height: 120px;\r\n}\r\n\r\n/* 预览区域样式 */\r\n.preview-area {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.preview-area .section-title {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.preview-wrapper {\r\n  padding: 0;\r\n}\r\n\r\n.preview-textarea {\r\n  width: 100%;\r\n}\r\n\r\n.preview-textarea ::v-deep .el-textarea__inner {\r\n  border-radius: 4px;\r\n  background-color: #ffffff;\r\n  font-family: inherit;\r\n  font-size: 14px;\r\n  line-height: 1.5;\r\n  min-height: 120px;\r\n}\r\n\r\n/* 弹窗标题居中 */\r\n::v-deep .el-dialog__header {\r\n  text-align: center;\r\n  background: #f8f9fa;\r\n  border-bottom: 1px solid #e9ecef;\r\n}\r\n\r\n::v-deep .el-dialog__title {\r\n  text-align: center;\r\n  width: 100%;\r\n  display: block;\r\n  font-weight: 600;\r\n  color: #303133;\r\n}\r\n\r\n/* 专门的弹窗样式 */\r\n::v-deep .basis-dialog {\r\n  margin-top: 5vh !important;\r\n}\r\n\r\n::v-deep .basis-dialog .el-dialog__body {\r\n  padding: 25px;\r\n  max-height: 75vh;\r\n  overflow-y: auto;\r\n  background-color: #ffffff;\r\n}\r\n\r\n::v-deep .basis-dialog .el-dialog__header {\r\n  padding: 20px 25px 15px;\r\n}\r\n\r\n::v-deep .basis-dialog .el-dialog__footer {\r\n  padding: 15px 25px 20px;\r\n  text-align: right;\r\n}\r\n\r\n/* 底部按钮样式 */\r\n.footer-buttons {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  gap: 10px;\r\n}\r\n\r\n/* 单选框组样式 */\r\n::v-deep .el-radio-group {\r\n  width: 100%;\r\n}\r\n\r\n::v-deep .el-radio {\r\n  margin-right: 0;\r\n  margin-bottom: 0;\r\n}\r\n\r\n::v-deep .el-radio__label {\r\n  display: flex;\r\n  align-items: center;\r\n  width: 100%;\r\n  font-size: 14px;\r\n  color: #606266;\r\n}\r\n\r\n::v-deep .el-radio__input.is-checked + .el-radio__label {\r\n  color: #409EFF;\r\n}\r\n\r\n/* 必填标识符样式 */\r\n.required-mark {\r\n  color: #F56C6C;\r\n  margin-right: 4px;\r\n  font-weight: bold;\r\n}\r\n</style>\r\n"]}]}