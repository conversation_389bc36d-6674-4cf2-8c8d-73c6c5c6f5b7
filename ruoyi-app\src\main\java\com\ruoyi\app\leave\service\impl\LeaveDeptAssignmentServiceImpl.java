package com.ruoyi.app.leave.service.impl;

import java.util.Collections;
import java.util.Date;
import java.util.List;

import com.google.common.collect.Lists;
import com.ruoyi.app.leave.dto.LeaveUserPermitDto;
import com.ruoyi.app.leave.dto.LeaveUserQueryDto;
import com.ruoyi.app.leave.dto.LeaveUserSaveDto;
import com.ruoyi.app.leave.enums.LeaveRoleEnum;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.mapper.SysUserMapper;
import com.ruoyi.system.mapper.SysUserRoleMapper;
import com.ruoyi.system.service.ISysRoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.app.leave.mapper.LeaveDeptAssignmentMapper;
import com.ruoyi.app.leave.domain.LeaveDeptAssignment;
import com.ruoyi.app.leave.service.ILeaveDeptAssignmentService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

/**
 * 出门证部门归属Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-13
 */
@Service
public class LeaveDeptAssignmentServiceImpl implements ILeaveDeptAssignmentService {
    @Autowired
    private LeaveDeptAssignmentMapper leaveDeptAssignmentMapper;

    @Autowired
    private SysUserMapper sysUserMapper;

    @Autowired
    private ISysRoleService roleService;

    @Autowired
    private SysUserRoleMapper sysUserRoleMapper;

    /**
     * 查询出门证部门归属
     *
     * @param id 出门证部门归属ID
     * @return 出门证部门归属
     */
    @Override
    public LeaveDeptAssignment selectLeaveDeptAssignmentById(Long id) {
        return leaveDeptAssignmentMapper.selectLeaveDeptAssignmentById(id);
    }

    /**
     * 查询出门证部门归属列表
     *
     * @param leaveDeptAssignment 出门证部门归属
     * @return 出门证部门归属
     */
    @Override
    public List<LeaveDeptAssignment> selectLeaveDeptAssignmentList(LeaveDeptAssignment leaveDeptAssignment) {
        return leaveDeptAssignmentMapper.selectLeaveDeptAssignmentList(leaveDeptAssignment);
    }

    /**
     * 新增出门证部门归属
     *
     * @param leaveDeptAssignment 出门证部门归属
     * @return 结果
     */
    @Override
    public int insertLeaveDeptAssignment(LeaveDeptAssignment leaveDeptAssignment) {
        leaveDeptAssignment.setCreateTime(DateUtils.getNowDate());
        return leaveDeptAssignmentMapper.insertLeaveDeptAssignment(leaveDeptAssignment);
    }

    /**
     * 修改出门证部门归属
     *
     * @param leaveDeptAssignment 出门证部门归属
     * @return 结果
     */
    @Override
    public int updateLeaveDeptAssignment(LeaveDeptAssignment leaveDeptAssignment) {
        leaveDeptAssignment.setUpdateTime(DateUtils.getNowDate());
        return leaveDeptAssignmentMapper.updateLeaveDeptAssignment(leaveDeptAssignment);
    }

    /**
     * 批量删除出门证部门归属
     *
     * @param ids 需要删除的出门证部门归属ID
     * @return 结果
     */
    @Override
    public int deleteLeaveDeptAssignmentByIds(Long[] ids) {
        return leaveDeptAssignmentMapper.deleteLeaveDeptAssignmentByIds(ids);
    }

    /**
     * 删除出门证部门归属信息
     *
     * @param id 出门证部门归属ID
     * @return 结果
     */
    @Override
    public int deleteLeaveDeptAssignmentById(Long id) {
        return leaveDeptAssignmentMapper.deleteLeaveDeptAssignmentById(id);
    }

    @Override
    public List<LeaveUserPermitDto> getHasPermitUserList(LeaveUserQueryDto queryDto) {
        List<LeaveUserPermitDto> list = leaveDeptAssignmentMapper.selectLeaveUserList(queryDto);
        List<String> roleList = LeaveRoleEnum.getCodeList();
        //各用户角色描述
        for (String roleKey : roleList) {
            List<String> hasPermitUserNos = sysUserRoleMapper.selectUserNamesByRoleKeyList(Lists.newArrayList(roleKey));
            for (LeaveUserPermitDto userPermitDto : list) {
                if (hasPermitUserNos.contains(userPermitDto.getUserName())) {
                    userPermitDto.setRoleNamesDesc(StringUtils.defaultString(userPermitDto.getRoleNamesDesc()) + LeaveRoleEnum.getDescByCode(roleKey) + "、");
                }
            }
        }
        //去除多余的分隔符
        for (LeaveUserPermitDto handleUser : list) {
            if (handleUser.getRoleNamesDesc() != null && !handleUser.getRoleNamesDesc().isEmpty()) {
                handleUser.setRoleNamesDesc(handleUser.getRoleNamesDesc().substring(0, handleUser.getRoleNamesDesc().length() - 1));
            }
        }
        return list;
    }

    @Override
    public LeaveUserPermitDto getUserInfo(LeaveUserQueryDto queryDto) {
        List<LeaveUserPermitDto> list = leaveDeptAssignmentMapper.selectLeaveUserList(queryDto);
        if (CollectionUtils.isEmpty(list) || list.size() != 1) {
            return null;
        }
        LeaveUserPermitDto userInfo = list.get(0);
        List<String> userRoleKeys = Lists.newArrayList();
        List<String> roleList = LeaveRoleEnum.getCodeList();
        //各用户角色描述
        for (String roleKey : roleList) {
            List<String> hasPermitUserNos = sysUserRoleMapper.selectUserNamesByRoleKeyList(Lists.newArrayList(roleKey));
            if (hasPermitUserNos.contains(userInfo.getUserName())) {
                userRoleKeys.add(roleKey);
            }
        }
        userInfo.setRoleKeys(userRoleKeys);
        return userInfo;

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(LeaveUserSaveDto userDto) {
        Date nowTime = DateUtils.getNowDate();
        //1.用户部门
        //1.1删除用户与部门绑定关系
        LeaveDeptAssignment deleteDto = new LeaveDeptAssignment();
        deleteDto.setUserId(userDto.getUserId());
        deleteDto.setDelFlag(1);
        deleteDto.setUpdateTime(nowTime);
        deleteDto.setUpdateBy(userDto.getHandleNo());
        leaveDeptAssignmentMapper.deleteLeaveDeptAssignmentByUserId(deleteDto);
        //1.2添加用户所属部门
        LeaveDeptAssignment saveDto = new LeaveDeptAssignment();
        saveDto.setUserId(userDto.getUserId());
        saveDto.setDeptId(userDto.getDeptId());
        saveDto.setCreateTime(nowTime);
        saveDto.setCreateBy(userDto.getHandleNo());
        leaveDeptAssignmentMapper.insertLeaveDeptAssignment(saveDto);

        //2.用户角色
        //2.1删除用户与角色绑定关系
        roleService.deleteAuthUsers(Lists.newArrayList(LeaveRoleEnum.getCodeList()), Lists.newArrayList(userDto.getUserId()).toArray(new Long[1]));
        //2.2添加用户角色
        roleService.insertAuthUsers(userDto.getRoleKeys(), Lists.newArrayList(userDto.getUserId()).toArray(new Long[1]));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(LeaveUserSaveDto userDto) {
        Date nowTime = DateUtils.getNowDate();
        //1.删除用户与部门绑定关系
        LeaveDeptAssignment deleteDto = new LeaveDeptAssignment();
        deleteDto.setUserId(userDto.getUserId());
        deleteDto.setDelFlag(1);
        deleteDto.setUpdateTime(nowTime);
        deleteDto.setUpdateBy(userDto.getHandleNo());
        leaveDeptAssignmentMapper.deleteLeaveDeptAssignmentByUserId(deleteDto);
        //2.删除用户与角色绑定关系
        roleService.deleteAuthUsers(Lists.newArrayList(LeaveRoleEnum.getCodeList()), Lists.newArrayList(userDto.getUserId()).toArray(new Long[1]));
    }
}
