package com.ruoyi.app.v1.service;

import com.ruoyi.app.v1.domain.LadingLog;
import com.ruoyi.app.v1.domain.LadingSubscribe;
import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.DataSourceType;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface ILadingSubscribeService {
    /**
     * 查询提单预约
     *
     * @param id 提单预约ID
     * @return 提单预约
     */
    public LadingSubscribe selectLadingSubscribeById(Long id);

    LadingSubscribe getLadingSubscribeByFLowNo(String flowNo);

    public LadingSubscribe selectLadingSubscribeByDelivyNo(String delivyNo);

    /**
     * 查询提单预约列表
     *
     * @param ladingSubscribe 提单预约
     * @return 提单预约集合
     */
    public List<LadingSubscribe> selectLadingSubscribeList(LadingSubscribe ladingSubscribe);

    /**
     * 查询司机预约列表
     *
     * @param ladingSubscribe
     * @return 司机预约列表
     */
    List<LadingSubscribe> getDriverReserveList(LadingSubscribe ladingSubscribe);

    /**
     * 获取提货码顺序号(产销)
     *
     * @param
     * @return 结果
     */
    public String getDelivyNo();

    /**
     * 获取提单日志
     *
     * @param ladingSubscribe
     * @return 结果
     */
    List<LadingLog> getLadingLogByFlowNo(LadingSubscribe ladingSubscribe);

    /**
     * 新增提单预约
     *
     * @param ladingSubscribe 提单预约
     * @return 结果
     */
    public int insertLadingSubscribe(LadingSubscribe ladingSubscribe);

    /**
     * 选中范围提单电文重发
     *
     * @param ladingSubscribe 提单预约
     * @return 结果
     */
    public int supplyUnSentReserve(LadingSubscribe ladingSubscribe);

    /**
     * 司机预约提货信息电文重发
     *
     * @param id
     * @return
     */
    public Boolean sendMessageAgain(Long id);

    /**
     * 批量删除提单预约
     *
     * @param ids 需要删除的提单预约ID
     * @return 结果
     */
    public int deleteLadingSubscribeByIds(Long[] ids);

    /**
     * 删除提单预约信息
     *
     * @param id 提单预约ID
     * @return 结果
     */
    public int deleteLadingSubscribeById(Long id);

    /**
     * 查询具体提单信息(产销)
     *
     * @param billOfLadingNo 提单号
     * @return 结果
     */
    public Map<String, Object> getLadingDetailByladingNo(String billOfLadingNo);

    /**
     * 查询提货点位置信息
     *
     * @return 结果
     */
    public Map<String, Object> getStation(String billOfLadingNo);

    /**
     * 根据货运单号查询承运单号(产销)
     *
     * @param billNo 货运单号
     * @return 结果
     */
    public List<Map<String, Object>> getCarryCompanyNameByBillNo(@Param("billNo") String billNo);


    /**
     * 根据车号查询提单号
     *
     * @param vehicle 车号
     * @return 结果
     */
    public List<Map<String, Object>> getLadingNoByVehicle(@Param("vehicle") String vehicle);

    /**
     * 根据提货码获取提单信息(产销)
     *
     * @param delivyNo 提货码
     * @return 结果
     */
    public Map<String, Object> getLadingDetailByDelivyNo(String delivyNo);


    /**
     * 查询司机提货码记录
     *
     * @param openID openID
     * @return 提货码集合
     */
    public List<Map<String, Object>> selectDriverLadingRecordResult(String openID);

    /**
     * 查询服务评价记录
     *
     * @param delivyNo 提货码
     * @return 提货码集合
     */
    public  Map<String,Object> selectLadingService(String delivyNo);

    /**
     * 新增服务评价
     *
     * @param ladingSubscribe 服务评价
     * @return 结果
     */
    public int insertLadingService(LadingSubscribe ladingSubscribe);

    /**
     * 修改提单评价
     *
     * @param ladingSubscribe 服务评价
     * @return 结果
     */
    public int updateLadingService(LadingSubscribe ladingSubscribe);

    /**
     * 插入提单备注
     *
     * @param ladingSubscribe
     * @return 结果
     */
    public int insertLadingRemark(LadingSubscribe ladingSubscribe);

    /**
     * 销售员备注信息电文重发
     *
     * @param id
     * @return true/false
     */
    public Boolean sendRemarkAgain(Long id);

    /**
     * 根据提单号查询提单备注
     *
     * @param billOfLadingNo
     * @return 结果
     */
    public Map<String,Object> selectLadingRemarkByBillNo(String billOfLadingNo);

    /**
     * 查询提单备注记录
     *
     * @param ladingSubscribe
     * @return 结果
     */
    public List<LadingSubscribe> selectLadingRemarkList(LadingSubscribe ladingSubscribe);

    /**
     * 插入货车轨迹
     *
     * @param ladingSubscribe
     * @return 结果
     */
    public int insertLocus(LadingSubscribe ladingSubscribe);

    /**
     * 补充预约时间
     *
     * @param ladingSubscribe
     * @return 结果
     */
    public int replenishSubscribe(LadingSubscribe ladingSubscribe);

    /**
     * 查询司机提货码记录
     *
     * @param billOfLadingNo 提单号
     * @return 提货码集合
     */
    public List<Map<String, Object>> selectLadingGoods(String billOfLadingNo);

    /**
     * 查询提单预约列表（返回TableDataInfo）
     *
     * @param ladingSubscribe 提单预约
     * @return TableDataInfo
     */
    public TableDataInfo getLadingSubscribeTableApp(LadingSubscribe ladingSubscribe);

    /**
     * 获取提单预约详情
     *
     * @param id 提单预约ID
     * @return 提单预约详情Map
     */
    public Map<String, Object> getLadingSubscribeDetail(Long id);
}
