package com.ruoyi.app.v1.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.ruoyi.app.external.aisuo.TruckProxyService;
import com.ruoyi.app.v1.domain.DriverCarV1;
import com.ruoyi.app.v1.domain.DriverUserV1;
import com.ruoyi.app.v1.domain.LadingLog;
import com.ruoyi.app.v1.domain.LadingSubscribe;
import com.ruoyi.app.v1.mapper.DriverV1Mapper;
import com.ruoyi.app.v1.mapper.LadingLogMapper;
import com.ruoyi.app.v1.mapper.LadingSubscribeMapper;
import com.ruoyi.app.v1.service.ILadingSubscribeService;
import com.ruoyi.app.visitor.vo.VisitorPhotoVo;
import com.ruoyi.app.vehicleAccess.common.utils.RequestUtils;
import com.ruoyi.app.vehicleAccess.domain.AccessInfo;
import com.ruoyi.app.vehicleAccess.domain.XctgVehicleAuditFlow;
import com.ruoyi.app.vehicleAccess.enums.AuditNode;
import com.ruoyi.app.vehicleAccess.enums.CarLicensePlateColor;
import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SnowFlakeUtil;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.http.HttpUtils;
import com.ruoyi.common.utils.sign.Base64;
import com.ruoyi.common.utils.spring.SpringUtils;
import com.ruoyi.common.constant.HttpStatus;
import com.ruoyi.socket.controller.SocketController;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 司机预约Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-03-21
 */
@Service
public class LadingSubscribeServiceImpl implements ILadingSubscribeService {

    private static final Logger log = LoggerFactory.getLogger(LadingSubscribeServiceImpl.class);

    @Autowired
    private LadingSubscribeMapper ladingSubscribeMapper;

    @Autowired
    private DriverV1Mapper driverV1Mapper;

    @Autowired
    private LadingLogMapper ladingLogMapper;

    @Autowired
    private TruckProxyService truckProxyService;

    /**
     * 查询提单预约
     *
     * @param id 提单预约ID
     * @return 提单预约
     */
    @Override
    public LadingSubscribe selectLadingSubscribeById(Long id) {
        return ladingSubscribeMapper.selectLadingSubscribeById(id);
    }

    @Override
    public LadingSubscribe getLadingSubscribeByFLowNo(String flowNo) {
        LadingSubscribe res = ladingSubscribeMapper.selectLadingSubscribeByFLowNo(flowNo);
        if (Optional.ofNullable(res.getLicensePlateColor()).isPresent()) {
            res.setLicensePlateColor(CarLicensePlateColor.getDescriptionByCode(res.getLicensePlateColor()));
        }
        return res;
    }

    @Override
    public LadingSubscribe selectLadingSubscribeByDelivyNo(String delivyNo) {
        return ladingSubscribeMapper.selectLadingSubscribeByDelivyNo(delivyNo);
    }


    /**
     * 查询提单预约列表
     *
     * @param ladingSubscribe 提单预约
     * @return 提单预约
     */
    @Override
    public List<LadingSubscribe> selectLadingSubscribeList(LadingSubscribe ladingSubscribe) {
        return ladingSubscribeMapper.selectLadingSubscribeList(ladingSubscribe);
    }

    @Override
    public List<LadingSubscribe> getDriverReserveList(LadingSubscribe ladingSubscribe) {
        List<LadingSubscribe> list = ladingSubscribeMapper.selectDriverReserveList(ladingSubscribe);
        list.forEach(el -> {
            if (Optional.ofNullable(el.getLicensePlateColor()).isPresent()) {
                el.setLicensePlateColor(CarLicensePlateColor.getDescriptionByCode(el.getLicensePlateColor()));
            }
        });
        return list;
    }

    @Override
    public String getDelivyNo() {
        return ladingSubscribeMapper.getDelivyNo();
    }

    @Override
    public List<LadingLog> getLadingLogByFlowNo(LadingSubscribe ladingSubscribe) {
        return ladingLogMapper.selectLadingLogByFlowNo(ladingSubscribe.getFlowNo());
    }

    /**
     * 新增提单预约
     *
     * @param ladingSubscribe 提单预约
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertLadingSubscribe(LadingSubscribe ladingSubscribe) {
        Date nowDate = new Date();
        // 获取司机信息
        DriverUserV1 driverUser = driverV1Mapper.getUserInfo(ladingSubscribe.getOpenId());
        int count = 0;
        // 插入司机预约记录
        StringBuffer flowNo = new StringBuffer(); //流水号
        flowNo.append("BILL");
        flowNo.append(SnowFlakeUtil.getDeptPassportReturnNoSnowFlakeId());
        ladingSubscribe.setFlowNo(flowNo.toString());
        ladingSubscribe.setDriverName(driverUser.getName());
        ladingSubscribe.setIdCard(driverUser.getIdCard());
        ladingSubscribe.setPhoneNo(driverUser.getPhone());
        ladingSubscribe.setPhoto(driverUser.getPhoto());
        ladingSubscribe.setDriverLicenseImgs(driverUser.getDriverLicenseImgs());
        ladingSubscribe.setVehicleLicenseImgs(driverUser.getVehicleLicenseImgs());
        ladingSubscribe.setDelivyTime(ladingSubscribe.getPredictEntryBeginDate());
        ladingSubscribe.setCreateTime(nowDate);
        count = ladingSubscribeMapper.insertLadingSubscribe(ladingSubscribe);
        //日志
        LadingLog ladingLog = new LadingLog();
        ladingLog.setFlowNo(flowNo.toString());
        ladingLog.setBillOfLadingNo(ladingSubscribe.getBillOfLadingNo());
        ladingLog.setDelivyNo(ladingSubscribe.getDelivyNo());
        ladingLog.setInfo(driverUser.getName() + "预约提货，车号：" + ladingSubscribe.getVehicleNo());
        ladingLog.setCreateTime(nowDate);
        ladingLog.setCreateBy(driverUser.getName());
        ladingLogMapper.insertLadingLog(ladingLog);
        //插入货车行踪
        ladingSubscribe.setEventNo("00");
        ladingSubscribe.setEventContent("货车行踪");
        ladingSubscribe.setWorkName(driverUser.getName());
        ladingSubscribeMapper.insertLocus(ladingSubscribe);
        if (count > 0) {
            Long id = ladingSubscribeMapper.selectLadingSubscribeId(ladingSubscribe);
            // 组织电文信息
            //预约时间，产销电文格式
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
            String delivyTime = sdf.format(ladingSubscribe.getDelivyTime());
            HashMap map = new HashMap();
            map.put("delivy_no", ladingSubscribe.getDelivyNo());
            map.put("bill_of_lading_no", ladingSubscribe.getBillOfLadingNo());
            map.put("vehicle_no", ladingSubscribe.getVehicleNo());
            map.put("carry_company_name", StringUtils.isEmpty(ladingSubscribe.getCarryCompanyName()) == true ? "" : ladingSubscribe.getCarryCompanyName());
            map.put("delivy_time", delivyTime);
            map.put("driver_id", ladingSubscribe.getIdCard());
            map.put("driver_name", ladingSubscribe.getDriverName());
            map.put("phone_no", ladingSubscribe.getPhoneNo());
            String remark = ladingSubscribe.getDelivyRemark() == null ? "" : ladingSubscribe.getDelivyRemark();
            map.put("delivy_remark", Base64.encode(remark.getBytes()));
            map.put("receiver", "XCC1");
            map.put("msgNo", "APXXD1");
            log.info("发送json电文信息 {}", map);
            // 发送产销电文
            String result = SpringUtils.getBean(SocketController.class).sendJson(map);
            log.info("接收json电文反馈 {}", result);
            if (StringUtils.equals(result, "A")) {
                ladingSubscribeMapper.updateDriverReserveStatus(id);
            }
        }
        //异步调用
        CompletableFuture.runAsync(() -> {
            try {
                //推送车辆入厂信息
                AccessInfo accessInfo = this.pushInfo(ladingSubscribe);
                truckProxyService.pushTruckInfo(accessInfo);
            } catch (Exception e) {
                ladingLog.setInfo(ladingSubscribe.getDriverName() + "申请入厂权限异常");
                ladingLogMapper.insertLadingLog(ladingLog);
                log.error(ladingSubscribe.getBillOfLadingNo() + ladingSubscribe.getDriverName() + "申请入厂权限异常");
            }
        });
        return count;
    }

    private AccessInfo pushInfo(LadingSubscribe entity) {
        AccessInfo accessInfo = new AccessInfo();
        accessInfo.setCarSerialNo(entity.getFlowNo());
        accessInfo.setIsCheck(1);
        accessInfo.setTimes(0);
        accessInfo.setCheckManager("销售提单");
        accessInfo.setCarNo(entity.getVehicleNo());
        accessInfo.setCarNoColor(CarLicensePlateColor.getDescriptionByCode(entity.getLicensePlateColor()));
        accessInfo.setCarType("货车");
        accessInfo.setIsPushOrder(0);
        accessInfo.setApplyDate(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, DateUtils.getNowDate()));
        accessInfo.setPredictEntryDate(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, entity.getPredictEntryBeginDate()));
        accessInfo.setPredictEntryBeginDate(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, entity.getPredictEntryBeginDate()));
        accessInfo.setPredictEntryEndDate(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, entity.getPredictEntryEndDate()));
        accessInfo.setChargeType("短期");
        accessInfo.setDriver1(entity.getDriverName());
        accessInfo.setCarMobile(entity.getPhoneNo());
        accessInfo.setApplyAccountName("销售");
        accessInfo.setApplyAccountDName("销售公司");
        //附加信息
        JSONObject userInfo = new JSONObject();
        userInfo.put("a_Number", entity.getIdCard());
        userInfo.put("d_Name", "销售公司");
        accessInfo.setCarDetail(userInfo.toJSONString());
        //其他单位司机推送身份证
        accessInfo.setCarPin(entity.getIdCard());
        accessInfo.setCompany(entity.getConsignUserName());
        accessInfo.setEntryReason("电子提单");

        //司机列表
        JSONArray driverArray = new JSONArray();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("pin", entity.getIdCard());
        driverArray.add(jsonObject);
        accessInfo.setTrunkDrivers(driverArray.toJSONString());

        return accessInfo;
    }


    @Override
    public int supplyUnSentReserve(LadingSubscribe ladingSubscribe) {
        List<LadingSubscribe> list = ladingSubscribeMapper.selectLadingSubscribeByIdRange(ladingSubscribe);
        // 组织电文信息
        for (LadingSubscribe item : list) {
            //预约时间，产销电文格式
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
            String delivyTime = sdf.format(item.getDelivyTime());
            HashMap map = new HashMap();
            map.put("delivy_no", item.getDelivyNo());
            map.put("bill_of_lading_no", item.getBillOfLadingNo());
            map.put("vehicle_no", item.getVehicleNo());
            map.put("carry_company_name", StringUtils.isEmpty(item.getCarryCompanyName()) == true ? "" : item.getCarryCompanyName());
            map.put("delivy_time", delivyTime);
            map.put("driver_id", item.getIdCard());
            map.put("driver_name", item.getDriverName());
            map.put("phone_no", item.getPhoneNo());
            String remark = ladingSubscribe.getDelivyRemark() == null ? "" : ladingSubscribe.getDelivyRemark();
            map.put("delivy_remark", Base64.encode(remark.getBytes()));
            map.put("receiver", "XCC1");
            map.put("msgNo", "APXXD1");
            log.info("发送json电文信息 {}", map);
            // 发送产销电文
            String result = SpringUtils.getBean(SocketController.class).sendJson(map);
            log.info("接收json电文反馈 {}", result);
            if (StringUtils.equals(result, "A")) {
                ladingSubscribeMapper.updateDriverReserveStatus(item.getId());
            }
        }

        return 1;
    }

    /**
     * 司机预约提货信息电文重发
     *
     * @param id
     * @return
     */
    @Override
    public Boolean sendMessageAgain(Long id) {
        // 组织电文信息
        LadingSubscribe ladingSubscribe = ladingSubscribeMapper.selectLadingSubscribeById(id);
        String remark = "";
        LadingSubscribe item = ladingSubscribeMapper.selectLadingRemarkByBillNo(ladingSubscribe.getBillOfLadingNo());
        if (null != item) {
            remark = item.getDelivyRemark() == null ? "" : item.getDelivyRemark();
        }
        HashMap map = new HashMap();
        map.put("delivy_no", ladingSubscribe.getDelivyNo() == null ? "" : ladingSubscribe.getDelivyNo());
        map.put("bill_of_lading_no", ladingSubscribe.getBillOfLadingNo() == null ? "" : ladingSubscribe.getBillOfLadingNo());
        map.put("vehicle_no", ladingSubscribe.getVehicleNo() == null ? "" : ladingSubscribe.getVehicleNo());
        map.put("carry_company_name", StringUtils.isEmpty(ladingSubscribe.getCarryCompanyName()) == true ? "" : ladingSubscribe.getCarryCompanyName());
        map.put("delivy_remark", Base64.encode(remark.getBytes()));
        map.put("receiver", "XCC1");
        map.put("msgNo", "APXXD1");
        if (null != ladingSubscribe.getDelivyTime()) {
            // 预约时间，产销电文格式
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
            String delivyTime = sdf.format(ladingSubscribe.getDelivyTime());
            map.put("delivy_time", delivyTime);
            map.put("driver_id", ladingSubscribe.getIdCard() == null ? "" : ladingSubscribe.getIdCard());
            map.put("driver_name", ladingSubscribe.getDriverName() == null ? "" : ladingSubscribe.getDriverName());
            map.put("phone_no", ladingSubscribe.getPhoneNo() == null ? "" : ladingSubscribe.getPhoneNo());
        }
        // 发送产销电文
        log.info("发送json电文信息 {}", map);
        String result = SpringUtils.getBean(SocketController.class).sendJson(map);
        log.info("接收json电文反馈 {}", result);
        if (StringUtils.equals(result, "A")) {
            ladingSubscribeMapper.updateDriverReserveStatus(id);
            return true;
        }
        return false;

    }

    /**
     * 批量删除提单预约
     *
     * @param ids 需要删除的提单预约ID
     * @return 结果
     */
    @Override
    public int deleteLadingSubscribeByIds(Long[] ids) {
        return ladingSubscribeMapper.deleteLadingSubscribeByIds(ids);
    }

    /**
     * 删除提单预约信息
     *
     * @param id 提单预约ID
     * @return 结果
     */
    @Override
    public int deleteLadingSubscribeById(Long id) {
        return ladingSubscribeMapper.deleteLadingSubscribeById(id);
    }

    /**
     * 查询具体提单信息(产销)
     *
     * @param billOfLadingNo 提单号
     * @return 结果
     */
    @Override
    public Map<String, Object> getLadingDetailByladingNo(String billOfLadingNo) {
        Map<String, Object> result = new HashMap<String, Object>();
        LadingSubscribe item = ladingSubscribeMapper.selectLadingDetailByladingNo(billOfLadingNo);
        if (item == null) {
            result.put("billOfLadingNo", "");
            result.put("stockCode", "");
            result.put("stockName", "");
            result.put("planWt", "");
            result.put("planEndTime", "");
            result.put("consignUserName", "");
            result.put("billRemark", "");
        } else {
            result.put("billOfLadingNo", item.getBillOfLadingNo());
            result.put("stockCode", item.getStockCode());
            result.put("stockName", item.getStockName());
            result.put("planWt", item.getPlanWt());
            result.put("planEndTime", item.getPlanEndTime() == null ? "" : item.getPlanEndTime());
            result.put("consignUserName", item.getConsignUserName());
            result.put("billRemark", item.getBillRemark() == null ? "" : item.getBillRemark());
        }

        return result;
    }

    /**
     * 查询提货点位置信息
     *
     * @return 结果
     */
    @Override
    public Map<String, Object> getStation(String billOfLadingNo) {
        List<Map<String, Object>> result = new ArrayList<Map<String, Object>>();
        LadingSubscribe ladingDetail = ladingSubscribeMapper.selectLadingDetailByladingNo(billOfLadingNo);
        Map<String, Object> ladingPlace = new HashMap<>();
        if (ladingDetail == null) {
            ladingPlace.put("name", "兴澄特种钢铁有限公司");
            ladingPlace.put("flag", "1");
            ladingPlace.put("longitude", 120.338219);
            ladingPlace.put("latitude", 31.942863);
        } else {
            if (ladingDetail.getStockCode() == null) {
                ladingPlace.put("name", "兴澄特种钢铁有限公司");
                ladingPlace.put("flag", "1");
                ladingPlace.put("longitude", 120.338219);
                ladingPlace.put("latitude", 31.942863);
            } else {
                LadingSubscribe stockPlace = ladingSubscribeMapper.selectStationDetailByStockCode(ladingDetail.getStockCode());
                if (stockPlace == null) {
                    ladingPlace.put("name", "兴澄特种钢铁有限公司");
                    ladingPlace.put("flag", "1");
                    ladingPlace.put("longitude", 120.338219);
                    ladingPlace.put("latitude", 31.942863);
                } else {
                    ladingPlace.put("name", stockPlace.getName());
                    ladingPlace.put("flag", "2");
                    ladingPlace.put("longitude", stockPlace.getLongitude());
                    ladingPlace.put("latitude", stockPlace.getLatitude());
                }
            }
        }
        return ladingPlace;
    }

    /**
     * 根据提单号查询承运单号(产销)
     *
     * @param billNo 提单单号
     * @return 结果
     */
    @Override
    public List<Map<String, Object>> getCarryCompanyNameByBillNo(String billNo) {
        List<Map<String, Object>> result = new ArrayList<Map<String, Object>>();
        List<LadingSubscribe> list = ladingSubscribeMapper.selectCarryCompanyNameByBillNo(billNo);
        for (LadingSubscribe item : list) {
            Map<String, Object> map = new HashMap<>();
            map.put("zuBillNo", item.getZuBillNo());
            map.put("fleetName", item.getFleetName());
            result.add(map);
        }
        return result;

    }


    /**
     * 根据车号查询提单号
     *
     * @param vehicle 车号
     * @return 结果
     */
    @Override
    public List<Map<String, Object>> getLadingNoByVehicle(String vehicle) {
        List<Map<String, Object>> result = new ArrayList<Map<String, Object>>();
        List<String> list = driverV1Mapper.getLadingNoByVehicle(vehicle);
        for (String item : list) {
            Map<String, Object> map = new HashMap<>();
            map.put("ladingNo", item);
            result.add(map);
        }
        return result;
    }

    /**
     * 根据提货码获取提单信息(产销)
     *
     * @param delivyNo 提货码
     * @return 结果
     */
    @Override
    public Map<String, Object> getLadingDetailByDelivyNo(String delivyNo) {
        LadingSubscribe item = ladingSubscribeMapper.selectLadingDetailByDelivyNo(delivyNo);
        Map<String, Object> result = new HashMap<String, Object>();
        if (item == null) {
            result.put("delivyNo", "");
            result.put("billOfLadingNo", "");
            result.put("vehicleNo", "");
            result.put("driverName", "");
            result.put("phoneNo", "");
            result.put("carryCompanyName", "");
            result.put("delivyRemark", "");
        } else {
            result.put("delivyNo", item.getDelivyNo());
            result.put("billOfLadingNo", item.getBillOfLadingNo());
            result.put("vehicleNo", item.getVehicleNo());
            result.put("driverName", item.getDriverName());
            result.put("phoneNo", item.getPhoneNo());
            result.put("carryCompanyName", item.getCarryCompanyName());
            result.put("delivyRemark", item.getDelivyRemark() == null ? "" : item.getDelivyRemark());
        }
        //获取出入厂时间信息
        LadingSubscribe timeInfo = ladingSubscribeMapper.selectLadingSubscribeByDelivyNo(delivyNo);
        if (null == timeInfo) {
            result.put("startTime", "");
            result.put("endTime", "");
        } else {
            if (null != timeInfo.getPredictEntryBeginDate()) {
                result.put("startTime", DateUtils.parseDateToStr(DateUtils.YYMMDD_HH_MM, timeInfo.getPredictEntryBeginDate()));
            } else {
                result.put("startTime", "");
            }
            if (null != timeInfo.getPredictEntryEndDate()) {
                result.put("endTime", DateUtils.parseDateToStr(DateUtils.YYMMDD_HH_MM, timeInfo.getPredictEntryEndDate()));
            } else {
                result.put("endTime", "");
            }
        }

        return result;
    }

    /**
     * 根据openID获取司机提货记录
     */
    @Override
    public List<Map<String, Object>> selectDriverLadingRecordResult(String openID) {
        List<LadingSubscribe> list = ladingSubscribeMapper.selectDriverLadingRecordResult(openID);
        List<Map<String, Object>> result = new ArrayList<Map<String, Object>>();
        for (LadingSubscribe item : list) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
            String nowTime = sdf.format(DateUtils.getNowDate());//当前日期
            String planEndTime = item.getPlanEndTime();//截止日期
            //比较大小,k=-1小于，k=0等于，k=1大于
            int k = nowTime.compareTo(planEndTime);
            int flag = 0;
            if (k <= 0) {
                flag = 1;
            } else {
                flag = 0;
            }
            Map<String, Object> map = new HashMap<>();
            map.put("stockName", item.getStockName() == null ? "" : item.getStockName());
            map.put("billOfLadingNo", item.getBillOfLadingNo() == null ? "" : item.getBillOfLadingNo());
            map.put("consignUserName", item.getConsignUserName() == null ? "" : item.getConsignUserName());
            map.put("delivyNo", item.getDelivyNo() == null ? "" : item.getDelivyNo());
            map.put("vendorName", item.getVendorName() == null ? "" : item.getVendorName());
            map.put("carryCompanyName", item.getCarryCompanyName() == null ? "" : item.getCarryCompanyName());
            map.put("vehicleNo", item.getVehicleNo() == null ? "" : item.getVehicleNo());
            map.put("flag", flag);//flag=1有效，flag=0无效
            map.put("valid", item.getDelivyTime() == null ? 0 : 1);//valid=0未预约，valid=1已预约
            result.add(map);
        }
        return result;
    }

    /**
     * 根据提货码查询服务评价
     */
    @Override
    public Map<String, Object> selectLadingService(String delivyNo) {
        LadingSubscribe item = ladingSubscribeMapper.selectLadingService(delivyNo);
        Map<String, Object> map = new HashMap<String, Object>();
        if (null != item) {
            map.put("rate", item.getRate() == null ? "" : item.getRate());
            map.put("service", item.getService() == null ? "" : item.getService());
        }
        return map;
    }

    /**
     * 新增服务评价
     */
    @Override
    public int insertLadingService(LadingSubscribe ladingSubscribe) {
        // 获取司机信息
        DriverUserV1 driverUser = driverV1Mapper.getUserInfo(ladingSubscribe.getOpenId());
        ladingSubscribe.setCreateTime(DateUtils.getNowDate());
        // 组织电文信息
        HashMap map = new HashMap();
        map.put("serialno", "99999");
        map.put("origin", "小程序");
        map.put("origindetail", ladingSubscribe.getBillOfLadingNo());
        map.put("yourname", driverUser.getName());
        map.put("phoneno", driverUser.getPhone());
        map.put("yourcarno", ladingSubscribe.getVehicleNo());
        map.put("classification", "场地分");
        map.put("mark", ladingSubscribe.getRate());
        map.put("receiver", "XCC1");
        map.put("msgNo", "APXXP1");
        log.info("发送json电文信息 {}", map);
        // 发送产销电文
        String result = SpringUtils.getBean(SocketController.class).sendJson(map);
        log.info("接收json电文反馈 {}", result);
        if (StringUtils.equals(result, "A")) {
            ladingSubscribeMapper.updateLadingServiceStatus(ladingSubscribe.getDelivyNo());
        }
        return ladingSubscribeMapper.insertLadingService(ladingSubscribe);
    }

    /**
     * 修改服务评价
     *
     * @param ladingSubscribe 提货码
     * @return 结果
     */
    @Override
    public int updateLadingService(LadingSubscribe ladingSubscribe) {
        // 获取司机信息
        DriverUserV1 driverUser = driverV1Mapper.getUserInfo(ladingSubscribe.getOpenId());
        ladingSubscribe.setUpdateTime(DateUtils.getNowDate());
        // 组织电文信息
        HashMap map = new HashMap();
        map.put("serialno", "99999");
        map.put("origin", "小程序");
        map.put("origindetail", ladingSubscribe.getBillOfLadingNo());
        map.put("yourname", driverUser.getName());
        map.put("phoneno", driverUser.getPhone());
        map.put("yourcarno", ladingSubscribe.getVehicleNo());
        map.put("classification", "场地分");
        map.put("mark", ladingSubscribe.getRate());
        map.put("receiver", "XCC1");
        map.put("msgNo", "APXXP1");
        log.info("发送json电文信息 {}", map);
        // 发送产销电文
        String result = SpringUtils.getBean(SocketController.class).sendJson(map);
        log.info("接收json电文反馈 {}", result);
        if (StringUtils.equals(result, "A")) {
            ladingSubscribeMapper.updateLadingServiceStatus(ladingSubscribe.getDelivyNo());
        }

        return ladingSubscribeMapper.updateLadingService(ladingSubscribe);
    }


    @Override
    public int insertLadingRemark(LadingSubscribe ladingSubscribe) {
        Date nowDate = new Date();
        ladingSubscribe.setCreateTime(nowDate);
        // 先查询再更新
        Long id = ladingSubscribeMapper.selectLadingRemarkId(ladingSubscribe.getBillOfLadingNo());
        int count = 0;
        if (id != null && id > 0) {
            ladingSubscribe.setId(id);
            ladingSubscribe.setUpdateTime(nowDate);
            count = ladingSubscribeMapper.updateLadingRemark(ladingSubscribe);
        } else {
            count = ladingSubscribeMapper.insertLadingRemark(ladingSubscribe);
        }
        if (count > 0) {
            Long idTemp = ladingSubscribeMapper.selectLadingRemarkId(ladingSubscribe.getBillOfLadingNo());
            // 组织电文信息
            HashMap map = new HashMap();
            map.put("bill_of_lading_no", ladingSubscribe.getBillOfLadingNo());
            map.put("delivy_remark", Base64.encode(ladingSubscribe.getDelivyRemark().getBytes()));
            map.put("receiver", "XCC1");
            map.put("msgNo", "APXXD1");
            log.info("发送json电文信息 {}", map);
            // 发送产销电文
            String result = SpringUtils.getBean(SocketController.class).sendJson(map);
            log.info("接收json电文反馈 {}", result);
            if (StringUtils.equals(result, "A")) {
                ladingSubscribeMapper.updateLadingRemarkStatus(idTemp);
            }

        }

        return count;

    }

    /**
     * 销售员备注信息电文重发
     *
     * @param id
     * @return
     */
    @Override
    public Boolean sendRemarkAgain(Long id) {
        LadingSubscribe ladingSubscribe = ladingSubscribeMapper.selectLadingRemarkById(id);
        // 组织电文信息
        //预约时间，产销电文格式
        HashMap map = new HashMap();
        map.put("bill_of_lading_no", ladingSubscribe.getBillOfLadingNo());
        map.put("delivy_remark", Base64.encode(ladingSubscribe.getDelivyRemark().getBytes()));
        map.put("receiver", "XCC1");
        map.put("msgNo", "APXXD1");
        log.info("发送json电文信息 {}", map);
        // 发送产销电文
        String result = SpringUtils.getBean(SocketController.class).sendJson(map);
        log.info("接收json电文反馈 {}", result);
        if (StringUtils.equals(result, "A")) {
            ladingSubscribeMapper.updateDriverReserveStatus(id);
            return true;
        }
        return false;

    }

    @Override
    public Map<String, Object> selectLadingRemarkByBillNo(String billOfLadingNo) {
        LadingSubscribe item = ladingSubscribeMapper.selectLadingRemarkByBillNo(billOfLadingNo);
        Map<String, Object> map = new HashMap<String, Object>();
        if (null != item) {
            map.put("billOfLadingNo", item.getBillOfLadingNo() == null ? "" : item.getBillOfLadingNo());
            map.put("delivyRemark", item.getDelivyRemark() == null ? "" : item.getDelivyRemark());
        } else {
            map.put("billOfLadingNo", billOfLadingNo);
            map.put("delivyRemark", "");
        }
        return map;
    }

    @Override
    public List<LadingSubscribe> selectLadingRemarkList(LadingSubscribe ladingSubscribe) {
        return ladingSubscribeMapper.selectLadingRemarkList(ladingSubscribe);
    }

    @Override
    public int insertLocus(LadingSubscribe ladingSubscribe) {
        Date nowDate = new Date();
        ladingSubscribe.setCreateTime(nowDate);
        ladingSubscribe.setEventNo("00");
        ladingSubscribe.setEventContent("货车行踪");
        // 获取司机信息
        DriverUserV1 driverUser = driverV1Mapper.getUserInfo(ladingSubscribe.getOpenId());
        ladingSubscribe.setDriverName(driverUser.getName());
        ladingSubscribe.setWorkName(driverUser.getName());
        ladingSubscribe.setIdCard(driverUser.getIdCard());
        ladingSubscribe.setPhoneNo(driverUser.getPhone());
        return ladingSubscribeMapper.insertLocus(ladingSubscribe);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int replenishSubscribe(LadingSubscribe ladingSubscribe) {
        //获取当前时间
        Date nowDate = new Date();
        ladingSubscribe.setUpdateTime(nowDate);
        //查询待更新的记录
        Long id = ladingSubscribeMapper.selectSubscribeIdByDelivyNo(ladingSubscribe.getDelivyNo());
        ladingSubscribe.setId(id);
        // 获取司机信息
        DriverUserV1 driverUser = driverV1Mapper.getUserInfo(ladingSubscribe.getOpenId());
        ladingSubscribe.setDriverName(driverUser.getName());
        ladingSubscribe.setIdCard(driverUser.getIdCard());
        ladingSubscribe.setPhoneNo(driverUser.getPhone());
        ladingSubscribe.setPhoto(driverUser.getPhoto());
        ladingSubscribe.setDriverLicenseImgs(driverUser.getDriverLicenseImgs());
        ladingSubscribe.setVehicleLicenseImgs(driverUser.getVehicleLicenseImgs());
        ladingSubscribe.setDelivyTime(ladingSubscribe.getPredictEntryBeginDate()); //预约时间

        //日志
        LadingSubscribe billLog = ladingSubscribeMapper.selectLadingSubscribeById(id);
        //车牌颜色
        List<DriverCarV1> carList = driverV1Mapper.getCarDetailInfo(billLog.getVehicleNo());
        if (carList.size() > 0) {
            String carColor = carList.get(0).getLicensePlateColor();
            if (StringUtils.isNotBlank(carColor)) {
                billLog.setLicensePlateColor(carColor);
                ladingSubscribe.setLicensePlateColor(carColor);
            } else {
                billLog.setLicensePlateColor("黄");
                ladingSubscribe.setLicensePlateColor("黄");
            }
        }
        LadingLog ladingLog = new LadingLog();
        ladingLog.setFlowNo(billLog.getFlowNo());
        ladingLog.setBillOfLadingNo(billLog.getBillOfLadingNo());
        ladingLog.setDelivyNo(billLog.getDelivyNo());
        ladingLog.setInfo(driverUser.getName() + "预约提货，车号：" + billLog.getVehicleNo());
        ladingLog.setCreateTime(nowDate);
        ladingLog.setCreateBy(driverUser.getName());
        ladingLogMapper.insertLadingLog(ladingLog);
        //插入货车行踪
        ladingSubscribe.setCreateTime(nowDate);
        ladingSubscribe.setEventNo("00");
        ladingSubscribe.setEventContent("货车行踪");
        ladingSubscribe.setWorkName(driverUser.getName());
        ladingSubscribeMapper.insertLocus(ladingSubscribe);

        //更新
        int count = ladingSubscribeMapper.updateLadingSubscribe(ladingSubscribe);
        if (count > 0) {
            // 组织电文信息
            //预约时间，产销电文格式
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
            String delivyTime = sdf.format(ladingSubscribe.getDelivyTime());
            HashMap map = new HashMap();
            map.put("delivy_no", ladingSubscribe.getDelivyNo());
            map.put("bill_of_lading_no", ladingSubscribe.getBillOfLadingNo());
            map.put("vehicle_no", billLog.getVehicleNo());
            map.put("carry_company_name", StringUtils.isEmpty(ladingSubscribe.getCarryCompanyName()) == true ? "" : ladingSubscribe.getCarryCompanyName());
            map.put("delivy_time", delivyTime);
            map.put("driver_id", ladingSubscribe.getIdCard());
            map.put("driver_name", ladingSubscribe.getDriverName());
            map.put("phone_no", ladingSubscribe.getPhoneNo());
            map.put("receiver", "XCC1");
            map.put("msgNo", "APXXD1");
            log.info("发送json电文信息 {}", map);
            // 发送产销电文
            String result = SpringUtils.getBean(SocketController.class).sendJson(map);
            log.info("接收json电文反馈 {}", result);
            if (StringUtils.equals(result, "A")) {
                ladingSubscribeMapper.updateDriverReserveStatus(id);
            }
        }

        try {
            //推送车辆入厂信息
            LadingSubscribe sendInfo = ladingSubscribeMapper.selectLadingSubscribeById(id);
            AccessInfo accessInfo = this.pushInfo(sendInfo);
            truckProxyService.pushTruckInfo(accessInfo);
        } catch (Exception e) {
            ladingLog.setInfo(ladingSubscribe.getDriverName() + "申请入厂权限异常");
            ladingLogMapper.insertLadingLog(ladingLog);
            log.error(ladingSubscribe.getBillOfLadingNo() + ladingSubscribe.getDriverName() + "申请入厂权限异常");
        }


        return count;
    }

    /**
     * 查询出入物资
     *
     * @param billOfLadingNo 提单
     * @return 提单
     */
    @DataSource(value = DataSourceType.XCC1)
    public List<Map<String, Object>> selectLadingGoods(String billOfLadingNo) {
        return ladingSubscribeMapper.selectLadingGoods(billOfLadingNo);
    }

    @Override
    public TableDataInfo getLadingSubscribeTableApp(LadingSubscribe ladingSubscribe) {
        TableDataInfo rspData = new TableDataInfo();
        try {
            // 1. 查询原始列表
            List<LadingSubscribe> list = ladingSubscribeMapper.selectDriverReserveList(ladingSubscribe);
            long total = new PageInfo(list).getTotal();

            // 2. 转换为展示格式
            List<Map<String, Object>> result = new ArrayList<>();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            SimpleDateFormat dateSdf = new SimpleDateFormat("yyyy-MM-dd");

            for (LadingSubscribe item : list) {
                Map<String, Object> map = new HashMap<>();
                map.put("id", item.getId());
                map.put("flowNo", item.getFlowNo() != null ? item.getFlowNo() : "");
                map.put("delivyNo", item.getDelivyNo() != null ? item.getDelivyNo() : "");
                map.put("billOfLadingNo", item.getBillOfLadingNo() != null ? item.getBillOfLadingNo() : "");
                map.put("fleetName", item.getFleetName() != null ? item.getFleetName() : "");
                map.put("stockCode", item.getStockCode() != null ? item.getStockCode() : "");
                map.put("stockName", item.getStockName() != null ? item.getStockName() : "");
                map.put("longitude", item.getLongitude() != null ? item.getLongitude() : "");
                map.put("latitude", item.getLatitude() != null ? item.getLatitude() : "");
                map.put("location", item.getLocation() != null ? item.getLocation() : "");
                map.put("code", item.getCode() != null ? item.getCode() : "");
                map.put("name", item.getName() != null ? item.getName() : "");
                map.put("planEndTime", item.getPlanEndTime() != null ? item.getPlanEndTime() : "");
                map.put("consignUserName", item.getConsignUserName() != null ? item.getConsignUserName() : "");
                map.put("vendorName", item.getVendorName() != null ? item.getVendorName() : "");
                map.put("planWt", item.getPlanWt() != null ? item.getPlanWt() : "");
                map.put("vehicleNo", item.getVehicleNo() != null ? item.getVehicleNo() : "");
                map.put("licensePlateColor", item.getLicensePlateColor() != null ? item.getLicensePlateColor() : "");
                map.put("licensePlateColorDesc", CarLicensePlateColor.getDescriptionByCode(item.getLicensePlateColor()));
                map.put("driverName", item.getDriverName() != null ? item.getDriverName() : "");
//                map.put("idCard", item.getIdCard() != null ? item.getIdCard() : "");
                map.put("phoneNo", item.getPhoneNo() != null ? item.getPhoneNo() : "");
                map.put("zuBillNo", item.getZuBillNo() != null ? item.getZuBillNo() : "");
                map.put("carryCompanyName", item.getCarryCompanyName() != null ? item.getCarryCompanyName() : "");
                map.put("delivyTime", item.getDelivyTime() != null ? dateSdf.format(item.getDelivyTime()) : "");
                map.put("delivyTime1", item.getDelivyTime1() != null ? item.getDelivyTime1() : "");
                map.put("status", item.getStatus() != null ? item.getStatus() : "");
                map.put("rate", item.getRate() != null ? item.getRate() : "");
                map.put("service", item.getService() != null ? item.getService() : "");
                map.put("delivyRemark", item.getDelivyRemark() != null ? item.getDelivyRemark() : "");
                map.put("eventNo", item.getEventNo() != null ? item.getEventNo() : "");
                map.put("eventContent", item.getEventContent() != null ? item.getEventContent() : "");
                map.put("files", item.getFiles() != null ? item.getFiles() : "");
                map.put("predictEntryBeginDate", item.getPredictEntryBeginDate() != null ? sdf.format(item.getPredictEntryBeginDate()) : "");
                map.put("predictEntryEndDate", item.getPredictEntryEndDate() != null ? sdf.format(item.getPredictEntryEndDate()) : "");
                map.put("passStatus", item.getPassStatus() != null ? item.getPassStatus() : "");
                map.put("entryTime", item.getEntryTime() != null ? sdf.format(item.getEntryTime()) : "");
                map.put("billRemark", item.getBillRemark() != null ? item.getBillRemark() : "");
                map.put("createTime", item.getCreateTime() != null ? sdf.format(item.getCreateTime()) : "");
                map.put("updateTime", item.getUpdateTime() != null ? sdf.format(item.getUpdateTime()) : "");
                map.put("createBy", item.getCreateBy() != null ? item.getCreateBy() : "");
                map.put("updateBy", item.getUpdateBy() != null ? item.getUpdateBy() : "");

                // 处理图片字段，参考详情接口的解析逻辑，返回包含url字段的对象数组
                map.put("photo", parseVisitorPhotoList(item.getPhoto()));
                map.put("driverLicenseImgs", parseVisitorPhotoList(item.getDriverLicenseImgs()));
                map.put("vehicleLicenseImgs", parseVisitorPhotoList(item.getVehicleLicenseImgs()));

                result.add(map);
            }

            // 3. 设置返回数据
            rspData.setTotal(total);
            rspData.setRows(result);
            rspData.setCode(HttpStatus.SUCCESS);
            rspData.setMsg("查询成功");

        } catch (Exception e) {
            log.error("查询提单预约列表发生异常", e);
            rspData.setTotal(0);
            rspData.setRows(new ArrayList<>());
            rspData.setCode(HttpStatus.ERROR);
            rspData.setMsg("查询失败：" + e.getMessage());
        }

        return rspData;
    }



    /**
     * 解析照片JSON为包含url字段的对象列表，优先使用fastjson解析为VisitorPhotoVo列表。
     * 若解析失败，则按逗号分隔URL兜底转换。
     */
    private List<VisitorPhotoVo> parseVisitorPhotoList(String json) {
        List<VisitorPhotoVo> list = new ArrayList<>();
        if (StringUtils.isBlank(json)) {
            return list;
        }
        String trimmed = json.trim();
        try {
            List<VisitorPhotoVo> parsed = JSONArray.parseArray(trimmed, VisitorPhotoVo.class);
            if (parsed != null) {
                return parsed;
            }
        } catch (Exception ignore) { }

        // 兜底：按逗号分隔解析
        String[] urls = trimmed.split(",");
        for (String url : urls) {
            if (StringUtils.isNotBlank(url)) {
                VisitorPhotoVo vo = new VisitorPhotoVo();
                vo.setUrl(url.trim());
                list.add(vo);
            }
        }
        return list;
    }

    @Override
    public Map<String, Object> getLadingSubscribeDetail(Long id) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 1. 根据ID查询提单预约信息
            LadingSubscribe ladingSubscribe = ladingSubscribeMapper.selectLadingSubscribeById(id);

            if (ladingSubscribe == null) {
                log.warn("未找到提单预约信息 - ID：{}", id);
                result.put("success", false);
                result.put("message", "未找到提单预约信息");
                return result;
            }

            // 2. 基本信息转换
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            SimpleDateFormat dateSdf = new SimpleDateFormat("yyyy-MM-dd");

            result.put("id", ladingSubscribe.getId());
            result.put("flowNo", ladingSubscribe.getFlowNo() != null ? ladingSubscribe.getFlowNo() : "");
            result.put("delivyNo", ladingSubscribe.getDelivyNo() != null ? ladingSubscribe.getDelivyNo() : "");
            result.put("billOfLadingNo", ladingSubscribe.getBillOfLadingNo() != null ? ladingSubscribe.getBillOfLadingNo() : "");
            result.put("fleetName", ladingSubscribe.getFleetName() != null ? ladingSubscribe.getFleetName() : "");
            result.put("stockCode", ladingSubscribe.getStockCode() != null ? ladingSubscribe.getStockCode() : "");
            result.put("stockName", ladingSubscribe.getStockName() != null ? ladingSubscribe.getStockName() : "");
            result.put("longitude", ladingSubscribe.getLongitude() != null ? ladingSubscribe.getLongitude() : "");
            result.put("latitude", ladingSubscribe.getLatitude() != null ? ladingSubscribe.getLatitude() : "");
            result.put("location", ladingSubscribe.getLocation() != null ? ladingSubscribe.getLocation() : "");
            result.put("code", ladingSubscribe.getCode() != null ? ladingSubscribe.getCode() : "");
            result.put("name", ladingSubscribe.getName() != null ? ladingSubscribe.getName() : "");
            result.put("planEndTime", ladingSubscribe.getPlanEndTime() != null ? ladingSubscribe.getPlanEndTime() : "");
            result.put("consignUserName", ladingSubscribe.getConsignUserName() != null ? ladingSubscribe.getConsignUserName() : "");
            result.put("vendorName", ladingSubscribe.getVendorName() != null ? ladingSubscribe.getVendorName() : "");
            result.put("planWt", ladingSubscribe.getPlanWt() != null ? ladingSubscribe.getPlanWt() : "");
            result.put("vehicleNo", ladingSubscribe.getVehicleNo() != null ? ladingSubscribe.getVehicleNo() : "");
            result.put("licensePlateColor", ladingSubscribe.getLicensePlateColor() != null ? ladingSubscribe.getLicensePlateColor() : "");
            result.put("licensePlateColorDesc", CarLicensePlateColor.getDescriptionByCode(ladingSubscribe.getLicensePlateColor()));
            result.put("driverName", ladingSubscribe.getDriverName() != null ? ladingSubscribe.getDriverName() : "");
//            result.put("idCard", ladingSubscribe.getIdCard() != null ? ladingSubscribe.getIdCard() : "");
            result.put("phoneNo", ladingSubscribe.getPhoneNo() != null ? ladingSubscribe.getPhoneNo() : "");
            result.put("zuBillNo", ladingSubscribe.getZuBillNo() != null ? ladingSubscribe.getZuBillNo() : "");
            result.put("carryCompanyName", ladingSubscribe.getCarryCompanyName() != null ? ladingSubscribe.getCarryCompanyName() : "");
            result.put("delivyTime", ladingSubscribe.getDelivyTime() != null ? dateSdf.format(ladingSubscribe.getDelivyTime()) : "");
            result.put("delivyTime1", ladingSubscribe.getDelivyTime1() != null ? ladingSubscribe.getDelivyTime1() : "");
            result.put("status", ladingSubscribe.getStatus() != null ? ladingSubscribe.getStatus() : "");
            result.put("rate", ladingSubscribe.getRate() != null ? ladingSubscribe.getRate() : "");
            result.put("service", ladingSubscribe.getService() != null ? ladingSubscribe.getService() : "");
            result.put("delivyRemark", ladingSubscribe.getDelivyRemark() != null ? ladingSubscribe.getDelivyRemark() : "");
            result.put("eventNo", ladingSubscribe.getEventNo() != null ? ladingSubscribe.getEventNo() : "");
            result.put("eventContent", ladingSubscribe.getEventContent() != null ? ladingSubscribe.getEventContent() : "");
            result.put("files", ladingSubscribe.getFiles() != null ? ladingSubscribe.getFiles() : "");
            result.put("predictEntryBeginDate", ladingSubscribe.getPredictEntryBeginDate() != null ? sdf.format(ladingSubscribe.getPredictEntryBeginDate()) : "");
            result.put("predictEntryEndDate", ladingSubscribe.getPredictEntryEndDate() != null ? sdf.format(ladingSubscribe.getPredictEntryEndDate()) : "");
            result.put("passStatus", ladingSubscribe.getPassStatus() != null ? ladingSubscribe.getPassStatus() : "");
            result.put("entryTime", ladingSubscribe.getEntryTime() != null ? sdf.format(ladingSubscribe.getEntryTime()) : "");
            result.put("billRemark", ladingSubscribe.getBillRemark() != null ? ladingSubscribe.getBillRemark() : "");
            result.put("createTime", ladingSubscribe.getCreateTime() != null ? sdf.format(ladingSubscribe.getCreateTime()) : "");
            result.put("updateTime", ladingSubscribe.getUpdateTime() != null ? sdf.format(ladingSubscribe.getUpdateTime()) : "");
            result.put("createBy", ladingSubscribe.getCreateBy() != null ? ladingSubscribe.getCreateBy() : "");
            result.put("updateBy", ladingSubscribe.getUpdateBy() != null ? ladingSubscribe.getUpdateBy() : "");

            // 3. 处理图片字段，参考合金预约：直接解析为包含url字段的对象数组
            result.put("photo", parseVisitorPhotoList(ladingSubscribe.getPhoto()));
            result.put("driverLicenseImgs", parseVisitorPhotoList(ladingSubscribe.getDriverLicenseImgs()));
            result.put("vehicleLicenseImgs", parseVisitorPhotoList(ladingSubscribe.getVehicleLicenseImgs()));

            // 4. 获取提单日志，按createTime倒序
            List<LadingLog> ladingLogs = ladingLogMapper.selectLadingLogByFlowNo(ladingSubscribe.getFlowNo());
            List<Map<String, String>> stepLogs = new ArrayList<>();
            if (ladingLogs != null) {
                ladingLogs.stream()
                        .sorted(Comparator.comparing(LadingLog::getCreateTime, Comparator.nullsLast(Comparator.naturalOrder())).reversed())
                        .forEach(log -> {
                            Map<String, String> step = new HashMap<>();
                            step.put("text", log.getCreateTime() == null ? "" : DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, log.getCreateTime()));
                            step.put("desc", log.getInfo() != null ? log.getInfo() : "");
                            stepLogs.add(step);
                        });
            }
            result.put("ladingLogs", stepLogs);

            result.put("success", true);
            result.put("message", "查询成功");

            log.info("获取提单预约详情成功 - ID：{}", id);

        } catch (Exception e) {
            log.error("获取提单预约详情发生异常 - ID：{}", id, e);
            result.put("success", false);
            result.put("message", "查询失败：" + e.getMessage());
        }

        return result;
    }
}
