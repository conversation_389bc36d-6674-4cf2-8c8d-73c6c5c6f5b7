package com.ruoyi.app.leave.enums;

/**
 * 计划状态 1-待分厂审批 2-待分厂复审 3-待生产指挥中心审批 4-审批完成  5-已出厂 6-部分收货 7-已完成
 * 11-驳回 12-废弃 13-过期
 *
 * 属性为code（int）、desc(String)
 *
 * <AUTHOR>
 * @date 2025/3/13 下午2:10
 */
public enum LeavePlanStatusEnum {
    WAIT_FACTORY_APPROVAL(1, "待分厂审批"),
    WAIT_FACTORY_RECHECK(2, "待分厂复审"),
    WAIT_PRODUCTION_APPROVAL(3, "待生产指挥中心审批"),
    APPROVAL_COMPLETE(4, "审批完成"),
    OUT_FACTORY(5, "已出厂"),
    PART_RECEIPT(6, "部分收货"),
    COMPLETE(7, "已完成"),
    REJECT(11, "驳回"),
    DISCARD(12, "废弃"),
    EXPIRE(13, "过期");

    private final int code;
    private final String desc;

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    LeavePlanStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static LeavePlanStatusEnum getByCode(int code) {
        for (LeavePlanStatusEnum value : LeavePlanStatusEnum.values()) {
            if (value.getCode() == code) {
                return value;
            }
        }
        return null;
    }

    public static LeavePlanStatusEnum getByDesc(String desc) {
        for (LeavePlanStatusEnum value : LeavePlanStatusEnum.values()) {
            if (value.getDesc().equals(desc)) {
                return value;
            }
        }
        return null;
    }
}
