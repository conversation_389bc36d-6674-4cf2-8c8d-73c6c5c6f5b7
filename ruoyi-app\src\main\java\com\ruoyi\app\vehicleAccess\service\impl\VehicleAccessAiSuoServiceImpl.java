package com.ruoyi.app.vehicleAccess.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.ruoyi.app.vehicleAccess.domain.AccessInfo;
import com.ruoyi.app.vehicleAccess.domain.XctgVehicleAuditFlow;
import com.ruoyi.app.vehicleAccess.enums.CarLicensePlateColor;
import com.ruoyi.app.vehicleAccess.service.IVehicleAccessAiSuoService;
import com.ruoyi.app.vehicleAccess.common.utils.RequestUtils;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.http.HttpUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.net.URLEncoder;
import java.util.Date;

/**
 * 车辆进出厂爱锁Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class VehicleAccessAiSuoServiceImpl implements IVehicleAccessAiSuoService
{
    private static final Logger log = LoggerFactory.getLogger(VehicleAccessAiSuoServiceImpl.class);

    /**
     * 推送车辆入厂信息URL（HTTPS 正式/测试，保留 HTTP 原地址）
     */
    private static final String VEHICLE_ENTRY_INFO_URL = "https://172.16.13.222:8080/asseapi/api/v1/exts/xccarsinfo";
    private static final String VEHICLE_ENTRY_INFO_URL_TEST = "https://172.16.13.222:8088/asseapi/api/v1/exts/xccarsinfo";
    private static final String VEHICLE_ENTRY_INFO_URL_HTTP = "http://172.16.13.222:8080/asseapi/api/v1/exts/xccarsinfo";

    /**
     * 车辆现场失效处理URL前缀（HTTPS 正式/测试，保留 HTTP 原地址）
     */
    private static final String VEHICLE_CANCEL_URL_PREFIX = "https://172.16.13.222:8080/asseapi/api/v1/exts/delonesyndata";
    private static final String VEHICLE_CANCEL_URL_PREFIX_TEST = "https://172.16.13.222:8088/asseapi/api/v1/exts/delonesyndata";
    private static final String VEHICLE_CANCEL_URL_PREFIX_HTTP = "http://172.16.13.222:8080/asseapi/api/v1/exts/delonesyndata";

    /**
     * 推送车辆入厂信息
     *
     * @param xctgVehicleAuditFlow 车辆审核流程对象
     * @return 推送结果
     */
    @Override
    public String pushVehicleEntryInfo(XctgVehicleAuditFlow xctgVehicleAuditFlow) {
        try {
            // 构建AccessInfo对象
            AccessInfo accessInfo = buildAccessInfo(xctgVehicleAuditFlow);

            // 解析URL参数
            String param = RequestUtils.parseUrlParams(accessInfo);
            log.info("推送车辆入厂信息参数: {}", param);

            // 发送POST请求
            log.info("推送车辆入厂信息请求: {}", accessInfo);
            String result = HttpUtils.sendPostToVehicle(VEHICLE_ENTRY_INFO_URL, param);
            log.info("推送车辆入厂信息响应: {}", result);

            return result;
        } catch (Exception e) {
            log.error("推送车辆入厂信息失败", e);
            throw new RuntimeException("推送车辆入厂信息失败", e);
        }
    }

    /**
     * 车辆现场失效处理
     *
     * @param xctgVehicleAuditFlow 车辆审核流程对象
     * @return 处理结果码
     */
    @Override
    public int cancelVehicleInfo(XctgVehicleAuditFlow xctgVehicleAuditFlow) {
        try {
            // 构建删除URL
            String url = VEHICLE_CANCEL_URL_PREFIX + "?carNo=" + URLEncoder.encode(xctgVehicleAuditFlow.getCarNumber(), "UTF-8")
                    + "&carSerialNo=" + URLEncoder.encode(xctgVehicleAuditFlow.getFlowNo(), "UTF-8")
                    + "&applyDate=" + URLEncoder.encode(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, DateUtils.getNowDate()), "UTF-8");

            log.info("车辆现场失效处理URL: {}", url);

            // 发送DELETE请求
            int code = HttpUtils.doDeleteVehicle(url);
            log.info("车辆现场失效处理响应码: {}", code);

            return code;
        } catch (Exception e) {
            log.error("车辆现场失效处理失败", e);
            throw new RuntimeException("车辆现场失效处理失败", e);
        }
    }

    /**
     * 构建AccessInfo对象
     *
     * @param entity 车辆审核流程对象
     * @return AccessInfo对象
     */
    private AccessInfo buildAccessInfo(XctgVehicleAuditFlow entity) {
        AccessInfo accessInfo = new AccessInfo();
        accessInfo.setCarSerialNo(entity.getFlowNo());
        accessInfo.setIsCheck(1);
        accessInfo.setTimes(entity.getTimes());
        accessInfo.setCheckManager(entity.getApplyUserName());
        accessInfo.setCarNo(entity.getCarNumber());

        // 设置车牌颜色
        if (StringUtils.isNotBlank(entity.getLicensePlateColor())) {
            accessInfo.setCarNoColor(CarLicensePlateColor.getDescriptionByCode(entity.getLicensePlateColor()));
        }

        accessInfo.setCarType("访客车");
        accessInfo.setIsPushOrder(0);
        accessInfo.setApplyDate(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, DateUtils.getNowDate()));

        // 设置预计入厂时间
        if (entity.getPredictEntryDate() != null) {
            accessInfo.setPredictEntryDate(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, entity.getPredictEntryDate()));
        }

        // 设置预计入厂开始时间
        if (entity.getPredictEntryBeginDate() != null) {
            accessInfo.setPredictEntryBeginDate(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, entity.getPredictEntryBeginDate()));
        }

        // 设置预计入厂结束时间
        if (entity.getPredictEntryEndDate() != null) {
            accessInfo.setPredictEntryEndDate(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, entity.getPredictEntryEndDate()));
        }

        accessInfo.setChargeType("短期");
        accessInfo.setApplyAccountNo(entity.getApplyUserNo());
        accessInfo.setApplyAccountName(entity.getApplyUserName());
        accessInfo.setApplyAccountDName(entity.getDeptName());
        accessInfo.setDriver1(entity.getDriver1());
        accessInfo.setCarOwner(entity.getDriver1());
        accessInfo.setCarMobile(entity.getCarMobile());
        accessInfo.setPassNumber(entity.getPassportNo()); // 通行证编号

        // 设置备注信息 - 内部员工推送工号
        JSONObject userInfo = new JSONObject();
        if (StringUtils.isNotBlank(entity.getPassportType()) && entity.getPassportType().equals("0")) {
            userInfo.put("a_Number", entity.getDriverUserNo1());
        } else {
            userInfo.put("a_Number", "");
        }
        userInfo.put("d_Name", entity.getDeptName());
        accessInfo.setCarDetail(userInfo.toJSONString());

        // 其他单位司机推送身份证
        if (StringUtils.isNotBlank(entity.getPassportType()) && entity.getPassportType().equals("1")) {
            if (StringUtils.isNotBlank(entity.getIdentityCard1())) {
                accessInfo.setCarPin(entity.getIdentityCard1());
            }
        }

        // 设置公司信息
        if (StringUtils.isNotBlank(entity.getWorkUnit())) {
            accessInfo.setCompany(entity.getWorkUnit());
        }

        // 设置来访事由
        if (StringUtils.isNotBlank(entity.getEntryReason())) {
            accessInfo.setEntryReason(entity.getEntryReason());
        }

        return accessInfo;
    }
}
