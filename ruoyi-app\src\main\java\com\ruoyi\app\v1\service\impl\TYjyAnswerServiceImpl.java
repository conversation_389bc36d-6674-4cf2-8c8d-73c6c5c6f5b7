package com.ruoyi.app.v1.service.impl;

import java.io.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.*;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.app.domain.OutputForm;
import com.ruoyi.app.service.IOutputFormService;
import com.ruoyi.app.v1.domain.*;
import com.ruoyi.app.v1.mapper.TYjyDeptMapper;
import com.ruoyi.app.v1.mapper.TYjyFormMapper;
import com.ruoyi.app.v1.mapper.TYjyScrapyMapper;
import com.ruoyi.app.v1.service.*;
import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import com.ruoyi.app.v1.mapper.TYjyAnswerMapper;

import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import javax.script.ScriptException;

/**
 * TYjyAnswerService业务层处理
 * 
 * <AUTHOR>
 * @date 2024-10-21
 */
@Service
public class TYjyAnswerServiceImpl implements ITYjyAnswerService 
{
    @Autowired
    private TYjyAnswerMapper tYjyAnswerMapper;
    @Autowired
    private TYjyDeptMapper tYjyDeptMapper;
    @Autowired
    private ITYjyFormService tYjyFormService;
    @Autowired
    private IStaticAnswerService staticAnswerService;

    @Autowired
    private ITYjyFormTriggerService tYjyFormTriggerService;


    @Autowired
    private ITYjyTriggerService tYjyTriggerService;

    @Autowired
    private TYjyScrapyMapper tYjyScrapyMapper;

    @Autowired
    private ITYjyDimensionalityService tYjyDimensionalityService;
    /**
     * 查询TYjyAnswer
     * 
     * @param id TYjyAnswerID
     * @return TYjyAnswer
     */
    @Override
    public TYjyAnswer selectTYjyAnswerById(Long id)
    {
        return tYjyAnswerMapper.selectTYjyAnswerById(id);
    }

    @Override
    public List<TYjyAnswer> selectAnswerHisByFormId(TYjyAnswer tYjyAnswer)
    {
        return tYjyAnswerMapper.selectAnswerHisByFormId(tYjyAnswer);
    }

    @Override
    public List<TYjyAnswer> selectCheckList(TYjyAnswer tYjyAnswer)
    {
        return tYjyAnswerMapper.selectCheckList(tYjyAnswer);
    }

    @Override
    public List<TYjyAnswer> selectAnswerLast3forcheck(TYjyAnswer tYjyAnswer)
    {
        return tYjyAnswerMapper.selectAnswerLast3forcheck(tYjyAnswer);
    }

    @Override
    public int selectCheckListCount(TYjyAnswer tYjyAnswer)
    {
        return tYjyAnswerMapper.selectCheckListCount(tYjyAnswer);
    }

    @Override
    public List<TYjyAnswer> selectAnswerTop3(TYjyAnswer tYjyAnswer)
    {
        return tYjyAnswerMapper.selectAnswerTop3(tYjyAnswer);
    }


    @Override
    public List<TYjyAnswer> selectAnswerTop4(TYjyAnswer tYjyAnswer)
    {
        return tYjyAnswerMapper.selectAnswerTop4(tYjyAnswer);
    }


    public List<TYjyAnswer> selectAnswerLast3(TYjyAnswer tYjyAnswer)
    {
        return tYjyAnswerMapper.selectAnswerLast3(tYjyAnswer);
    }
    /**
     * 查询TYjyAnswer列表
     * 
     * @param tYjyAnswer TYjyAnswer
     * @return TYjyAnswer
     */
    @Override
    public List<TYjyAnswer> selectTYjyAnswerList(TYjyAnswer tYjyAnswer)
    {
        return tYjyAnswerMapper.selectTYjyAnswerList(tYjyAnswer);
    }

    @Override
    public List<TYjyAnswer> selectTYjyAnswerListCount(TYjyAnswer tYjyAnswer)
    {
        return tYjyAnswerMapper.selectTYjyAnswerListCount(tYjyAnswer);
    }

    @Override
    public List<TYjyAnswer> selectTYjyAnswerListSum(TYjyAnswerSearch tYjyAnswerSearch)
    {
        return tYjyAnswerMapper.selectTYjyAnswerListSum(tYjyAnswerSearch);
    }
    @Override
    public List<TYjyAnswer> selectAnswerWithPermission(TYjyAnswer tYjyAnswer)
    {
        return tYjyAnswerMapper.selectAnswerWithPermission(tYjyAnswer);
    }
    /**
     * 新增TYjyAnswer
     * 
     * @param tYjyAnswer TYjyAnswer
     * @return 结果
     */
    @Override
    public int insertTYjyAnswer(TYjyAnswer tYjyAnswer)
    {
//        TYjyAnswer isExit = new TYjyAnswer();
//        isExit.setFormId(tYjyAnswer.getFormId());
//        isExit.setFcDate(tYjyAnswer.getFcDate());
//        isExit.setDelFlag("0");
//        List<TYjyAnswer> search=tYjyAnswerMapper.selectTYjyAnswerList(isExit);
//        if(search.size()>0)
//        {
//            for(TYjyAnswer item:search)
//            {
//                tYjyAnswerMapper.deleteTYjyAnswerById(item.getId());
//            }
//        }
        tYjyAnswer.setCreateTime(DateUtils.getNowDate());
        return tYjyAnswerMapper.insertTYjyAnswer(tYjyAnswer);
    }

    /**
     * 修改TYjyAnswer
     * 
     * @param tYjyAnswer TYjyAnswer
     * @return 结果
     */
    @Override
    public int updateTYjyAnswer(TYjyAnswer tYjyAnswer)
    {
        tYjyAnswer.setUpdateTime(DateUtils.getNowDate());
        return tYjyAnswerMapper.updateTYjyAnswer(tYjyAnswer);
    }

    /**
     * 批量删除TYjyAnswer
     * 
     * @param ids 需要删除的TYjyAnswerID
     * @return 结果
     */
    @Override
    public int deleteTYjyAnswerByIds(Long[] ids)
    {
        return tYjyAnswerMapper.deleteTYjyAnswerByIds(ids);
    }

    /**
     * 删除TYjyAnswer信息
     * 
     * @param id TYjyAnswerID
     * @return 结果
     */
    @Override
    public int deleteTYjyAnswerById(Long id)
    {
        return tYjyAnswerMapper.deleteTYjyAnswerById(id);
    }

    @Override
    public List<TYjyExport> selectExportList(List<Long> list, String startDate, String endDate) {
        Map params = new HashMap<>();
        params.put("startDate", startDate);
        params.put("endDate", endDate);
        params.put("list", list);
        return tYjyAnswerMapper.selectExportList(params);
    }

    @Override
    public List<TYjyExport> selectExportListByFormId(List<Long> list, String startDate, String endDate) {
        Map params = new HashMap<>();
        params.put("startDate", startDate.substring(0,8)+"01");
        params.put("endDate", endDate.substring(0,8)+"31");
        params.put("list", list);
        return tYjyAnswerMapper.selectExportListByFormId(params);
    }

    @Override
    public List<TYjyExport> selectExportFormList(List<Long> list) {
        return tYjyAnswerMapper.selectExportFormList(list);
    }

    @Override
    public List<TYjyExport> selectExportFormListByFormId(List<Long> list) {
        return tYjyAnswerMapper.selectExportFormListByFormId(list);
    }


    //运营信息定时任务 新增每日需要填报的问题
    @Override
//    @Scheduled(cron = "1 0 0 * * ?")//定时时间可以进行控
    public void updateGas() {
        Date currentDate = new Date(); // 创建Calendar实例，并设置为当前时间
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(currentDate); //
        calendar.add(Calendar.DAY_OF_YEAR,-1);
        int currentYear = calendar.get(Calendar.YEAR); // 获取当前年份
        int currentMonth = calendar.get(Calendar.MONTH); // 获取当前月份
        int currentDay = calendar.get(Calendar.DAY_OF_MONTH); // 获取当前日期
        String time=""+ currentYear;
        if(currentMonth<=8)
        {
            time=time+"-0"+(currentMonth+1);
        }
        else
        {
            time=time+(currentMonth+1);
        }
        if(currentDay<=9)
        {
            time=time+"-0"+currentDay;
        }
        else
        {
            time=time+"-"+currentDay;
        }
        TYjyUpdateGas TYjyUpdateGas=new TYjyUpdateGas();
        TYjyUpdateGas.setTime(time);
        TYjyUpdateGas.setConcet("%&6");
        List<TYjyUpdateGas> updateList=tYjyAnswerMapper.selectUpdateGas(TYjyUpdateGas);
        Map<String,Double> updateMap=new HashMap<>();
        for(TYjyUpdateGas item: updateList)
        {
            updateMap.put(item.getPointCode(),item.getDisplayValue());
        }
        Map<String,Double> insertMap=new HashMap<>();

        insertMap.put("总产量 氧气",updateMap.get("V32016000307800228_SVt"));
        insertMap.put("总产量 吸附氧气",updateMap.get("V32016000307800231_SVt"));
        insertMap.put("总产量 中压氮气",updateMap.get("V32016000307800213_SVt"));
        insertMap.put("总产量 低压氮气",updateMap.get("ZY14KQ002_SVt")+updateMap.get("ZY13KQ018_SVt"));
        //ZY14KQ005_SVt缺失数据[能源氧气数据.POINT_CODE] == 'ZY14KQ005_SVt' or [能源氧气数据.POINT_CODE] == 'ZY11KQ013_SVt' or
        //[能源氧气数据.POINT_CODE] == 'ZY12KQ008_SVt' or [能源氧气数据.POINT_CODE] == 'ZY13KQ001_SVt'
        //insertMap.put("总产量 氩气",updateMap.get("ZY14KQ005_SVt")+updateMap.get("ZY11KQ013_SVt")+updateMap.get("ZY12KQ008_SVt")+updateMap.get("ZY13KQ001_SVt"));
        insertMap.put("总产量 仪表空气",updateMap.get("V32016000307800266_SVt"));
        insertMap.put("总产量 普通空气",updateMap.get("KYZK11011_SVt"));

        insertMap.put("钙业分厂 氧气",updateMap.get("SHYO2L001_SVt"));
        insertMap.put("钙业分厂 低压氮气",updateMap.get("SH0N2L010_SVt")+updateMap.get("SHYN2L010_SVt"));
        insertMap.put("钙业分厂 仪表空气",updateMap.get("SH0CA2010_SVt")+updateMap.get("SHYCA1010_SVt")+updateMap.get("SHYCA2010_SVt"));
        insertMap.put("矿渣微粉 仪表空气",updateMap.get("WF0CA2010_SVt"));

        insertMap.put("烧结分厂/1#烧结 低压氮气",updateMap.get("SJ3K12117_SVt"));
        insertMap.put("烧结分厂/1#烧结 仪表空气",updateMap.get("SJ3K12150_SVt"));
        insertMap.put("烧结分厂/1#烧结 普通空气",updateMap.get("SJ3K12120_SVt"));

        insertMap.put("烧结分厂/2#烧结 中压氮气",updateMap.get("S2JN2M001_SVt"));
        insertMap.put("烧结分厂/2#烧结 低压氮气",updateMap.get("SJ2N2L010_SVt"));
        insertMap.put("烧结分厂/2#烧结 仪表空气",updateMap.get("SJ2CA2010_SVt"));
        insertMap.put("烧结分厂/2#烧结 普通空气",updateMap.get("SJ2CA1010_SVt"));

        insertMap.put("炼铁分厂/1#高炉 氧气",updateMap.get("LT1K13032_SVt"));
        insertMap.put("炼铁分厂/1#高炉 吸附氧气",updateMap.get("V32016000307800161_SVt"));
        insertMap.put("炼铁分厂/1#高炉 低压氮气",updateMap.get("LT1K16014_SVt"));
        insertMap.put("炼铁分厂/1#高炉 仪表空气",updateMap.get("V32016000307800663_SVt"));


        insertMap.put("炼铁分厂/2#高炉 氧气",updateMap.get("GL2O2L010_SVt")+updateMap.get("GL2O2M010_SVt"));
        insertMap.put("炼铁分厂/2#高炉 吸附氧气",updateMap.get("GL2O2L011_SVt"));
        insertMap.put("炼铁分厂/2#高炉 低压氮气",updateMap.get("GL2N2L010_SVt"));
        insertMap.put("炼铁分厂/2#高炉 仪表空气",updateMap.get("V32016000307800265_SVt"));


        insertMap.put("炼铁分厂/3#高炉 氧气",updateMap.get("GL3O2L010_SVt"));
        insertMap.put("炼铁分厂/3#高炉 吸附氧气",updateMap.get("V32016000307800162_SVt"));
        insertMap.put("炼铁分厂/3#高炉 低压氮气",updateMap.get("GL3N2L010_SVt"));
        insertMap.put("炼铁分厂/3#高炉 仪表空气",updateMap.get("V32016000307800664_SVt"));

        insertMap.put("炼铁分厂/小喷煤 中压氮气",updateMap.get("XPMN2M001_SVt"));
        insertMap.put("炼铁分厂/小喷煤 低压氮气",updateMap.get("XPMN2L001_SVt"));
        insertMap.put("炼铁分厂/小喷煤 普通空气",updateMap.get("XPMCA0001_SVt"));

        insertMap.put("炼铁分厂/大喷煤 中压氮气",updateMap.get("V32016000307800662_SVt"));
        insertMap.put("炼铁分厂/大喷煤 低压氮气",updateMap.get("PM2N2L010_SVt1"));


        insertMap.put("一炼钢 氧气",updateMap.get("L1G0O2001_SVt")+updateMap.get("FCJO20001_SVt"));
        insertMap.put("一炼钢 低压氮气",updateMap.get("L1GN2L001_SVt"));
        insertMap.put("一炼钢 氩气",updateMap.get("L1G0Ar001_SVt"));
        insertMap.put("一炼钢 仪表空气",updateMap.get("V32016000307800665_SVt"));
        insertMap.put("一炼钢 普通空气",updateMap.get("L1G0KQ001_SVt"));

        insertMap.put("一炼连铸 氧气",updateMap.get("L1G0O2002_SVt")+updateMap.get("O02K11001_SVt"));
        insertMap.put("一炼连铸 普通空气",updateMap.get("L1G0KQ002_SVt"));

        insertMap.put("二炼钢 氧气",updateMap.get("L2G0O2002_SVt"));
        insertMap.put("二炼钢 中压氮气",updateMap.get("L2GN2M002_SVt")+updateMap.get("L2GN2M001_SVt"));
        insertMap.put("二炼钢 低压氮气",updateMap.get("L2GN2L001_SVt"));
        insertMap.put("二炼钢 氩气",updateMap.get("L2G0Ar001_SVt"));
        insertMap.put("二炼钢 仪表空气",updateMap.get("L2G1KQ001_SVt"));
        insertMap.put("二炼钢 普通空气",updateMap.get("L2G0KQ003_SVt"));

        insertMap.put("二炼连铸 氧气",updateMap.get("L2G0O2001_SVt"));
        insertMap.put("二炼连铸 普通空气",updateMap.get("L2G0KQ004_SVt"));


        insertMap.put("小方坯连铸 普通空气",updateMap.get("L2G0KQ002_SVt"));

        insertMap.put("大方坯连铸 普通空气",updateMap.get("L2G0KQ001_SVt"));

        insertMap.put("一轧钢 氧气",updateMap.get("O02K11001_SVt"));
        insertMap.put("一轧钢 低压氮气",updateMap.get("N2LK11001_SVt"));
        insertMap.put("一轧钢 仪表空气",updateMap.get("K1QK11001_SVt")+updateMap.get("K1QK11002_SVt"));
        insertMap.put("一轧钢 普通空气",updateMap.get("KYZK11028_SVt")+updateMap.get("K0QK11002_SVt")+updateMap.get("K0QK11001_SVt"));

        insertMap.put("二轧/大棒 氧气",updateMap.get("Z2G0O2001_SVt"));
        insertMap.put("二轧/大棒 低压氮气",updateMap.get("ZG2N2L011_SVt"));
        insertMap.put("二轧/大棒 仪表空气",updateMap.get("ZG2CA2011_SVt"));
        insertMap.put("二轧/大棒 普通空气",updateMap.get("ZG2CA1013_SVt"));

        insertMap.put("二轧/小棒 氧气",updateMap.get("Z2G0O2002_SVt"));
        insertMap.put("二轧/小棒 低压氮气",updateMap.get("ZG2N2L012_SVt"));
        insertMap.put("二轧/小棒 仪表空气",updateMap.get("ZG2CA2012_SVt"));
        insertMap.put("二轧/小棒 普通空气",updateMap.get("ZG2CA1011_SVt"));


        insertMap.put("特板炼钢 氧气",updateMap.get("TLGO2M010_SVt1"));
        insertMap.put("特板炼钢 中压氮气",updateMap.get("TLGN2M010_SVt1"));
        insertMap.put("特板炼钢 低压氮气",updateMap.get("TLGN2L010_SVt1"));
        insertMap.put("特板炼钢 氩气",updateMap.get("TLGAr0010_SVt1"));
        insertMap.put("特板炼钢 仪表空气",updateMap.get("LGTCA0010_SVt1"));
        insertMap.put("特板炼钢 普通空气",updateMap.get("KYZK11027_SVt")+updateMap.get("TLGCA0010_SVt1"));

        insertMap.put("3500中板 氧气",updateMap.get("ZB0O2L010_SVt1"));
        insertMap.put("3500中板 低压氮气",updateMap.get("ZB0N2L012_SVt"));
        insertMap.put("3500中板 仪表空气",updateMap.get("ZB0CA2010_SVt1"));
        insertMap.put("3500中板 普通空气",updateMap.get("ZB0CA1010_SVt1"));

        insertMap.put("3500常化线 低压氮气",updateMap.get("ZB0N2L010_SVt1"));

        insertMap.put("4300厚板 氧气",updateMap.get("V32016000307800217_SVt"));
        insertMap.put("4300厚板 低压氮气",updateMap.get("HB0N2L010_SVt1"));
        insertMap.put("4300厚板 仪表空气",updateMap.get("V32016000307800264_SVt"));
        insertMap.put("4300厚板 普通空气",updateMap.get("HB0CA1010_SVt1"));

        insertMap.put("热处理 氧气",updateMap.get("RCLO2L010_SVt1")+updateMap.get("RCLO2L011_SVt1"));
        insertMap.put("热处理 低压氮气",updateMap.get("RCLN2L010_SVt1"));
        insertMap.put("热处理 仪表空气",updateMap.get("RCLCA0010_SVt1"));

        insertMap.put("高线分厂 氧气",updateMap.get("GX0O2L010_SVt1"));
        insertMap.put("高线分厂 低压氮气",updateMap.get("GX0N2L010_SVt1"));
        insertMap.put("高线分厂 仪表空气",updateMap.get("GX0CA2010_SVt1"));
        insertMap.put("高线分厂 普通空气",updateMap.get("GX0CA1010_SVt1"));

        insertMap.put("线材深加工 低压氮气",updateMap.get("SJGN2L012_SVt"));
        insertMap.put("线材深加工 仪表空气",updateMap.get("GX0CA2010_SVt1"));


        insertMap.put("棒材深加工 低压氮气",updateMap.get("YLCN2L001_SVt"));// 棒材深加工 和 兴澄钢球 的低压氮气合并在一起的

        insertMap.put("热电分厂/发电 低压氮气",updateMap.get("HQZK11011_SVt"));
        insertMap.put("热电分厂/发电 仪表空气",updateMap.get("HQZK11015_SVt"));

        insertMap.put("热电分厂/鼓风 低压氮气",updateMap.get("GF0N2L010_SVt"));
        insertMap.put("热电分厂/鼓风 仪表空气",updateMap.get("GF0CA2010_SVt"));

        insertMap.put("热电分厂/亚临界 低压氮气",updateMap.get("YLJN2L001_SVt"));
        insertMap.put("热电分厂/亚临界 仪表空气",updateMap.get("YLJCA1001_SVt"));

        insertMap.put("水处理/一期 低压氮气",updateMap.get("Z2XN2L001_SVt"));
        insertMap.put("水处理/一期 仪表空气",updateMap.get("ZY12KQ010_SVt"));

        insertMap.put("水处理/二期 低压氮气",updateMap.get("V32016000307800163_SVt"));
        insertMap.put("水处理/二期 仪表空气",updateMap.get("ZG2CA0010_SVt1"));
        insertMap.put("水处理/二期 普通空气",updateMap.get("SCLYSK001_SVt"));

        insertMap.put("水处理/三期 普通空气",updateMap.get("SZ3CA0010_SVt1"));

        insertMap.put("煤气分厂 低压氮气",updateMap.get("MQ0K11023_SVt")+updateMap.get("MQ1K11070_SVt"));

        insertMap.put("储运公司 普通空气",updateMap.get("KYZK11016_SVt"));

        insertMap.put("综合利用 低压氮气",updateMap.get("ZH0N2L001_SVt"));
        insertMap.put("综合利用 普通空气",updateMap.get("ZH0CA0001_SVt"));

        insertMap.put("合金炉分厂 仪表空气",updateMap.get("HJLCA2001_SVt"));
        insertMap.put("合金炉分厂 普通空气",updateMap.get("HJLCA1001_SVt"));

        TYjyForm tYjyForm=new TYjyForm();
        tYjyForm.setDimensionalityId(Long.valueOf("273"));
        List<TYjyForm> formList =tYjyFormService.selectTYjyFormList(tYjyForm);

        TYjyDept searchdept=new TYjyDept();
        List<TYjyDept> deptList=tYjyDeptMapper.selectTYjyDeptList(searchdept);
        Map<String,String> deptListmap=new HashMap<>();
        for(TYjyDept deptitem:deptList)
        {
            deptListmap.put(deptitem.getPath(),deptitem.getDeptName());
        }
        for(TYjyForm item:formList)
        {
            TYjyAnswer tYjyAnswer=new TYjyAnswer();
            String value=null;
            if(insertMap.get(item.getDimensionalityNames()+" "+item.getFormQuestion())!=null)
            {
                value=insertMap.get(item.getDimensionalityNames()+" "+item.getFormQuestion()).toString();
//                System.out.println("test01");
////                System.out.println(tYjyAnswer);
//                System.out.println(value);
//                System.out.println(item);
            }
            else
            {
                value="";
            }
            tYjyAnswer.setFormValue(value);//核心环节
            tYjyAnswer.setCreateTime(DateUtils.getNowDate());
            tYjyAnswer.setFormId(item.getId());
            tYjyAnswer.setFcDate(time);
            tYjyAnswer.setDelFlag("0");
            tYjyAnswer.setVersion(Long.valueOf(1));
            tYjyAnswer.setCheckHistory("");

            String json=item.getDistributeDept();//实际上这里的逻辑非常的愚蠢，需要重新考虑填报部门和表单之间的关系,可以在新增的地方重新进行改造调整
//            if(json!=null)
//            {
//                JSONArray jsonlist=JSONArray.parseArray(json);
//                for(int i=0;i<jsonlist.size();i++)
//                {
//                    tYjyAnswer.setCreatorDept(jsonlist.get(i).toString());
//                    //此处新增常态表的导入
//                    JSONArray checklist=tYjyFormService.getchecklist(item.getCheckerList(),tYjyAnswer.getCreatorDept());
//                    if(checklist==null || checklist.size()==0)
//                    {
//                        tYjyAnswer.setStatus("2");
//                        staticAnswer(tYjyAnswer,deptListmap,item);
//                    }
//                    else
//                    {
//                        tYjyAnswer.setStatus("0");
////                        JSONObject userinfo =tYjyFormService.userinfo(checklist.get(0).toString());
////                        tYjyAnswer.setCheckUserName(userinfo.get("userName").toString());
////                        tYjyAnswer.setCheckWorkNo(userinfo.get("workNo").toString());
//                    }
//                    tYjyAnswer.setCheckHistory("系统自动抓取数据");
//                    tYjyAnswer.setCheckerList(item.getCheckerList());
//                    tYjyAnswerMapper.insertTYjyAnswer(tYjyAnswer);
//                }
//            }
//            System.out.println("test01");
//            System.out.println(tYjyAnswer);
        }
//        TYjyDept searchdept=new TYjyDept();
//        List<TYjyDept> dept=tYjyDeptMapper.selectTYjyDeptList(searchdept);
//        Map<String,String> map=new HashMap<>();
//        for(TYjyDept item:dept)
//        {
//            map.put(item.getPath(),item.getDeptName());
//        }
//        System.out.println("test");
    }


    //运营信息定时任务 新增每日需要填报的问题
    @Override
//    @Scheduled(cron = "1 0 0 * * ?")//定时时间可以进行控
    public void newupdateGas() {
        Calendar calendar = Calendar.getInstance();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Date currentDate = new Date();
        calendar.setTime(currentDate);
//        calendar.add(Calendar.DAY_OF_YEAR, -31);
//        int count=0;
        TYjyScrapy tYjyScrapy = new TYjyScrapy();
        tYjyScrapy.setOrigin("qtjs");
        tYjyScrapy.setUseFlag("0");
        List<TYjyScrapy> spList = tYjyScrapyMapper.selectTYjyScrapyList(tYjyScrapy);
//        while(count<30)
//        {
//            count=count+1;
            calendar.add(Calendar.DAY_OF_YEAR, -1);
            String time = dateFormat.format(calendar.getTime());
            TYjyUpdateGas TYjyUpdateGas = new TYjyUpdateGas();
            TYjyUpdateGas.setTime(time);
            TYjyUpdateGas.setConcet("%&6");
            List<TYjyUpdateGas> updateList = tYjyAnswerMapper.selectUpdateGas(TYjyUpdateGas);
            Map<String, String> updateMap = new HashMap<>();
            for (TYjyUpdateGas item : updateList) {
                updateMap.put(item.getPointCode(), item.getDisplayValue().toString());
            }
            List<TYjyAnswer> show = new ArrayList<>();
            for (TYjyScrapy item : spList) {
                String[] formList = item.getFormList().split(",");
                String rule = item.getTriggerRule();
//            int count=0;
                for (String formitem : formList) {
                    if (updateMap.get(formitem) != null) {
                        rule = rule.replace(formitem, updateMap.get(formitem));
                    } else {
                        System.out.println("数据缺失：" + formitem);
                        rule = rule.replace(formitem, "0");
//                    count=1;
//                    break;
                    }
                }
//            if(count==1)
//            {
//                continue;
//            }
                rule = rule.replace("+-", "-");
                rule = rule.replace("--", "+");
                Object result = null;
                try {
                    ScriptEngineManager manager = new ScriptEngineManager();
                    ScriptEngine engine = manager.getEngineByName("js");
                    result = engine.eval(rule);
                } catch (ScriptException e) {
                    e.printStackTrace();
                }
                TYjyAnswer finallinsert = new TYjyAnswer();
                TYjyForm checksearch = tYjyFormService.selectTYjyFormById(item.getFormId());
                if (checksearch.getFormType().equals("0")) {
                    BigDecimal bd = new BigDecimal(result.toString());
                    bd = bd.setScale(0, RoundingMode.HALF_UP);
                    finallinsert.setFormValue(bd.stripTrailingZeros().toPlainString());
                } else {
                    if (item.getDecimalNum() != null) {
                        BigDecimal bd = new BigDecimal(result.toString());
                        bd = bd.setScale(item.getDecimalNum(), RoundingMode.HALF_UP);
                        finallinsert.setFormValue(bd.stripTrailingZeros().toPlainString());
                    } else {
                        BigDecimal bd = new BigDecimal(result.toString());
                        finallinsert.setFormValue(bd.stripTrailingZeros().toPlainString());
                    }
                }
                finallinsert.setFormId(item.getFormId());
                finallinsert.setFcDate(time);
                finallinsert.setCreatorName("系统自动抓取");
                finallinsert.setFormFile("[]");
                dealaddalone(finallinsert);
            }
//        }
    }

//完整版自动抓取
    @Override
    @Scheduled(cron = "0 0 6 * * ?")
    public void scrapyUpadte() {
        Calendar calendar = Calendar.getInstance();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Date currentDate = new Date();
        calendar.setTime(currentDate);
        calendar.add(Calendar.DAY_OF_YEAR, -1);
        String time = dateFormat.format(calendar.getTime());
        TYjyScrapy tYjyScrapy = new TYjyScrapy();
        List<TYjyScrapy> originList = tYjyScrapyMapper.selectOriginList(tYjyScrapy);
        for(TYjyScrapy oitem:originList)
        {
            TYjyScrapy search = new TYjyScrapy();
            search.setOrigin(oitem.getOrigin());
            search.setUseFlag("0");
            List<TYjyScrapy> spListall = tYjyScrapyMapper.selectTYjyScrapyList(search);//获取对应数据源所有的配置
            List<TYjyScrapy> spList=scrapyClear(spListall,time);//筛选今天要更新的配置，如果没有需要更新的配置则continue
            if(spList.size()==0)
            {
                continue;
            }
            List<TYjyUpdateGas> updateList=scrapySearch(oitem.getOrigin(),time);
            Map<String, String> updateMap = new HashMap<>();
            for (TYjyUpdateGas item : updateList) {
                if(item.getDisplayValue()==null)
                {
                    int countnum=1;
                    updateMap.put(item.getPointCode(), "0");
                }
                else
                {
                    updateMap.put(item.getPointCode(), item.getDisplayValue().toString());
                }
            }
            for (TYjyScrapy item : spList)
            {
                if(item.getType().equals("0"))
                {
                    String[] formList = item.getFormList().split(",");
                    String rule = item.getTriggerRule();
                    for (String formitem : formList)
                    {
                        if (updateMap.get(formitem) != null) {
                            rule = rule.replace(formitem, updateMap.get(formitem));
                        } else {
                            System.out.println("数据缺失：" + formitem);
                            rule = rule.replace(formitem, "0");
                        }
                    }
                    rule = rule.replace("+-", "-");
                    rule = rule.replace("--", "+");
                    Object result = null;
                    try
                    {
                        ScriptEngineManager manager = new ScriptEngineManager();
                        ScriptEngine engine = manager.getEngineByName("js");
                        result = engine.eval(rule);
                    } catch (ScriptException e) {
                        e.printStackTrace();
                    }
                    TYjyAnswer finallinsert = new TYjyAnswer();
                    TYjyForm checksearch = tYjyFormService.selectTYjyFormById(item.getFormId());
                    if (checksearch.getFormType().equals("0"))
                    {
                        BigDecimal bd = new BigDecimal(result.toString());
                        bd = bd.setScale(0, RoundingMode.HALF_UP);
                        finallinsert.setFormValue(bd.stripTrailingZeros().toPlainString());
                    } else
                    {
                        if (item.getDecimalNum() != null)
                        {
                            BigDecimal bd = new BigDecimal(result.toString());
                            bd = bd.setScale(item.getDecimalNum(), RoundingMode.HALF_UP);
                            finallinsert.setFormValue(bd.stripTrailingZeros().toPlainString());
                        }
                        else
                        {
                            BigDecimal bd = new BigDecimal(result.toString());
                            finallinsert.setFormValue(bd.stripTrailingZeros().toPlainString());
                        }
                    }
                    finallinsert.setFormId(item.getFormId());
                    finallinsert.setFcDate(time);
                    finallinsert.setFormFile("[]");
                    antidealaddalone(finallinsert);
                }
                if(item.getType().equals("1"))
                {
                    TYjyAnswer finallinsert = new TYjyAnswer();
                    finallinsert.setFormValue(item.getTriggerRule());
                    finallinsert.setFormId(item.getFormId());
                    finallinsert.setFcDate(time);
                    finallinsert.setFormFile("[]");
                    antidealaddalone(finallinsert);
                }
            }
        }
    }

    public List<TYjyScrapy> scrapyClear(List<TYjyScrapy> spListall,String time)
    {
        String time0=time;//日
        String time1=dealTime(time,"1");//月
        String time2=dealTime(time,"2");//季度
        String time3=dealTime(time,"3");//半年
        String time4=dealTime(time,"4");//年
        String time5=dealTime(time,"5");//周
        List<TYjyScrapy> spList=new ArrayList<>();
        for(TYjyScrapy item:spListall)
        {
            if(item.getFrequency().equals("0"))
            {
                spList.add(item);
            }
            if(item.getFrequency().equals("1")&&time1.equals(time))
            {
                spList.add(item);
            }
            if(item.getFrequency().equals("2")&&time2.equals(time))
            {
                spList.add(item);
            }
            if(item.getFrequency().equals("3")&&time3.equals(time))
            {
                spList.add(item);
            }
            if(item.getFrequency().equals("4")&&time4.equals(time))
            {
                spList.add(item);
            }
            if(item.getFrequency().equals("5")&&time5.equals(time))
            {
                spList.add(item);
            }
        }
        return spList;
    }
    public List<TYjyUpdateGas> scrapySearch(String origin,String time)
    {
        List<TYjyUpdateGas> updateList=new ArrayList<>();
        if(origin.equals("qtjs"))//适用于 气体结算月报 煤气月报 蒸汽月报
        {
            TYjyUpdateGas TYjyUpdateGas = new TYjyUpdateGas();
            TYjyUpdateGas.setTime(time);
            TYjyUpdateGas.setConcet("%&6");
            updateList = tYjyAnswerMapper.selectUpdateGas(TYjyUpdateGas);
        }

        if(origin.equals("dianli"))//适用于 气体结算月报 煤气月报 蒸汽月报
        {
            TYjyUpdateGas TYjyUpdateGas = new TYjyUpdateGas();
            TYjyUpdateGas.setTime(time);
            TYjyUpdateGas.setConcet("%&6");
            updateList = tYjyAnswerMapper.selectUpdateGas(TYjyUpdateGas);
        }

//        if(origin.equals("dianli1"))//仅适用于数据补充环节，还有很多的问题需要之后在思考，然后还有的部分内容需要不停的核算处理
//        {
//            TYjyUpdateGas TYjyUpdateGas = new TYjyUpdateGas();
//            TYjyUpdateGas.setTime(time);
//            TYjyUpdateGas.setConcet("%&6");
//            updateList = tYjyAnswerMapper.selectUpdateGas(TYjyUpdateGas);
//        }
        if(origin.equals("trq"))//适用于 天然气月报 natural gas
        {
            TYjyUpdateGas TYjyUpdateGas = new TYjyUpdateGas();
            TYjyUpdateGas.setTime(time);
            TYjyUpdateGas.setBeginTime(trqTime(time));
            TYjyUpdateGas.setConcet("%&5");
            updateList = tYjyAnswerMapper.selectUpdateNaturalGas(TYjyUpdateGas);
        }
        return updateList;
    }

    public String trqTime(String time)
    {
        String retime="";
        Date currentDate = new Date();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        Calendar calendar = Calendar.getInstance();
        if(time!=null)
        {
            try {
                currentDate = formatter.parse(time);
                calendar.setTime(currentDate);
            } catch (ParseException e) {
                // 异常处理代码
                e.printStackTrace();
                // 或者其他错误处理逻辑
            }
        }
        int currentYear = calendar.get(Calendar.YEAR); // 获取当前年份
        int currentMonth = calendar.get(Calendar.MONTH); // 获取当前月份
        int currentDay = calendar.get(Calendar.DAY_OF_MONTH); // 获取当前日期
        if(currentDay>19)
        {
            calendar.set(currentYear,currentMonth,19);
        }
        else
        {
            calendar.set(currentYear,currentMonth-1,19);
        }
        retime=formatter.format(calendar.getTime());
        return retime;
    }
    @Override
    public void localgasupdate(String fcDate) {
        Calendar calendar = Calendar.getInstance();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

        Date currentDate = new Date();

        String InsertFcDate=fcDate;
        if(InsertFcDate!=null)
        {
            try {
                currentDate = dateFormat.parse(InsertFcDate);
                calendar.setTime(currentDate);
            } catch (ParseException e) {
                // 异常处理代码
                e.printStackTrace();
                // 或者其他错误处理逻辑
            }
        }
        calendar.setTime(currentDate);
        int count=0;
//        TYjyScrapy tYjyScrapy = new TYjyScrapy();
//        tYjyScrapy.setOrigin("qtjs");
//        tYjyScrapy.setUseFlag("0");
//        List<TYjyScrapy> spList = tYjyScrapyMapper.selectTYjyScrapyList(tYjyScrapy);
        TYjyScrapy tYjyScrapy = new TYjyScrapy();
        List<TYjyScrapy> originList = tYjyScrapyMapper.selectOriginList(tYjyScrapy);
        while(count<1)
        {

//            calendar.add(Calendar.MONTH, -1);
            calendar.add(Calendar.DAY_OF_YEAR, -1);
            String time = dateFormat.format(calendar.getTime());
            for(TYjyScrapy oitem:originList)
            {
                TYjyScrapy search = new TYjyScrapy();
                search.setOrigin(oitem.getOrigin());
                search.setUseFlag("0");
                List<TYjyScrapy> spListall = tYjyScrapyMapper.selectTYjyScrapyList(search);//获取对应数据源所有的配置
                List<TYjyScrapy> spList=scrapyClear(spListall,time);//筛选今天要更新的配置，如果没有需要更新的配置则continue
                if(spList.size()==0)
                {
                    continue;
                }
                List<TYjyUpdateGas> updateList=scrapySearch(oitem.getOrigin(),time);
                Map<String, String> updateMap = new HashMap<>();
                for (TYjyUpdateGas item : updateList) {
                    if(item.getDisplayValue()==null)
                    {
                        int countnum=1;
                    }
                    else
                    {
                        updateMap.put(item.getPointCode(), item.getDisplayValue().toString());
                    }
                }
                for (TYjyScrapy item : spList)
                {
                    if(item.getType().equals("0"))
                    {
                        String[] formList = item.getFormList().split(",");
                        String rule = item.getTriggerRule();
                        for (String formitem : formList)
                        {
                            if (updateMap.get(formitem) != null) {
                                rule = rule.replace(formitem, updateMap.get(formitem));
                            } else {
                                System.out.println("数据缺失：" + formitem);
                                rule = rule.replace(formitem, "0");
                            }
                        }
                        rule = rule.replace("+-", "-");
                        rule = rule.replace("--", "+");
                        Object result = null;
                        try
                        {
                            ScriptEngineManager manager = new ScriptEngineManager();
                            ScriptEngine engine = manager.getEngineByName("js");
                            result = engine.eval(rule);
                        } catch (ScriptException e) {
                            e.printStackTrace();
                        }
                        TYjyAnswer finallinsert = new TYjyAnswer();
                        TYjyForm checksearch = tYjyFormService.selectTYjyFormById(item.getFormId());
                        if (checksearch.getFormType().equals("0"))
                        {
                            BigDecimal bd = new BigDecimal(result.toString());
                            bd = bd.setScale(0, RoundingMode.HALF_UP);
                            finallinsert.setFormValue(bd.stripTrailingZeros().toPlainString());
                        } else
                        {
                            if (item.getDecimalNum() != null)
                            {
                                BigDecimal bd = new BigDecimal(result.toString());
                                bd = bd.setScale(item.getDecimalNum(), RoundingMode.HALF_UP);
                                finallinsert.setFormValue(bd.stripTrailingZeros().toPlainString());
                            }
                            else
                            {
                                BigDecimal bd = new BigDecimal(result.toString());
                                finallinsert.setFormValue(bd.stripTrailingZeros().toPlainString());
                            }
                        }
                        finallinsert.setFormId(item.getFormId());
                        finallinsert.setFcDate(time);
                        finallinsert.setFormFile("[]");
                        antidealaddalone(finallinsert);
                    }
                    if(item.getType().equals("1"))
                    {
                        TYjyAnswer finallinsert = new TYjyAnswer();
                        finallinsert.setFormValue(item.getTriggerRule());
                        finallinsert.setFormId(item.getFormId());
                        finallinsert.setFcDate(time);
                        finallinsert.setFormFile("[]");
                        antidealaddalone(finallinsert);
                    }
                }
            }
            count=count+1;
        }
    }

    public void localgasupdatePlus(String fcDate,String orign){
        Calendar calendar = Calendar.getInstance();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

        Date currentDate = new Date();

        String InsertFcDate=fcDate;
        if(InsertFcDate!=null)
        {
            try {
                currentDate = dateFormat.parse(InsertFcDate);
                calendar.setTime(currentDate);
            } catch (ParseException e) {
                // 异常处理代码
                e.printStackTrace();
                // 或者其他错误处理逻辑
            }
        }
        calendar.setTime(currentDate);
        int count=0;
        TYjyScrapy tYjyScrapy = new TYjyScrapy();
        List<TYjyScrapy> originList = tYjyScrapyMapper.selectOriginList(tYjyScrapy);
        while(count<1)
        {
            calendar.add(Calendar.DAY_OF_YEAR, -1);
            String time = dateFormat.format(calendar.getTime());
            for(TYjyScrapy oitem:originList)
            {
                if(!orign.equals(oitem.getOrigin()))
                {
                    continue;
                }
                TYjyScrapy search = new TYjyScrapy();
                search.setOrigin(oitem.getOrigin());
                search.setUseFlag("0");
                List<TYjyScrapy> spListall = tYjyScrapyMapper.selectTYjyScrapyList(search);//获取对应数据源所有的配置
                List<TYjyScrapy> spList=scrapyClear(spListall,time);//筛选今天要更新的配置，如果没有需要更新的配置则continue
                if(spList.size()==0)
                {
                    continue;
                }
                List<TYjyUpdateGas> updateList=scrapySearch(oitem.getOrigin(),time);
                Map<String, String> updateMap = new HashMap<>();
                for (TYjyUpdateGas item : updateList) {
                    if(item.getDisplayValue()==null)
                    {
                        int countnum=1;
                    }
                    else
                    {
                        updateMap.put(item.getPointCode(), item.getDisplayValue().toString());
                    }
                }
                for (TYjyScrapy item : spList)
                {
                    if(item.getType().equals("0"))
                    {
                        String[] formList = item.getFormList().split(",");
                        String rule = item.getTriggerRule();
                        for (String formitem : formList)
                        {
                            if (updateMap.get(formitem) != null) {
                                rule = rule.replace(formitem, updateMap.get(formitem));
                            } else {
                                System.out.println("数据缺失：" + formitem);
                                rule = rule.replace(formitem, "0");
                            }
                        }
                        rule = rule.replace("+-", "-");
                        rule = rule.replace("--", "+");
                        Object result = null;
                        try
                        {
                            ScriptEngineManager manager = new ScriptEngineManager();
                            ScriptEngine engine = manager.getEngineByName("js");
                            result = engine.eval(rule);
                        } catch (ScriptException e) {
                            e.printStackTrace();
                        }
                        TYjyAnswer finallinsert = new TYjyAnswer();
                        TYjyForm checksearch = tYjyFormService.selectTYjyFormById(item.getFormId());
                        if (checksearch.getFormType().equals("0"))
                        {
                            BigDecimal bd = new BigDecimal(result.toString());
                            bd = bd.setScale(0, RoundingMode.HALF_UP);
                            finallinsert.setFormValue(bd.stripTrailingZeros().toPlainString());
                        } else
                        {
                            if (item.getDecimalNum() != null)
                            {
                                BigDecimal bd = new BigDecimal(result.toString());
                                bd = bd.setScale(item.getDecimalNum(), RoundingMode.HALF_UP);
                                finallinsert.setFormValue(bd.stripTrailingZeros().toPlainString());
                            }
                            else
                            {
                                BigDecimal bd = new BigDecimal(result.toString());
                                finallinsert.setFormValue(bd.stripTrailingZeros().toPlainString());
                            }
                        }
                        finallinsert.setFormId(item.getFormId());
                        finallinsert.setFcDate(time);
                        finallinsert.setFormFile("[]");
                        antidealaddalone(finallinsert);
                    }
                    if(item.getType().equals("1"))
                    {
                        TYjyAnswer finallinsert = new TYjyAnswer();
                        finallinsert.setFormValue(item.getTriggerRule());
                        finallinsert.setFormId(item.getFormId());
                        finallinsert.setFcDate(time);
                        finallinsert.setFormFile("[]");
                        antidealaddalone(finallinsert);
                    }
                }
            }
            count=count+1;
        }
    }



    //气体结算批量同步功能
    @Override
//    @Scheduled(cron = "1 0 0 * * ?")//定时时间可以进行控
    public void qtjsdatedeal(){
        Calendar calendar = Calendar.getInstance();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Date currentDate = new Date();
        calendar.setTime(currentDate);

        TYjyScrapy tYjyScrapy = new TYjyScrapy();
        tYjyScrapy.setOrigin("qtjs");
        tYjyScrapy.setUseFlag("0");
        List<TYjyScrapy> spList = tYjyScrapyMapper.selectTYjyScrapyList(tYjyScrapy);
        HashSet<String> show=new HashSet<>();
        int count=0;
        while(count<100)
        {
            count=count+1;
            calendar.add(Calendar.DAY_OF_YEAR, -1);
            String time = dateFormat.format(calendar.getTime());
            TYjyUpdateGas TYjyUpdateGas = new TYjyUpdateGas();
            TYjyUpdateGas.setTime(time);
            TYjyUpdateGas.setConcet("%&6");
            List<TYjyUpdateGas> updateList = tYjyAnswerMapper.selectUpdateGas(TYjyUpdateGas);
            Map<String, String> updateMap = new HashMap<>();
            for (TYjyUpdateGas item : updateList) {
                updateMap.put(item.getPointCode(), item.getDisplayValue().toString());
            }
            for (TYjyScrapy item : spList) {
                String[] formList = item.getFormList().split(",");
                for (String formitem : formList) {
                    if (updateMap.get(formitem) != null) {
                    } else {
                        System.out.println("数据缺失：" + formitem);
                        show.add(formitem);
                        break;
                    }
                }

            }
        }
        System.out.println(show.size());
    }


    @Override
    public int staticAnswer(TYjyAnswer tYjyAnswer,Map<String,String> deptListmap,TYjyForm tYjyForm)
    {
        StaticAnswer insert=staticAnswerService.getTYjyDimensionalityinfo(tYjyForm.getDimensionalityId());
        insert.setFormId(tYjyForm.getId());
        insert.setFormNote(tYjyForm.getFormNote());
        insert.setFormQuestion(tYjyForm.getFormQuestion());
        insert.setFormType(tYjyForm.getFormType());
        insert.setFrequency(tYjyForm.getFrequency());
        insert.setFormValue(tYjyAnswer.getFormValue());
        insert.setFcDate(tYjyAnswer.getFcDate());
        insert.setDeptCode(tYjyAnswer.getCreatorDept());
        insert.setDeptName(deptListmap.get(tYjyAnswer.getCreatorDept()));

        insert.setFormNote1(tYjyForm.getFormNote1());
        insert.setReason(tYjyAnswer.getReason());
        insert.setMeasure(tYjyAnswer.getMeasure());

        return staticAnswerService.insertStaticAnswer(insert);//以后再改
    }


    @Override
    public int staticAnswer(TYjyAnswer tYjyAnswer,Map<String,String> deptListmap)
    {
        TYjyForm tYjyForm=tYjyFormService.selectTYjyFormById(tYjyAnswer.getFormId());
        StaticAnswer insert=staticAnswerService.getTYjyDimensionalityinfo(tYjyForm.getDimensionalityId());
        insert.setFormId(tYjyForm.getId());
        insert.setFormNote(tYjyForm.getFormNote());
        insert.setFormQuestion(tYjyForm.getFormQuestion());
        insert.setFormType(tYjyForm.getFormType());
        insert.setFrequency(tYjyForm.getFrequency());
        insert.setFormValue(tYjyAnswer.getFormValue());
        insert.setFcDate(tYjyAnswer.getFcDate());
        insert.setDeptCode(tYjyAnswer.getCreatorDept());
        insert.setDeptName(deptListmap.get(tYjyAnswer.getCreatorDept()));
        insert.setFormNote1(tYjyForm.getFormNote1());
        insert.setReason(tYjyAnswer.getReason());
        insert.setMeasure(tYjyAnswer.getMeasure());
        return staticAnswerService.insertStaticAnswer(insert);//以后再改
    }

    @Override
    public int editForm(TYjyForm tYjyForm)
    {
        StaticAnswer update=new StaticAnswer();
        update.setFormId(tYjyForm.getId());
        if(tYjyForm.getFormNote()!=null)
        {
            update.setFormNote(tYjyForm.getFormNote());
        }
        if(tYjyForm.getFormNote1()!=null)
        {
            update.setFormNote1(tYjyForm.getFormNote1());
        }
        return staticAnswerService.updateStaticAnswer(update);//以后再改
    }

    @Override
    public int dealaddalone(TYjyAnswer tYjyAnswer)
    {
        TYjyForm checksearch=tYjyFormService.selectTYjyFormById(tYjyAnswer.getFormId());
        SysUser user= new SysUser();
        try
        {
            user= SecurityUtils.getLoginUser().getUser();
        }catch (Exception e)
        {
            user.setNickName("系统自动抓取");
            user.setUserName("X107086717");
        }
        String fcDate=dealTime(tYjyAnswer.getFcDate(),checksearch.getFrequency());
        return finalinsert(tYjyAnswer,checksearch,user,fcDate);
    }

    @Override
    public int adddeal(TYjyAnswer tYjyAnswer,SysUser user,TYjyForm checksearch)
    {
        String fcDate=dealTime(tYjyAnswer.getFcDate(),checksearch.getFrequency());
        return finalinsert(tYjyAnswer,checksearch,user,fcDate);
    }

    @Override
    public int antidealaddalone(TYjyAnswer tYjyAnswer)
    {
        TYjyForm checksearch=tYjyFormService.selectTYjyFormById(tYjyAnswer.getFormId());
        SysUser user= new SysUser();
        user.setNickName("系统自动抓取");
        user.setUserName("X107086717");
        String fcDate=dealTime(tYjyAnswer.getFcDate(),checksearch.getFrequency());
        return finalinsert(tYjyAnswer,checksearch,user,fcDate);
    }

   //此处可以进一步的优化处理，但是先不考虑，优先处理获取不到小数的问题
    public int dealnewadd(List<JSONObject> json)
    {
        SysUser user= SecurityUtils.getLoginUser().getUser();
        for (JSONObject item : json) {
            TYjyAnswer tYjyAnswer=new TYjyAnswer();
            if(item.get("fcDate")!=null)
            {
                tYjyAnswer.setFcDate(item.get("fcDate").toString());
            }
            if(item.get("formValue")==null)
            {
                tYjyAnswer.setFormValue(null);
            }
            else
            {
                tYjyAnswer.setFormValue(item.get("formValue").toString());
            }
            if(item.get("creatorDept")!=null)
            {
                tYjyAnswer.setCreatorDept(item.get("creatorDept").toString());
            }

            tYjyAnswer.setFormId(Long.valueOf(item.get("formId").toString()));
            if(item.get("reason")!=null)
            {
                tYjyAnswer.setReason(item.get("reason").toString());
            }
            if(item.get("measure")!=null)
            {
                tYjyAnswer.setMeasure(item.get("measure").toString());
            }
            TYjyForm searchInfo=new TYjyForm();
            searchInfo.setId(tYjyAnswer.getFormId());
            searchInfo.setWorkNo(user.getUserName());
            TYjyForm checksearch=tYjyFormService.selectTYjyFormByIdWithPermission(searchInfo);
            if(checksearch.getRuleType()!=null)
            {
                if(checksearch.getRuleType().equals("11"))
                {
                    continue;
                }
            }
            String fcDate=dealTime(tYjyAnswer.getFcDate(),checksearch.getFrequency());
            finalinsert(tYjyAnswer,checksearch,user,fcDate);
        }
        return 1;
    }

    public String dealTime(String InsertFcDate,String insertFrequency)
    {
        Date currentDate = new Date();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        Calendar calendar = Calendar.getInstance();
        if(InsertFcDate!=null)
        {
            try {
                currentDate = formatter.parse(InsertFcDate);
                calendar.setTime(currentDate);
            } catch (ParseException e) {
                // 异常处理代码
                e.printStackTrace();
                // 或者其他错误处理逻辑
            }
        }
        int currentYear = calendar.get(Calendar.YEAR); // 获取当前年份
        int currentMonth = calendar.get(Calendar.MONTH); // 获取当前月份
        int currentDay = calendar.get(Calendar.DAY_OF_MONTH); // 获取当前日期
        String fcDate=currentYear+"";
        if(currentMonth<9)
        {
            fcDate=fcDate+"-0"+(currentMonth+1);
        }
        else
        {
            fcDate=fcDate+"-"+(currentMonth+1);
        }
        if(currentDay<10)
        {
            fcDate=fcDate+"-0"+currentDay;
        }
        else
        {
            fcDate=fcDate+"-"+currentDay;
        }
        if(insertFrequency.equals("0"))
        {
            fcDate=fcDate;
        }else if(insertFrequency.equals("1"))
        {
            if(currentMonth<9)
            {
                fcDate=currentYear+"-0"+(currentMonth+1)+"-01";
            }
            else
            {
                fcDate=currentYear+"-"+(currentMonth+1)+"-01";
            }
        }else if(insertFrequency.equals("2"))
        {
            if(currentMonth<=2)
            {
                fcDate=currentYear+"-01-01";
            }else if(currentMonth<=5)
            {
                fcDate=currentYear+"-04-01";
            }else if(currentMonth<=8)
            {
                fcDate=currentYear+"-07-01";
            }else if(currentMonth<=11)
            {
                fcDate=currentYear+"-10-01";
            }
        }else if(insertFrequency.equals("3"))
        {
            if(currentMonth<=5)
            {
                fcDate=currentYear+"-01-01";
            }
            else
            {
                fcDate=currentYear+"-07-01";
            }
        }else if(insertFrequency.equals("4"))
        {
            fcDate=currentYear+"-01-01";
        }else if(insertFrequency.equals("5"))
        {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            calendar.add(Calendar.DAY_OF_YEAR,-1);
            calendar.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
            fcDate=dateFormat.format(calendar.getTime());
        }else if(insertFrequency.equals("6"))
        {
            //假设要获取所有的数据，理论上应该不会用到这个方法，但是还是要防备，理论上应该要越详细越合理，但是还是要考虑更多细节限制上的问题，还需要进一步的思考细节，完善填报结果
            fcDate=null;
        }
        return fcDate;
    }

    @Override
    public String dealTimeForEnd(String InsertFcDate,String insertFrequency)
    {
        Date currentDate = new Date();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        Calendar calendar = Calendar.getInstance();
        if(InsertFcDate!=null)
        {
            try {
                currentDate = formatter.parse(InsertFcDate);
                calendar.setTime(currentDate);
            } catch (ParseException e) {
                // 异常处理代码
                e.printStackTrace();
                // 或者其他错误处理逻辑
            }
        }
        int currentYear = calendar.get(Calendar.YEAR); // 获取当前年份
        int currentMonth = calendar.get(Calendar.MONTH); // 获取当前月份
        int currentDay = calendar.get(Calendar.DAY_OF_MONTH); // 获取当前日期
        String fcDate=currentYear+"";
        if(currentMonth<9)
        {
            fcDate=fcDate+"-0"+(currentMonth+1);
        }
        else
        {
            fcDate=fcDate+"-"+(currentMonth+1);
        }
        if(currentDay<10)
        {
            fcDate=fcDate+"-0"+currentDay;
        }
        else
        {
            fcDate=fcDate+"-"+currentDay;
        }
        if(insertFrequency.equals("0"))
        {
            fcDate=fcDate;
        }else if(insertFrequency.equals("1"))
        {
            LocalDate inputDate = LocalDate.parse(InsertFcDate, DateTimeFormatter.ISO_DATE);
            // 获取当月的最后一天
            LocalDate endOfMonth = inputDate.with(TemporalAdjusters.lastDayOfMonth());
            fcDate=endOfMonth.toString();

//            if(currentMonth<9)
//            {
//                fcDate=currentYear+"-0"+(currentMonth+1)+"-01";
//            }
//            else
//            {
//                fcDate=currentYear+"-"+(currentMonth+1)+"-01";
//            }
        }else if(insertFrequency.equals("2"))
        {
            if(currentMonth<=2)
            {
                fcDate=currentYear+"-03-31";
            }else if(currentMonth<=5)
            {
                fcDate=currentYear+"-06-30";
            }else if(currentMonth<=8)
            {
                fcDate=currentYear+"-09-30";
            }else if(currentMonth<=11)
            {
                fcDate=currentYear+"-12-31";
            }
        }else if(insertFrequency.equals("3"))
        {
            if(currentMonth<=5)
            {
                fcDate=currentYear+"-06-30";
            }
            else
            {
                fcDate=currentYear+"-12-31";
            }
        }else if(insertFrequency.equals("4"))
        {
            fcDate=currentYear+"-12-31";
        }else if(insertFrequency.equals("5"))
        {
            LocalDate inputDate = LocalDate.parse(InsertFcDate, DateTimeFormatter.ISO_DATE);
            LocalDate endOfWeek = inputDate.with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY));
            fcDate=endOfWeek.toString();
        }else if(insertFrequency.equals("6"))
        {
            fcDate=null;
        }
        return fcDate;
    }


    @Override
    public int branceInsert(TYjyAnswer tYjyAnswer,TYjyForm checksearch,String workNo,String nickName,String fcDate,Map<String,String> deptListmap)
    {
        Date currentDate = new Date();
        int updateType=0;
        SimpleDateFormat addtime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String formattedDate = addtime.format(currentDate);
        tYjyAnswer.setCreatorDept(tYjyAnswer.getCreatorDept());//可能存在问题
        tYjyAnswer.setFcDate(tYjyDimensionalityService.dealfcdate(fcDate,checksearch.getFrequency()));
        tYjyAnswer.setCreatorName(nickName);
        tYjyAnswer.setCreatorNo(workNo);
        tYjyAnswer.setWorkNo(workNo);
        tYjyAnswer.setCreateTime(currentDate);
        tYjyAnswer.setCheckHistory(checksearch.getCheckHistory()+formattedDate+" "+nickName+" 提交审批;\n");
        tYjyAnswer.setCheckerList(checksearch.getCheckerList());
        tYjyAnswer.setFrequency(checksearch.getFrequency());
        tYjyAnswer.setDimensionalityPath(checksearch.getDimensionalityPath());
        tYjyAnswer.setDeptShow(checksearch.getDeptShow());
        String timeSwitch = deadlineTime(tYjyAnswer,checksearch);
        if(checksearch.getStatus().equals("2"))
        {
            if(timeSwitch.equals("1"))
            {
                return 1;
            }
            else
            {
                tYjyAnswerMapper.deleteTYjyAnswerById(checksearch.getAnswerId());
                tYjyAnswer.setVersion(Long.valueOf(checksearch.getVersion()+1));
                tYjyAnswer.setCheckHistory(checksearch.getCheckHistory());
            }
        }
        else
        {
            tYjyAnswer.setVersion(Long.valueOf(1));
            tYjyAnswer.setCheckHistory("");
        }


        //此处新增常态表的导入
        JSONArray checklist=tYjyFormService.getchecklist(checksearch.getCheckerList(),tYjyAnswer.getCreatorDept());
        if(checklist==null || checklist.size()==0)
        {
            tYjyAnswer.setStatus("2");
            staticAnswer(tYjyAnswer,deptListmap,checksearch);
        }
        else
        {
            tYjyAnswer.setStatus("0");
            JSONObject userinfo =tYjyFormService.userinfo(checklist.get(0).toString());
            tYjyAnswer.setCheckUserName(userinfo.get("userName").toString());
            tYjyAnswer.setCheckWorkNo(userinfo.get("workNo").toString());
        }
        tYjyAnswerMapper.insertTYjyAnswer(tYjyAnswer);
        formtrigger(tYjyAnswer,updateType);
        return 1;
    }

    @Override
    public int finalinsert(TYjyAnswer tYjyAnswer,TYjyForm checksearch,SysUser user,String fcDate)
    {
        Date currentDate = new Date();
        SimpleDateFormat addtime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String formattedDate = addtime.format(currentDate);
        tYjyAnswer.setCreatorDept(tYjyAnswer.getCreatorDept());
        tYjyAnswer.setFcDate(fcDate);
        tYjyAnswer.setCreatorName(user.getNickName());
        tYjyAnswer.setCreatorNo(user.getUserName());
        tYjyAnswer.setWorkNo(user.getUserName());
        tYjyAnswer.setCreateTime(currentDate);
        tYjyAnswer.setCheckHistory(tYjyAnswer.getCheckHistory()+formattedDate+" "+user.getNickName()+" 提交审批;\n");
        tYjyAnswer.setCheckerList(checksearch.getCheckerList());
        tYjyAnswer.setFrequency(checksearch.getFrequency());
        tYjyAnswer.setDimensionalityPath(checksearch.getDimensionalityPath());
        return insertaction(tYjyAnswer,0,checksearch);
    }

    public int insertaction(TYjyAnswer tYjyAnswer,int updateType,TYjyForm checksearch)
    {
        tYjyAnswer.setCheckerList(checksearch.getCheckerList());
        tYjyAnswer.setFrequency(checksearch.getFrequency());
        tYjyAnswer.setDimensionalityPath(checksearch.getDimensionalityPath());
        tYjyAnswer.setDeptShow(checksearch.getDeptShow());
        Date currentDate = new Date();
        SimpleDateFormat addtime = new SimpleDateFormat("yyyy-MM-dd");
        // 判断是否有截止日期和问题填报是否过期，未来可能还会被有更多的可能性会被探讨
        String timeSwitch = deadlineTime(tYjyAnswer,checksearch);
        TYjyAnswer search=new TYjyAnswer();
        search.setFormId(tYjyAnswer.getFormId());
        search.setFcDate(tYjyAnswer.getFcDate());
        search.setDelFlag("0");
        List<TYjyAnswer> list = tYjyAnswerMapper.selectTYjyAnswerListCount(search);
        if(list.size()>0)
        {
            if(timeSwitch.equals("1")&&list.get(0).getStatus().equals("2"))
            {
                return 1;
            }
            else
            {
                tYjyAnswerMapper.deleteTYjyAnswerById(list.get(0).getId());
                tYjyAnswer.setVersion(Long.valueOf(list.get(0).getVersion()+1));
                tYjyAnswer.setCheckHistory(list.get(0).getCheckHistory());
            }

        }
        else
        {
            tYjyAnswer.setVersion(Long.valueOf(1));
            tYjyAnswer.setCheckHistory("");
        }
        TYjyDept searchdept=new TYjyDept();
        List<TYjyDept> deptList=tYjyDeptMapper.selectTYjyDeptList(searchdept);
        Map<String,String> deptListmap=new HashMap<>();
        for(TYjyDept deptitem:deptList)
        {
            deptListmap.put(deptitem.getPath(),deptitem.getDeptName());
        }
        //此处新增常态表的导入
        JSONArray checklist=tYjyFormService.getchecklist(checksearch.getCheckerList(),tYjyAnswer.getCreatorDept());
        if(checklist==null || checklist.size()==0)
        {
            tYjyAnswer.setStatus("2");
            staticAnswer(tYjyAnswer,deptListmap,checksearch);
        }
        else
        {
            tYjyAnswer.setStatus("0");
            JSONObject userinfo =tYjyFormService.userinfo(checklist.get(0).toString());
            tYjyAnswer.setCheckUserName(userinfo.get("userName").toString());
            tYjyAnswer.setCheckWorkNo(userinfo.get("workNo").toString());
        }
        tYjyAnswerMapper.insertTYjyAnswer(tYjyAnswer);
        formtrigger(tYjyAnswer,updateType);
//        if(updateType<=1)
//        {
//            formtrigger(tYjyAnswer,updateType);
//        }
        return 1;
    }


    //此处需要进一步考虑如何保证累加数据的稳定性，防止不正常的递归及其他错误
    public int formtrigger(TYjyAnswer tYjyAnswer,int updateType)
    {
        TYjyFormTrigger tYjyFormTrigger=new TYjyFormTrigger();
        tYjyFormTrigger.setFormId(tYjyAnswer.getFormId());
        List<TYjyFormTrigger> list=tYjyFormTriggerService.selectTYjyFormTriggerList(tYjyFormTrigger);
        for(TYjyFormTrigger item:list)
        {

            if(item.getTriggerType().equals("0")&&(updateType==0))
            {
                TYjyAnswer insertanswer=deepCopy(tYjyAnswer);
                TYjyTrigger tYjyTrigger=tYjyTriggerService.selectTYjyTriggerById(item.getTriggerId());
                String[] formList=tYjyTrigger.getFormList().split(",");
                for(String formId:formList)
                {
                    if(!Long.valueOf(formId).equals(tYjyAnswer.getFormId()))
                    {
                        TYjyForm checksearch=tYjyFormService.selectTYjyFormById(Long.valueOf(formId));
                        insertanswer.setFormId(Long.valueOf(formId));
                        insertanswer.setCheckerList(checksearch.getCheckerList());
                        insertanswer.setFrequency(checksearch.getFrequency());
                        insertanswer.setDimensionalityPath(checksearch.getDimensionalityPath());
                        insertaction(insertanswer,1,checksearch);//这里的数据控制还需要进一步的考虑，避免有循环产生，导致系统卡死，现在数据优化情况还需要进一步的考虑
                    }
                }
            }
            if(item.getTriggerType().equals("3")&&(updateType==0))
            {
                TYjyAnswer insertanswer=deepCopy(tYjyAnswer);
                TYjyTrigger tYjyTrigger=tYjyTriggerService.selectTYjyTriggerById(item.getTriggerId());
                String[] formList=tYjyTrigger.getFormList().split(",");
                for(String formId:formList)
                {
                    if(!Long.valueOf(formId).equals(tYjyAnswer.getFormId()))
                    {
                        TYjyForm checksearch=tYjyFormService.selectTYjyFormById(Long.valueOf(formId));
                        insertanswer.setFormId(Long.valueOf(formId));
                        insertanswer.setCheckerList(checksearch.getCheckerList());
                        insertanswer.setFrequency(checksearch.getFrequency());
                        insertanswer.setDimensionalityPath(checksearch.getDimensionalityPath());
                        insertaction(insertanswer,1,checksearch);//这里的数据控制还需要进一步的考虑，避免有循环产生，导致系统卡死，现在数据优化情况还需要进一步的考虑
                    }
                }
            }
            if(item.getTriggerType().equals("1"))
            {
                TYjyTrigger tYjyTrigger=tYjyTriggerService.selectTYjyTriggerById(item.getTriggerId());
                String[] formList=tYjyTrigger.getFormList().split(",");
                String rule=tYjyTrigger.getTriggerRule();
                Map<String,String> numMap=new HashMap<>();
                int count=0;
                for(String formId:formList)
                {
                    TYjyAnswer search=new TYjyAnswer();
                    search.setFormId(Long.valueOf(formId));
                    search.setFcDate(tYjyAnswer.getFcDate());
                    List<TYjyAnswer> savelist=tYjyAnswerMapper.selectTYjyAnswerList(search);
                    if(savelist.size()>0)
                    {
                        numMap.put("A"+formId+"A",savelist.get(0).getFormValue());
                    }
                    else
                    {
                        count=count+1;
                        break;
                    }
                }
                if(count==0)
                {
                    for(String formitem:numMap.keySet())
                    {
                        rule =rule.replace(formitem,numMap.get(formitem));
                    }
                    rule =rule.replace("+-","-");
                    rule =rule.replace("--","+");
                    Object result=null;
                    try {
                        ScriptEngineManager manager = new ScriptEngineManager();
                        ScriptEngine engine = manager.getEngineByName("js");
                        result = engine.eval(rule);
                    } catch (ScriptException e) {
                        e.printStackTrace();
                    }
                    TYjyAnswer finallinsert=new TYjyAnswer();


                    TYjyForm checksearch=tYjyFormService.selectTYjyFormById(tYjyTrigger.getFormId());
                    if(checksearch.getFormType().equals("0"))
                    {
                        BigDecimal bd = new BigDecimal(result.toString());
                        bd = bd.setScale(0, RoundingMode.HALF_UP);
                        finallinsert.setFormValue(bd.stripTrailingZeros().toPlainString());
                    }
                    else
                    {
                        if(tYjyTrigger.getDecimalNum()!=null)
                        {
                            BigDecimal bd = new BigDecimal(result.toString());
                            bd = bd.setScale(tYjyTrigger.getDecimalNum(), RoundingMode.HALF_UP);
                            finallinsert.setFormValue(bd.stripTrailingZeros().toPlainString());
                        }
                        else
                        {
                            BigDecimal bd = new BigDecimal(result.toString());
                            finallinsert.setFormValue(bd.stripTrailingZeros().toPlainString());
                        }
                        
                    }

                    finallinsert.setFormId(tYjyTrigger.getFormId());
                    finallinsert.setFcDate(tYjyAnswer.getFcDate());
                    finallinsert.setFormFile("[]");
                    dealaddalone(finallinsert);
//                    TYjyForm checksearch=tYjyFormService.selectTYjyFormById(Long.valueOf(formId));
//                    insertanswer.setFormId(Long.valueOf(formId));
//                    insertaction(insertanswer,0,checksearch);
                    //此处缺乏考虑，仍然需要进一步的优化，但是目前最先要解决的是填报部门处的屎山，先重新把逻辑理顺之后再来优化填报内容方面的问题
//                    TYjyAnswer finallinsert=new TYjyAnswer();
//                    finallinsert.setFormId(item.getFormId());
//                    finallinsert.setFormValue(result.toString());
//                    finallinsert.setFcDate(tYjyAnswer.getFcDate());
//                    tYjyAnswerMapper.insertTYjyAnswer(insertanswer);
//                    formtrigger(insertanswer,"0");
                }
            }
            if(item.getTriggerType().equals("2"))
            {
                //在计算累计时，之后所有的数据全部到位，要考虑是不是所有数据全部到位之后才需要计算数智,这个也应该从两个方面考虑
                TYjyTrigger tYjyTrigger=tYjyTriggerService.selectTYjyTriggerById(item.getTriggerId());
                String[] formList=tYjyTrigger.getFormList().split(",");

                Map<String,String> numMap=new HashMap<>();
                int count=0;
                //对每个数据都要处理，这是一个速率非常非常慢的计算过程，需要进一步的优化，需要考虑如何转变需要考虑

                String endTime=dealTimeForEnd(tYjyAnswer.getFcDate(),tYjyTrigger.getFrequency());
                TYjyAnswerSearch timerate=new TYjyAnswerSearch();
                timerate.setFormId(tYjyTrigger.getFormId());
                timerate.setBeginTime(tYjyAnswer.getFcDate());
                timerate.setFinalTime(endTime);
                List<TYjyAnswer> ratelist=tYjyAnswerMapper.selectTYjyAnswerListSum(timerate);
                List<String> dealtimelist=new ArrayList<>();
                dealtimelist.add(tYjyAnswer.getFcDate());
                endTime=tYjyAnswer.getFcDate();
                for(TYjyAnswer answerItem:ratelist)
                {
                    dealtimelist.add(answerItem.getFcDate());
                    endTime=answerItem.getFcDate();
                }
                if(dealtimelist.size()==0)
                {
                    dealtimelist.add(tYjyAnswer.getFcDate());
                }
                String beginTime=dealTime(tYjyAnswer.getFcDate(),tYjyTrigger.getFrequency());

                for(String formId:formList)
                {
                    TYjyAnswerSearch search=new TYjyAnswerSearch();
                    search.setFormId(Long.valueOf(formId));
                    search.setBeginTime(beginTime);
                    search.setFinalTime(endTime);
                    List<TYjyAnswer> savelist=tYjyAnswerMapper.selectTYjyAnswerListSum(search);
                    for(String timelist:dealtimelist)
                    {
                        float sum=0;
                        for (TYjyAnswer sumitem:savelist)
                        {
                            if(timelist.compareTo(sumitem.getFcDate())>=0)
                            {
                                sum=sum+Float.valueOf(sumitem.getFormValue());
                            }
                            else
                            {
                                break;
                            }

                        }
                        numMap.put("A"+formId+"A "+timelist,Float.valueOf(sum).toString());
                    }
//                    if(savelist.size()>0)
//                    {
//                        float sum=0;
//                        for (TYjyAnswer sumitem:savelist)
//                        {
//                            sum=sum+Float.valueOf(sumitem.getFormValue());
//                        }
//                        numMap.put("A"+formId+"A",Float.valueOf(sum).toString());
//                    }
//                    else
//                    {
//                        count=count+1;
//                        break;
//                    }
                }
//                if(count==0)
                TYjyForm checksearch = tYjyFormService.selectTYjyFormById(tYjyTrigger.getFormId());
                if(1==1)
                {
                    for(String timelist:dealtimelist)
                    {
                        String rule=tYjyTrigger.getTriggerRule();

                        String timeLength=calculateDifference(beginTime,timelist,checksearch.getFrequency());
                        rule = rule.replace("ATIMEA", timeLength);//专门用于计算累加后的平均值,该方法不一定有用，但是不能不防

                        for (String formitem : formList)
                        {
                            rule = rule.replace("A"+formitem+"A", numMap.get("A"+formitem+"A "+timelist));
                        }
                        rule = rule.replace("+-", "-");
                        rule = rule.replace("--", "+");
                        Object result = null;
                        try
                        {
                            ScriptEngineManager manager = new ScriptEngineManager();
                            ScriptEngine engine = manager.getEngineByName("js");
                            result = engine.eval(rule);
                        } catch (ScriptException e) {
                            e.printStackTrace();
                        }
                        TYjyAnswer finallinsert = new TYjyAnswer();
                        finallinsert.setFormId(tYjyTrigger.getFormId());

                        if (checksearch.getFormType().equals("0"))
                        {
                            BigDecimal bd = new BigDecimal(result.toString());
                            bd = bd.setScale(0, RoundingMode.HALF_UP);
                            finallinsert.setFormValue(bd.stripTrailingZeros().toPlainString());
                        }
                        else
                        {
                            if(tYjyTrigger.getDecimalNum()!=null)
                            {
                                BigDecimal bd = new BigDecimal(result.toString());
                                bd = bd.setScale(tYjyTrigger.getDecimalNum(), RoundingMode.HALF_UP);
                                finallinsert.setFormValue(bd.stripTrailingZeros().toPlainString());
                            }
                            else
                            {
                                BigDecimal bd = new BigDecimal(result.toString());
                                finallinsert.setFormValue(bd.stripTrailingZeros().toPlainString());
                            }

                        }
                        //                    finallinsert.setFormValue(result.toString());
                        finallinsert.setFcDate(timelist);
                        finallinsert.setFormFile("[]");
                        dealaddalone(finallinsert);
                    }
                }
            }
        }
        return 1;
    }

    @SuppressWarnings("unchecked")
    public static <T extends Serializable> T deepCopy(T object) {
        try {
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            ObjectOutputStream oos = new ObjectOutputStream(bos);
            oos.writeObject(object);
            oos.flush();
            ByteArrayInputStream bis = new ByteArrayInputStream(bos.toByteArray());
            ObjectInputStream ois = new ObjectInputStream(bis);
            return (T) ois.readObject();
        } catch (IOException | ClassNotFoundException e) {
            throw new RuntimeException("Deep copy failed", e);
        }
    }

    //时间差计算
    @Override
    public String calculateDifference(String startDateStr, String endDateStr, String unit) {
        DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        // 解析日期
        LocalDate startDate = LocalDate.parse(startDateStr, DATE_FORMATTER);
        LocalDate endDate = LocalDate.parse(endDateStr, DATE_FORMATTER);
        String re="";
        if(unit.equals("0"))
        {
            re=Long.valueOf(ChronoUnit.DAYS.between(startDate, endDate)+1).toString();
        }
        if(unit.equals("1"))
        {
            re=Long.valueOf(ChronoUnit.MONTHS.between(startDate, endDate)+1).toString();
        }
        if(unit.equals("2"))
        {
            re=Long.valueOf((ChronoUnit.MONTHS.between(startDate, endDate)/3)+1).toString();
        }
        if(unit.equals("3"))
        {
            re=Long.valueOf((ChronoUnit.MONTHS.between(startDate, endDate)/6)+1).toString();
        }
        if(unit.equals("4"))
        {
            re=Long.valueOf((ChronoUnit.YEARS.between(startDate, endDate))+1).toString();
        }
        if(unit.equals("5"))
        {
            re=Long.valueOf((ChronoUnit.DAYS.between(startDate, endDate)/7)+1).toString();
        }
        return re;
        //        BigDecimal bd = new BigDecimal(result.toString());
//        bd = bd.setScale(tYjyTrigger.getDecimalNum(), RoundingMode.HALF_UP);
//        finallinsert.setFormValue(bd.toString());

    }


    public String deadlineTime(TYjyAnswer tYjyAnswer,TYjyForm checksearch)
    {

        if(checksearch.getDeadlineSwitch().equals("1"))
        {
            String Deadlinetime=null;

            Calendar calendar = Calendar.getInstance();
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            Date currentDate = new Date();
            calendar.setTime(currentDate);
            String nowtime=dateFormat.format(calendar.getTime());
            try {
                currentDate = dateFormat.parse(tYjyAnswer.getFcDate());
                calendar.setTime(currentDate);
            } catch (ParseException e) {
                // 异常处理代码
                e.printStackTrace();
                // 或者其他错误处理逻辑
            }
            String[] timelist= checksearch.getDeadlineDate().split("/");
            calendar.add(Calendar.YEAR,Integer.valueOf(timelist[0]));
            calendar.add(Calendar.MONTH,Integer.valueOf(timelist[1]));
            calendar.add(Calendar.DAY_OF_YEAR,Integer.valueOf(timelist[2]));
            Deadlinetime=dateFormat.format(calendar.getTime());
            if(nowtime.compareTo(Deadlinetime)>0)
            {
                return "1";
            }
            else
            {
                return "0";
            }
        }
        else
        {
            return "0";
        }
    }



    public void mailDateUpdate(TYjyAnswer tYjyAnswer,TYjyForm checksearch)
    {
        //发送完邮件后需要重新重置判定时间
        //完成最新月份的填报和审核之后需要继续进行再装填操作
        //开关重启之后需要进行邮件的再配置，如果晚于提醒时间，那么需要重新再进行配置操作
        //有大量的细节需要再优化
        //在报表配置了信息之后，自动生成一个截止日期，在截止日到达时进行，判断和查询，如果未填报或者未审核则需要进行邮件通知，如果已经达到标准了则不进行邮件通知，同时更新相应的填报日期和邮件通知日期
        return;
    }

    @Autowired
    private IOutputFormService outputFormService;

    @Override
    public List<JSONObject> importDateUpdate()
    {
        List<JSONObject> infoMation=new ArrayList<>();

        Date currentDate = new Date(); // 创建Calendar实例，并设置为当前时间
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(currentDate); //
        calendar.add(Calendar.DAY_OF_YEAR,-1);
        String endTime = dateFormat.format(calendar.getTime());


        //获取昨天的钢铁产量


        //获取前天的钢铁产量


        TYjyUpdateGas TYjyUpdateGas = new TYjyUpdateGas();
        TYjyUpdateGas.setTime(endTime);
        TYjyUpdateGas.setConcet("%&5");
        List<TYjyUpdateGas> updateList = tYjyAnswerMapper.selectUpdateEveryDayGas(TYjyUpdateGas);
        Map<String, String> updateMap = new HashMap<>();
        for (TYjyUpdateGas item : updateList) {
            if(item.getDisplayValue()==null)
            {
                int countnum=1;
                updateMap.put(item.getPointCode(), "0");
            }
            else
            {
                updateMap.put(item.getPointCode(), item.getDisplayValue().toString());
            }
        }
        calendar.add(Calendar.DAY_OF_YEAR,-1);
        String yesTime = dateFormat.format(calendar.getTime());
        TYjyUpdateGas = new TYjyUpdateGas();
        TYjyUpdateGas.setTime(yesTime);
        TYjyUpdateGas.setConcet("%&5");
        List<TYjyUpdateGas> yesupdateList = tYjyAnswerMapper.selectUpdateEveryDayGas(TYjyUpdateGas);
        Map<String, String> yesupdateMap = new HashMap<>();
        for (TYjyUpdateGas item : yesupdateList) {
            if(item.getDisplayValue()==null)
            {
                int countnum=1;
                yesupdateMap.put(item.getPointCode(), "0");
            }
            else
            {
                yesupdateMap.put(item.getPointCode(), item.getDisplayValue().toString());
            }
        }

        BigDecimal todayWeight=productionInfo(endTime.replace("-",""),"钢产量合计");
        BigDecimal yesWeight=productionInfo(yesTime.replace("-",""),"钢产量合计");;


        List<Long> formUpdateList=new ArrayList<>();
        formUpdateList.add(Long.valueOf("4763"));
        formUpdateList.add(Long.valueOf("4764"));
        formUpdateList.add(Long.valueOf("4765"));
        formUpdateList.add(Long.valueOf("4766"));
        formUpdateList.add(Long.valueOf("4767"));
        formUpdateList.add(Long.valueOf("4768"));
        formUpdateList.add(Long.valueOf("4769"));
        formUpdateList.add(Long.valueOf("5264"));
        formUpdateList.add(Long.valueOf("5266"));
        formUpdateList.add(Long.valueOf("6150"));
        formUpdateList.add(Long.valueOf("5866"));
        formUpdateList.add(Long.valueOf("6172"));
        TYjyScrapy tYjyScrapy=new TYjyScrapy();
        List<TYjyScrapy> spList = tYjyScrapyMapper.selectTYjyScrapyList(tYjyScrapy);
        Map<Long,TYjyScrapy>  spMap=new HashMap<>();
        for(TYjyScrapy item:spList)
        {
            spMap.put(item.getFormId(),item);
        }
        for(Long formItem:formUpdateList)
        {
//            TYjyScrapy ScrapyItem=tYjyScrapyMapper.selectTYjyScrapyByFormId(formItem);
            TYjyScrapy ScrapyItem=spMap.get(formItem);
            TYjyForm checksearch = tYjyFormService.selectTYjyFormById(ScrapyItem.getFormId());
            BigDecimal todayDate=dateGet(ScrapyItem,updateMap,checksearch);
            BigDecimal ysetDate=dateGet(ScrapyItem,yesupdateMap,checksearch);
            JSONObject addItem=new JSONObject();
            if(formItem.toString().equals("6172"))
            {
                addItem.put("name","外购管道气总量（m3）");
            }
            else
            {
                addItem.put("name",checksearch.getFormQuestionExport());
            }

            addItem.put("today",todayDate.stripTrailingZeros().toPlainString());
            addItem.put("yesterday",ysetDate.stripTrailingZeros().toPlainString());

            addItem.put("todayrate",todayDate.divide(todayWeight, 0, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString());
            addItem.put("yesterdayrate",ysetDate.divide(yesWeight, 0, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString());


            addItem.put("unit",checksearch.getUnit());
            addItem.put("targetMin","0");
            addItem.put("targetMax","0");
//            BigDecimal change=(todayDate.subtract(ysetDate).divide(ysetDate));

            Float floatValue=(todayDate.floatValue()-ysetDate.floatValue())/ysetDate.floatValue();
            floatValue=floatValue*100;
            BigDecimal changevalue=new BigDecimal(floatValue.toString());
            changevalue = changevalue.setScale(2, RoundingMode.HALF_UP);
            addItem.put("change",changevalue.stripTrailingZeros().toPlainString());


            if(floatValue>=-10 && floatValue<= 10)
            {
                addItem.put("status","good");
            }
            else
            {
                if(floatValue>=-20 && floatValue<= 20)
                {
                    addItem.put("status","warning");
                }
                else
                {
                    addItem.put("status","danger");
                }
            }
            Float toRate=todayDate.divide(todayWeight, 5, RoundingMode.HALF_UP).floatValue();
            Float yesRate=ysetDate.divide(yesWeight, 5, RoundingMode.HALF_UP).floatValue();

            Float floatRate=100*((toRate-yesRate)/yesRate);

            BigDecimal changevalueRate=new BigDecimal(floatRate.toString());
            changevalueRate = changevalueRate.setScale(2, RoundingMode.HALF_UP);
            addItem.put("changeRate",changevalueRate.stripTrailingZeros().toPlainString());


            if(floatValue>=-10 && floatValue<= 10)
            {
                addItem.put("status","good");
            }
            else
            {
                addItem.put("status","warning");
//                if(floatValue>=-20 && floatValue<= 20)
//                {
//                    addItem.put("status","warning");
//                }
//                else
//                {
//                    addItem.put("status","danger");
//                }
            }
            infoMation.add(addItem);
        }


        return infoMation;
    }

    public BigDecimal dateGet(TYjyScrapy ScrapyItem,Map<String, String> updateMap,TYjyForm checksearch)
    {
        String[] formList = ScrapyItem.getFormList().split(",");
        String rule = ScrapyItem.getTriggerRule();
        for (String formitem : formList)
        {
            if (updateMap.get(formitem) != null) {
                rule = rule.replace(formitem, updateMap.get(formitem));
            } else {
                rule = rule.replace(formitem, "0");
            }
        }
        rule = rule.replace("+-", "-");
        rule = rule.replace("--", "+");
        Object result = null;
        try
        {
            ScriptEngineManager manager = new ScriptEngineManager();
            ScriptEngine engine = manager.getEngineByName("js");
            result = engine.eval(rule);
        } catch (ScriptException e) {
            e.printStackTrace();
        }
        if (checksearch.getFormType().equals("0"))
        {
            BigDecimal bd = new BigDecimal(result.toString());
            bd = bd.setScale(0, RoundingMode.HALF_UP);
            return bd;
//            finallinsert.setFormValue(bd.stripTrailingZeros().toPlainString());
        } else
        {
            if (ScrapyItem.getDecimalNum() != null)
            {
                BigDecimal bd = new BigDecimal(result.toString());
                bd = bd.setScale(ScrapyItem.getDecimalNum(), RoundingMode.HALF_UP);
                return bd;
//                finallinsert.setFormValue(bd.stripTrailingZeros().toPlainString());
            }
            else
            {
                BigDecimal bd = new BigDecimal(result.toString());
                return bd;
//                finallinsert.setFormValue(bd.stripTrailingZeros().toPlainString());
            }
        }
    }

    public BigDecimal productionInfo(String time,String itemName) {
        OutputForm outputForm = new OutputForm();
        outputForm.setItemName(itemName);
        outputForm.setDateCode(time.replace("-", ""));
        List<OutputForm> dailyProduction = outputFormService.selectDailyInfoByItemName(outputForm);
        if (dailyProduction.size() == 0)
        {
            return BigDecimal.valueOf(0);
        }
        else
        {
            return dailyProduction.get(0).getDayWeight();
        }
    }
}
