<template>
  <div class="app-container">
    <div class="card-title">
      <el-tag>全体用户</el-tag>
    </div>
    <el-card>
      <!--用户数据-->
      <el-form
        :model="queryParams"
        ref="queryForm"
        :inline="true"
        v-show="showSearch"
        label-width="auto"
      >
        <el-form-item label="用户账号" prop="userName">
          <el-input
            v-model="queryParams.userName"
            placeholder="请输入用户账号"
            clearable
            size="small"
            style="width: 240px"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="用户姓名" prop="nickName">
          <el-input
            v-model="queryParams.nickName"
            placeholder="请输入用户姓名"
            clearable
            size="small"
            style="width: 240px"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="cyan"
            icon="el-icon-search"
            size="mini"
            @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
            >重置</el-button
          >
        </el-form-item>
      </el-form>
      <el-table
        v-loading="loading"
        :data="userList"
        :row-class-name="tableRowClassName"
      >
        <el-table-column
          label="用户账号"
          align="center"
          prop="userName"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="用户姓名"
          align="center"
          prop="nickName"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
        >
          <template slot-scope="scope">
            <el-button
              type="primary"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              >修改</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getUserList"
      />
    </el-card>
    <div class="card-title-Sec">
      <el-tag type="success">已分配用户</el-tag>
    </div>
    <el-card>
      <!--已分配用户数据-->
      <el-form
        :model="userQueryParams"
        ref="userQueryForm"
        :inline="true"
        v-show="showSearch"
        label-width="auto"
      >
        <el-form-item label="用户账号" prop="userName">
          <el-input
            v-model="userQueryParams.userName"
            placeholder="请输入用户账号"
            clearable
            size="small"
            style="width: 240px"
            @keyup.enter.native="handleQuerySec"
          />
        </el-form-item>
        <el-form-item label="用户姓名" prop="nickName">
          <el-input
            v-model="userQueryParams.nickName"
            placeholder="请输入用户姓名"
            clearable
            size="small"
            style="width: 240px"
            @keyup.enter.native="handleQuerySec"
          />
        </el-form-item>
        <el-form-item label="部门" prop="deptId">
          <el-select
            v-model="userQueryParams.deptId"
            style="width: 200px"
            size="small"
            placeholder="请选择部门"
            clearable
            filterable
          >
            <el-option
              v-for="item in deptList"
              :key="item.id"
              :label="item.storeName"
              :value="item.id"
            >
              <span style="float: left">{{ item.storeName }}</span>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button
            type="cyan"
            icon="el-icon-search"
            size="mini"
            @click="handleQuerySec"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuerySec"
            >重置</el-button
          >
        </el-form-item>
      </el-form>
      <el-table
        v-loading="loadingPermit"
        :data="hasPermitUserList"
        :row-class-name="tableRowClassName"
      >
        <el-table-column
          label="用户账号"
          align="center"
          prop="userName"
          width="150px"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="用户姓名"
          align="center"
          prop="nickName"
          width="150px"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="部门"
          align="center"
          prop="deptName"
          width="200px"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="权限"
          align="center"
          prop="roleNamesDesc"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
        >
          <template slot-scope="scope">
            <el-button
              type="danger"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="totalPermit > 0"
        :total="totalPermit"
        :page.sync="userQueryParams.pageNum"
        :limit.sync="userQueryParams.pageSize"
        @pagination="getHasPermitUsers"
      />
    </el-card>

    <!-- 添加或修改参数配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-form-item label="用户姓名">
            <el-input v-model="form.nickName" readonly />
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item label="部门" prop="deptId">
            <el-select
              v-model="form.deptId"
              placeholder="请选择部门"
              filterable
            >
              <el-option
                v-for="item in deptList"
                :key="item.id"
                :label="item.storeName"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item label="角色" prop="roleId">
            <el-select
              v-model="form.roleKeys"
              multiple
              placeholder="请选择用户权限"
              filterable
            >
              <el-option
                v-for="item in roleOptions"
                :key="item.index"
                :label="item.value"
                :value="item.code"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>


<script>
import {
  listDept,
  listRole,
  listAllUsers,
  listHasPermitUsers,
  getInfo,
  updateInfo,
  deleteInfo,
} from "@/api/leave/permit";

export default {
  name: "exitPermitConfig",
  components: {},
  data() {
    return {
      showSearch: true,
      deptList: [],
      roleOptions: [],
      userList: [],
      hasPermitUserList: [],
      // 总条数
      total: 0,
      loading: false,
      // 已分配
      totalPermit: 0,
      loadingPermit: false,
      // 查询参数
      queryParams: {
        userName: null,
        nickName: null,
        pageNum: 1,
        pageSize: 10,
      },
      userQueryParams: {
        userName: null,
        nickName: null,
        deptId: null,
        pageNum: 1,
        pageSize: 10,
      },
      form: {},
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      rules: {
        deptId: [{ required: true, message: "请选择部门", trigger: "change" }],
      },
    };
  },
  created() {
    //初始化
    this.init();
  },
  methods: {
    //初始化
    init() {
      this.getHasPermitUsers();
      this.getUserList();
      this.getDeptList();
      this.getRoleList();
    },
    //部门列表
    getDeptList() {
      listDept().then((res) => {
        this.deptList = res.data;
      });
    },
    //角色列表
    getRoleList() {
      listRole().then((res) => {
        this.roleOptions = res.data;
      });
    },
    //待分配用户列表
    getUserList() {
      this.loading = true;
      listAllUsers(this.queryParams).then((response) => {
        this.userList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    //已分配用户列表
    getHasPermitUsers() {
      this.loadingPermit = true;
      listHasPermitUsers(this.userQueryParams).then((res) => {
        this.hasPermitUserList = res.rows;
        this.totalPermit = res.total;
        this.loadingPermit = false;
      });
    },
    //搜索按钮操作
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getUserList();
    },
    //重置按钮操作
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    //搜索按钮操作
    handleQuerySec() {
      this.userQueryParams.pageNum = 1;
      this.getHasPermitUsers();
    },
    //重置按钮操作
    resetQuerySec() {
      this.resetForm("userQueryForm");
      this.handleQuerySec();
    },
    // 表单重置
    reset() {
      this.form = {
        userId: null,
        userName: null,
        nickName: null,
        deptId: null,
        roleKeys: [],
      };
      this.resetForm("form");
    },
    //编辑用户
    handleUpdate(row) {
      this.reset();
      const userId = row.userId;
      getInfo({ userId: userId }).then((res) => {
        this.open = true;
        this.form.userId = userId;
        this.form.nickName = row.nickName;
        if (res.data != undefined) {
          this.form.deptId = res.data.deptId;
          this.form.roleKeys = res.data.roleKeys;
        }
      });
    },
    //删除用户
    handleDelete(row) {
      const userId = row.userId;
      this.$modal
        .confirm('是否删除"' + row.nickName + '"权限？')
        .then(() => {
          deleteInfo({ userId: userId });
        })
        .then(() => {
          this.$modal.msgSuccess("删除成功");
          this.handleQuerySec();
        })
        .catch(() => {});
    },
    //提交表单
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          updateInfo(this.form).then((response) => {
            this.$modal.msgSuccess("修改成功");
            this.open = false;
            this.handleQuerySec();
          });
        }
      });
    },
    cancel() {
      this.open = false;
      this.reset();
    },
    tableRowClassName({ row, rowIndex }) {
      if (row.status == "1") {
        return "warning-row";
      }
      return "";
    },
  },
};
</script>


<style lang="scss" scoped>
.card-title {
  margin-bottom: 10px;
}
.card-title-Sec {
  margin-top: 10px;
  margin-bottom: 10px;
}
.basic {
  margin-top: 20px;
  margin-bottom: 20px;
}
.card-footer {
  display: flex;
  justify-content: flex-end;
}
</style>