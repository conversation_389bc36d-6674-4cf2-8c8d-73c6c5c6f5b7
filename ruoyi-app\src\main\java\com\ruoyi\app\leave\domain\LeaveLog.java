package com.ruoyi.app.leave.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 出门证日志对象 leave_log
 * 
 * <AUTHOR>
 * @date 2025-03-13
 */
public class LeaveLog extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 日志类型 1-计划申请单 2-计划任务 */
    @Excel(name = "日志类型 1-计划申请单 2-计划任务")
    private Integer logType;

    /** 申请单号 */
    @Excel(name = "申请单号")
    private Long applyNo;

    /** 任务号 */
    @Excel(name = "任务号")
    private String taskNo;

    /** 日志内容 */
    @Excel(name = "日志内容")
    private String info;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setLogType(Integer logType) 
    {
        this.logType = logType;
    }

    public Integer getLogType() 
    {
        return logType;
    }
    public void setApplyNo(Long applyNo) 
    {
        this.applyNo = applyNo;
    }

    public Long getApplyNo() 
    {
        return applyNo;
    }
    public void setTaskNo(String taskNo) 
    {
        this.taskNo = taskNo;
    }

    public String getTaskNo() 
    {
        return taskNo;
    }
    public void setInfo(String info) 
    {
        this.info = info;
    }

    public String getInfo() 
    {
        return info;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("logType", getLogType())
            .append("applyNo", getApplyNo())
            .append("taskNo", getTaskNo())
            .append("info", getInfo())
            .append("createTime", getCreateTime())
            .append("createBy", getCreateBy())
            .toString();
    }
}
