package com.ruoyi.app.supply.mapper;

import com.ruoyi.app.supply.domain.SupplyInfo;
import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;

import java.util.List;

/**
 * 供应商信息Mapper接口
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface SupplyInfoMapper {
    
    /**
     * 查询供应商信息
     *
     * @param supplyCode 供应商代码
     * @return 供应商信息
     */
    @DataSource(value = DataSourceType.SUPPLY)
    public SupplyInfo selectSupplyInfoByCode(Integer supplyCode);

    /**
     * 查询供应商信息列表
     *
     * @param supplyInfo 供应商信息
     * @return 供应商信息集合
     */
    @DataSource(value = DataSourceType.SUPPLY)
    public List<SupplyInfo> selectSupplyInfoList(SupplyInfo supplyInfo);

    /**
     * 新增供应商信息
     *
     * @param supplyInfo 供应商信息
     * @return 结果
     */
    @DataSource(value = DataSourceType.SUPPLY)
    public int insertSupplyInfo(SupplyInfo supplyInfo);

    /**
     * 修改供应商信息
     *
     * @param supplyInfo 供应商信息
     * @return 结果
     */
    @DataSource(value = DataSourceType.SUPPLY)
    public int updateSupplyInfo(SupplyInfo supplyInfo);

    /**
     * 删除供应商信息
     *
     * @param supplyCode 供应商代码
     * @return 结果
     */
    @DataSource(value = DataSourceType.SUPPLY)
    public int deleteSupplyInfoByCode(Integer supplyCode);

    /**
     * 批量删除供应商信息
     *
     * @param supplyCodes 需要删除的数据主键集合
     * @return 结果
     */
    @DataSource(value = DataSourceType.SUPPLY)
    public int deleteSupplyInfoByCodes(Integer[] supplyCodes);
}
