<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="计划号" prop="planNo">
        <el-input
          v-model="queryParams.planNo"
          placeholder="请输入计划号"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="计划类型 1-出厂不返回 2-出厂返回 3-跨区调拨 4-退货申请" prop="planType">
        <el-select v-model="queryParams.planType" placeholder="请选择计划类型 1-出厂不返回 2-出厂返回 3-跨区调拨 4-退货申请" clearable size="small">
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
      <el-form-item label="业务类型
1-通用" prop="businessCategory">
        <el-input
          v-model="queryParams.businessCategory"
          placeholder="请输入业务类型
1-通用"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="是否计量 计量-1 不计量-0" prop="measureFlag">
        <el-input
          v-model="queryParams.measureFlag"
          placeholder="请输入是否计量 计量-1 不计量-0"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="计划量 计划量计量时才存在" prop="plannedAmount">
        <el-input
          v-model="queryParams.plannedAmount"
          placeholder="请输入计划量 计划量计量时才存在"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="收货单位" prop="receiveCompany">
        <el-input
          v-model="queryParams.receiveCompany"
          placeholder="请输入收货单位"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="收货单位code" prop="receiveCompanyCode">
        <el-input
          v-model="queryParams.receiveCompanyCode"
          placeholder="请输入收货单位code"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="返回单位" prop="targetCompany">
        <el-input
          v-model="queryParams.targetCompany"
          placeholder="请输入返回单位"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="返回单位code" prop="targetCompanyCode">
        <el-input
          v-model="queryParams.targetCompanyCode"
          placeholder="请输入返回单位code"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="申请单位" prop="sourceCompany">
        <el-input
          v-model="queryParams.sourceCompany"
          placeholder="请输入申请单位"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="申请单位code" prop="sourceCompanyCode">
        <el-input
          v-model="queryParams.sourceCompanyCode"
          placeholder="请输入申请单位code"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="计划返回时间" prop="planReturnTime">
        <el-date-picker clearable size="small" style="width: 200px"
          v-model="queryParams.planReturnTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="选择计划返回时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="实际返回时间  多次返厂情况下填最新一次" prop="realReturnTime">
        <el-date-picker clearable size="small" style="width: 200px"
          v-model="queryParams.realReturnTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="选择实际返回时间  多次返厂情况下填最新一次">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="监装人" prop="monitor">
        <el-input
          v-model="queryParams.monitor"
          placeholder="请输入监装人"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="物资专管员" prop="specialManager">
        <el-input
          v-model="queryParams.specialManager"
          placeholder="请输入物资专管员"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="申请有效期" prop="expireTime">
        <el-date-picker clearable size="small" style="width: 200px"
          v-model="queryParams.expireTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="选择申请有效期">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="物资类型 1-钢材 2-钢板 3-其他" prop="itemType">
        <el-select v-model="queryParams.itemType" placeholder="请选择物资类型 1-钢材 2-钢板 3-其他" clearable size="small">
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
      <el-form-item label="计划状态 1-待分厂审批 2-待分厂复审 3-待生产指挥中心审批 4-审批完成  5-已出厂 6-部分收货 7-已完成
11-驳回 12-废弃 13-过期" prop="planStatus">
        <el-select v-model="queryParams.planStatus" placeholder="请选择计划状态 1-待分厂审批 2-待分厂复审 3-待生产指挥中心审批 4-审批完成  5-已出厂 6-部分收货 7-已完成
11-驳回 12-废弃 13-过期" clearable size="small">
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
      <el-form-item label="申请时间" prop="applyTime">
        <el-date-picker clearable size="small" style="width: 200px"
          v-model="queryParams.applyTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="选择申请时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="申请人" prop="applyWorkNo">
        <el-input
          v-model="queryParams.applyWorkNo"
          placeholder="请输入申请人"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="分厂领导审批时间" prop="factoryApproveTime">
        <el-date-picker clearable size="small" style="width: 200px"
          v-model="queryParams.factoryApproveTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="选择分厂领导审批时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="分厂领导工号" prop="factoryApproveWorkNo">
        <el-input
          v-model="queryParams.factoryApproveWorkNo"
          placeholder="请输入分厂领导工号"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="分厂审核结果 0-拒绝 1-同意" prop="factoryApproveFlag">
        <el-input
          v-model="queryParams.factoryApproveFlag"
          placeholder="请输入分厂审核结果 0-拒绝 1-同意"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="分厂复审结果 0-拒绝 1-同意" prop="factorySecApproveFlag">
        <el-input
          v-model="queryParams.factorySecApproveFlag"
          placeholder="请输入分厂复审结果 0-拒绝 1-同意"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="分厂复审时间" prop="factorySecApproveTime">
        <el-date-picker clearable size="small" style="width: 200px"
          v-model="queryParams.factorySecApproveTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="选择分厂复审时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="分厂复审审批人工号" prop="factorySecApproveWorkNo">
        <el-input
          v-model="queryParams.factorySecApproveWorkNo"
          placeholder="请输入分厂复审审批人工号"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="生产指挥中心审批时间" prop="centerApproveTime">
        <el-date-picker clearable size="small" style="width: 200px"
          v-model="queryParams.centerApproveTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="选择生产指挥中心审批时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="生产指挥中心审批人工号" prop="centerApproveWorkNo">
        <el-input
          v-model="queryParams.centerApproveWorkNo"
          placeholder="请输入生产指挥中心审批人工号"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="生产指挥中心审核结果 0-拒绝 1-同意" prop="centerApproveFlag">
        <el-input
          v-model="queryParams.centerApproveFlag"
          placeholder="请输入生产指挥中心审核结果 0-拒绝 1-同意"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="cyan" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['leave:plan:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['leave:plan:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['leave:plan:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['leave:plan:export']"
        >导出</el-button>
      </el-col>
	  <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="planList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="主键" align="center" prop="id" />
      <el-table-column label="申请编号" align="center" prop="applyNo" />
      <el-table-column label="计划号" align="center" prop="planNo" />
      <el-table-column label="计划类型 1-出厂不返回 2-出厂返回 3-跨区调拨 4-退货申请" align="center" prop="planType" />
      <el-table-column label="业务类型
1-通用" align="center" prop="businessCategory" />
      <el-table-column label="是否计量 计量-1 不计量-0" align="center" prop="measureFlag" />
      <el-table-column label="计划量 计划量计量时才存在" align="center" prop="plannedAmount" />
      <el-table-column label="收货单位" align="center" prop="receiveCompany" />
      <el-table-column label="收货单位code" align="center" prop="receiveCompanyCode" />
      <el-table-column label="返回单位" align="center" prop="targetCompany" />
      <el-table-column label="返回单位code" align="center" prop="targetCompanyCode" />
      <el-table-column label="申请单位" align="center" prop="sourceCompany" />
      <el-table-column label="申请单位code" align="center" prop="sourceCompanyCode" />
      <el-table-column label="计划返回时间" align="center" prop="planReturnTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.planReturnTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="实际返回时间  多次返厂情况下填最新一次" align="center" prop="realReturnTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.realReturnTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="监装人" align="center" prop="monitor" />
      <el-table-column label="物资专管员" align="center" prop="specialManager" />
      <el-table-column label="申请有效期" align="center" prop="expireTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.expireTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="出厂原因" align="center" prop="reason" />
      <el-table-column label="物资类型 1-钢材 2-钢板 3-其他" align="center" prop="itemType" />
      <el-table-column label="计划状态 1-待分厂审批 2-待分厂复审 3-待生产指挥中心审批 4-审批完成  5-已出厂 6-部分收货 7-已完成
11-驳回 12-废弃 13-过期" align="center" prop="planStatus" />
      <el-table-column label="申请时间" align="center" prop="applyTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.applyTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="申请人" align="center" prop="applyWorkNo" />
      <el-table-column label="分厂领导审批时间" align="center" prop="factoryApproveTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.factoryApproveTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="分厂领导工号" align="center" prop="factoryApproveWorkNo" />
      <el-table-column label="分厂审核结果 0-拒绝 1-同意" align="center" prop="factoryApproveFlag" />
      <el-table-column label="分厂领导审核意见" align="center" prop="factoryApproveContent" />
      <el-table-column label="分厂复审结果 0-拒绝 1-同意" align="center" prop="factorySecApproveFlag" />
      <el-table-column label="分厂复审时间" align="center" prop="factorySecApproveTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.factorySecApproveTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="分厂复审审批人工号" align="center" prop="factorySecApproveWorkNo" />
      <el-table-column label="分厂复审审核意见" align="center" prop="factorySecApproveContent" />
      <el-table-column label="生产指挥中心审批时间" align="center" prop="centerApproveTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.centerApproveTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="生产指挥中心审批人工号" align="center" prop="centerApproveWorkNo" />
      <el-table-column label="生产指挥中心审核结果 0-拒绝 1-同意" align="center" prop="centerApproveFlag" />
      <el-table-column label="生产指挥中心审核意见" align="center" prop="centerApproveContent" />
      <el-table-column label="申请文件，允许多个" align="center" prop="applyFileUrl" />
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['leave:plan:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['leave:plan:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改出门证计划申请对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="计划号" prop="planNo">
          <el-input v-model="form.planNo" placeholder="请输入计划号" />
        </el-form-item>
        <el-form-item label="计划类型 1-出厂不返回 2-出厂返回 3-跨区调拨 4-退货申请" prop="planType">
          <el-select v-model="form.planType" placeholder="请选择计划类型 1-出厂不返回 2-出厂返回 3-跨区调拨 4-退货申请">
            <el-option label="请选择字典生成" value="" />
          </el-select>
        </el-form-item>
        <el-form-item label="业务类型
1-通用" prop="businessCategory">
          <el-input v-model="form.businessCategory" placeholder="请输入业务类型
1-通用" />
        </el-form-item>
        <el-form-item label="是否计量 计量-1 不计量-0" prop="measureFlag">
          <el-input v-model="form.measureFlag" placeholder="请输入是否计量 计量-1 不计量-0" />
        </el-form-item>
        <el-form-item label="计划量 计划量计量时才存在" prop="plannedAmount">
          <el-input v-model="form.plannedAmount" placeholder="请输入计划量 计划量计量时才存在" />
        </el-form-item>
        <el-form-item label="收货单位" prop="receiveCompany">
          <el-input v-model="form.receiveCompany" placeholder="请输入收货单位" />
        </el-form-item>
        <el-form-item label="收货单位code" prop="receiveCompanyCode">
          <el-input v-model="form.receiveCompanyCode" placeholder="请输入收货单位code" />
        </el-form-item>
        <el-form-item label="返回单位" prop="targetCompany">
          <el-input v-model="form.targetCompany" placeholder="请输入返回单位" />
        </el-form-item>
        <el-form-item label="返回单位code" prop="targetCompanyCode">
          <el-input v-model="form.targetCompanyCode" placeholder="请输入返回单位code" />
        </el-form-item>
        <el-form-item label="申请单位" prop="sourceCompany">
          <el-input v-model="form.sourceCompany" placeholder="请输入申请单位" />
        </el-form-item>
        <el-form-item label="申请单位code" prop="sourceCompanyCode">
          <el-input v-model="form.sourceCompanyCode" placeholder="请输入申请单位code" />
        </el-form-item>
        <el-form-item label="计划返回时间" prop="planReturnTime">
          <el-date-picker clearable size="small" style="width: 200px"
            v-model="form.planReturnTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="选择计划返回时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="实际返回时间  多次返厂情况下填最新一次" prop="realReturnTime">
          <el-date-picker clearable size="small" style="width: 200px"
            v-model="form.realReturnTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="选择实际返回时间  多次返厂情况下填最新一次">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="监装人" prop="monitor">
          <el-input v-model="form.monitor" placeholder="请输入监装人" />
        </el-form-item>
        <el-form-item label="物资专管员" prop="specialManager">
          <el-input v-model="form.specialManager" placeholder="请输入物资专管员" />
        </el-form-item>
        <el-form-item label="申请有效期" prop="expireTime">
          <el-date-picker clearable size="small" style="width: 200px"
            v-model="form.expireTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="选择申请有效期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="出厂原因" prop="reason">
          <el-input v-model="form.reason" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="物资类型 1-钢材 2-钢板 3-其他" prop="itemType">
          <el-select v-model="form.itemType" placeholder="请选择物资类型 1-钢材 2-钢板 3-其他">
            <el-option label="请选择字典生成" value="" />
          </el-select>
        </el-form-item>
        <el-form-item label="计划状态 1-待分厂审批 2-待分厂复审 3-待生产指挥中心审批 4-审批完成  5-已出厂 6-部分收货 7-已完成
11-驳回 12-废弃 13-过期">
          <el-radio-group v-model="form.planStatus">
            <el-radio label="1">请选择字典生成</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="申请时间" prop="applyTime">
          <el-date-picker clearable size="small" style="width: 200px"
            v-model="form.applyTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="选择申请时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="申请人" prop="applyWorkNo">
          <el-input v-model="form.applyWorkNo" placeholder="请输入申请人" />
        </el-form-item>
        <el-form-item label="分厂领导审批时间" prop="factoryApproveTime">
          <el-date-picker clearable size="small" style="width: 200px"
            v-model="form.factoryApproveTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="选择分厂领导审批时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="分厂领导工号" prop="factoryApproveWorkNo">
          <el-input v-model="form.factoryApproveWorkNo" placeholder="请输入分厂领导工号" />
        </el-form-item>
        <el-form-item label="分厂审核结果 0-拒绝 1-同意" prop="factoryApproveFlag">
          <el-input v-model="form.factoryApproveFlag" placeholder="请输入分厂审核结果 0-拒绝 1-同意" />
        </el-form-item>
        <el-form-item label="分厂领导审核意见">
          <editor v-model="form.factoryApproveContent" :min-height="192"/>
        </el-form-item>
        <el-form-item label="分厂复审结果 0-拒绝 1-同意" prop="factorySecApproveFlag">
          <el-input v-model="form.factorySecApproveFlag" placeholder="请输入分厂复审结果 0-拒绝 1-同意" />
        </el-form-item>
        <el-form-item label="分厂复审时间" prop="factorySecApproveTime">
          <el-date-picker clearable size="small" style="width: 200px"
            v-model="form.factorySecApproveTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="选择分厂复审时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="分厂复审审批人工号" prop="factorySecApproveWorkNo">
          <el-input v-model="form.factorySecApproveWorkNo" placeholder="请输入分厂复审审批人工号" />
        </el-form-item>
        <el-form-item label="分厂复审审核意见">
          <editor v-model="form.factorySecApproveContent" :min-height="192"/>
        </el-form-item>
        <el-form-item label="生产指挥中心审批时间" prop="centerApproveTime">
          <el-date-picker clearable size="small" style="width: 200px"
            v-model="form.centerApproveTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="选择生产指挥中心审批时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="生产指挥中心审批人工号" prop="centerApproveWorkNo">
          <el-input v-model="form.centerApproveWorkNo" placeholder="请输入生产指挥中心审批人工号" />
        </el-form-item>
        <el-form-item label="生产指挥中心审核结果 0-拒绝 1-同意" prop="centerApproveFlag">
          <el-input v-model="form.centerApproveFlag" placeholder="请输入生产指挥中心审核结果 0-拒绝 1-同意" />
        </el-form-item>
        <el-form-item label="生产指挥中心审核意见">
          <editor v-model="form.centerApproveContent" :min-height="192"/>
        </el-form-item>
        <el-form-item label="申请文件，允许多个" prop="applyFileUrl">
          <el-input v-model="form.applyFileUrl" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listPlan, getPlan, delPlan, addPlan, updatePlan, exportPlan } from "@/api/leave/plan";
import Editor from '@/components/Editor';

export default {
  name: "Plan",
  components: {
    Editor,
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 出门证计划申请表格数据
      planList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        planNo: null,
        planType: null,
        businessCategory: null,
        measureFlag: null,
        plannedAmount: null,
        receiveCompany: null,
        receiveCompanyCode: null,
        targetCompany: null,
        targetCompanyCode: null,
        sourceCompany: null,
        sourceCompanyCode: null,
        planReturnTime: null,
        realReturnTime: null,
        monitor: null,
        specialManager: null,
        expireTime: null,
        reason: null,
        itemType: null,
        planStatus: null,
        applyTime: null,
        applyWorkNo: null,
        factoryApproveTime: null,
        factoryApproveWorkNo: null,
        factoryApproveFlag: null,
        factoryApproveContent: null,
        factorySecApproveFlag: null,
        factorySecApproveTime: null,
        factorySecApproveWorkNo: null,
        factorySecApproveContent: null,
        centerApproveTime: null,
        centerApproveWorkNo: null,
        centerApproveFlag: null,
        centerApproveContent: null,
        applyFileUrl: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询出门证计划申请列表 */
    getList() {
      this.loading = true;
      listPlan(this.queryParams).then(response => {
        this.planList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        applyNo: null,
        planNo: null,
        planType: null,
        businessCategory: null,
        measureFlag: null,
        plannedAmount: null,
        receiveCompany: null,
        receiveCompanyCode: null,
        targetCompany: null,
        targetCompanyCode: null,
        sourceCompany: null,
        sourceCompanyCode: null,
        planReturnTime: null,
        realReturnTime: null,
        monitor: null,
        specialManager: null,
        expireTime: null,
        reason: null,
        itemType: null,
        planStatus: 0,
        applyTime: null,
        applyWorkNo: null,
        factoryApproveTime: null,
        factoryApproveWorkNo: null,
        factoryApproveFlag: null,
        factoryApproveContent: null,
        factorySecApproveFlag: null,
        factorySecApproveTime: null,
        factorySecApproveWorkNo: null,
        factorySecApproveContent: null,
        centerApproveTime: null,
        centerApproveWorkNo: null,
        centerApproveFlag: null,
        centerApproveContent: null,
        applyFileUrl: null,
        remark: null,
        createTime: null,
        createBy: null,
        updateTime: null,
        updateBy: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加出门证计划申请";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getPlan(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改出门证计划申请";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updatePlan(this.form).then(response => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addPlan(this.form).then(response => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$confirm('是否确认删除出门证计划申请编号为"' + ids + '"的数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return delPlan(ids);
        }).then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        })
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm('是否确认导出所有出门证计划申请数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return exportPlan(queryParams);
        }).then(response => {
          this.download(response.msg);
        })
    }
  }
};
</script>
