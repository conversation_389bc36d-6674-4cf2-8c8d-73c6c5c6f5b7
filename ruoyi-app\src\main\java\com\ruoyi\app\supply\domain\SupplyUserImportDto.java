package com.ruoyi.app.supply.domain;

import com.ruoyi.common.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;

/**
 * 供应商用户导入DTO
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
public class SupplyUserImportDto {
    
    /** 用户编号 */
    @Excel(name = "用户编号")
    private String userCode;

    /** 用户姓名 */
    @Excel(name = "用户姓名")
    private String userName;

    /** 供应商代码 */
    @Excel(name = "供应商代码")
    private String supplyCode;

    /** 供应商名称 */
    @Excel(name = "供应商名称")
    private String supplyName;

    /** 身份证 */
    @Excel(name = "身份证")
    private String idcard;

    /** 状态 */
    @Excel(name = "状态", readConverterExp = "1=有效,0=无效")
    private Integer state;

    /** 岗位 */
    @Excel(name = "岗位")
    private String userPost;

    /** 服务分厂代码 */
    @Excel(name = "服务分厂代码")
    private String userDeptC;

    /** 服务分厂名称 */
    @Excel(name = "服务分厂名称")
    private String userDeptName;

    /** 有效期起始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "有效期起始时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date userTimeBegin;

    /** 有效期结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "有效期结束时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date userTimeEnd;

    /** 岗位识别卡发放时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "岗位识别卡发放时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date userFacTime;

    /** 岗位识别卡工种 */
    @Excel(name = "岗位识别卡工种")
    private String userFacWork;

    /** 岗位识别卡级别 */
    @Excel(name = "岗位识别卡级别")
    private String userFacClass;

    /** 半年内变更相关方单位 */
    @Excel(name = "半年内变更相关方单位", readConverterExp = "1=是,0=否")
    private Integer isChange;

    /** 预计退休年龄 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "预计退休年龄", width = 30, dateFormat = "yyyy-MM-dd")
    private Date retireDate;


    // SupplyUserTec 三级教育卡相关字段
    /** 性别 */
    @Excel(name = "性别")
    private String sex;

    /** 出生年月 */
    @Excel(name = "出生年月")
    private String birth;

    /** 文化水平 */
    @Excel(name = "文化水平")
    private String educationLevel;

    /** 曾受过何种安全技术培训 */
    @Excel(name = "曾受过何种安全技术培训")
    private String safetyTraining;

    /** 考试结果 */
    @Excel(name = "考试结果")
    private String examRes;

    /** 家庭住址 */
    @Excel(name = "家庭住址")
    private String address;

    /** 上岗时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "上岗时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date employmentDate;

    /** 公司级培训开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "公司级培训开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date firstArtTime;

    /** 公司级培训结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "公司级培训结束时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date firEndTime;

    /** 公司级培训学时 */
    @Excel(name = "公司级培训学时")
    private String firHours;

    /** 厂部级培训开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "厂部级培训开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date secStartTime;

    /** 厂部级培训结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "厂部级培训结束时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date secEndTime;

    /** 厂部级培训学时 */
    @Excel(name = "厂部级培训学时")
    private String secHours;

    /** 班组级培训开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "班组级培训开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date thiStartTime;

    /** 班组级培训结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "班组级培训结束时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date thiEndTime;

    /** 班组级培训学时 */
    @Excel(name = "班组级培训学时")
    private String thiHours;

    // 导入结果相关字段
    private String result;
    private String reason;

    // Getters and Setters
    public String getUserCode() {
        return userCode;
    }

    public void setUserCode(String userCode) {
        this.userCode = userCode;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getSupplyCode() {
        return supplyCode;
    }

    public void setSupplyCode(String supplyCode) {
        this.supplyCode = supplyCode;
    }

    public String getSupplyName() {
        return supplyName;
    }

    public void setSupplyName(String supplyName) {
        this.supplyName = supplyName;
    }

    public String getIdcard() {
        return idcard;
    }

    public void setIdcard(String idcard) {
        this.idcard = idcard;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public String getUserPost() {
        return userPost;
    }

    public void setUserPost(String userPost) {
        this.userPost = userPost;
    }

    public String getUserDeptC() {
        return userDeptC;
    }

    public void setUserDeptC(String userDeptC) {
        this.userDeptC = userDeptC;
    }

    public String getUserDeptName() {
        return userDeptName;
    }

    public void setUserDeptName(String userDeptName) {
        this.userDeptName = userDeptName;
    }

    public Date getUserTimeBegin() {
        return userTimeBegin;
    }

    public void setUserTimeBegin(Date userTimeBegin) {
        this.userTimeBegin = userTimeBegin;
    }

    public Date getUserTimeEnd() {
        return userTimeEnd;
    }

    public void setUserTimeEnd(Date userTimeEnd) {
        this.userTimeEnd = userTimeEnd;
    }

    public Date getUserFacTime() {
        return userFacTime;
    }

    public void setUserFacTime(Date userFacTime) {
        this.userFacTime = userFacTime;
    }

    public String getUserFacWork() {
        return userFacWork;
    }

    public void setUserFacWork(String userFacWork) {
        this.userFacWork = userFacWork;
    }

    public String getUserFacClass() {
        return userFacClass;
    }

    public void setUserFacClass(String userFacClass) {
        this.userFacClass = userFacClass;
    }

    public Integer getIsChange() {
        return isChange;
    }

    public void setIsChange(Integer isChange) {
        this.isChange = isChange;
    }

    public Date getRetireDate() {
        return retireDate;
    }

    public void setRetireDate(Date retireDate) {
        this.retireDate = retireDate;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    // SupplyUserTec 相关字段的 getter/setter 方法
    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getBirth() {
        return birth;
    }

    public void setBirth(String birth) {
        this.birth = birth;
    }

    public String getEducationLevel() {
        return educationLevel;
    }

    public void setEducationLevel(String educationLevel) {
        this.educationLevel = educationLevel;
    }

    public String getSafetyTraining() {
        return safetyTraining;
    }

    public void setSafetyTraining(String safetyTraining) {
        this.safetyTraining = safetyTraining;
    }

    public String getExamRes() {
        return examRes;
    }

    public void setExamRes(String examRes) {
        this.examRes = examRes;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public Date getEmploymentDate() {
        return employmentDate;
    }

    public void setEmploymentDate(Date employmentDate) {
        this.employmentDate = employmentDate;
    }

    public Date getFirstArtTime() {
        return firstArtTime;
    }

    public void setFirstArtTime(Date firstArtTime) {
        this.firstArtTime = firstArtTime;
    }

    public Date getFirEndTime() {
        return firEndTime;
    }

    public void setFirEndTime(Date firEndTime) {
        this.firEndTime = firEndTime;
    }

    public String getFirHours() {
        return firHours;
    }

    public void setFirHours(String firHours) {
        this.firHours = firHours;
    }

    public Date getSecStartTime() {
        return secStartTime;
    }

    public void setSecStartTime(Date secStartTime) {
        this.secStartTime = secStartTime;
    }

    public Date getSecEndTime() {
        return secEndTime;
    }

    public void setSecEndTime(Date secEndTime) {
        this.secEndTime = secEndTime;
    }

    public String getSecHours() {
        return secHours;
    }

    public void setSecHours(String secHours) {
        this.secHours = secHours;
    }

    public Date getThiStartTime() {
        return thiStartTime;
    }

    public void setThiStartTime(Date thiStartTime) {
        this.thiStartTime = thiStartTime;
    }

    public Date getThiEndTime() {
        return thiEndTime;
    }

    public void setThiEndTime(Date thiEndTime) {
        this.thiEndTime = thiEndTime;
    }

    public String getThiHours() {
        return thiHours;
    }

    public void setThiHours(String thiHours) {
        this.thiHours = thiHours;
    }
}
