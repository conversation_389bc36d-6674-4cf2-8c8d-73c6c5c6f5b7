{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\qualityCost\\companySummary2\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\qualityCost\\companySummary2\\index.vue", "mtime": 1756099891078}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBsaXN0Q29tcGFueVN1bW1hcnlEYXRhIH0gZnJvbSAiQC9hcGkvcXVhbGl0eUNvc3QvY29tcGFueVN1bW1hcnkxIjsNCmltcG9ydCB7IHRlc3RGYWN0b3J5RGF0YSwgdGVzdEZhY3RvcnlEYXRhV2l0aFJlYWxWYWx1ZXMsIHRlc3REYXRhTWFwcGluZyB9IGZyb20gIi4vdGVzdERhdGEiOw0KDQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogIkNvbXBhbnlTdW1tYXJ5MSIsDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIC8vIOmBrue9qeWxgg0KICAgICAgbG9hZGluZzogZmFsc2UsDQogICAgICAvLyDotKjph4/miJDmnKzmlbDmja7liJfooagNCiAgICAgIHF1YWxpdHlDb3N0TGlzdDogW10sDQogICAgICAvLyDlkIjlubbljZXlhYPmoLzphY3nva4NCiAgICAgIG1lcmdlQ2VsbHM6IFtdLA0KICAgICAgLy8g6KGo5qC86auY5bqmDQogICAgICB0YWJsZUhlaWdodDogbnVsbCwNCiAgICAgIC8vIOaYr+WQpumcgOimgea7muWKqOadoQ0KICAgICAgbmVlZFNjcm9sbGJhcjogdHJ1ZSwNCiAgICAgIC8vIOW5tOS7vemAieaLqQ0KICAgICAgc2VsZWN0ZWRZZWFyOiBuZXcgRGF0ZSgpLmdldEZ1bGxZZWFyKCksDQogICAgICAvLyDlubTku73pgInpobkNCiAgICAgIHllYXJPcHRpb25zOiBbXSwNCiAgICAgIC8vIOexu+Wei+mAieaLqQ0KICAgICAgc2VsZWN0ZWRUeXBlOiAn5oql5bqfJywNCiAgICAgIC8vIOexu+Wei+mAiemhuQ0KICAgICAgdHlwZU9wdGlvbnM6IFsn5pS55YikJywgJ+aKpeW6nycsICfohLHlkIjlkIwnLCAn6YCA6LSnJ10NCiAgICB9Ow0KICB9LA0KICBjb21wdXRlZDogew0KICAgIC8qKiDlrrnlmajmoLflvI8gKi8NCiAgICBjb250YWluZXJTdHlsZSgpIHsNCiAgICAgIHJldHVybiB7DQogICAgICAgIGhlaWdodDogdGhpcy50YWJsZUhlaWdodCA/IGAke3RoaXMudGFibGVIZWlnaHR9cHhgIDogJ2F1dG8nDQogICAgICB9Ow0KICAgIH0sDQogICAgLyoqIOihqOagvOWuueWZqOagt+W8jyAqLw0KICAgIHRhYmxlQ29udGFpbmVyU3R5bGUoKSB7DQogICAgICByZXR1cm4gew0KICAgICAgICBoZWlnaHQ6IHRoaXMudGFibGVIZWlnaHQgPyBgJHt0aGlzLnRhYmxlSGVpZ2h0IC0gODB9cHhgIDogJ2F1dG8nDQogICAgICB9Ow0KICAgIH0NCiAgfSwNCiAgbW91bnRlZCgpIHsNCiAgICB0aGlzLmNhbGN1bGF0ZVRhYmxlSGVpZ2h0KCk7DQogICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoJ3Jlc2l6ZScsIHRoaXMuY2FsY3VsYXRlVGFibGVIZWlnaHQpOw0KICAgIHRoaXMuZ2VuZXJhdGVZZWFyT3B0aW9ucygpOw0KICB9LA0KICBiZWZvcmVEZXN0cm95KCkgew0KICAgIHdpbmRvdy5yZW1vdmVFdmVudExpc3RlbmVyKCdyZXNpemUnLCB0aGlzLmNhbGN1bGF0ZVRhYmxlSGVpZ2h0KTsNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIGdldENvbXBhbnlTdW1tYXJ5RGF0YSgpIHsNCiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7DQogICAgICANCiAgICAgIGNvbnN0IHBhcmFtcyA9IHsNCiAgICAgICAgeWVhck1vbnRoOiB0aGlzLnNlbGVjdGVkWWVhciwNCiAgICAgICAgY29tcGFueVN1bW1hcnlUeXBlOiB0aGlzLnNlbGVjdGVkVHlwZQ0KICAgICAgfTsNCg0KICAgICAgbGlzdENvbXBhbnlTdW1tYXJ5RGF0YShwYXJhbXMpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICBjb25zb2xlLmxvZygnbGlzdENvbXBhbnlTdW1tYXJ5RGF0YTonLCByZXNwb25zZSk7DQogICAgICAgIGlmIChyZXNwb25zZS5kYXRhKSB7DQogICAgICAgICAgLy8g5aSE55CG5YiG5Y6C5pWw5o2u5qC85byPDQogICAgICAgICAgdGhpcy5wcm9jZXNzRmFjdG9yeURhdGEocmVzcG9uc2UuZGF0YSk7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy5xdWFsaXR5Q29zdExpc3QgPSBbXTsNCiAgICAgICAgfQ0KICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgIH0pLmNhdGNoKGVycm9yID0+IHsNCiAgICAgICAgY29uc29sZS5lcnJvcign6I635Y+W5pWw5o2u5aSx6LSlOicsIGVycm9yKTsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign6I635Y+W6LSo6YeP5oiQ5pys5pWw5o2u5aSx6LSlJyk7DQogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgICAgfSk7DQogICAgfSwNCg0KICAgIC8qKiDlpITnkIbliIbljoLmlbDmja7moLzlvI8gKi8NCiAgICBwcm9jZXNzRmFjdG9yeURhdGEocmF3RGF0YSkgew0KICAgICAgY29uc29sZS5sb2coJ+W8gOWni+WkhOeQhuWIhuWOguaVsOaNrjonLCByYXdEYXRhKTsNCiAgICAgIA0KICAgICAgLy8g5qOA5p+l5piv5ZCm5Li65YiG5Y6C5pWw5o2u5qC85byPDQogICAgICBpZiAodGhpcy5pc0ZhY3RvcnlEYXRhRm9ybWF0KHJhd0RhdGEpKSB7DQogICAgICAgIGNvbnNvbGUubG9nKCfmo4DmtYvliLDliIbljoLmlbDmja7moLzlvI/vvIzlvIDlp4vlpITnkIYuLi4nKTsNCiAgICAgICAgDQogICAgICAvLyDovazmjaLkuLrooajmoLzmoLzlvI8NCiAgICAgICAgY29uc3QgdGFibGVEYXRhID0gdGhpcy5jb252ZXJ0RmFjdG9yeURhdGFUb1RhYmxlKHJhd0RhdGEpOw0KICAgICAgICANCiAgICAgICAgLy8g5pu05paw6KGo5qC85pWw5o2uDQogICAgICAgIHRoaXMudXBkYXRlVGFibGVXaXRoRmFjdG9yeURhdGEodGFibGVEYXRhKTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIGNvbnNvbGUubG9nKCfkuI3mmK/liIbljoLmlbDmja7moLzlvI/vvIzkvb/nlKjpu5jorqTlpITnkIYnKTsNCiAgICAgICAgdGhpcy5xdWFsaXR5Q29zdExpc3QgPSBbXTsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLyoqIOajgOafpeaYr+WQpuS4uuWIhuWOguaVsOaNruagvOW8jyAqLw0KICAgIGlzRmFjdG9yeURhdGFGb3JtYXQoZGF0YSkgew0KICAgICAgaWYgKCFkYXRhIHx8IHR5cGVvZiBkYXRhICE9PSAnb2JqZWN0JykgcmV0dXJuIGZhbHNlOw0KICAgICAgDQogICAgICBjb25zdCBmYWN0b3J5S2V5cyA9IE9iamVjdC5rZXlzKGRhdGEpOw0KICAgICAgaWYgKGZhY3RvcnlLZXlzLmxlbmd0aCA9PT0gMCkgcmV0dXJuIGZhbHNlOw0KICAgICAgDQogICAgICBjb25zdCBmaXJzdEZhY3RvcnkgPSBkYXRhW2ZhY3RvcnlLZXlzWzBdXTsNCiAgICAgIGlmICghZmlyc3RGYWN0b3J5IHx8IHR5cGVvZiBmaXJzdEZhY3RvcnkgIT09ICdvYmplY3QnKSByZXR1cm4gZmFsc2U7DQogICAgICANCiAgICAgIGNvbnN0IG1vbnRoS2V5cyA9IE9iamVjdC5rZXlzKGZpcnN0RmFjdG9yeSk7DQogICAgICBpZiAobW9udGhLZXlzLmxlbmd0aCA9PT0gMCkgcmV0dXJuIGZhbHNlOw0KICAgICAgDQogICAgICBjb25zdCBmaXJzdE1vbnRoID0gZmlyc3RGYWN0b3J5W21vbnRoS2V5c1swXV07DQogICAgICByZXR1cm4gZmlyc3RNb250aCAmJiANCiAgICAgICAgICAgICB0eXBlb2YgZmlyc3RNb250aCA9PT0gJ29iamVjdCcgJiYgDQogICAgICAgICAgICAgJ2Nvc3RFeCcgaW4gZmlyc3RNb250aCAmJiANCiAgICAgICAgICAgICAnY29zdFRvbicgaW4gZmlyc3RNb250aDsNCiAgICB9LA0KDQogICAgLyoqIOa4heeQhuaooeaLn+aVsOaNriAqLw0KICAgIGNsZWFuTW9ja0RhdGEoZGF0YSkgew0KICAgICAgY29uc3QgY2xlYW5lZERhdGEgPSB7fTsNCiAgICAgIA0KICAgICAgT2JqZWN0LmtleXMoZGF0YSkuZm9yRWFjaChmYWN0b3J5TmFtZSA9PiB7DQogICAgICAgIGNvbnN0IGZhY3RvcnlEYXRhID0gZGF0YVtmYWN0b3J5TmFtZV07DQogICAgICAgIGNvbnN0IHZhbGlkTW9udGhzID0ge307DQogICAgICAgIA0KICAgICAgICBPYmplY3Qua2V5cyhmYWN0b3J5RGF0YSkuZm9yRWFjaChtb250aEtleSA9PiB7DQogICAgICAgICAgY29uc3QgbW9udGhEYXRhID0gZmFjdG9yeURhdGFbbW9udGhLZXldOw0KICAgICAgICAgIA0KICAgICAgICAgIC8vIOajgOafpeaYr+WQpuS4uuaooeaLn+aVsOaNrg0KICAgICAgICAgIGlmICghdGhpcy5pc01vY2tEYXRhKG1vbnRoRGF0YSkpIHsNCiAgICAgICAgICAgIHZhbGlkTW9udGhzW21vbnRoS2V5XSA9IG1vbnRoRGF0YTsNCiAgICAgICAgICB9DQogICAgICAgIH0pOw0KICAgICAgICANCiAgICAgICAgLy8g5Y+q5pyJ5b2T5YiG5Y6C5pyJ5pyJ5pWI5pWw5o2u5pe25omN5L+d55WZDQogICAgICAgIGlmIChPYmplY3Qua2V5cyh2YWxpZE1vbnRocykubGVuZ3RoID4gMCkgew0KICAgICAgICAgIGNsZWFuZWREYXRhW2ZhY3RvcnlOYW1lXSA9IHZhbGlkTW9udGhzOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICAgIA0KICAgICAgY29uc29sZS5sb2coJ+a4heeQhuWQjueahOaVsOaNrjonLCBjbGVhbmVkRGF0YSk7DQogICAgICByZXR1cm4gY2xlYW5lZERhdGE7DQogICAgfSwNCg0KICAgIC8qKiDliKTmlq3mmK/lkKbkuLrmqKHmi5/mlbDmja4gKi8NCiAgICBpc01vY2tEYXRhKG1vbnRoRGF0YSkgew0KICAgICAgcmV0dXJuICgNCiAgICAgICAgbW9udGhEYXRhLmNvc3RDZW50ZXJOYW1lID09PSBudWxsICYmDQogICAgICAgIG1vbnRoRGF0YS5jb3N0RXggPT09IDAgJiYNCiAgICAgICAgbW9udGhEYXRhLmNvc3RUb24gPT09IDAgJiYNCiAgICAgICAgbW9udGhEYXRhLnllYXJNb250aCA9PT0gbnVsbA0KICAgICAgKTsNCiAgICB9LA0KDQogICAgLyoqIOWwhuWIhuWOguaVsOaNrui9rOaNouS4uuihqOagvOagvOW8jyAqLw0KICAgIGNvbnZlcnRGYWN0b3J5RGF0YVRvVGFibGUoZmFjdG9yeURhdGEpIHsNCiAgICAgIGNvbnN0IHRhYmxlRGF0YSA9IFtdOw0KICAgICAgDQogICAgICBPYmplY3Qua2V5cyhmYWN0b3J5RGF0YSkuZm9yRWFjaChmYWN0b3J5TmFtZSA9PiB7DQogICAgICAgIGNvbnN0IGZhY3RvcnlNb250aHMgPSBmYWN0b3J5RGF0YVtmYWN0b3J5TmFtZV07DQogICAgICAgIA0KICAgICAgICBPYmplY3Qua2V5cyhmYWN0b3J5TW9udGhzKS5mb3JFYWNoKG1vbnRoS2V5ID0+IHsNCiAgICAgICAgICBjb25zdCBtb250aERhdGEgPSBmYWN0b3J5TW9udGhzW21vbnRoS2V5XTsNCiAgICAgICAgICANCiAgICAgICAgICAvLyDliJvlu7rooajmoLzooYzmlbDmja4NCiAgICAgICAgICBjb25zdCByb3dEYXRhID0gew0KICAgICAgICAgICAgZmFjdG9yeU5hbWU6IGZhY3RvcnlOYW1lLA0KICAgICAgICAgICAgbW9udGg6IG1vbnRoS2V5LA0KICAgICAgICAgICAgY29zdEV4OiBtb250aERhdGEuY29zdEV4IHx8IDAsDQogICAgICAgICAgICBjb3N0VG9uOiBtb250aERhdGEuY29zdFRvbiB8fCAwLA0KICAgICAgICAgICAgeWVhck1vbnRoOiBtb250aERhdGEueWVhck1vbnRoLA0KICAgICAgICAgICAgY29zdENlbnRlck5hbWU6IG1vbnRoRGF0YS5jb3N0Q2VudGVyTmFtZQ0KICAgICAgICAgIH07DQogICAgICAgICAgDQogICAgICAgICAgdGFibGVEYXRhLnB1c2gocm93RGF0YSk7DQogICAgICAgIH0pOw0KICAgICAgfSk7DQogICAgICANCiAgICAgIGNvbnNvbGUubG9nKCfovazmjaLlkI7nmoTooajmoLzmlbDmja46JywgdGFibGVEYXRhKTsNCiAgICAgIHJldHVybiB0YWJsZURhdGE7DQogICAgfSwNCg0KICAgIC8qKiDmm7TmlrDooajmoLzmmL7npLrliIbljoLmlbDmja4gKi8NCiAgICB1cGRhdGVUYWJsZVdpdGhGYWN0b3J5RGF0YSh0YWJsZURhdGEpIHsNCiAgICAgIGlmICh0YWJsZURhdGEubGVuZ3RoID09PSAwKSB7DQogICAgICAgIHRoaXMucXVhbGl0eUNvc3RMaXN0ID0gW107DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCiAgICAgIA0KICAgICAgLy8g6I635Y+W5omA5pyJ5YiG5Y6C5ZCN56ewDQogICAgICBjb25zdCBmYWN0b3J5TmFtZXMgPSBbLi4ubmV3IFNldCh0YWJsZURhdGEubWFwKHJvdyA9PiByb3cuZmFjdG9yeU5hbWUpKV07DQogICAgICANCiAgICAgIC8vIOWIm+W7uuihqOagvOihjOaVsOaNrg0KICAgICAgdGhpcy5xdWFsaXR5Q29zdExpc3QgPSBmYWN0b3J5TmFtZXMubWFwKGZhY3RvcnlOYW1lID0+IHsNCiAgICAgICAgY29uc3Qgcm93RGF0YSA9IHsNCiAgICAgICAgICBjb21wYW55OiBmYWN0b3J5TmFtZQ0KICAgICAgICB9Ow0KICAgICAgICANCiAgICAgICAgLy8g5Yid5aeL5YyW5omA5pyJ5pyI5Lu955qE5pWw5o2u5Li6MA0KICAgICAgICBjb25zdCBtb250aHMgPSBbJ2phbnVhcnknLCAnZmVicnVhcnknLCAnbWFyY2gnLCAnYXByaWwnLCAnbWF5JywgJ2p1bmUnLA0KICAgICAgICAgICAgICAgICAgICAgICAnanVseScsICdhdWd1c3QnLCAnc2VwdGVtYmVyJywgJ29jdG9iZXInLCAnbm92ZW1iZXInLCAnZGVjZW1iZXInXTsNCiAgICAgICAgDQogICAgICAgIG1vbnRocy5mb3JFYWNoKChtb250aCwgaW5kZXgpID0+IHsNCiAgICAgICAgICBjb25zdCBtb250aE51bWJlciA9IGluZGV4ICsgMTsNCiAgICAgICAgICBjb25zdCBtb250aERhdGEgPSB0YWJsZURhdGEuZmluZChyb3cgPT4gDQogICAgICAgICAgICByb3cuZmFjdG9yeU5hbWUgPT09IGZhY3RvcnlOYW1lICYmIHJvdy5tb250aCA9PT0gbW9udGhOdW1iZXIudG9TdHJpbmcoKQ0KICAgICAgICAgICk7DQogICAgICAgICAgDQogICAgICAgICAgaWYgKG1vbnRoRGF0YSkgew0KICAgICAgICAgICAgcm93RGF0YVtgJHttb250aH1BbW91bnRgXSA9IG1vbnRoRGF0YS5jb3N0RXg7DQogICAgICAgICAgICByb3dEYXRhW2Ake21vbnRofVBlclRvbmBdID0gbW9udGhEYXRhLmNvc3RUb247DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHJvd0RhdGFbYCR7bW9udGh9QW1vdW50YF0gPSAwOw0KICAgICAgICAgICAgcm93RGF0YVtgJHttb250aH1QZXJUb25gXSA9IDA7DQogICAgICAgICAgfQ0KICAgICAgICB9KTsNCiAgICAgICAgDQogICAgICAgIC8vIOiuoeeul+W5tOW6pue0r+iuoQ0KICAgICAgICByb3dEYXRhLnllYXJseVRvdGFsQW1vdW50ID0gbW9udGhzLnJlZHVjZSgoc3VtLCBtb250aCkgPT4gc3VtICsgKHJvd0RhdGFbYCR7bW9udGh9QW1vdW50YF0gfHwgMCksIDApOw0KICAgICAgICByb3dEYXRhLnllYXJseVRvdGFsUGVyVG9uID0gbW9udGhzLnJlZHVjZSgoc3VtLCBtb250aCkgPT4gc3VtICsgKHJvd0RhdGFbYCR7bW9udGh9UGVyVG9uYF0gfHwgMCksIDApOw0KICAgICAgICANCiAgICAgICAgcmV0dXJuIHJvd0RhdGE7DQogICAgICB9KTsNCiAgICAgIA0KICAgICAgY29uc29sZS5sb2coJ+abtOaWsOWQjueahOihqOagvOaVsOaNrjonLCB0aGlzLnF1YWxpdHlDb3N0TGlzdCk7DQogICAgfSwNCg0KDQogICAgLyoqIOagvOW8j+WMlui0p+W4gSAqLw0KICAgIGZvcm1hdEN1cnJlbmN5KHZhbHVlKSB7DQogICAgICBpZiAodmFsdWUgPT09IG51bGwgfHwgdmFsdWUgPT09IHVuZGVmaW5lZCB8fCB2YWx1ZSA9PT0gJycgfHwgTnVtYmVyKHZhbHVlKSA9PT0gMCkgew0KICAgICAgICByZXR1cm4gJy0nOw0KICAgICAgfQ0KICAgICAgY29uc3QgbnVtID0gTnVtYmVyKHZhbHVlKTsNCiAgICAgIC8vIOmHkemineaYvuekuuS4uuWFg++8jOS4jei9rOaNouS4uuS4hw0KICAgICAgcmV0dXJuIG51bS50b0xvY2FsZVN0cmluZygnemgtQ04nLCB7DQogICAgICAgIG1pbmltdW1GcmFjdGlvbkRpZ2l0czogMiwNCiAgICAgICAgbWF4aW11bUZyYWN0aW9uRGlnaXRzOiAyDQogICAgICB9KTsNCiAgICB9LA0KDQogICAgLyoqIOagvOW8j+WMluWQqOS9jSAqLw0KICAgIGZvcm1hdFRvbih2YWx1ZSkgew0KICAgICAgaWYgKHZhbHVlID09PSBudWxsIHx8IHZhbHVlID09PSB1bmRlZmluZWQgfHwgdmFsdWUgPT09ICcnIHx8IE51bWJlcih2YWx1ZSkgPT09IDApIHsNCiAgICAgICAgcmV0dXJuICctJzsNCiAgICAgIH0NCiAgICAgIGNvbnN0IG51bSA9IE51bWJlcih2YWx1ZSk7DQogICAgICAvLyDlkKjkvY3mmL7npLrkuLrlkKjvvIzkv53nlZky5L2N5bCP5pWwDQogICAgICByZXR1cm4gbnVtLnRvTG9jYWxlU3RyaW5nKCd6aC1DTicsIHsNCiAgICAgICAgbWluaW11bUZyYWN0aW9uRGlnaXRzOiAyLA0KICAgICAgICBtYXhpbXVtRnJhY3Rpb25EaWdpdHM6IDINCiAgICAgIH0pOw0KICAgIH0sDQoNCiAgICAvKiog6K6h566X6KGo5qC86auY5bqmICovDQogICAgY2FsY3VsYXRlVGFibGVIZWlnaHQoKSB7DQogICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgIGNvbnN0IHdpbmRvd0hlaWdodCA9IHdpbmRvdy5pbm5lckhlaWdodDsNCiAgICAgICAgLy8g5YeP5Y676aG16Z2i5aS06YOo44CB5qCH6aKY562J6auY5bqm77yM5aSn57qmMTAwcHgNCiAgICAgICAgY29uc3QgYXZhaWxhYmxlSGVpZ2h0ID0gd2luZG93SGVpZ2h0IC0gMTAwOw0KICAgICAgICAvLyDorr7nva7ooajmoLzmnIDlpKfpq5jluqbvvIzmnIDlsI80MDBweO+8jOacgOWkp+S4jei2hei/h+WPr+eUqOmrmOW6pueahDkwJQ0KICAgICAgICB0aGlzLnRhYmxlSGVpZ2h0ID0gTWF0aC5tYXgoNDAwLCBNYXRoLm1pbig4MDAsIGF2YWlsYWJsZUhlaWdodCAqIDAuOSkpOw0KICAgICAgICB0aGlzLm5lZWRTY3JvbGxiYXIgPSB0aGlzLnF1YWxpdHlDb3N0TGlzdC5sZW5ndGggPiA4Ow0KICAgICAgfSk7DQogICAgfSwNCg0KICAgIC8qKiDnlJ/miJDlubTku73pgInpobkgKi8NCiAgICBnZW5lcmF0ZVllYXJPcHRpb25zKCkgew0KICAgICAgY29uc3QgY3VycmVudFllYXIgPSBuZXcgRGF0ZSgpLmdldEZ1bGxZZWFyKCk7DQogICAgICBmb3IgKGxldCBpID0gMjAwMDsgaSA8PSBjdXJyZW50WWVhcjsgaSsrKSB7DQogICAgICAgIHRoaXMueWVhck9wdGlvbnMucHVzaChpKTsNCiAgICAgIH0NCiAgICAgIHRoaXMuZ2V0Q29tcGFueVN1bW1hcnlEYXRhKCkNCiAgICB9LA0KDQogICAgICAgICAvKiog5bm05Lu96YCJ5oup5Y+Y5YyWICovDQogICAgIGhhbmRsZVllYXJDaGFuZ2UoeWVhcikgew0KICAgICAgIHRoaXMuc2VsZWN0ZWRZZWFyID0geWVhcjsNCiAgICAgICAvLyDph43mlrDojrflj5bmlbDmja4NCiAgICAgICB0aGlzLmdldENvbXBhbnlTdW1tYXJ5RGF0YSgpOw0KICAgICB9LA0KDQogICAgIC8qKiDnsbvlnovpgInmi6nlj5jljJYgKi8NCiAgICAgaGFuZGxlVHlwZUNoYW5nZSh0eXBlKSB7DQogICAgICAgdGhpcy5zZWxlY3RlZFR5cGUgPSB0eXBlOw0KICAgICAgIC8vIOmHjeaWsOiOt+WPluaVsOaNrg0KICAgICAgIHRoaXMuZ2V0Q29tcGFueVN1bW1hcnlEYXRhKCk7DQogICAgIH0sDQoNCiAgICAgLyoqIOa1i+ivleWIhuWOguaVsOaNruWkhOeQhiAqLw0KICAgICB0ZXN0RmFjdG9yeURhdGFQcm9jZXNzaW5nKCkgew0KICAgICAgIGNvbnNvbGUubG9nKCflvIDlp4vmtYvor5XliIbljoLmlbDmja7lpITnkIYuLi4nKTsNCiAgICAgICANCiAgICAgICAvLyDmtYvor5UxOiDnuq/mqKHmi5/mlbDmja4NCiAgICAgICBjb25zb2xlLmxvZygnPT09IOa1i+ivlTE6IOe6r+aooeaLn+aVsOaNriA9PT0nKTsNCiAgICAgICB0aGlzLnByb2Nlc3NGYWN0b3J5RGF0YSh0ZXN0RmFjdG9yeURhdGEpOw0KICAgICAgIA0KICAgICAgIC8vIOetieW+hTLnp5LlkI7mtYvor5XnnJ/lrp7mlbDmja4NCiAgICAgICBzZXRUaW1lb3V0KCgpID0+IHsNCiAgICAgICAgIGNvbnNvbGUubG9nKCc9PT0g5rWL6K+VMjog5YyF5ZCr55yf5a6e5pWw5o2uID09PScpOw0KICAgICAgICAgdGhpcy5wcm9jZXNzRmFjdG9yeURhdGEodGVzdEZhY3RvcnlEYXRhV2l0aFJlYWxWYWx1ZXMpOw0KICAgICAgIH0sIDIwMDApOw0KICAgICAgIA0KICAgICAgIC8vIOetieW+hTTnp5LlkI7mtYvor5XmlbDmja7mmKDlsIQNCiAgICAgICBzZXRUaW1lb3V0KCgpID0+IHsNCiAgICAgICAgIGNvbnNvbGUubG9nKCc9PT0g5rWL6K+VMzog5pWw5o2u5pig5bCE6aqM6K+BID09PScpOw0KICAgICAgICAgdGhpcy5wcm9jZXNzRmFjdG9yeURhdGEodGVzdERhdGFNYXBwaW5nKTsNCiAgICAgICAgIHRoaXMudmVyaWZ5RGF0YU1hcHBpbmcoKTsNCiAgICAgICB9LCA0MDAwKTsNCiAgICAgfSwNCg0KICAgICAvKiog6aqM6K+B5pWw5o2u5pig5bCEICovDQogICAgIHZlcmlmeURhdGFNYXBwaW5nKCkgew0KICAgICAgIGNvbnNvbGUubG9nKCfpqozor4HmlbDmja7mmKDlsIQuLi4nKTsNCiAgICAgICANCiAgICAgICBpZiAodGhpcy5xdWFsaXR5Q29zdExpc3QubGVuZ3RoID4gMCkgew0KICAgICAgICAgdGhpcy5xdWFsaXR5Q29zdExpc3QuZm9yRWFjaCgocm93LCBpbmRleCkgPT4gew0KICAgICAgICAgICBjb25zb2xlLmxvZyhg56ysJHtpbmRleCArIDF96KGMIC0g5YiG5Y6COiAke3Jvdy5jb21wYW55fWApOw0KICAgICAgICAgICBjb25zb2xlLmxvZyhgICDkuIDmnIjph5Hpop06ICR7cm93LmphbnVhcnlBbW91bnR9LCDkuIDmnIjlkKjkvY06ICR7cm93LmphbnVhcnlQZXJUb259YCk7DQogICAgICAgICAgIGNvbnNvbGUubG9nKGAgIOS6jOaciOmHkeminTogJHtyb3cuZmVicnVhcnlBbW91bnR9LCDkuozmnIjlkKjkvY06ICR7cm93LmZlYnJ1YXJ5UGVyVG9ufWApOw0KICAgICAgICAgICBjb25zb2xlLmxvZyhgICDkuInmnIjph5Hpop06ICR7cm93Lm1hcmNoQW1vdW50fSwg5LiJ5pyI5ZCo5L2NOiAke3Jvdy5tYXJjaFBlclRvbn1gKTsNCiAgICAgICAgIH0pOw0KICAgICAgIH0NCiAgICAgfQ0KICB9DQp9Ow0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4SA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/qualityCost/companySummary2", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <!-- 质量成本表格 -->\r\n    <div :style=\"containerStyle\">\r\n      <!-- 标题和产量信息 -->\r\n      <div style=\"position: relative; width: 100%; display: flex; justify-content: center; margin: 20px 0;\">\r\n        <h3 style=\"text-align: center; margin: 0;\">各分厂改判汇总表</h3>\r\n        <div style=\"position: absolute; right: 0; bottom: -5px; font-size: 16px; font-weight: bold; color: #333;\">\r\n          <!-- 类型选择框 -->\r\n          <!-- <el-select \r\n            v-model=\"selectedType\" \r\n            placeholder=\"选择类型\" \r\n            style=\"width: 100px; margin-right: 10px;\"\r\n            @change=\"handleTypeChange\">\r\n            <el-option\r\n              v-for=\"type in typeOptions\"\r\n              :key=\"type\"\r\n              :label=\"type\"\r\n              :value=\"type\">\r\n            </el-option>\r\n          </el-select> -->\r\n          <!-- 年份选择框 -->\r\n          <el-select \r\n            v-model=\"selectedYear\" \r\n            placeholder=\"选择年份\" \r\n            style=\"width: 100px; margin-right: 20px;\"\r\n            @change=\"handleYearChange\">\r\n            <el-option\r\n              v-for=\"year in yearOptions\"\r\n              :key=\"year\"\r\n              :label=\"year + '年'\"\r\n              :value=\"year\">\r\n            </el-option>\r\n          </el-select>\r\n         \r\n        </div>\r\n      </div>\r\n\r\n      <div :style=\"tableContainerStyle\">\r\n        <vxe-table\r\n          :loading=\"loading\"\r\n          :data=\"qualityCostList\"\r\n          border\r\n          v-bind=\"tableHeight ? { height: tableHeight } : {}\"\r\n          :class=\"{ 'no-scroll': !needScrollbar }\"\r\n          style=\"table-layout: fixed; width: 100%; margin: 0 auto; padding: 0;\"\r\n          :header-cell-style=\"{ backgroundColor: '#f5f7fa', color: '#303133' }\"\r\n          :merge-cells=\"mergeCells\"\r\n          stripe\r\n          :auto-resize=\"!tableHeight\"\r\n          :scroll-y=\"{enabled: needScrollbar}\"\r\n          :scroll-x=\"{enabled: true}\">\r\n          <vxe-column title=\"分厂\" align=\"center\" field=\"company\" width=\"10%\" fixed=\"left\">\r\n            <template #default=\"{ row }\">\r\n              <span :style=\"{ fontWeight: 'bold' }\">\r\n                {{ row.company }}\r\n              </span>\r\n            </template>\r\n          </vxe-column>\r\n\r\n          <!-- 一月分组 -->\r\n          <vxe-colgroup title=\"一月\" align=\"center\">\r\n            <vxe-column title=\"金额（元）\" align=\"center\" field=\"januaryAmount\" width=\"7%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatCurrency(row.januaryAmount) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n            <vxe-column title=\"吨位\" align=\"center\" field=\"januaryPerTon\" width=\"7%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatTon(row.januaryPerTon) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n          </vxe-colgroup>\r\n\r\n          <!-- 二月分组 -->\r\n          <vxe-colgroup title=\"二月\" align=\"center\">\r\n            <vxe-column title=\"金额（元）\" align=\"center\" field=\"februaryAmount\" width=\"7%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatCurrency(row.februaryAmount) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n            <vxe-column title=\"吨位\" align=\"center\" field=\"februaryPerTon\" width=\"7%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatTon(row.februaryPerTon) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n          </vxe-colgroup>\r\n\r\n          <!-- 三月分组 -->\r\n          <vxe-colgroup title=\"三月\" align=\"center\">\r\n            <vxe-column title=\"金额（元）\" align=\"center\" field=\"marchAmount\" width=\"7%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatCurrency(row.marchAmount) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n            <vxe-column title=\"吨位\" align=\"center\" field=\"marchPerTon\" width=\"7%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatTon(row.marchPerTon) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n          </vxe-colgroup>\r\n\r\n          <!-- 四月分组 -->\r\n          <vxe-colgroup title=\"四月\" align=\"center\">\r\n            <vxe-column title=\"金额（元）\" align=\"center\" field=\"aprilAmount\" width=\"7%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatCurrency(row.aprilAmount) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n            <vxe-column title=\"吨位\" align=\"center\" field=\"aprilPerTon\" width=\"7%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatTon(row.aprilPerTon) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n          </vxe-colgroup>\r\n\r\n          <!-- 五月分组 -->\r\n          <vxe-colgroup title=\"五月\" align=\"center\">\r\n            <vxe-column title=\"金额（元）\" align=\"center\" field=\"mayAmount\" width=\"7%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatCurrency(row.mayAmount) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n            <vxe-column title=\"吨位\" align=\"center\" field=\"mayPerTon\" width=\"7%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatTon(row.mayPerTon) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n          </vxe-colgroup>\r\n\r\n          <!-- 六月分组 -->\r\n          <vxe-colgroup title=\"六月\" align=\"center\">\r\n            <vxe-column title=\"金额（元）\" align=\"center\" field=\"juneAmount\" width=\"7%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatCurrency(row.juneAmount) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n            <vxe-column title=\"吨位\" align=\"center\" field=\"junePerTon\" width=\"7%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatTon(row.junePerTon) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n          </vxe-colgroup>\r\n\r\n          <!-- 七月分组 -->\r\n          <vxe-colgroup title=\"七月\" align=\"center\">\r\n            <vxe-column title=\"金额（元）\" align=\"center\" field=\"julyAmount\" width=\"7%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatCurrency(row.julyAmount) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n            <vxe-column title=\"吨位\" align=\"center\" field=\"julyPerTon\" width=\"7%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatTon(row.julyPerTon) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n          </vxe-colgroup>\r\n\r\n          <!-- 八月分组 -->\r\n          <vxe-colgroup title=\"八月\" align=\"center\">\r\n            <vxe-column title=\"金额（元）\" align=\"center\" field=\"augustAmount\" width=\"7%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatCurrency(row.augustAmount) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n            <vxe-column title=\"吨位\" align=\"center\" field=\"augustPerTon\" width=\"7%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatTon(row.augustPerTon) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n          </vxe-colgroup>\r\n\r\n          <!-- 九月分组 -->\r\n          <vxe-colgroup title=\"九月\" align=\"center\">\r\n            <vxe-column title=\"金额（元）\" align=\"center\" field=\"septemberAmount\" width=\"7%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatCurrency(row.septemberAmount) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n            <vxe-column title=\"吨位\" align=\"center\" field=\"septemberPerTon\" width=\"7%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatTon(row.septemberPerTon) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n          </vxe-colgroup>\r\n\r\n          <!-- 十月分组 -->\r\n          <vxe-colgroup title=\"十月\" align=\"center\">\r\n            <vxe-column title=\"金额（元）\" align=\"center\" field=\"octoberAmount\" width=\"7%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatCurrency(row.octoberAmount) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n            <vxe-column title=\"吨位\" align=\"center\" field=\"octoberPerTon\" width=\"7%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatTon(row.octoberPerTon) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n          </vxe-colgroup>\r\n\r\n          <!-- 十一月分组 -->\r\n          <vxe-colgroup title=\"十一月\" align=\"center\">\r\n            <vxe-column title=\"金额（元）\" align=\"center\" field=\"novemberAmount\" width=\"7%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatCurrency(row.novemberAmount) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n            <vxe-column title=\"吨位\" align=\"center\" field=\"novemberPerTon\" width=\"7%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatTon(row.novemberPerTon) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n          </vxe-colgroup>\r\n\r\n          <!-- 十二月分组 -->\r\n          <vxe-colgroup title=\"十二月\" align=\"center\">\r\n            <vxe-column title=\"金额（元）\" align=\"center\" field=\"decemberAmount\" width=\"7%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatCurrency(row.decemberAmount) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n            <vxe-column title=\"吨位\" align=\"center\" field=\"decemberPerTon\" width=\"7%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatTon(row.decemberPerTon) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n          </vxe-colgroup>\r\n\r\n          <!-- 年度累计分组 -->\r\n          <vxe-colgroup title=\"年度累计\" align=\"center\">\r\n            <vxe-column title=\"金额（元）\" align=\"center\" field=\"yearlyTotalAmount\" width=\"8%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'bold' }\">\r\n                  {{ formatCurrency(row.yearlyTotalAmount) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n            <vxe-column title=\"吨位\" align=\"center\" field=\"yearlyTotalPerTon\" width=\"8%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'bold' }\">\r\n                  {{ formatTon(row.yearlyTotalPerTon) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n          </vxe-colgroup>\r\n        </vxe-table>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listCompanySummaryData } from \"@/api/qualityCost/companySummary1\";\r\nimport { testFactoryData, testFactoryDataWithRealValues, testDataMapping } from \"./testData\";\r\n\r\n\r\nexport default {\r\n  name: \"CompanySummary1\",\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: false,\r\n      // 质量成本数据列表\r\n      qualityCostList: [],\r\n      // 合并单元格配置\r\n      mergeCells: [],\r\n      // 表格高度\r\n      tableHeight: null,\r\n      // 是否需要滚动条\r\n      needScrollbar: true,\r\n      // 年份选择\r\n      selectedYear: new Date().getFullYear(),\r\n      // 年份选项\r\n      yearOptions: [],\r\n      // 类型选择\r\n      selectedType: '报废',\r\n      // 类型选项\r\n      typeOptions: ['改判', '报废', '脱合同', '退货']\r\n    };\r\n  },\r\n  computed: {\r\n    /** 容器样式 */\r\n    containerStyle() {\r\n      return {\r\n        height: this.tableHeight ? `${this.tableHeight}px` : 'auto'\r\n      };\r\n    },\r\n    /** 表格容器样式 */\r\n    tableContainerStyle() {\r\n      return {\r\n        height: this.tableHeight ? `${this.tableHeight - 80}px` : 'auto'\r\n      };\r\n    }\r\n  },\r\n  mounted() {\r\n    this.calculateTableHeight();\r\n    window.addEventListener('resize', this.calculateTableHeight);\r\n    this.generateYearOptions();\r\n  },\r\n  beforeDestroy() {\r\n    window.removeEventListener('resize', this.calculateTableHeight);\r\n  },\r\n  methods: {\r\n    getCompanySummaryData() {\r\n      this.loading = true;\r\n      \r\n      const params = {\r\n        yearMonth: this.selectedYear,\r\n        companySummaryType: this.selectedType\r\n      };\r\n\r\n      listCompanySummaryData(params).then(response => {\r\n        console.log('listCompanySummaryData:', response);\r\n        if (response.data) {\r\n          // 处理分厂数据格式\r\n          this.processFactoryData(response.data);\r\n        } else {\r\n          this.qualityCostList = [];\r\n        }\r\n        this.loading = false;\r\n      }).catch(error => {\r\n        console.error('获取数据失败:', error);\r\n        this.$message.error('获取质量成本数据失败');\r\n        this.loading = false;\r\n      });\r\n    },\r\n\r\n    /** 处理分厂数据格式 */\r\n    processFactoryData(rawData) {\r\n      console.log('开始处理分厂数据:', rawData);\r\n      \r\n      // 检查是否为分厂数据格式\r\n      if (this.isFactoryDataFormat(rawData)) {\r\n        console.log('检测到分厂数据格式，开始处理...');\r\n        \r\n      // 转换为表格格式\r\n        const tableData = this.convertFactoryDataToTable(rawData);\r\n        \r\n        // 更新表格数据\r\n        this.updateTableWithFactoryData(tableData);\r\n      } else {\r\n        console.log('不是分厂数据格式，使用默认处理');\r\n        this.qualityCostList = [];\r\n      }\r\n    },\r\n\r\n    /** 检查是否为分厂数据格式 */\r\n    isFactoryDataFormat(data) {\r\n      if (!data || typeof data !== 'object') return false;\r\n      \r\n      const factoryKeys = Object.keys(data);\r\n      if (factoryKeys.length === 0) return false;\r\n      \r\n      const firstFactory = data[factoryKeys[0]];\r\n      if (!firstFactory || typeof firstFactory !== 'object') return false;\r\n      \r\n      const monthKeys = Object.keys(firstFactory);\r\n      if (monthKeys.length === 0) return false;\r\n      \r\n      const firstMonth = firstFactory[monthKeys[0]];\r\n      return firstMonth && \r\n             typeof firstMonth === 'object' && \r\n             'costEx' in firstMonth && \r\n             'costTon' in firstMonth;\r\n    },\r\n\r\n    /** 清理模拟数据 */\r\n    cleanMockData(data) {\r\n      const cleanedData = {};\r\n      \r\n      Object.keys(data).forEach(factoryName => {\r\n        const factoryData = data[factoryName];\r\n        const validMonths = {};\r\n        \r\n        Object.keys(factoryData).forEach(monthKey => {\r\n          const monthData = factoryData[monthKey];\r\n          \r\n          // 检查是否为模拟数据\r\n          if (!this.isMockData(monthData)) {\r\n            validMonths[monthKey] = monthData;\r\n          }\r\n        });\r\n        \r\n        // 只有当分厂有有效数据时才保留\r\n        if (Object.keys(validMonths).length > 0) {\r\n          cleanedData[factoryName] = validMonths;\r\n        }\r\n      });\r\n      \r\n      console.log('清理后的数据:', cleanedData);\r\n      return cleanedData;\r\n    },\r\n\r\n    /** 判断是否为模拟数据 */\r\n    isMockData(monthData) {\r\n      return (\r\n        monthData.costCenterName === null &&\r\n        monthData.costEx === 0 &&\r\n        monthData.costTon === 0 &&\r\n        monthData.yearMonth === null\r\n      );\r\n    },\r\n\r\n    /** 将分厂数据转换为表格格式 */\r\n    convertFactoryDataToTable(factoryData) {\r\n      const tableData = [];\r\n      \r\n      Object.keys(factoryData).forEach(factoryName => {\r\n        const factoryMonths = factoryData[factoryName];\r\n        \r\n        Object.keys(factoryMonths).forEach(monthKey => {\r\n          const monthData = factoryMonths[monthKey];\r\n          \r\n          // 创建表格行数据\r\n          const rowData = {\r\n            factoryName: factoryName,\r\n            month: monthKey,\r\n            costEx: monthData.costEx || 0,\r\n            costTon: monthData.costTon || 0,\r\n            yearMonth: monthData.yearMonth,\r\n            costCenterName: monthData.costCenterName\r\n          };\r\n          \r\n          tableData.push(rowData);\r\n        });\r\n      });\r\n      \r\n      console.log('转换后的表格数据:', tableData);\r\n      return tableData;\r\n    },\r\n\r\n    /** 更新表格显示分厂数据 */\r\n    updateTableWithFactoryData(tableData) {\r\n      if (tableData.length === 0) {\r\n        this.qualityCostList = [];\r\n        return;\r\n      }\r\n      \r\n      // 获取所有分厂名称\r\n      const factoryNames = [...new Set(tableData.map(row => row.factoryName))];\r\n      \r\n      // 创建表格行数据\r\n      this.qualityCostList = factoryNames.map(factoryName => {\r\n        const rowData = {\r\n          company: factoryName\r\n        };\r\n        \r\n        // 初始化所有月份的数据为0\r\n        const months = ['january', 'february', 'march', 'april', 'may', 'june',\r\n                       'july', 'august', 'september', 'october', 'november', 'december'];\r\n        \r\n        months.forEach((month, index) => {\r\n          const monthNumber = index + 1;\r\n          const monthData = tableData.find(row => \r\n            row.factoryName === factoryName && row.month === monthNumber.toString()\r\n          );\r\n          \r\n          if (monthData) {\r\n            rowData[`${month}Amount`] = monthData.costEx;\r\n            rowData[`${month}PerTon`] = monthData.costTon;\r\n          } else {\r\n            rowData[`${month}Amount`] = 0;\r\n            rowData[`${month}PerTon`] = 0;\r\n          }\r\n        });\r\n        \r\n        // 计算年度累计\r\n        rowData.yearlyTotalAmount = months.reduce((sum, month) => sum + (rowData[`${month}Amount`] || 0), 0);\r\n        rowData.yearlyTotalPerTon = months.reduce((sum, month) => sum + (rowData[`${month}PerTon`] || 0), 0);\r\n        \r\n        return rowData;\r\n      });\r\n      \r\n      console.log('更新后的表格数据:', this.qualityCostList);\r\n    },\r\n\r\n\r\n    /** 格式化货币 */\r\n    formatCurrency(value) {\r\n      if (value === null || value === undefined || value === '' || Number(value) === 0) {\r\n        return '-';\r\n      }\r\n      const num = Number(value);\r\n      // 金额显示为元，不转换为万\r\n      return num.toLocaleString('zh-CN', {\r\n        minimumFractionDigits: 2,\r\n        maximumFractionDigits: 2\r\n      });\r\n    },\r\n\r\n    /** 格式化吨位 */\r\n    formatTon(value) {\r\n      if (value === null || value === undefined || value === '' || Number(value) === 0) {\r\n        return '-';\r\n      }\r\n      const num = Number(value);\r\n      // 吨位显示为吨，保留2位小数\r\n      return num.toLocaleString('zh-CN', {\r\n        minimumFractionDigits: 2,\r\n        maximumFractionDigits: 2\r\n      });\r\n    },\r\n\r\n    /** 计算表格高度 */\r\n    calculateTableHeight() {\r\n      this.$nextTick(() => {\r\n        const windowHeight = window.innerHeight;\r\n        // 减去页面头部、标题等高度，大约100px\r\n        const availableHeight = windowHeight - 100;\r\n        // 设置表格最大高度，最小400px，最大不超过可用高度的90%\r\n        this.tableHeight = Math.max(400, Math.min(800, availableHeight * 0.9));\r\n        this.needScrollbar = this.qualityCostList.length > 8;\r\n      });\r\n    },\r\n\r\n    /** 生成年份选项 */\r\n    generateYearOptions() {\r\n      const currentYear = new Date().getFullYear();\r\n      for (let i = 2000; i <= currentYear; i++) {\r\n        this.yearOptions.push(i);\r\n      }\r\n      this.getCompanySummaryData()\r\n    },\r\n\r\n         /** 年份选择变化 */\r\n     handleYearChange(year) {\r\n       this.selectedYear = year;\r\n       // 重新获取数据\r\n       this.getCompanySummaryData();\r\n     },\r\n\r\n     /** 类型选择变化 */\r\n     handleTypeChange(type) {\r\n       this.selectedType = type;\r\n       // 重新获取数据\r\n       this.getCompanySummaryData();\r\n     },\r\n\r\n     /** 测试分厂数据处理 */\r\n     testFactoryDataProcessing() {\r\n       console.log('开始测试分厂数据处理...');\r\n       \r\n       // 测试1: 纯模拟数据\r\n       console.log('=== 测试1: 纯模拟数据 ===');\r\n       this.processFactoryData(testFactoryData);\r\n       \r\n       // 等待2秒后测试真实数据\r\n       setTimeout(() => {\r\n         console.log('=== 测试2: 包含真实数据 ===');\r\n         this.processFactoryData(testFactoryDataWithRealValues);\r\n       }, 2000);\r\n       \r\n       // 等待4秒后测试数据映射\r\n       setTimeout(() => {\r\n         console.log('=== 测试3: 数据映射验证 ===');\r\n         this.processFactoryData(testDataMapping);\r\n         this.verifyDataMapping();\r\n       }, 4000);\r\n     },\r\n\r\n     /** 验证数据映射 */\r\n     verifyDataMapping() {\r\n       console.log('验证数据映射...');\r\n       \r\n       if (this.qualityCostList.length > 0) {\r\n         this.qualityCostList.forEach((row, index) => {\r\n           console.log(`第${index + 1}行 - 分厂: ${row.company}`);\r\n           console.log(`  一月金额: ${row.januaryAmount}, 一月吨位: ${row.januaryPerTon}`);\r\n           console.log(`  二月金额: ${row.februaryAmount}, 二月吨位: ${row.februaryPerTon}`);\r\n           console.log(`  三月金额: ${row.marchAmount}, 三月吨位: ${row.marchPerTon}`);\r\n         });\r\n       }\r\n     }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n/* 页面容器样式 */\r\n.app-container {\r\n  position: relative;\r\n  padding: 20px;\r\n}\r\n\r\n/* 表格容器样式 */\r\n.table-container {\r\n  margin-top: 20px;\r\n}\r\n\r\n/* 表格滚动容器 */\r\n.table-scroll-container {\r\n  width: 100%;\r\n  position: relative;\r\n  border: 1px solid #ebeef5;\r\n  border-radius: 4px;\r\n}\r\n\r\n/* 表格单元格内容 */\r\n.table-cell-content {\r\n  padding: 10px 0;\r\n}\r\n</style>\r\n\r\n<style>\r\n/* 非scoped样式，确保能够覆盖vxe-table的默认样式 */\r\n\r\n/* 强制覆盖所有滚动条样式 - 15px粗细，淡蓝色主题 */\r\n.vxe-table ::-webkit-scrollbar {\r\n  width: 15px !important;\r\n  height: 15px !important;\r\n}\r\n\r\n.vxe-table ::-webkit-scrollbar-track {\r\n  background: #e3f2fd !important;\r\n  border-radius: 7px !important;\r\n}\r\n\r\n.vxe-table ::-webkit-scrollbar-thumb {\r\n  background: #64b5f6 !important;\r\n  border-radius: 7px !important;\r\n  border: none !important;\r\n}\r\n\r\n.vxe-table ::-webkit-scrollbar-thumb:hover {\r\n  background: #42a5f5 !important;\r\n}\r\n\r\n.vxe-table ::-webkit-scrollbar-thumb:active {\r\n  background: #2196f3 !important;\r\n}\r\n\r\n.vxe-table ::-webkit-scrollbar-corner {\r\n  background: #e3f2fd !important;\r\n}\r\n\r\n/* vxe-table 表格边框和样式 */\r\n.vxe-table ::v-deep .vxe-table {\r\n  border: 1px solid #ebeef5;\r\n}\r\n\r\n.vxe-table ::v-deep .vxe-table--border-line {\r\n  border-color: #ebeef5;\r\n}\r\n\r\n/* 表头样式 */\r\n.vxe-table ::v-deep .vxe-table--header-wrapper .vxe-table--header {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n.vxe-table ::v-deep .vxe-table--header .vxe-header--column {\r\n  background-color: #f5f7fa;\r\n  color: #303133;\r\n  font-weight: 500;\r\n}\r\n\r\n/* 表头分组样式 */\r\n.vxe-table ::v-deep .vxe-table--header .vxe-header--group {\r\n  background-color: #e8f4fd !important;\r\n  color: #1890ff !important;\r\n  font-weight: 600 !important;\r\n  border: 1px solid #d1e7ff !important;\r\n}\r\n\r\n/* 确保分组表头显示 */\r\n.vxe-table ::v-deep .vxe-table--header .vxe-header--group .vxe-header--group-title {\r\n  background-color: #e8f4fd !important;\r\n  color: #1890ff !important;\r\n  font-weight: 600 !important;\r\n  padding: 8px 4px !important;\r\n  text-align: center !important;\r\n}\r\n\r\n/* 子列表头样式 */\r\n.vxe-table ::v-deep .vxe-table--header .vxe-header--column {\r\n  background-color: #f5f7fa !important;\r\n  color: #303133 !important;\r\n  font-weight: 500 !important;\r\n  border: 1px solid #ebeef5 !important;\r\n}\r\n\r\n/* 确保表头分组正确显示 */\r\n.vxe-table ::v-deep .vxe-table--header-wrapper {\r\n  border: 1px solid #ebeef5 !important;\r\n}\r\n\r\n.vxe-table ::v-deep .vxe-table--header {\r\n  border-bottom: 1px solid #ebeef5 !important;\r\n}\r\n\r\n/* 强制显示分组表头 */\r\n.vxe-table ::v-deep .vxe-table--header .vxe-header--group {\r\n  display: table-cell !important;\r\n  vertical-align: middle !important;\r\n}\r\n\r\n/* 分组表头文字样式 */\r\n.vxe-table ::v-deep .vxe-header--group-title {\r\n  display: block !important;\r\n  width: 100% !important;\r\n  text-align: center !important;\r\n  font-size: 14px !important;\r\n  line-height: 1.5 !important;\r\n}\r\n\r\n/* 确保表头分组边框显示 */\r\n.vxe-table ::v-deep .vxe-table--header .vxe-header--group {\r\n  border-right: 1px solid #d1e7ff !important;\r\n  border-bottom: 1px solid #d1e7ff !important;\r\n}\r\n\r\n/* 最后一个分组表头右边框 */\r\n.vxe-table ::v-deep .vxe-table--header .vxe-header--group:last-child {\r\n  border-right: 1px solid #ebeef5 !important;\r\n}\r\n\r\n/* 确保表头文字居中 */\r\n.vxe-table ::v-deep .vxe-table--header .vxe-header--column .vxe-cell--title {\r\n  text-align: center !important;\r\n  display: flex !important;\r\n  align-items: center !important;\r\n  justify-content: center !important;\r\n  height: 100% !important;\r\n}\r\n</style> "]}]}