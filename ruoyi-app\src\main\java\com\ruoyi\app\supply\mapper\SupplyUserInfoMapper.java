package com.ruoyi.app.supply.mapper;

import com.ruoyi.app.supply.domain.SupplyUserInfo;

import java.util.List;

/**
 * 相关方人员Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-3-22
 */
public interface SupplyUserInfoMapper
{
    /**
     * 查询相关方人员信息
     * 
     * @param id 人员ID
     * @return 人员信息
     */
    public SupplyUserInfo selectSupplyUserInfoById(Integer id);

    /**
     * 查询人员信息列表
     * 
     * @param supplyUserInfo 人员信息
     * @return 人员信息集合
     */
    public List<SupplyUserInfo> selectSupplyUserInfoList(SupplyUserInfo supplyUserInfo);

    /**
     * 新增人员信息
     * 
     * @param supplyUserInfo 人员信息
     * @return 结果
     */
    public int insertSupplyUserInfo(SupplyUserInfo supplyUserInfo);

    /**
     * 修改人员信息
     * 
     * @param supplyUserInfo 人员信息
     * @return 结果
     */
    public int updateSupplyUserInfo(SupplyUserInfo supplyUserInfo);

    /**
     * 删除人员信息
     * 
     * @param id 人员信息主键
     * @return 结果
     */
    public int deleteSupplyUserInfoById(Integer id);

    /**
     * 批量删除人员信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSupplyUserInfoByIds(Integer[] ids);

    /**
     * 根据身份证号和状态查询用户信息
     *
     * @param idcard 身份证号
     * @param state 状态
     * @return 用户信息
     */
    public SupplyUserInfo selectSupplyUserInfoByIdcardAndState(String idcard, Integer state);

}