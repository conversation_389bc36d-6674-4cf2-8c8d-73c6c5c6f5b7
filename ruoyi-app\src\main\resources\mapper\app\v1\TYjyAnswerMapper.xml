<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.app.v1.mapper.TYjyAnswerMapper">
    
    <resultMap type="TYjyAnswer" id="TYjyAnswerResult">
        <result property="id"    column="id"    />
        <result property="formId"    column="form_id"    />
        <result property="fcDate"    column="fc_date"    />
        <result property="version"    column="version"    />
        <result property="formValue"    column="form_value"    />
        <result property="formFile"    column="form_file"    />
        <result property="createTime"    column="create_time"    />
        <result property="creatorNo"    column="creator_no"    />
        <result property="creatorName"    column="creator_name"    />
        <result property="creatorDept"    column="creator_dept"    />
        <result property="updateTime"    column="update_time"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="deleteTime"    column="delete_time"    />
        <result property="checkerList"    column="checker_list"    />
        <result property="status"    column="status"    />
        <result property="checkWorkNo"    column="check_work_no"    />
        <result property="checkUserName"    column="check_user_name"    />
        <result property="checkHistory"    column="check_history"    />
        <result property="frequency"    column="frequency"    />
        <result property="dimensionalityPath"    column="dimensionality_path"    />
        <result property="formQuestion"    column="form_question"    />
        <result property="formType"    column="form_type"    />
        <result property="reason"    column="reason"    />
        <result property="measure"    column="measure"    />
        <result property="assessment"    column="assessment"    />
    </resultMap>

    <resultMap type="Integer" id="countResult">
        <result property="count"    column="count"    />
    </resultMap>

    <resultMap type="TYjyUpdateGas" id="TYjyUpdateGasResult">
        <result property="pointCode"    column="POINT_CODE"    />
        <result property="displayValue"    column="DISPLAY_VALUE"    />
        <result property="settlementTime"    column="SETTLEMENT_TIME"    />
    </resultMap>

<!--    <resultMap type="TYjyUpdateGas" id="TYjyUpdateGasResult">-->
<!--        <result property="pointCode"    column="POINT_CODE"    />-->
<!--        <result property="displayValue"    column="DISPLAY_VALUE"    />-->
<!--        <result property="settlementTime"    column="SETTLEMENT_TIME"    />-->
<!--    </resultMap>-->

    <sql id="selectTYjyAnswerVo">
        select distinct t.id, t.form_id, t.fc_date, t.version, t.form_value,t.form_file, t.create_time, t.creator_no, t.creator_name, t.creator_dept, t.update_time, t.del_flag, t.delete_time,t.checker_list,t.status,t.check_work_no,t.check_user_name,t.check_history,t.reason,t.measure,t.assessment
        from t_yjy_answer t left join t_yjy_permission p on t.form_id=p.form_id
    </sql>

    <select id="selectTYjyAnswerList" parameterType="TYjyAnswer" resultMap="TYjyAnswerResult">
        <include refid="selectTYjyAnswerVo"/>
        <where>
            del_flag='0'
            <if test="formId != null "> and t.form_id = #{formId}</if>
            <if test="fcDate != null  and fcDate != ''"> and t.fc_date = #{fcDate}</if>
            <if test="version != null "> and t.version = #{version}</if>
            <if test="formValue != null  and formValue != ''"> and t.form_value = #{formValue}</if>
            <if test="creatorDept != null  and creatorDept != ''"> and t.creator_dept = #{creatorDept}</if>
            <if test="workNo != null  and workNo != ''"> and p.work_no = #{workNo}</if>
        </where>
    </select>


    <select id="selectTYjyAnswerListCount" parameterType="TYjyAnswer" resultMap="TYjyAnswerResult">
        <include refid="selectTYjyAnswerVo"/>
        <where>
            del_flag='0'
            <if test="formId != null "> and t.form_id = #{formId}</if>
            <if test="fcDate != null  and fcDate != ''"> and t.fc_date = #{fcDate}</if>
            <if test="version != null "> and t.version = #{version}</if>
            <if test="formValue != null  and formValue != ''"> and t.form_value = #{formValue}</if>
            <if test="creatorDept != null  and creatorDept != ''"> and t.creator_dept = #{creatorDept}</if>
            <if test="workNo != null  and workNo != ''"> and p.work_no = #{workNo}</if>
        </where>
        order by t.fc_date desc
    </select>

    <select id="selectTYjyAnswerListSum" parameterType="TYjyAnswerSearch" resultMap="TYjyAnswerResult">
        select distinct t.id, t.form_id, t.fc_date, t.version, t.form_value,t.form_file, t.create_time, t.creator_no, t.creator_name, t.creator_dept, t.update_time, t.del_flag, t.delete_time,t.checker_list,t.status,t.check_work_no,t.check_user_name,t.check_history,t.reason,t.measure
        from t_yjy_answer t
        <where>
            del_flag='0'
            <if test="formId != null "> and t.form_id = #{formId}</if>
            <if test="beginTime != null  and beginTime != ''"> and t.fc_date >= #{beginTime}</if>
            <if test="finalTime != null  and finalTime != ''"> and #{finalTime} >= t.fc_date </if>
        </where>
        order by t.fc_date
    </select>



    <select id="selectCheckList" parameterType="TYjyAnswer" resultMap="TYjyAnswerResult">
        select t.id, t.form_id, t.fc_date, t.version, t.form_value,t.form_file, t.create_time, t.creator_no, t.creator_name, t.creator_dept, t.update_time, t.del_flag, t.delete_time,t.checker_list,t.status,t.check_work_no,t.check_user_name,t.check_history,t.reason,t.measure,t.assessment,u.form_question,u.frequency,u.form_type
        from t_yjy_answer t left join  t_yjy_form u on t.form_id=u.id
        <where>
            t.del_flag='0' and t.status != '2' and t.status != '3' and t.status != '4'
            <if test="formId != null "> and t.form_id = #{formId}</if>
            <if test="fcDate != null  and fcDate != ''"> and t.fc_date = #{fcDate}</if>
            <if test="version != null "> and t.version = #{version}</if>
            <if test="formValue != null  and formValue != ''"> and t.form_value = #{formValue}</if>
            <if test="creatorDept != null  and creatorDept != ''"> and t.creator_dept = #{creatorDept}</if>
            <if test="checkWorkNo != null  and checkWorkNo != ''"> and t.check_work_no = #{checkWorkNo}</if>
            <if test="status != null  and status != ''"> and t.status = #{status}</if>
            <if test="formQuestion != null  and formQuestion != ''"> and u.form_question = #{formQuestion}</if>
            <if test="frequency != null  and frequency != ''"> and u.frequency = #{frequency}</if>
        </where>
    </select>


    <select id="selectCheckListCount" parameterType="TYjyAnswer" resultMap="countResult">
        select count(1) count
        from t_yjy_answer t left join  t_yjy_form u on t.form_id=u.id
        <where>
            t.del_flag='0' and t.status != '2' and t.status != '3' and t.status != '4'
            <if test="formId != null "> and t.form_id = #{formId}</if>
            <if test="fcDate != null  and fcDate != ''"> and t.fc_date = #{fcDate}</if>
            <if test="version != null "> and t.version = #{version}</if>
            <if test="formValue != null  and formValue != ''"> and t.form_value = #{formValue}</if>
            <if test="creatorDept != null  and creatorDept != ''"> and t.creator_dept = #{creatorDept}</if>
            <if test="checkWorkNo != null  and checkWorkNo != ''"> and t.check_work_no = #{checkWorkNo}</if>
            <if test="status != null  and status != ''"> and t.status = #{status}</if>
            <if test="formQuestion != null  and formQuestion != ''"> and u.form_question = #{formQuestion}</if>
            <if test="frequency != null  and frequency != ''"> and u.frequency = #{frequency}</if>
        </where>
    </select>

    <sql id="selectTYjyAnswerWithFormVo">
        select t.id, t.form_id, t.fc_date, t.version, t.form_value,t.form_file, t.create_time, t.creator_no, t.creator_name, t.creator_dept, t.update_time, t.del_flag, t.delete_time,u.dimensionality_id,u.form_question,u.frequency,u.form_type,t.checker_list,t.status,t.check_work_no,t.check_user_name,t.check_history,t.reason,t.measure,t.assessment
        from t_yjy_answer t left join  t_yjy_form u on t.form_id=u.id
    </sql>

    <select id="selectWithfrequency" parameterType="TYjyAnswer" resultMap="TYjyAnswerResult">
        <where>
            t.del_flag='0'
            <if test="formId != null "> and t.form_id = #{formId}</if>
            <if test="fcDate != null  and fcDate != ''"> and t.fc_date = #{fcDate}</if>
            <if test="version != null "> and t.version = #{version}</if>
            <if test="formValue != null  and formValue != ''"> and t.form_value = #{formValue}</if>
            <if test="creatorDept != null  and creatorDept != ''"> and t.creator_dept = #{creatorDept}</if>
        </where>
    </select>


    <select id="selectAnswerWithPermission" parameterType="TYjyAnswer" resultMap="TYjyAnswerResult">
        select t.id, t.form_id, t.fc_date, t.version, t.form_value,t.form_file, t.create_time, t.creator_no, t.creator_name, t.creator_dept, t.update_time, t.del_flag, t.delete_time,t.checker_list,t.status,t.check_work_no,t.check_user_name,t.check_history,t.reason,t.measure,,t.assessment
        from t_yjy_answer t left join t_yjy_permission j on t.form_id=j.form_id
        <where>
            del_flag='0'
            <if test="formId != null "> and t.form_id = #{formId}</if>
            <if test="fcDate != null  and fcDate != ''"> and t.fc_date >= #{fcDate}</if>
            <if test="version != null "> and t.version = #{version}</if>
            <if test="formValue != null  and formValue != ''"> and t.form_value = #{formValue}</if>
            <if test="creatorDept != null  and creatorDept != ''"> and t.creator_dept = #{creatorDept}</if>
        </where>
    </select>

    <select id="selectAnswerHisByFormId" parameterType="TYjyAnswer" resultMap="TYjyAnswerResult">
        select t.id, t.form_id, t.fc_date, t.version, t.form_value,t.form_file, t.create_time, t.creator_no, t.creator_name, t.creator_dept, t.update_time, t.del_flag, t.delete_time,t.checker_list,t.status,t.check_work_no,t.check_user_name,t.check_history,t.reason,t.measure,t.assessment
        from t_yjy_answer t
        <where>
            del_flag='0'
            <if test="formId != null "> and t.form_id = #{formId}</if>
            <if test="creatorDept != null  and creatorDept != ''"> and t.creator_dept = #{creatorDept}</if>
            <if test="fcDate != null  and fcDate != ''"> and t.fc_date >= #{fcDate}</if>
        </where>
        order by  t.fc_date desc
        limit 30
    </select>


    <select id="selectAnswerTop3" parameterType="TYjyAnswer" resultMap="TYjyAnswerResult">
        SET @row_number = 0;
        SET @group_type = NULL;
        SELECT distinct t.id, t.form_id, t.fc_date, t.version, t.form_value,t.form_file, t.create_time, t.creator_no, t.creator_name, t.creator_dept, t.update_time, t.del_flag, t.delete_time,t.checker_list,t.status,t.check_work_no,t.check_user_name,t.check_history,t.reason,t.measure,t.assessment
        FROM (
        SELECT
        (CASE when @group_type = r.group_type then @row_number:=@row_number + 1 else @row_number:= 0 end) as rownum,
        (CASE when @group_type = r.group_type then @group_type:=r.group_type else @group_type:=r.group_type end) issame,
        r.*
        FROM
        (
        SELECT i.*,concat(i.form_id,i.creator_dept) group_type
        FROM t_yjy_answer i left join t_yjy_permission j on i.form_id=j.form_id
        <where>
            i.del_flag='0' and i.creator_dept=j.dept_code and i.status='2'
<!--            <if test="formId != null "> and i.form_id = #{formId}</if>-->
<!--            <if test="creatorDept != null  and creatorDept != ''"> and creator_dept = #{creatorDept}</if>-->
            <if test="workNo != null  and workNo != ''"> and j.work_no = #{workNo}</if>
            <if test="fcDate != null  and fcDate != ''"> and #{fcDate} >= i.fc_date</if>
        </where>
        ORDER BY i.form_id,i.creator_dept,i.fc_date desc) AS r
        ) AS t
        WHERE  3 >= t.rownum;
    </select>


    <select id="selectAnswerTop4" parameterType="TYjyAnswer" resultMap="TYjyAnswerResult">
        SELECT distinct i.id, i.form_id, i.fc_date, i.version, i.form_value,i.form_file, i.create_time, i.creator_no, i.creator_name, i.creator_dept, i.update_time, i.del_flag, i.delete_time,i.checker_list,i.status,i.check_work_no,i.check_user_name,i.check_history,i.reason,i.measure,i.assessment
        FROM t_yjy_answer i
        <where>
            i.del_flag='0' and  ((#{fcDate} > i.fc_date and i.status='2') or #{fcDate} = i.fc_date)
            <if test="formId != null "> and i.form_id = #{formId}</if>
            <if test="creatorDept != null  and creatorDept != ''"> and #{creatorDept} = i.creator_dept</if>
        </where>
        ORDER BY i.fc_date desc limit 4
    </select>


    <select id="selectAnswerLast3" parameterType="TYjyAnswer" resultMap="TYjyAnswerResult">
        SELECT distinct i.id, i.form_id, i.fc_date, i.version, i.form_value,i.form_file, i.create_time, i.creator_no, i.creator_name, i.creator_dept, i.update_time, i.del_flag, i.delete_time,i.checker_list,i.status,i.check_work_no,i.check_user_name,i.check_history,i.reason,i.measure,i.assessment
        FROM t_yjy_answer i left join t_yjy_permission j on i.form_id=j.form_id
        <where>
            del_flag='0' and i.status='2'
            <if test="formId != null "> and i.form_id = #{formId}</if>
            <if test="workNo != null  and workNo != ''"> and j.work_no = #{workNo}</if>
            <if test="fcDate != null  and fcDate != ''"> and #{fcDate} > i.fc_date</if>
        </where>
        ORDER BY i.fc_date desc limit 3
    </select>

    <select id="selectAnswerLast3forcheck" parameterType="TYjyAnswer" resultMap="TYjyAnswerResult">
        SELECT distinct i.id, i.form_id, i.fc_date, i.version, i.form_value,i.form_file, i.create_time, i.creator_no, i.creator_name, i.creator_dept, i.update_time, i.del_flag, i.delete_time,i.checker_list,i.status,i.check_work_no,i.check_user_name,i.check_history,i.reason,i.measure,i.assessment,j.form_question,j.frequency,j.form_type
        FROM t_yjy_answer i  left join t_yjy_form j on i.form_id=j.id
        <where>
            i.del_flag='0' and i.status='2'
            <if test="formId != null "> and i.form_id = #{formId}</if>
<!--            <if test="workNo != null  and workNo != ''"> and j.check_work_no = #{checkWorkNo}</if>-->
            <if test="fcDate != null  and fcDate != ''"> and #{fcDate} > i.fc_date</if>
            <if test="creatorDept != null  and creatorDept != ''"> and #{creatorDept} = i.creator_dept</if>
        </where>
        ORDER BY i.fc_date desc limit 3
    </select>

    <select id="selectTYjyAnswerById" parameterType="Long" resultMap="TYjyAnswerResult">
        select t.id, t.form_id, t.fc_date, t.version, t.form_value,t.form_file, t.create_time, t.creator_no, t.creator_name, t.creator_dept, t.update_time, t.del_flag, t.delete_time,t.checker_list,t.status,t.check_work_no,t.check_user_name,t.check_history,t.reason,t.measure,t.assessment
        from t_yjy_answer t
        where t.id = #{id}
    </select>
        
    <insert id="insertTYjyAnswer" parameterType="TYjyAnswer" useGeneratedKeys="true" keyProperty="id">
        insert into t_yjy_answer
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="formId != null">form_id,</if>
            <if test="fcDate != null">fc_date,</if>
            <if test="version != null">version,</if>
            <if test="formValue != null">form_value,</if>
            <if test="formFile != null">form_file,</if>
            <if test="createTime != null">create_time,</if>
            <if test="creatorNo != null">creator_no,</if>
            <if test="creatorName != null">creator_name,</if>
            <if test="creatorDept != null">creator_dept,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="deleteTime != null">delete_time,</if>
            <if test="checkerList != null">checker_list,</if>
            <if test="status != null">status,</if>
            <if test="checkWorkNo != null">check_work_no,</if>
            <if test="checkUserName != null">check_user_name,</if>
            <if test="checkHistory != null">check_history,</if>
            <if test="reason != null">reason,</if>
            <if test="measure != null">measure,</if>
            <if test="frequency != null">frequency,</if>
            <if test="dimensionalityPath != null">dimensionality_path,</if>
            <if test="assessment != null">assessment,</if>
            <if test="deptShow != null">dept_show,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="formId != null">#{formId},</if>
            <if test="fcDate != null">#{fcDate},</if>
            <if test="version != null">#{version},</if>
            <if test="formValue != null">#{formValue},</if>
            <if test="formFile != null">#{formFile},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="creatorNo != null">#{creatorNo},</if>
            <if test="creatorName != null">#{creatorName},</if>
            <if test="creatorDept != null">#{creatorDept},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="deleteTime != null">#{deleteTime},</if>
            <if test="checkerList != null">#{checkerList},</if>
            <if test="status != null">#{status},</if>
            <if test="checkWorkNo != null">#{checkWorkNo},</if>
            <if test="checkUserName != null">#{checkUserName},</if>
            <if test="checkHistory != null">#{checkHistory},</if>
            <if test="reason != null">#{reason},</if>
            <if test="measure != null">#{measure},</if>
            <if test="frequency != null">#{frequency},</if>
            <if test="dimensionalityPath != null">#{dimensionalityPath},</if>
            <if test="assessment != null">#{assessment},</if>
            <if test="deptShow != null">#{deptShow},</if>
         </trim>
    </insert>

    <update id="updateTYjyAnswer" parameterType="TYjyAnswer">
        update t_yjy_answer
        <trim prefix="SET" suffixOverrides=",">
            <if test="formId != null">form_id = #{formId},</if>
            <if test="fcDate != null">fc_date = #{fcDate},</if>
            <if test="version != null">version = #{version},</if>
            <if test="formValue != null">form_value = #{formValue},</if>
            <if test="formFile != null">form_file = #{formFile},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="creatorNo != null">creator_no = #{creatorNo},</if>
            <if test="creatorName != null">creator_name = #{creatorName},</if>
            <if test="creatorDept != null">creator_dept = #{creatorDept},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="deleteTime != null">delete_time = #{deleteTime},</if>
            <if test="checkerList != null">checker_list = #{checkerList},</if>
            <if test="status != null">status = #{status},</if>
            <if test="checkWorkNo != null">check_work_no = #{checkWorkNo},</if>
            <if test="checkUserName != null">check_user_name = #{checkUserName},</if>
            <if test="checkHistory != null">check_history = #{checkHistory},</if>
            <if test="reason != null">reason = #{reason},</if>
            <if test="measure != null">measure = #{measure},</if>
            <if test="assessment != null">assessment = #{assessment},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteTYjyAnswerById" parameterType="Long">
        update t_yjy_answer set del_flag='1'  where id = #{id}
    </update>

    <update id="deleteTYjyAnswerByIds" parameterType="String">
        update t_yjy_answer set del_flag='1' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>


    <select id="selectExportFormList"  resultType="TYjyExport">
        select f.id as formId,f.dimensionality_id as dimensionalityId,
               d.path,f.form_question as question,f.frequency,
               f.form_note as note,f.unit as unit,f.note_list as noteList
        from  t_yjy_form f
        LEFT JOIN t_yjy_dimensionality d on f.dimensionality_id = d.id
        where 1=1
        and f.del_flag = '0'
        and f.dimensionality_id in
        <foreach  collection="list"  separator="," open="(" close=")" index="" item="item">
            #{item,jdbcType=INTEGER}
        </foreach>
        order by f.frequency,d.path,f.sort_num,f.id
    </select>

    <select id="selectExportFormListByFormId"  resultType="TYjyExport">
        select f.id as formId,f.dimensionality_id as dimensionalityId,
        d.path,f.form_question as question,f.frequency,
        f.form_note as note,f.unit as unit,f.maximum,f.minimum
        from  t_yjy_form f
        LEFT JOIN t_yjy_dimensionality d on f.dimensionality_id = d.id
        where 1=1
        and f.del_flag = '0'
        and f.id in
        <foreach  collection="list"  separator="," open="(" close=")" index="" item="item">
            #{item,jdbcType=INTEGER}
        </foreach>
        order by f.frequency,d.path,f.sort_num,f.id
    </select>

    <select id="selectExportList"  parameterType="Map" resultType="TYjyExport">
        select f.id as formId,f.dimensionality_id as dimensionalityId,
               d.path,f.form_question as question,f.frequency ,f.maximum,f.minimum, f.form_note as note,f.unit as unit,
               a.form_value as formValue,
               a.form_file as formFile,a.fc_date as fcDate,a.reason,a.measure
        from t_yjy_answer a LEFT JOIN t_yjy_form f on a.form_id = f.id
        LEFT JOIN t_yjy_dimensionality d on f.dimensionality_id = d.id
        where 1=1
          and a.del_flag = '0'
          and f.del_flag = '0'
          and a.`status` = '2'
         <if test="creatorDept != null">and creator_dept = #{creatorDept},</if>
          and a.fc_date>=#{startDate}
          and #{endDate}>=a.fc_date
          and f.dimensionality_id in
        <foreach  collection="list"  separator="," open="(" close=")" index="" item="item">
            #{item,jdbcType=INTEGER}
        </foreach>
        order by f.frequency,d.path,f.sort_num,f.id,a.fc_date
    </select>

    <select id="selectExportListByFormId"  parameterType="Map" resultType="TYjyExport">
        select f.id as formId,f.dimensionality_id as dimensionalityId,
        d.path,f.form_question as question,f.frequency ,f.maximum,f.minimum, f.form_note as note,f.unit as unit,
        a.form_value as formValue,
        a.form_file as formFile,a.fc_date as fcDate,a.reason,a.measure
        from t_yjy_answer a LEFT JOIN t_yjy_form f on a.form_id = f.id
        LEFT JOIN t_yjy_dimensionality d on f.dimensionality_id = d.id
        where 1=1
        and a.del_flag = '0'
        and f.del_flag = '0'
        and a.`status` = '2'
        <if test="creatorDept != null">and creator_dept = #{creatorDept},</if>
        and a.fc_date>=#{startDate}
        and #{endDate}>=a.fc_date
        and f.id in
        <foreach  collection="list"  separator="," open="(" close=")" index="" item="item">
            #{item,jdbcType=INTEGER}
        </foreach>
        order by f.frequency,d.path,f.sort_num,f.id,a.fc_date
    </select>



<!--    <select id="selectUpdateGas"  parameterType="TYjyUpdateGas" resultType="TYjyAnswerResult">-->
<!--        select POINT_CODE,DISPLAY_VALUE,SETTLEMENT_TIME-->
<!--        from dt_nyxt where WHOLE_POINT like #{concet} and SETTLEMENT_TIME=to_date(#{time},'yyyy-mm-dd')-->
<!--    </select>-->

    <!--  此处仍然存在疑问，开会之后再做确认 -->
    <select id="selectUpdateGas" parameterType="TYjyUpdateGas" resultMap="TYjyUpdateGasResult">
        <!--        select POINT_CODE,DISPLAY_VALUE,SETTLEMENT_TIME
        SELECT
            POINT_CODE,
            CASE
                WHEN POINT_CODE = 'V32016000307800663_SVt' THEN
                    TO_CHAR(TO_NUMBER(
                        CASE WHEN INSTR(DISPLAY_VALUE, '.') > 0 THEN SUBSTR(DISPLAY_VALUE, 1, INSTR(DISPLAY_VALUE, '.') - 1)
                        ELSE DISPLAY_VALUE END) - 300000)
                WHEN POINT_CODE = 'K0QK11001_SVt' THEN
                    TO_CHAR(TO_NUMBER(
                        CASE WHEN INSTR(DISPLAY_VALUE, '.') > 0 THEN SUBSTR(DISPLAY_VALUE, 1, INSTR(DISPLAY_VALUE, '.') - 1)
                        ELSE DISPLAY_VALUE END) + 300000)
                ELSE
                    CASE WHEN INSTR(DISPLAY_VALUE, '.') > 0 THEN SUBSTR(DISPLAY_VALUE, 1, INSTR(DISPLAY_VALUE, '.') - 1)
                    ELSE DISPLAY_VALUE END
            END AS DISPLAY_VALUE,
            SETTLEMENT_TIME
        from dt_nyxt where WHOLE_POINT like #{concet} and SETTLEMENT_TIME=to_date(#{time},'yyyy-mm-dd')-->
        SELECT
        POINT_CODE,
        CASE
        WHEN POINT_CODE = 'V32016000307800663_SVt' THEN
        TO_CHAR(TO_NUMBER(DISPLAY_VALUE) - 300000)
        WHEN POINT_CODE = 'K0QK11001_SVt' THEN
        TO_CHAR(TO_NUMBER(DISPLAY_VALUE) + 300000)
        ELSE DISPLAY_VALUE
        END AS DISPLAY_VALUE,
        SETTLEMENT_TIME
        from dt_nyxt where WHOLE_POINT like #{concet} and SETTLEMENT_TIME=to_date(#{time},'yyyy-mm-dd')
    </select>

    <select id="selectUpdateNaturalGas" parameterType="TYjyUpdateGas" resultMap="TYjyUpdateGasResult">
        SELECT POINT_CODE,sum(DISPLAY_VALUE) as DISPLAY_VALUE
        from dt_nyxt where WHOLE_POINT like #{concet} and SETTLEMENT_TIME>to_date(#{beginTime},'yyyy-mm-dd') and to_date(#{time},'yyyy-mm-dd')>=SETTLEMENT_TIME
        group by POINT_CODE
    </select>


    <select id="selectUpdateEveryDayGas" parameterType="TYjyUpdateGas" resultMap="TYjyUpdateGasResult">
        SELECT POINT_CODE,DISPLAY_VALUE
        from dt_nyxt
        where WHOLE_POINT like #{concet} and SETTLEMENT_TIME=to_date(#{time},'yyyy-mm-dd')
    </select>


   </mapper>


        <!--        select i.*,j.* from t_yjy_form i   -->
<!--        left join t_yjy_answer j on i.id=j.form_id   -->
<!--        where i.dimensionality_id=2 and i.del_flag='0' and j.del_flag='0' and i.frequency='1' and j.fc_date is not null and j.id is not null   -->