package com.ruoyi.questionnaire.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.utils.http.HttpUtils;
import com.ruoyi.questionnaire.domain.DictData;
import com.ruoyi.questionnaire.domain.Safe;
import com.ruoyi.questionnaire.domain.SafeChartData;

import com.ruoyi.questionnaire.mapper.SafeMapper;
import com.ruoyi.questionnaire.service.ISafeService;
import com.ruoyi.common.constant.HttpStatus;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 安全问卷Service业务层处理
 * 
 * <AUTHOR>
 * @date 2021-01-15
 */
@Service
public class SafeServiceImpl implements ISafeService 
{
    @Autowired
    private SafeMapper safeMapper;

    @Autowired
    private RedisCache redisCache;

    /**
     * 配置文件中appid
     */
    @Value("${wx.xctg.appid}")
    private String appid;

    /**
     * 配置文件中secret
     */
    @Value("${wx.xctg.secret}")
    private String secret;

    /**
     * 获取access_token url
     */
    @Value("${wx.credentialUrl}")
    private String credentialUrl;

    /**
     * 推送消息 url
     */
    @Value("${wx.sendUrl}")
    private String sendUrl;

    public static final String template_id = "T8CvPFI03jtNx7iVvT0w7bnr7vbCmZcOwHoPisHkuzw";

    /**
     * 查询安全问卷
     * 
     * @param id 安全问卷ID
     * @return 安全问卷
     */
    @Override
    public Safe selectSafeQuestionnaireById(Long id)
    {
        return safeMapper.selectSafeQuestionnaireById(id);
    }

    /**
     * 查询安全问卷详情
     *
     * @param id 安全问卷ID
     * @return 安全问卷
     */
    @Override
    public Map<String, Object> getDetail(Long id)
    {
        Map<String, Object> map = new HashMap<>();
        Safe safe = safeMapper.selectSafeQuestionnaireById(id);
        if (safe != null) {
            map.put("phone", safe.getPhone());
            map.put("type", safeMapper.selectSafeQuestionnaireName(safe.getType()));
            map.put("locationStation", safeMapper.selectBranchName(safe.getLocationStation()));
            map.put("locationName", safe.getLocationName());
            map.put("latitude", safe.getLatitude() == null ? "" : safe.getLatitude());
            map.put("longitude", safe.getLongitude() == null ? "" : safe.getLongitude());
            map.put("remark", safe.getRemark());
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            map.put("createTime", sdf.format(safe.getCreateTime()));
            map.put("handleTime", safe.getHandleTime() == null ? "" : sdf.format(safe.getHandleTime()));
            map.put("handleRemark", safe.getHandleRemark() == null ? "" : safe.getHandleRemark());
            map.put("handleName", safe.getHandleName() == null ? "" : safe.getHandleName());
            List<String> images = new ArrayList<>();
            if (safe.getFile1() != null)
                images.add(safe.getFile1());
            if (safe.getFile2() != null)
                images.add(safe.getFile1());
            if (safe.getFile3() != null)
                images.add(safe.getFile1());
            map.put("images", images);
        }
        return map;
    }

    /**
     * 查询安全问卷列表
     * 
     * @param safe 安全问卷
     * @return 安全问卷
     */
    @Override
    public List<Safe> selectSafeQuestionnaireList(Safe safe)
    {
        List<Safe> list = safeMapper.selectSafeQuestionnaireList(safe);
        for (Safe question: list) {
            List<String> images = new ArrayList<>();
            if (question.getFile1() != null)
                images.add(question.getFile1());
            if (question.getFile2() != null)
                images.add(question.getFile2());
            if (question.getFile3() != null)
                images.add(question.getFile3());
            question.setImages(images);
        }
        return list;
    }

    /**
     * 新增安全问卷
     * 
     * @param safe 安全问卷
     * @return 结果
     */
    @Override
    public int insertSafeQuestionnaire(Safe safe)
    {
        return safeMapper.insertSafeQuestionnaire(safe);
    }

    /**
     * 修改安全问卷
     * 
     * @param safe 安全问卷
     * @return 结果
     */
    @Override
    public int updateSafeQuestionnaire(Safe safe)
    {
        safe.setUpdateTime(DateUtils.getNowDate());
        return safeMapper.updateSafeQuestionnaire(safe);
    }

    /**
     * 批量删除安全问卷
     * 
     * @param ids 需要删除的安全问卷ID
     * @return 结果
     */
    @Override
    public int deleteSafeQuestionnaireByIds(Long[] ids)
    {
        return safeMapper.deleteSafeQuestionnaireByIds(ids);
    }

    /**
     * 删除安全问卷信息
     * 
     * @param id 安全问卷ID
     * @return 结果
     */
    @Override
    public int deleteSafeQuestionnaireById(Long id)
    {
        return safeMapper.deleteSafeQuestionnaireById(id);
    }

    /**
     * 处理安全问卷信息
     *
     * @param safe 安全问卷
     * @return 结果
     */
    @Override
    public AjaxResult handleSafeQuestionnaireById(Safe safe) {
        if (safeMapper.handleSafeQuestionnaireById(safe) > 0) {
            // 消息推送
            try{
                String accessToken = null;
                RestTemplate restTemplate = new RestTemplate();
                // 获取access_token
                if (redisCache.getCacheObject("access_token") != null) {
                    accessToken = redisCache.getCacheObject("access_token");
                } else {
                    String rspStr = HttpUtils.sendGet(credentialUrl, "grant_type=client_credential&appid=" + appid + "&secret=" + secret, Constants.UTF8);
                    JSONObject obj = JSONObject.parseObject(rspStr);
                    accessToken = obj.getString("access_token");
                    redisCache.setCacheObject("access_token", accessToken, 7000, TimeUnit.SECONDS);
                }

                Safe openQuestion = safeMapper.selectOpenIdById(safe.getId());

                JSONObject postData = new JSONObject();
                postData.put("access_token", accessToken);
                postData.put("touser", openQuestion.getOpenId());
                postData.put("template_id", template_id);
                JSONObject data = new JSONObject();
                JSONObject valueData1 = new JSONObject();
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                valueData1.put("value", sdf.format(openQuestion.getCreateTime()));
                JSONObject valueData2 = new JSONObject();
                valueData2.put("value", "我们已收到您的意见并责成相关人员进行处理");
                data.put("time2", valueData1);
                data.put("thing3", valueData2);
                postData.put("data", data);
                postData.put("page", "pages/deal/deal?id=" +  safe.getId());
                // 推送消息类型 默认真机
                // postData.put("miniprogram_state", "developer");
                String rspStr = HttpUtils.sendPost(sendUrl+"?access_token=" + accessToken, postData.toJSONString());
                JSONObject obj = JSONObject.parseObject(rspStr);
                return AjaxResult.success("数据处理成功," + obj.getString("errmsg"));
            }catch (Exception e) {
                return AjaxResult.success("数据处理成功但推送失败");
            }
        }
        else {
            return AjaxResult.error("数据处理失败");
        }
    }

    /**
     * 获取字典
     */
    @Override
    public Map<String, Object> getDict() {
        Map<String, Object> map = new HashMap<>();
        List<DictData> typeList = safeMapper.selectSafeQuestionnaireDict();
        List<DictData> branchList = safeMapper.selectBranchDict();
        map.put("type", typeList);
        map.put("branch", branchList);
        return map;
    }

    /**
     * 获取列表
     */
    @Override
    public TableDataInfo getList(String openId){
        List<Safe> list = safeMapper.selectListByOpenId(openId);
        long total = new PageInfo(list).getTotal();
        List<Map<String, Object>> result = new ArrayList<Map<String, Object>>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        // 获取字典数据 进行翻译
        List<DictData> typeList = safeMapper.selectSafeQuestionnaireDict();
        JSONObject typeJson = new JSONObject();
        for (DictData dict : typeList) {
            typeJson.put(dict.getValue(), dict.getText());
        }
        List<DictData> branchList = safeMapper.selectBranchDict();
        JSONObject branchJson = new JSONObject();
        for (DictData dict : branchList) {
            branchJson.put(dict.getValue(), dict.getText());
        }
        for (Safe safe : list) {
            Map<String, Object> item = new HashMap<>();
            item.put("id", safe.getId());
            item.put("createTime", sdf.format(safe.getCreateTime()));
            item.put("type", typeJson.getString(safe.getType()));
            item.put("locationStation", branchJson.getString(safe.getLocationStation()));
            item.put("remark", safe.getRemark());
            item.put("handleTime", safe.getHandleTime() == null ? "" : sdf.format(safe.getHandleTime()));
            item.put("handleName", safe.getHandleName() == null ? "" : safe.getHandleName());
            item.put("handleRemark", safe.getHandleRemark() == null ? "" : safe.getHandleRemark());
            result.add(item);
        }

        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setMsg("查询成功");
        rspData.setRows(result);
        rspData.setTotal(total);
        return rspData;
    }

    @Override
    public Map<String, Object> getNumByYear(Safe safe) {
        Map<String, Object> map = new HashMap<>();
        List<SafeChartData> list = safeMapper.selectNumByYear(safe);
        List<SafeChartData> deallist= safeMapper.selectDealNumByYear(safe);
        String []linecharttime = new String[12];
        int []linechartnum = new int[12];
        int []linechartdealnum = new int[12];
        for (int i=0;i<12;i++) {
            String time = safe.getYear();
            if(i<9){time = time+"-0"+String.valueOf(i+1);}
            else {time = time+"-"+String.valueOf(i+1);}
            linecharttime[i]=time;
            for(int j=0;j<list.size();j++){
                String a = list.get(j).getTime();
                if(list.get(j).getTime().equals(time)){
                    linechartnum[i]=list.get(j).getNum();
                    break;
                }
            }
            for(int j=0;j<deallist.size();j++){
                if(deallist.get(j).getTime().equals(time)){
                    linechartdealnum[i]=deallist.get(j).getDealnum();
                    break;
                }
            }
        }
        Map<String, Object> linechart = new HashMap<>();
        linechart.put("timedata", linecharttime);
        linechart.put("num", linechartnum);
        linechart.put("dealnum", linechartdealnum);
        map.put("linechart",linechart);


        List<SafeChartData> typelist = safeMapper.selectTypeNumByYear(safe);
        List<DictData> types = safeMapper.selectSafeQuestionnaireDict();
        String []barcharttype = new String[types.size()];
        int [][]barchartnum = new int[types.size()][12];
        for(int i=0;i<types.size();i++)
        {
            barcharttype[i] = types.get(i).getText();
            for(int j=0;j<12;j++){
                String time = safe.getYear();
                if(i<9){time = time+"-0"+String.valueOf(j+1);}
                else {time = time+"-"+String.valueOf(j+1);}
                for(int k=0;k<typelist.size();k++){
                    if(typelist.get(k).getTime().equals(time)&&typelist.get(k).getType().equals(types.get(i).getValue())){
                        barchartnum[i][j] = typelist.get(k).getNum();
                    }
                }
            }
        }
        Map<String, Object> barchart1 = new HashMap<>();
        barchart1.put("type",barcharttype);
        barchart1.put("barnum1",barchartnum);
        map.put("barchart1",barchart1);


        List<SafeChartData> branchlist = safeMapper.selectLocationStationNumByYear(safe);
        List<DictData> branchs = safeMapper.selectBranchDict();
        String []barchartbranch = new String[branchs.size()];
        int [][]barchartnum1 = new int[branchs.size()][12];
        for(int i=0;i<branchs.size();i++)
        {
            barchartbranch[i] = branchs.get(i).getText();
            for(int j=0;j<12;j++){
                String time = safe.getYear();
                if(i<9){time = time+"-0"+String.valueOf(j+1);}
                else {time = time+"-"+String.valueOf(j+1);}
                for(int k=0;k<branchlist.size();k++){
                    if(branchlist.get(k).getTime().equals(time)&&branchlist.get(k).getBranch().equals(branchs.get(i).getValue())){
                        barchartnum1[i][j] = branchlist.get(k).getNum();
                    }
                }
            }
        }
        Map<String, Object> barchart2 = new HashMap<>();
        barchart2.put("branch",barchartbranch);
        barchart2.put("barnum2",barchartnum1);
        map.put("barchart2",barchart2);
        return map;
    }


}
