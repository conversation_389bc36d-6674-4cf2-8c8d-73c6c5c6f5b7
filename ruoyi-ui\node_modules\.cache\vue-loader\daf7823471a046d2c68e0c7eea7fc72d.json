{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\assess\\self\\index.vue?vue&type=template&id=630aa830", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\assess\\self\\index.vue", "mtime": 1756099891057}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}