package com.ruoyi.app.leave.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 出门证任务对象 leave_task
 * 
 * <AUTHOR>
 * @date 2025-03-13
 */
public class LeaveTask extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 任务号 */
    @Excel(name = "任务号")
    private String taskNo;

    /** 申请单号 */
    @Excel(name = "申请单号")
    private String applyNo;

    /** 任务类型 1-出厂 2-返厂 3-跨区调拨 */
    @Excel(name = "任务类型 1-出厂 2-返厂 3-跨区调拨")
    private Integer taskType;

    /** 计划号 */
    @Excel(name = "计划号")
    private String planNo;

    /** 任务状态  1-待过皮重 2-待装货 3-待过毛重 4-待出厂 5-待返厂 6-待过毛重(复磅) 7-待卸货 8-待过皮重(复磅) 9-完成 */
    @Excel(name = "任务状态  1-待过皮重 2-待装货 3-待过毛重 4-待出厂 5-待返厂 6-待过毛重(复磅) 7-待卸货 8-待过皮重(复磅) 9-完成")
    private Integer taskStatus;

    /** 炉号 */
    @Excel(name = "炉号")
    private String heatNo;

    /** 钢种 */
    @Excel(name = "钢种")
    private String steelGrade;

    /** 离厂大门 任务类型1、3 */
    @Excel(name = "离厂大门 任务类型1、3")
    private Integer leaveDoor;

    /** 离厂时间 任务类型1、3 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "离厂时间 任务类型1、3", width = 30, dateFormat = "yyyy-MM-dd")
    private Date leaveTime;

    /** 进厂大门 任务类型2、3 */
    @Excel(name = "进厂大门 任务类型2、3")
    private Integer enterDoor;

    /** 进厂时间 任务类型2、3 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "进厂时间 任务类型2、3", width = 30, dateFormat = "yyyy-MM-dd")
    private Date enterTime;

    /** 装货人 */
    @Excel(name = "装货人")
    private String loadingWorkNo;

    /** 装货时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "装货时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date loadingTime;

    /** 卸货人 */
    @Excel(name = "卸货人")
    private String unloadingWorkNo;

    /** 卸货时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "卸货时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date unloadingTime;

    /** 司机名称 */
    @Excel(name = "司机名称")
    private String driverName;

    /** 性别 1-男 2-女 */
    @Excel(name = "性别 1-男 2-女")
    private Integer sex;

    /** 手机号 */
    @Excel(name = "手机号")
    private String mobilePhone;

    /** 身份证号 */
    @Excel(name = "身份证号")
    private String idCardNo;

    /** 车牌号 */
    @Excel(name = "车牌号")
    private String carNum;

    /** 车辆排放标准 1-国五 2-国六 3-新能源 */
    @Excel(name = "车辆排放标准 1-国五 2-国六 3-新能源")
    private Integer vehicleEmissionStandards;

    /** 人脸照片 */
    @Excel(name = "人脸照片")
    private String faceImg;

    /** 行驶证照片 */
    @Excel(name = "行驶证照片")
    private String drivingLicenseImg;

    /** 驾驶证照片 */
    @Excel(name = "驾驶证照片")
    private String driverLicenseImg;

    /** 公司名称  */
    @Excel(name = "公司名称 ")
    private String companyName;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setTaskNo(String taskNo) 
    {
        this.taskNo = taskNo;
    }

    public String getTaskNo() 
    {
        return taskNo;
    }
    public void setApplyNo(String applyNo) 
    {
        this.applyNo = applyNo;
    }

    public String getApplyNo() 
    {
        return applyNo;
    }
    public void setTaskType(Integer taskType) 
    {
        this.taskType = taskType;
    }

    public Integer getTaskType() 
    {
        return taskType;
    }
    public void setPlanNo(String planNo) 
    {
        this.planNo = planNo;
    }

    public String getPlanNo() 
    {
        return planNo;
    }
    public void setTaskStatus(Integer taskStatus) 
    {
        this.taskStatus = taskStatus;
    }

    public Integer getTaskStatus() 
    {
        return taskStatus;
    }
    public void setHeatNo(String heatNo) 
    {
        this.heatNo = heatNo;
    }

    public String getHeatNo() 
    {
        return heatNo;
    }
    public void setSteelGrade(String steelGrade) 
    {
        this.steelGrade = steelGrade;
    }

    public String getSteelGrade() 
    {
        return steelGrade;
    }
    public void setLeaveDoor(Integer leaveDoor) 
    {
        this.leaveDoor = leaveDoor;
    }

    public Integer getLeaveDoor() 
    {
        return leaveDoor;
    }
    public void setLeaveTime(Date leaveTime) 
    {
        this.leaveTime = leaveTime;
    }

    public Date getLeaveTime() 
    {
        return leaveTime;
    }
    public void setEnterDoor(Integer enterDoor) 
    {
        this.enterDoor = enterDoor;
    }

    public Integer getEnterDoor() 
    {
        return enterDoor;
    }
    public void setEnterTime(Date enterTime) 
    {
        this.enterTime = enterTime;
    }

    public Date getEnterTime() 
    {
        return enterTime;
    }
    public void setLoadingWorkNo(String loadingWorkNo) 
    {
        this.loadingWorkNo = loadingWorkNo;
    }

    public String getLoadingWorkNo() 
    {
        return loadingWorkNo;
    }
    public void setLoadingTime(Date loadingTime) 
    {
        this.loadingTime = loadingTime;
    }

    public Date getLoadingTime() 
    {
        return loadingTime;
    }
    public void setUnloadingWorkNo(String unloadingWorkNo) 
    {
        this.unloadingWorkNo = unloadingWorkNo;
    }

    public String getUnloadingWorkNo() 
    {
        return unloadingWorkNo;
    }
    public void setUnloadingTime(Date unloadingTime) 
    {
        this.unloadingTime = unloadingTime;
    }

    public Date getUnloadingTime() 
    {
        return unloadingTime;
    }
    public void setDriverName(String driverName) 
    {
        this.driverName = driverName;
    }

    public String getDriverName() 
    {
        return driverName;
    }
    public void setSex(Integer sex) 
    {
        this.sex = sex;
    }

    public Integer getSex() 
    {
        return sex;
    }
    public void setMobilePhone(String mobilePhone) 
    {
        this.mobilePhone = mobilePhone;
    }

    public String getMobilePhone() 
    {
        return mobilePhone;
    }
    public void setIdCardNo(String idCardNo) 
    {
        this.idCardNo = idCardNo;
    }

    public String getIdCardNo() 
    {
        return idCardNo;
    }
    public void setCarNum(String carNum) 
    {
        this.carNum = carNum;
    }

    public String getCarNum() 
    {
        return carNum;
    }
    public void setVehicleEmissionStandards(Integer vehicleEmissionStandards) 
    {
        this.vehicleEmissionStandards = vehicleEmissionStandards;
    }

    public Integer getVehicleEmissionStandards() 
    {
        return vehicleEmissionStandards;
    }
    public void setFaceImg(String faceImg) 
    {
        this.faceImg = faceImg;
    }

    public String getFaceImg() 
    {
        return faceImg;
    }
    public void setDrivingLicenseImg(String drivingLicenseImg) 
    {
        this.drivingLicenseImg = drivingLicenseImg;
    }

    public String getDrivingLicenseImg() 
    {
        return drivingLicenseImg;
    }
    public void setDriverLicenseImg(String driverLicenseImg) 
    {
        this.driverLicenseImg = driverLicenseImg;
    }

    public String getDriverLicenseImg() 
    {
        return driverLicenseImg;
    }
    public void setCompanyName(String companyName) 
    {
        this.companyName = companyName;
    }

    public String getCompanyName() 
    {
        return companyName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("taskNo", getTaskNo())
            .append("applyNo", getApplyNo())
            .append("taskType", getTaskType())
            .append("planNo", getPlanNo())
            .append("taskStatus", getTaskStatus())
            .append("heatNo", getHeatNo())
            .append("steelGrade", getSteelGrade())
            .append("leaveDoor", getLeaveDoor())
            .append("leaveTime", getLeaveTime())
            .append("enterDoor", getEnterDoor())
            .append("enterTime", getEnterTime())
            .append("loadingWorkNo", getLoadingWorkNo())
            .append("loadingTime", getLoadingTime())
            .append("unloadingWorkNo", getUnloadingWorkNo())
            .append("unloadingTime", getUnloadingTime())
            .append("driverName", getDriverName())
            .append("sex", getSex())
            .append("mobilePhone", getMobilePhone())
            .append("idCardNo", getIdCardNo())
            .append("carNum", getCarNum())
            .append("vehicleEmissionStandards", getVehicleEmissionStandards())
            .append("faceImg", getFaceImg())
            .append("drivingLicenseImg", getDrivingLicenseImg())
            .append("driverLicenseImg", getDriverLicenseImg())
            .append("companyName", getCompanyName())
            .append("createTime", getCreateTime())
            .append("createBy", getCreateBy())
            .append("updateTime", getUpdateTime())
            .append("updateBy", getUpdateBy())
            .toString();
    }
}
