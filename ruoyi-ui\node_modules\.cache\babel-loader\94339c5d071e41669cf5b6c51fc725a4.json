{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\dataReport\\form\\admin.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\dataReport\\form\\admin.vue", "mtime": 1756099891060}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\babel.config.js", "mtime": 1688548084091}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_Tree<PERSON>iew", "_interopRequireDefault", "require", "_dimensionality", "_dimensionalitypermission", "_form", "_dept", "_axios", "xlsx", "_interopRequireWildcard", "name", "components", "TreeView", "data", "loading", "newOpen", "SpecialImportOpen", "mouthImportOpen", "searchopen", "total", "pageSizes", "queryParams", "pageNum", "pageSize", "dimensionalityName", "isUse", "rootList", "detail", "rootId", "drawer", "query", "startDate", "endDate", "title", "exportOpen", "deadlineOpen", "deadlineTitle", "deadlineForm", "dimensionalityPath", "dateValue", "deptList", "form", "userList", "adminOpen", "adminTitle", "specialFcDate", "dimensionalityId", "noteShow", "created", "getList", "getDept", "methods", "clickNode", "$event", "node", "target", "parentElement", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "click", "_this", "getStatusList<PERSON><PERSON><PERSON>min", "then", "res", "rows", "i", "length", "id", "containsSubstring", "showboot", "showMouth", "_this2", "listDept", "children", "console", "log", "dealdeptList", "row", "count", "value", "path", "label", "deptName", "handleQueryDept", "$refs", "cascaderHandle", "dropDownVisible", "reset<PERSON><PERSON>y", "resetForm", "handleQuery", "handleAdd", "handleDetail", "rootRuleType", "ruleType", "getDetail", "handleDeadLine", "submitForm", "_this3", "deadlineSwitch", "deadlineDate", "$modal", "msgError", "deadlineDateCheck", "split", "test", "deadlinebranch", "response", "msgSuccess", "cancel", "_this4", "getRootListById", "undefined", "$forceUpdate", "handleClose", "handleExport", "clickChangeTime", "addClick", "_this5", "addDimensionality", "exportData", "$notify", "error", "message", "downloadFile", "_objectSpread2", "default", "exportDataPreview", "_this6", "downloadXlsx", "blob", "reader", "FileReader", "readAsA<PERSON>y<PERSON><PERSON>er", "onload", "evt", "customBlobContent", "result", "ints", "Uint8Array", "slice", "size", "workBook", "read", "type", "sheetNames", "SheetNames", "sheetName", "workSheet", "Sheets", "excelTable", "utils", "sheet_to_json", "tableThead", "Array", "from", "Object", "keys", "map", "item", "excelData", "exceltitle", "excelHtml", "exportMouthDataPreview", "_this7", "exportMouthData", "onDateChange", "toUpdateUsers", "$router", "push", "handleDateChange", "handleaAdminList", "_this8", "listPermission", "handlefill", "fcDate", "handleAnswer", "handleSpecial", "handleMouth", "downloadTemplateSpecialPreview", "_this9", "queryImport", "url", "downloadTemplateSpecial", "substring", "string", "includes", "mouthCheck", "aloneList", "handlePreview", "_this0", "handlePreview1", "_this1", "now", "Date", "getFirstOfYear", "getFirstOfMonth", "firstDayOfYear", "getFullYear", "formatDate", "firstDayOfMonth", "getMonth", "date", "year", "month", "String", "padStart", "day", "getDate", "concat"], "sources": ["src/views/dataReport/form/admin.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form\r\n      :model=\"queryParams\"\r\n      ref=\"queryForm\"\r\n      :inline=\"true\"\r\n      label-width=\"68px\"\r\n    >\r\n      <el-form-item label=\"扎口部门\" prop=\"deptCode\">\r\n        <el-cascader\r\n          ref=\"cascaderHandle\"\r\n          :options=\"deptList\"\r\n          clearable\r\n          filterable\r\n          v-model=\"queryParams.deptCode\"\r\n          :props=\"{ expandTrigger: 'hover', emitPath: false,checkStrictly: true }\"\r\n          :show-all-levels=\"false\"\r\n          @change=\"handleQueryDept\"\r\n        >\r\n        <span\r\n              slot-scope=\"{ node, data }\"\r\n              style=\"margin-left: -10px; padding-left: 10px; display: block\"\r\n              @click=\"clickNode($event, node)\"\r\n              >{{ data.label }}</span\r\n            >\r\n        </el-cascader>\r\n      </el-form-item>\r\n      <el-form-item label=\"报表名称\" prop=\"dimensionalityName\">\r\n        <el-input\r\n          v-model=\"queryParams.dimensionalityName\"\r\n          placeholder=\"请输入名称\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"填报时间\">\r\n                <el-date-picker\r\n                  v-model=\"queryParams.fcDate\"\r\n                  value-format=\"yyyy-MM-dd\"\r\n                  type=\"date\"\r\n                  @change=\"handleDateChange\"\r\n                  placeholder=\"选择日期\">\r\n                </el-date-picker>\r\n      </el-form-item>\r\n      <!-- <el-form-item label=\"是否在用启用\" prop=\"isUse\">\r\n        <el-select v-model=\"queryParams.isUse\" placeholder=\"请选择\">\r\n          <el-option label=\"启用\" value=\"1\"></el-option>\r\n          <el-option label=\"停用\" value=\"0\"></el-option>\r\n        </el-select>\r\n      </el-form-item> -->\r\n      <el-form-item>\r\n        <el-button\r\n          type=\"cyan\"\r\n          icon=\"el-icon-search\"\r\n          size=\"mini\"\r\n          @click=\"handleQuery\"\r\n          >搜索</el-button\r\n        >\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\"\r\n          >重置</el-button\r\n        >\r\n      </el-form-item>\r\n    </el-form>\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          icon=\"el-icon-plus\"\r\n          size=\"mini\"\r\n          @click=\"handleAdd\"\r\n          >新建报表</el-button\r\n        >\r\n      </el-col>\r\n    </el-row>\r\n    <el-table v-loading=\"loading\" :data=\"rootList\" border>\r\n      <el-table-column label=\"扎口部门\" align=\"center\" prop=\"deptName\" width=\"240\"/>\r\n      <!-- <el-table-column\r\n        label=\"扎口部门及人员\"\r\n        align=\"center\"\r\n        class-name=\"small-padding fixed-width\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              @click=\"handleaAdminList(scope.row)\"\r\n              >{{scope.row.deptName}}</el-button\r\n            >\r\n        </template>\r\n      </el-table-column> -->\r\n      <!-- <el-table-column label=\"报表名称\" align=\"center\" prop=\"dimensionalityName\"/> -->\r\n      <el-table-column\r\n        label=\"报表名称\"\r\n        align=\"center\"\r\n        class-name=\"small-padding fixed-width\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              @click=\"handleAnswer(scope.row)\"\r\n              >{{scope.row.dimensionalityName}}</el-button\r\n            >\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"当期完成率\" align=\"center\" prop=\"countRate\" width=\"160\"/>\r\n      <el-table-column label=\"当期应填数量\" align=\"center\" prop=\"shouldCount\" width=\"160\" />\r\n      <!-- <el-table-column\r\n        label=\"当期应填数量\"\r\n        align=\"center\"\r\n        class-name=\"small-padding fixed-width\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              @click=\"handlefill(scope.row)\"\r\n              >{{scope.row.shouldCount}}</el-button\r\n            >\r\n        </template>\r\n      </el-table-column> -->\r\n\r\n      <el-table-column\r\n        label=\"当期未填数量\"\r\n        align=\"center\"\r\n        class-name=\"small-padding fixed-width\"\r\n        width=\"160\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              @click=\"handlefill(scope.row)\"\r\n              >{{scope.row.notCount}}</el-button\r\n            >\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <!-- <el-table-column label=\"当期应填数量\" align=\"center\" prop=\"\"  @cell-click=\"handleDetail(scope.row)\"/>\r\n      <el-table-column label=\"当期未填数量\" align=\"center\" prop=\"hasCount\"/> -->\r\n      \r\n      <!-- <el-table-column label=\"是否在用\" align=\"center\" prop=\"isUse\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag\r\n            style=\"margin-left: 10px\"\r\n            :type=\"scope.row.isUse == '1'? 'success' : 'danger'\"\r\n            >{{ scope.row.isUse == \"1\" ? \"启用\" : \"停用\" }}</el-tag\r\n          >\r\n        </template>\r\n      </el-table-column> -->\r\n      <el-table-column\r\n        label=\"操作\"\r\n        align=\"center\"\r\n        class-name=\"small-padding fixed-width\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            v-if=\"scope.row.ruleType != '5' \"\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleDetail(scope.row)\"\r\n            >维度管理</el-button\r\n          >\r\n          <el-button\r\n            v-if=\"scope.row.ruleType != '5' && scope.row.ruleType != '0'\"\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleDeadLine(scope.row)\"\r\n            >截止日期</el-button\r\n          >\r\n          <el-button\r\n            v-if=\"\r\n                  scope.row.ruleType == '1' ||\r\n                  scope.row.ruleType == '3' ||\r\n                  scope.row.ruleType == '4'\r\n                 \"\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"toUpdateUsers(scope.row)\"\r\n            >分配权限</el-button\r\n          >\r\n          <el-button\r\n              v-if=\"\r\n                  scope.row.ruleType == '0' ||\r\n                  scope.row.ruleType == '2' ||\r\n                  scope.row.ruleType == '4' ||\r\n                  scope.row.ruleType == '5'\r\n                 \"\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleExport(scope.row)\"\r\n            >导出数据</el-button>\r\n            <el-button\r\n              v-if=\"\r\n                  (scope.row.ruleType == '0' ||\r\n                  scope.row.ruleType == '2' ||\r\n                  scope.row.ruleType == '4' ||\r\n                  scope.row.ruleType == '5') &&\r\n                  aloneList(scope.row.dimensionalityName)\r\n                 \"\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleSpecial(scope.row)\"\r\n            >单周期数据导出</el-button>\r\n            <el-button\r\n              v-if=\"\r\n                  (scope.row.ruleType == '0' ||\r\n                  scope.row.ruleType == '2' ||\r\n                  scope.row.ruleType == '4' ||\r\n                  scope.row.ruleType == '5') &&\r\n                  containsSubstring('工装',scope.row.dimensionalityName)\r\n                 \"\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleMouth(scope.row)\"\r\n            >规整化数据导出</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination\r\n      v-show=\"total > 0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      :pageSizes=\"pageSizes\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <el-drawer\r\n      title=\"详情\"\r\n      :visible.sync=\"drawer\"\r\n      direction=\"rtl\"\r\n      size=\"80%\"\r\n      :before-close=\"handleClose\"\r\n    >\r\n      <tree-view :node=\"detail\" @refreshData=\"getDetail\"></tree-view>\r\n    </el-drawer>\r\n    <el-dialog title=\"选择导出范围\" :visible.sync=\"exportOpen\" width=\"400px\" append-to-body destroy-on-close>\r\n      <span>数据日期范围：</span>\r\n        <el-date-picker\r\n          v-model=\"dateValue\"\r\n          type=\"daterange\"\r\n          range-separator=\"至\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          @change=\"onDateChange\">\r\n        </el-date-picker>        \r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <div class=\"el-upload__tip\" slot=\"tip\">\r\n          <el-checkbox v-model=\"noteShow\" />是否在导出内容中展示指标修正历史\r\n        </div>\r\n      <el-button type=\"success\" @click=\"exportDataPreview\">预 览</el-button>\r\n      <el-button type=\"primary\" @click=\"exportData\">导 出</el-button>\r\n      <!-- <el-button @click=\"exportOpen = false\">取 消</el-button> -->\r\n    </div>\r\n    </el-dialog>\r\n\r\n    \r\n    <el-dialog title=\"选择导出范围\" :visible.sync=\"mouthImportOpen\" width=\"400px\" append-to-body destroy-on-close>\r\n      <span>数据日期范围：</span>\r\n        <el-date-picker\r\n          v-model=\"dateValue\"\r\n          type=\"daterange\"\r\n          range-separator=\"至\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          @change=\"onDateChange\">\r\n        </el-date-picker>        \r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n      <el-button type=\"primary\" @click=\"exportMouthDataPreview\">预 览</el-button>\r\n      <el-button type=\"primary\" @click=\"exportMouthData\">导 出</el-button>\r\n      <!-- <el-button @click=\"mouthImportOpen = false\">取 消</el-button> -->\r\n    </div>\r\n    </el-dialog>\r\n\r\n    <el-dialog title=\"选择导出范围\" :visible.sync=\"newOpen\" width=\"400px\" append-to-body destroy-on-close>\r\n      <el-form ref=\"form\" :model=\"form\" label-width=\"80px\">\r\n        <el-form-item label=\"名称\" prop=\"dimensionalityName\">\r\n          <el-input\r\n            v-model=\"form.dimensionalityName\"\r\n            placeholder=\"请输入名称\"\r\n            clearable\r\n            size=\"small\"\r\n            @keyup.enter.native=\"handleQuery\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"扎口部门\" prop=\"deptCode\">\r\n          <el-cascader\r\n            :options=\"deptList\"\r\n            clearable\r\n            v-model=\"form.deptCode\"\r\n            :props=\"{ expandTrigger: 'hover', emitPath: false,checkStrictly: true }\"\r\n            :show-all-levels=\"false\"\r\n          >\r\n          </el-cascader>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"addClick\">确 定</el-button>\r\n        <el-button @click=\"newOpen = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <el-dialog :title=\"adminTitle\" :visible.sync=\"adminOpen\" width=\"1000px\" append-to-body>\r\n      <el-table v-loading=\"loading\" :data=\"userList\">\r\n        <el-table-column label=\"用户工号\" align=\"center\" prop=\"workNo\" />\r\n        <el-table-column label=\"用户姓名\" align=\"center\" prop=\"userName\" />\r\n        <!-- <el-table-column label=\"用户姓名\" align=\"center\" prop=\"userName\" /> -->\r\n      </el-table>\r\n    </el-dialog>\r\n\r\n    <el-dialog :title=\"deadlineTitle\" :visible.sync=\"deadlineOpen\" width=\"800px\" append-to-body>\r\n      <el-form ref=\"deadlineForm\" :model=\"deadlineForm\" label-width=\"160px\">\r\n        <el-form-item label=\"截止日期开关\" prop=\"deadlineSwitch\">\r\n          <el-switch\r\n            v-model=\"deadlineForm.deadlineSwitch\"\r\n            active-color=\"#13ce66\"\r\n            inactive-color=\"#ff4949\"\r\n            active-value=\"1\"\r\n            inactive-value=\"0\"\r\n            >\r\n          </el-switch>\r\n        </el-form-item>\r\n        <el-form-item v-if=\" deadlineForm.deadlineSwitch == '1' \" label=\"截止日期\" prop=\"deadlineDate\">\r\n          <el-input type=\"text\"\r\n                    v-model=\"deadlineForm.deadlineDate\" \r\n                    placeholder=\"截止日期格式为(年/月/日)\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item v-if=\" deadlineForm.deadlineSwitch == '1' \"  label=\"邮件通知开关\" prop=\"mailSwitch\">\r\n          <el-switch\r\n            v-model=\"deadlineForm.mailSwitch\"\r\n            active-color=\"#13ce66\"\r\n            inactive-color=\"#ff4949\"\r\n            active-value=\"1\"\r\n            inactive-value=\"0\"\r\n            >\r\n          </el-switch>\r\n        </el-form-item>\r\n        <el-form-item v-if=\" deadlineForm.mailSwitch == '1' \" label=\"通知时间\" prop=\"deadlineDate\">\r\n          <el-input type=\"text\"\r\n                    v-model=\"deadlineForm.countdown\" \r\n                    placeholder=\"设置在截止日期前几天进行通知\"></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- <el-dialog\r\n      title=\"单周期报表导出\"\r\n      :visible.sync=\"SpecialImportOpen\"\r\n      width=\"400px\"\r\n      append-to-body\r\n      destroy-on-close\r\n    >\r\n      <span>选择导出时间：</span>\r\n      <el-date-picker\r\n            v-model=\"specialFcDate\"\r\n            value-format=\"yyyy-MM-dd\"\r\n            type=\"date\"\r\n            :default-value=\"new Date()\"\r\n            placeholder=\"选择时间\"\r\n          >\r\n      </el-date-picker>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"downloadTemplateSpecial\">数据下载</el-button>\r\n      </div>\r\n    </el-dialog> -->\r\n\r\n    <el-dialog title=\"单周期报表导出\" :visible.sync=\"SpecialImportOpen\" width=\"400px\" append-to-body destroy-on-close>\r\n      <span>选择导出时间：</span>\r\n      <el-date-picker\r\n            v-model=\"specialFcDate\"\r\n            value-format=\"yyyy-MM-dd\"\r\n            type=\"date\"\r\n            :default-value=\"new Date()\"\r\n            placeholder=\"选择时间\"\r\n          >\r\n      </el-date-picker>     \r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n      <el-button type=\"success\" @click=\"downloadTemplateSpecialPreview\">数据预览</el-button>\r\n      <el-button type=\"primary\" @click=\"downloadTemplateSpecial\">数据下载</el-button>\r\n    </div>\r\n    </el-dialog>\r\n\r\n    <el-dialog  title=\"数据预览\"  :visible.sync=\"searchopen\" width=\"1800px\" >\r\n        <div class=\"test\">\r\n          <vue-office-excel\r\n              :src=\"customBlobContent\"\r\n              style=\"height: 100vh;\"\r\n          />\r\n        </div>\r\n    </el-dialog>\r\n\r\n  </div>\r\n</template>\r\n  \r\n  <script>\r\nimport TreeView from \"@/components/TreeView\";\r\nimport {\r\n  rootListDimensionality,\r\n  getRootListById,\r\n  addDimensionality,\r\n  getStatusListWithadmin\r\n} from \"@/api/tYjy/dimensionality\";\r\n\r\nimport {\r\n  listPermission,\r\n} from \"@/api/tYjy/dimensionalitypermission\";\r\n\r\nimport {\r\n  deadlinebranch,\r\n  updateForm,\r\n} from \"@/api/tYjy/form\";\r\n\r\nimport { listDept } from \"@/api/tYjy/dept\";\r\nimport axios from \"axios\";\r\nimport * as xlsx from 'xlsx';\r\nexport default {\r\n  name: \"Dimensionality\",\r\n  components: {\r\n    TreeView,\r\n  },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      newOpen:false,\r\n      SpecialImportOpen:false,\r\n      mouthImportOpen:false,\r\n      searchopen:false,\r\n      total:0,\r\n      pageSizes:[20,50,100],\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 20,\r\n        dimensionalityName: null,\r\n        isUse: null,\r\n      },\r\n      rootList: [],\r\n      detail: {},\r\n      rootId:null,\r\n      drawer:false,\r\n      query:{\r\n        startDate:null,\r\n        endDate:null,\r\n        rootId:null,\r\n        title:null,\r\n      },\r\n      exportOpen:false,\r\n      deadlineOpen:false,\r\n      deadlineTitle:\"批量修改截止日期\",\r\n      deadlineForm:\r\n      {\r\n        dimensionalityPath:null\r\n      },\r\n      dateValue:null,\r\n      deptList: [],\r\n      form:{},\r\n      userList:[],\r\n      adminOpen:false,\r\n      adminTitle:\"管理员名单\",\r\n      specialFcDate:null,\r\n      dimensionalityName:null,\r\n      dimensionalityId:null,\r\n      noteShow:false //是否展示指标\r\n\r\n\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n    this.getDept();\r\n  },\r\n  methods: {\r\n    clickNode($event, node) {\r\n      $event.target.parentElement.parentElement.firstElementChild.click();\r\n    },\r\n    getList() {\r\n      this.loading = true;\r\n      getStatusListWithadmin(this.queryParams).then((res) => {\r\n        this.rootList = res.rows;\r\n        for(let i=0;i<this.rootList.length;i++)\r\n        {\r\n          if(this.rootList[i].id==273 || this.rootList[i].id==840 || this.rootList[i].id==873 || this.rootList[i].id==1077 || this.rootList[i].id==1059 || this.containsSubstring('安全责任工资',this.rootList[i].dimensionalityName))\r\n          {\r\n            this.rootList[i].showboot=1\r\n          }\r\n          else\r\n          {\r\n            this.rootList[i].showboot=0\r\n          }\r\n          if(this.containsSubstring('工装',this.rootList[i].dimensionalityName))\r\n          {\r\n            this.rootList[i].showMouth=1\r\n          }\r\n          else\r\n          {\r\n            this.rootList[i].showMouth=0\r\n          }\r\n        }\r\n        this.total = res.total;\r\n        this.loading = false;\r\n      });\r\n      // rootListDimensionality(this.queryParams).then((res) => {\r\n      //   this.rootList = res.rows;\r\n      //   this.total = res.total;\r\n      //   this.loading = false;\r\n      // });\r\n    },\r\n    getDept() {\r\n      listDept().then((res) => {\r\n        this.deptList = res.rows[0].children;\r\n        console.log(res);\r\n        for(let i=0;i<this.deptList.length;i++)\r\n        {\r\n          this.dealdeptList(this.deptList[i],0)\r\n        }\r\n      });\r\n    },\r\n    dealdeptList(row,count)\r\n    {\r\n       row.value=row.path\r\n       row.label=row.deptName\r\n       if(row.children.length>0 && count<1)\r\n       {\r\n          for(let i=0;i<row.children.length;i++)\r\n          {\r\n            this.dealdeptList(row.children[i],count+1)\r\n          }\r\n       }\r\n       else\r\n       {\r\n          row.children=null\r\n       }\r\n    },\r\n    handleQueryDept() {\r\n      this.$refs.cascaderHandle.dropDownVisible = false;\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n\r\n\r\n    handleAdd() {\r\n      this.newOpen=true\r\n      // let that = this;\r\n      // this.$prompt(\"请输入名称\", \"提示\", {\r\n      //   confirmButtonText: \"确定\",\r\n      //   cancelButtonText: \"取消\",\r\n      // })\r\n      //   .then(({ value }) => {\r\n      //     let form = {};\r\n      //     form.dimensionalityName = value;\r\n      //     addDimensionality(form).then((res) => {\r\n      //       that.getList();\r\n      //     });\r\n      //   })\r\n      //   .catch(() => {\r\n      //     that.$message({\r\n      //       type: \"info\",\r\n      //       message: \"取消操作\",\r\n      //     });\r\n      //   });\r\n    },\r\n    handleDetail(row){\r\n      this.rootId = row.id;\r\n      this.rootRuleType = row.ruleType;\r\n      this.getDetail();\r\n      this.drawer = true;\r\n    },\r\n    handleDeadLine(row){\r\n      this.deadlineForm={dimensionalityPath:null}\r\n      this.deadlineForm.dimensionalityPath=row.path\r\n      this.deadlineOpen=true\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      if(this.deadlineForm.deadlineSwitch==1)\r\n      {\r\n        if(this.deadlineForm.deadlineDate==null)\r\n        {\r\n          this.$modal.msgError(\"截止日期不能为空\");\r\n          return\r\n        }\r\n        let deadlineDateCheck=this.deadlineForm.deadlineDate.split(\"/\")\r\n        if(deadlineDateCheck.length!=3)\r\n        {\r\n          this.$modal.msgError(\"截止日期格式不正确，正确格式是 年/月/日 \");\r\n          return\r\n        }\r\n        if(!/^-?(0|([1-9]?\\d)|100)$/.test(deadlineDateCheck[0]))\r\n        {\r\n          this.$modal.msgError(\"截止日期中年应是在-100到100之间的整数\");\r\n          return\r\n        }\r\n        if(!/^-?(0|([0]?\\d)|11|12)$/.test(deadlineDateCheck[1]))\r\n        {\r\n          this.$modal.msgError(\"截止日期中月应是在-12到12之间的整数\");\r\n          return\r\n        }\r\n        if(!/^-?(0|([1-2]?\\d)|31|30)$/.test(deadlineDateCheck[2]))\r\n        {\r\n          this.$modal.msgError(\"截止日期中日应是在-31到31之间的整数\");\r\n          return\r\n        }\r\n      }\r\n      deadlinebranch(this.deadlineForm).then((response) => \r\n      {\r\n        this.msgSuccess(\"批量修改截止日期成功\");\r\n        this.deadlineOpen = false;\r\n      });\r\n    },\r\n\r\n    cancel() {\r\n      this.deadlineOpen = false;\r\n    },\r\n\r\n    getDetail(){\r\n    getRootListById({id : this.rootId,ruleType:this.rootRuleType}).then(res => {\r\n        this.detail = res.data;\r\n        if(this.detail == null || this.detail == undefined)this.detail = {}\r\n        console.log(this.detail)\r\n        this.$forceUpdate();\r\n      });\r\n    },\r\n    handleClose(){\r\n      this.drawer = false;\r\n      this.getList();\r\n      this.$forceUpdate();\r\n    },\r\n    handleExport(row){\r\n      this.query.rootId  = row.id;\r\n      this.query.title = row.dimensionalityName;\r\n      this.clickChangeTime();\r\n      this.exportOpen = true;\r\n    },\r\n\r\n    addClick() {\r\n      // this.form.deptId=parseInt(this.form.deptId.split(\",\")[-1])\r\n      addDimensionality(this.form).then((res) => {\r\n        this.newOpen = false;\r\n        this.getList();\r\n        this.form={};\r\n      });\r\n    },\r\n    exportData() {\r\n      if (\r\n        this.query.startDate == null ||\r\n        this.query.startDate == \"\"||\r\n        this.query.endDate == null||\r\n        this.query.endDate == \"\"\r\n      ) {\r\n        this.$notify.error({\r\n          title: \"错误\",\r\n          message: \"导出前请先输入开始结束时间\",\r\n        });\r\n        return;\r\n      }\r\n      this.query.noteShow=this.noteShow\r\n      this.downloadFile(\r\n        \"/web/TYjy/dimensionality/exportStatistics\",\r\n        {\r\n          ...this.query,\r\n        },\r\n        \"(\" +\r\n          this.query.startDate +\r\n          \"-\" +\r\n          this.query.endDate +\r\n          \")\" +\r\n          this.query.title +\r\n          `.xlsx`\r\n      );\r\n    },\r\n    exportDataPreview()\r\n    {\r\n      if (\r\n        this.query.startDate == null ||\r\n        this.query.startDate == \"\"||\r\n        this.query.endDate == null||\r\n        this.query.endDate == \"\"\r\n      ) {\r\n        this.$notify.error({\r\n          title: \"错误\",\r\n          message: \"导出前请先输入开始结束时间\",\r\n        });\r\n        return;\r\n      }\r\n      this.query.noteShow=this.noteShow\r\n      this.downloadXlsx(\r\n        \"/web/TYjy/dimensionality/exportStatistics\",\r\n        {\r\n          ...this.query,\r\n        },\r\n        this.dimensionalityName+\"(\" +this.specialFcDate+\r\n          \")\" +\r\n          `数据.xlsx`\r\n      ).then((blob) => {\r\n        let reader = new FileReader();\r\n        reader.readAsArrayBuffer(blob);\r\n        reader.onload = (evt) => {\r\n          this.customBlobContent=reader.result;\r\n          let ints = new Uint8Array(evt.target.result); //要使用读取的内容，所以将读取内容转化成Uint8Array\r\n          ints = ints.slice(0, blob.size);\r\n          let workBook = xlsx.read(ints, { type: \"array\" });\r\n          let sheetNames = workBook.SheetNames;\r\n          let sheetName = sheetNames[0];\r\n          let workSheet = workBook.Sheets[sheetName];\r\n          //获取Excle内容，并将空内容用空值保存\r\n          let excelTable = xlsx.utils.sheet_to_json(workSheet);\r\n          // 获取Excel头部\r\n          let tableThead = Array.from(Object.keys(excelTable[0])).map(\r\n            (item) => {\r\n              return item\r\n            }\r\n          );\r\n          this.excelData = excelTable;\r\n          this.exceltitle=tableThead\r\n          this.excelHtml= excelTable\r\n          this.searchopen = true;\r\n        }\r\n      });\r\n    },\r\n    exportMouthDataPreview(){\r\n      if (\r\n        this.query.startDate == null ||\r\n        this.query.startDate == \"\"||\r\n        this.query.endDate == null||\r\n        this.query.endDate == \"\"\r\n      ) {\r\n        this.$notify.error({\r\n          title: \"错误\",\r\n          message: \"导出前请先输入开始结束时间\",\r\n        });\r\n        return;\r\n      }\r\n      this.query.rootId = this.dimensionalityId\r\n      this.query.type=\"1\"\r\n      this.downloadXlsx(\r\n          \"/web/TYjy/answer/exportEverymouth\",\r\n          {\r\n            ...this.query,\r\n          },\r\n          this.dimensionalityName+\"(\" +this.specialFcDate+\r\n            \")\" +\r\n            `数据.xlsx`\r\n        ).then((blob) => {\r\n          let reader = new FileReader();\r\n          reader.readAsArrayBuffer(blob);\r\n          \r\n          reader.onload = (evt) => {\r\n            this.customBlobContent=reader.result;\r\n            let ints = new Uint8Array(evt.target.result); //要使用读取的内容，所以将读取内容转化成Uint8Array\r\n            ints = ints.slice(0, blob.size);\r\n            let workBook = xlsx.read(ints, { type: \"array\" });\r\n            let sheetNames = workBook.SheetNames;\r\n            let sheetName = sheetNames[0];\r\n            let workSheet = workBook.Sheets[sheetName];\r\n            //获取Excle内容，并将空内容用空值保存\r\n            let excelTable = xlsx.utils.sheet_to_json(workSheet);\r\n            // 获取Excel头部\r\n            let tableThead = Array.from(Object.keys(excelTable[0])).map(\r\n              (item) => {\r\n                return item\r\n              }\r\n            );\r\n            this.excelData = excelTable;\r\n            this.exceltitle=tableThead\r\n            this.excelHtml= excelTable\r\n            this.searchopen = true;\r\n          }\r\n        });\r\n    },\r\n    exportMouthData(){\r\n      if (\r\n        this.query.startDate == null ||\r\n        this.query.startDate == \"\"||\r\n        this.query.endDate == null||\r\n        this.query.endDate == \"\"\r\n      ) {\r\n        this.$notify.error({\r\n          title: \"错误\",\r\n          message: \"导出前请先输入开始结束时间\",\r\n        });\r\n        return;\r\n      }\r\n      this.query.rootId = this.dimensionalityId\r\n      this.query.type=\"1\"\r\n      this.downloadFile(\r\n        \"/web/TYjy/answer/exportEverymouth\",\r\n        {\r\n          ...this.query,\r\n        },\r\n        \"(\" +\r\n          this.query.startDate +\r\n          \"-\" +\r\n          this.query.endDate +\r\n          \")\" +\r\n          this.dimensionalityName +\r\n          `.xlsx`\r\n      );\r\n    },\r\n    onDateChange(){\r\n      console.log(this.dateValue)\r\n      if(this.dateValue != null && this.dateValue != \"\"){\r\n        this.query.startDate = this.dateValue[0] ;\r\n        this.query.endDate = this.dateValue[1];\r\n      }else{\r\n        this.query.startDate = \"\";\r\n        this.query.endDate = \"\";\r\n      }\r\n    },\r\n    toUpdateUsers(row){\r\n      const dimensionalityId = row.id;\r\n      this.$router.push(\"/dataReport/dimensionality-auth/dimensionalityPermission/\" + dimensionalityId);\r\n      // this.$router.go(0)\r\n    },\r\n    handleDateChange() {\r\n      this.getList();\r\n    },\r\n    handleaAdminList(row){\r\n\r\n      const dimensionalityId = row.id;\r\n      listPermission({dimensionalityId:dimensionalityId}).then((response) => {\r\n        this.userList = response.rows;\r\n        // this.total = response.total;\r\n        // this.loading = false;\r\n        this.adminOpen = true;\r\n      });\r\n\r\n      // const fcDate = this.queryParams.fcDate;\r\n      // const dimensionalityName = row.dimensionalityName;\r\n      // this.$router.push({ path: '/dataReport/adminfill-auth/adminfillstatus', query: { dimensionalityId:dimensionalityId, fcDate:fcDate,dimensionalityName:dimensionalityName} });\r\n    },\r\n    handlefill(row){\r\n      // const dimensionalityId = row.id;\r\n      // this.$router.push(\"/dataReport/adminfill-auth/adminfillstatus/\" + dimensionalityId);\r\n      const dimensionalityId = row.id;\r\n      const fcDate = this.queryParams.fcDate;\r\n      const dimensionalityName = row.dimensionalityName;\r\n      this.$router.push({ path: '/dataReport/adminfill-auth/adminfillstatus/'+ dimensionalityId, query: { dimensionalityId:dimensionalityId, fcDate:fcDate,dimensionalityName:dimensionalityName} });\r\n    },\r\n    handleAnswer(row){\r\n      // const dimensionalityId = row.id;\r\n      // this.$router.push(\"/dataReport/adminfill-auth/adminfillstatus/\" + dimensionalityId);\r\n      const dimensionalityId = row.id;\r\n      const fcDate = this.queryParams.fcDate;\r\n      const dimensionalityName= row.dimensionalityName;\r\n      this.$router.push({ path: '/dataReport/answerShow-auth/answerShow/'+ dimensionalityId, query: { dimensionalityId:dimensionalityId, fcDate:fcDate,dimensionalityName:dimensionalityName} });\r\n    },\r\n\r\n  handleSpecial(row){\r\n      // this.query.rootId  = row.id;\r\n      this.dimensionalityName = row.dimensionalityName;\r\n      this.dimensionalityId=row.id;\r\n      this.SpecialImportOpen = true;\r\n      \r\n    },\r\n  handleMouth(row){\r\n      // this.query.rootId  = row.id;\r\n      this.dimensionalityName = row.dimensionalityName;\r\n      this.dimensionalityId=row.id;\r\n      this.clickChangeTime();\r\n      this.mouthImportOpen = true;\r\n    },\r\n  downloadTemplateSpecialPreview(){\r\n    if (this.specialFcDate == null ) {\r\n        this.specialFcDate= this.queryParams.fcDate\r\n      }\r\n\r\n      // if (this.specialFcDate == null ) {\r\n      //   this.$notify.error({\r\n      //     title: \"错误\",\r\n      //     message: \"未选择时间\",\r\n      //   });\r\n      //   return;\r\n      // }\r\n      let queryImport={}\r\n      queryImport.rootId = this.dimensionalityId\r\n      queryImport.fcDate = this.specialFcDate\r\n      queryImport.type=\"1\"\r\n      let url=\"\"\r\n      if(this.dimensionalityName=='研究院目标指标一览')\r\n      {\r\n        url=\"/web/TYjy/answer/exportWithTemplate\"\r\n      }\r\n      else\r\n      {\r\n        url=\"/web/TYjy/answer/exportTemplateSpecial\"\r\n      }\r\n      this.downloadXlsx(\r\n          url,\r\n          {\r\n            ...queryImport,\r\n          },\r\n          this.dimensionalityName+\"(\" +this.specialFcDate+\r\n            \")\" +\r\n            `数据.xlsx`\r\n        ).then((blob) => {\r\n          let reader = new FileReader();\r\n          reader.readAsArrayBuffer(blob);\r\n          \r\n          reader.onload = (evt) => {\r\n            this.customBlobContent=reader.result;\r\n            let ints = new Uint8Array(evt.target.result); //要使用读取的内容，所以将读取内容转化成Uint8Array\r\n            ints = ints.slice(0, blob.size);\r\n            let workBook = xlsx.read(ints, { type: \"array\" });\r\n            let sheetNames = workBook.SheetNames;\r\n            let sheetName = sheetNames[0];\r\n            let workSheet = workBook.Sheets[sheetName];\r\n            //获取Excle内容，并将空内容用空值保存\r\n            let excelTable = xlsx.utils.sheet_to_json(workSheet);\r\n            // 获取Excel头部\r\n            let tableThead = Array.from(Object.keys(excelTable[0])).map(\r\n              (item) => {\r\n                return item\r\n              }\r\n            );\r\n            this.excelData = excelTable;\r\n            this.exceltitle=tableThead\r\n            this.excelHtml= excelTable\r\n            this.searchopen = true;\r\n          }\r\n        });\r\n  },\r\n  downloadTemplateSpecial(){\r\n      if (this.specialFcDate == null ) {\r\n        this.specialFcDate= this.queryParams.fcDate\r\n      }\r\n\r\n      // if (this.specialFcDate == null ) {\r\n      //   this.$notify.error({\r\n      //     title: \"错误\",\r\n      //     message: \"未选择时间\",\r\n      //   });\r\n      //   return;\r\n      // }\r\n      let queryImport={}\r\n      queryImport.rootId = this.dimensionalityId\r\n      queryImport.fcDate = this.specialFcDate\r\n      queryImport.type=\"1\"\r\n      let url=\"\"\r\n      if(this.dimensionalityName=='研究院目标指标一览')\r\n      {\r\n        url=\"/web/TYjy/answer/exportWithTemplate\"\r\n      }\r\n      else\r\n      {\r\n        url=\"/web/TYjy/answer/exportTemplateSpecial\"\r\n      }\r\n      this.downloadFile(\r\n        url,\r\n        {\r\n          ...queryImport,\r\n        },\r\n        this.dimensionalityName+\"(\" +this.specialFcDate+\r\n          \")\" +\r\n          `数据.xlsx`\r\n      );\r\n    },\r\n\r\n    //此处为按钮判断管理\r\n    containsSubstring(substring, string) \r\n    {\r\n      return string.includes(substring);\r\n    },\r\n    //支持预览和导出\r\n    mouthCheck(string)\r\n    {\r\n      if(string== '六化指标')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '工装上线验收合格率统计')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '2025年经济责任制技经指标')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '研究院技经提升指标跟踪')\r\n      {\r\n        return true;\r\n      }\r\n      return false;\r\n    },\r\n    //支持单周期导出\r\n    aloneList(string) {\r\n      if(string== '气体结算月报')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '高炉、转炉煤气月报表')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '天然气消耗月报表')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '蒸汽消耗月报表')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '电量月报表')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '特板事业部2025年经济责任制奖罚汇总')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '研究院目标指标一览')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '安全责任工资考核表')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '安全责任工资考核汇总')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '水处理水量报表')\r\n      {\r\n        return true;\r\n      }\r\n      return false;\r\n    },\r\n\r\n    //数据预览模块处理\r\n    handlePreview() {\r\n      let queryImport={}\r\n      queryImport.rootId = this.queryParams.dimensionalityId\r\n      queryImport.fcDate = this.queryParams.fcDate\r\n      queryImport.type=\"1\"\r\n      if(this.dimensionalityName=='研究院目标指标一览')\r\n      {\r\n          this.downloadXlsx(\r\n          \"/web/TYjy/answer/exportWithTemplate\",\r\n          {\r\n            ...queryImport,\r\n          },\r\n          this.dimensionalityName+\"(\" +this.specialFcDate+\r\n            \")\" +\r\n            `数据.xlsx`\r\n        ).then((blob) => {\r\n          let reader = new FileReader();\r\n          reader.readAsArrayBuffer(blob);\r\n          \r\n          reader.onload = (evt) => {\r\n            this.customBlobContent=reader.result;\r\n            let ints = new Uint8Array(evt.target.result); //要使用读取的内容，所以将读取内容转化成Uint8Array\r\n            ints = ints.slice(0, blob.size);\r\n            let workBook = xlsx.read(ints, { type: \"array\" });\r\n            let sheetNames = workBook.SheetNames;\r\n            let sheetName = sheetNames[0];\r\n            let workSheet = workBook.Sheets[sheetName];\r\n            //获取Excle内容，并将空内容用空值保存\r\n            let excelTable = xlsx.utils.sheet_to_json(workSheet);\r\n            // 获取Excel头部\r\n            let tableThead = Array.from(Object.keys(excelTable[0])).map(\r\n              (item) => {\r\n                return item\r\n              }\r\n            );\r\n            this.excelData = excelTable;\r\n            this.exceltitle=tableThead\r\n            this.excelHtml= excelTable\r\n            this.searchopen = true;\r\n          }\r\n        });\r\n      }\r\n      else\r\n      {\r\n        this.downloadXlsx(\r\n        \"/web/TYjy/answer/exportTemplateSpecial\",\r\n        {\r\n          ...queryImport,\r\n        },\r\n        this.dimensionalityName+\"(\" +this.specialFcDate+\r\n          \")\" +\r\n          `数据.xlsx`\r\n      ).then((blob) => {\r\n        let reader = new FileReader();\r\n        reader.readAsArrayBuffer(blob);\r\n        reader.onload = (evt) => {\r\n          this.customBlobContent=reader.result;\r\n          let ints = new Uint8Array(evt.target.result); //要使用读取的内容，所以将读取内容转化成Uint8Array\r\n          ints = ints.slice(0, blob.size);\r\n          let workBook = xlsx.read(ints, { type: \"array\" });\r\n          let sheetNames = workBook.SheetNames;\r\n          let sheetName = sheetNames[0];\r\n          let workSheet = workBook.Sheets[sheetName];\r\n          //获取Excle内容，并将空内容用空值保存\r\n          let excelTable = xlsx.utils.sheet_to_json(workSheet);\r\n          // 获取Excel头部\r\n          let tableThead = Array.from(Object.keys(excelTable[0])).map(\r\n            (item) => {\r\n              return item\r\n            }\r\n          );\r\n          this.excelData = excelTable;\r\n          this.exceltitle=tableThead\r\n          this.excelHtml= excelTable\r\n          this.searchopen = true;\r\n        }\r\n      });\r\n      }\r\n    },\r\n    // 时间段预览\r\n    handlePreview1() {\r\n      // let queryImport={}\r\n      // queryImport.rootId = this.queryParams.dimensionalityId\r\n      // queryImport.fcDate = this.queryParams.fcDate\r\n      // queryImport.type=\"1\"\r\n        if (\r\n        this.queryImport.startDate == null ||\r\n        this.queryImport.startDate == \"\"||\r\n        this.queryImport.endDate == null||\r\n        this.queryImport.endDate == \"\"\r\n      ) {\r\n        this.$notify.error({\r\n          title: \"错误\",\r\n          message: \"导出前请先输入开始结束时间\",\r\n        });\r\n        return;\r\n      }\r\n      this.queryImport.rootId = this.queryParams.dimensionalityId\r\n      this.queryImport.type=\"1\"\r\n      this.downloadXlsx(\r\n        \"/web/TYjy/answer/exportTemplateNomral\",\r\n        {\r\n          ...this.queryImport,\r\n        },\r\n        this.dimensionalityName+\"(\" +this.specialFcDate+\r\n          \")\" +\r\n          `数据.xlsx`\r\n      ).then((blob) => {\r\n        let reader = new FileReader();\r\n        reader.readAsArrayBuffer(blob);\r\n        \r\n        reader.onload = (evt) => {\r\n          this.customBlobContent=reader.result;\r\n          let ints = new Uint8Array(evt.target.result); //要使用读取的内容，所以将读取内容转化成Uint8Array\r\n          ints = ints.slice(0, blob.size);\r\n          let workBook = xlsx.read(ints, { type: \"array\" });\r\n          let sheetNames = workBook.SheetNames;\r\n          let sheetName = sheetNames[0];\r\n          let workSheet = workBook.Sheets[sheetName];\r\n          //获取Excle内容，并将空内容用空值保存\r\n          let excelTable = xlsx.utils.sheet_to_json(workSheet);\r\n          // 获取Excel头部\r\n          let tableThead = Array.from(Object.keys(excelTable[0])).map(\r\n            (item) => {\r\n              return item\r\n            }\r\n          );\r\n          this.excelData = excelTable;\r\n          this.exceltitle=tableThead\r\n          this.excelHtml= excelTable\r\n          this.searchopen = true;\r\n        }\r\n      });\r\n    },\r\n\r\n    clickChangeTime()\r\n    {\r\n         let now =new Date();\r\n         this.query.startDate=this.getFirstOfYear(now);\r\n         this.query.endDate=this.getFirstOfMonth(now);\r\n         this.dateValue=[];\r\n         this.dateValue.push(this.query.startDate);\r\n         this.dateValue.push(this.query.endDate);\r\n    },\r\n    // 获取时间的优化处理\r\n    getFirstOfYear(now)\r\n    {\r\n      let  firstDayOfYear = new Date(now.getFullYear(), 0, 1);\r\n      return this.formatDate(firstDayOfYear);\r\n    },\r\n    getFirstOfMonth(now)\r\n    {\r\n      let firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);\r\n      return this.formatDate(firstDayOfMonth);\r\n    },\r\n    // 日期格式化函数（转为 yyyy-MM-dd）\r\n    formatDate(date) \r\n    {\r\n      const year = date.getFullYear();\r\n      const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从0开始需+1\r\n      const day = String(date.getDate()).padStart(2, '0');\r\n      return `${year}-${month}-${day}`;\r\n    },\r\n\r\n  },\r\n};\r\n</script>\r\n<style lang=\"less\">\r\n.v-modal {\r\n  display: none;\r\n}\r\n</style>\r\n  "], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0ZA,IAAAA,SAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,eAAA,GAAAD,OAAA;AAOA,IAAAE,yBAAA,GAAAF,OAAA;AAIA,IAAAG,KAAA,GAAAH,OAAA;AAKA,IAAAI,KAAA,GAAAJ,OAAA;AACA,IAAAK,MAAA,GAAAN,sBAAA,CAAAC,OAAA;AACA,IAAAM,IAAA,GAAAC,uBAAA,CAAAP,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCACA;EACAQ,IAAA;EACAC,UAAA;IACAC,QAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACAC,OAAA;MACAC,iBAAA;MACAC,eAAA;MACAC,UAAA;MACAC,KAAA;MACAC,SAAA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,kBAAA;QACAC,KAAA;MACA;MACAC,QAAA;MACAC,MAAA;MACAC,MAAA;MACAC,MAAA;MACAC,KAAA;QACAC,SAAA;QACAC,OAAA;QACAJ,MAAA;QACAK,KAAA;MACA;MACAC,UAAA;MACAC,YAAA;MACAC,aAAA;MACAC,YAAA,EACA;QACAC,kBAAA;MACA;MACAC,SAAA;MACAC,QAAA;MACAC,IAAA;MACAC,QAAA;MACAC,SAAA;MACAC,UAAA;MACAC,aAAA;MACArB,kBAAA;MACAsB,gBAAA;MACAC,QAAA;IAGA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACAC,SAAA,WAAAA,UAAAC,MAAA,EAAAC,IAAA;MACAD,MAAA,CAAAE,MAAA,CAAAC,aAAA,CAAAA,aAAA,CAAAC,iBAAA,CAAAC,KAAA;IACA;IACAT,OAAA,WAAAA,QAAA;MAAA,IAAAU,KAAA;MACA,KAAA7C,OAAA;MACA,IAAA8C,sCAAA,OAAAvC,WAAA,EAAAwC,IAAA,WAAAC,GAAA;QACAH,KAAA,CAAAjC,QAAA,GAAAoC,GAAA,CAAAC,IAAA;QACA,SAAAC,CAAA,MAAAA,CAAA,GAAAL,KAAA,CAAAjC,QAAA,CAAAuC,MAAA,EAAAD,CAAA,IACA;UACA,IAAAL,KAAA,CAAAjC,QAAA,CAAAsC,CAAA,EAAAE,EAAA,WAAAP,KAAA,CAAAjC,QAAA,CAAAsC,CAAA,EAAAE,EAAA,WAAAP,KAAA,CAAAjC,QAAA,CAAAsC,CAAA,EAAAE,EAAA,WAAAP,KAAA,CAAAjC,QAAA,CAAAsC,CAAA,EAAAE,EAAA,YAAAP,KAAA,CAAAjC,QAAA,CAAAsC,CAAA,EAAAE,EAAA,YAAAP,KAAA,CAAAQ,iBAAA,WAAAR,KAAA,CAAAjC,QAAA,CAAAsC,CAAA,EAAAxC,kBAAA,GACA;YACAmC,KAAA,CAAAjC,QAAA,CAAAsC,CAAA,EAAAI,QAAA;UACA,OAEA;YACAT,KAAA,CAAAjC,QAAA,CAAAsC,CAAA,EAAAI,QAAA;UACA;UACA,IAAAT,KAAA,CAAAQ,iBAAA,OAAAR,KAAA,CAAAjC,QAAA,CAAAsC,CAAA,EAAAxC,kBAAA,GACA;YACAmC,KAAA,CAAAjC,QAAA,CAAAsC,CAAA,EAAAK,SAAA;UACA,OAEA;YACAV,KAAA,CAAAjC,QAAA,CAAAsC,CAAA,EAAAK,SAAA;UACA;QACA;QACAV,KAAA,CAAAxC,KAAA,GAAA2C,GAAA,CAAA3C,KAAA;QACAwC,KAAA,CAAA7C,OAAA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACAoC,OAAA,WAAAA,QAAA;MAAA,IAAAoB,MAAA;MACA,IAAAC,cAAA,IAAAV,IAAA,WAAAC,GAAA;QACAQ,MAAA,CAAA9B,QAAA,GAAAsB,GAAA,CAAAC,IAAA,IAAAS,QAAA;QACAC,OAAA,CAAAC,GAAA,CAAAZ,GAAA;QACA,SAAAE,CAAA,MAAAA,CAAA,GAAAM,MAAA,CAAA9B,QAAA,CAAAyB,MAAA,EAAAD,CAAA,IACA;UACAM,MAAA,CAAAK,YAAA,CAAAL,MAAA,CAAA9B,QAAA,CAAAwB,CAAA;QACA;MACA;IACA;IACAW,YAAA,WAAAA,aAAAC,GAAA,EAAAC,KAAA,EACA;MACAD,GAAA,CAAAE,KAAA,GAAAF,GAAA,CAAAG,IAAA;MACAH,GAAA,CAAAI,KAAA,GAAAJ,GAAA,CAAAK,QAAA;MACA,IAAAL,GAAA,CAAAJ,QAAA,CAAAP,MAAA,QAAAY,KAAA,MACA;QACA,SAAAb,CAAA,MAAAA,CAAA,GAAAY,GAAA,CAAAJ,QAAA,CAAAP,MAAA,EAAAD,CAAA,IACA;UACA,KAAAW,YAAA,CAAAC,GAAA,CAAAJ,QAAA,CAAAR,CAAA,GAAAa,KAAA;QACA;MACA,OAEA;QACAD,GAAA,CAAAJ,QAAA;MACA;IACA;IACAU,eAAA,WAAAA,gBAAA;MACA,KAAAC,KAAA,CAAAC,cAAA,CAAAC,eAAA;MACA,KAAAhE,WAAA,CAAAC,OAAA;MACA,KAAA2B,OAAA;IACA;IAEA,aACAqC,UAAA,WAAAA,WAAA;MACA,KAAAC,SAAA;MACA,KAAAC,WAAA;IACA;IACA,aACAA,WAAA,WAAAA,YAAA;MACA,KAAAnE,WAAA,CAAAC,OAAA;MACA,KAAA2B,OAAA;IACA;IAGAwC,SAAA,WAAAA,UAAA;MACA,KAAA1E,OAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACA2E,YAAA,WAAAA,aAAAd,GAAA;MACA,KAAAhD,MAAA,GAAAgD,GAAA,CAAAV,EAAA;MACA,KAAAyB,YAAA,GAAAf,GAAA,CAAAgB,QAAA;MACA,KAAAC,SAAA;MACA,KAAAhE,MAAA;IACA;IACAiE,cAAA,WAAAA,eAAAlB,GAAA;MACA,KAAAvC,YAAA;QAAAC,kBAAA;MAAA;MACA,KAAAD,YAAA,CAAAC,kBAAA,GAAAsC,GAAA,CAAAG,IAAA;MACA,KAAA5C,YAAA;IACA;IACA,WACA4D,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,SAAA3D,YAAA,CAAA4D,cAAA,OACA;QACA,SAAA5D,YAAA,CAAA6D,YAAA,UACA;UACA,KAAAC,MAAA,CAAAC,QAAA;UACA;QACA;QACA,IAAAC,iBAAA,QAAAhE,YAAA,CAAA6D,YAAA,CAAAI,KAAA;QACA,IAAAD,iBAAA,CAAApC,MAAA,OACA;UACA,KAAAkC,MAAA,CAAAC,QAAA;UACA;QACA;QACA,8BAAAG,IAAA,CAAAF,iBAAA,MACA;UACA,KAAAF,MAAA,CAAAC,QAAA;UACA;QACA;QACA,8BAAAG,IAAA,CAAAF,iBAAA,MACA;UACA,KAAAF,MAAA,CAAAC,QAAA;UACA;QACA;QACA,gCAAAG,IAAA,CAAAF,iBAAA,MACA;UACA,KAAAF,MAAA,CAAAC,QAAA;UACA;QACA;MACA;MACA,IAAAI,oBAAA,OAAAnE,YAAA,EAAAwB,IAAA,WAAA4C,QAAA,EACA;QACAT,MAAA,CAAAU,UAAA;QACAV,MAAA,CAAA7D,YAAA;MACA;IACA;IAEAwE,MAAA,WAAAA,OAAA;MACA,KAAAxE,YAAA;IACA;IAEA0D,SAAA,WAAAA,UAAA;MAAA,IAAAe,MAAA;MACA,IAAAC,+BAAA;QAAA3C,EAAA,OAAAtC,MAAA;QAAAgE,QAAA,OAAAD;MAAA,GAAA9B,IAAA,WAAAC,GAAA;QACA8C,MAAA,CAAAjF,MAAA,GAAAmC,GAAA,CAAAjD,IAAA;QACA,IAAA+F,MAAA,CAAAjF,MAAA,YAAAiF,MAAA,CAAAjF,MAAA,IAAAmF,SAAA,EAAAF,MAAA,CAAAjF,MAAA;QACA8C,OAAA,CAAAC,GAAA,CAAAkC,MAAA,CAAAjF,MAAA;QACAiF,MAAA,CAAAG,YAAA;MACA;IACA;IACAC,WAAA,WAAAA,YAAA;MACA,KAAAnF,MAAA;MACA,KAAAoB,OAAA;MACA,KAAA8D,YAAA;IACA;IACAE,YAAA,WAAAA,aAAArC,GAAA;MACA,KAAA9C,KAAA,CAAAF,MAAA,GAAAgD,GAAA,CAAAV,EAAA;MACA,KAAApC,KAAA,CAAAG,KAAA,GAAA2C,GAAA,CAAApD,kBAAA;MACA,KAAA0F,eAAA;MACA,KAAAhF,UAAA;IACA;IAEAiF,QAAA,WAAAA,SAAA;MAAA,IAAAC,MAAA;MACA;MACA,IAAAC,iCAAA,OAAA5E,IAAA,EAAAoB,IAAA,WAAAC,GAAA;QACAsD,MAAA,CAAArG,OAAA;QACAqG,MAAA,CAAAnE,OAAA;QACAmE,MAAA,CAAA3E,IAAA;MACA;IACA;IACA6E,UAAA,WAAAA,WAAA;MACA,IACA,KAAAxF,KAAA,CAAAC,SAAA,YACA,KAAAD,KAAA,CAAAC,SAAA,UACA,KAAAD,KAAA,CAAAE,OAAA,YACA,KAAAF,KAAA,CAAAE,OAAA,QACA;QACA,KAAAuF,OAAA,CAAAC,KAAA;UACAvF,KAAA;UACAwF,OAAA;QACA;QACA;MACA;MACA,KAAA3F,KAAA,CAAAiB,QAAA,QAAAA,QAAA;MACA,KAAA2E,YAAA,CACA,iDAAAC,cAAA,CAAAC,OAAA,MAEA,KAAA9F,KAAA,GAEA,MACA,KAAAA,KAAA,CAAAC,SAAA,GACA,MACA,KAAAD,KAAA,CAAAE,OAAA,GACA,MACA,KAAAF,KAAA,CAAAG,KAAA,UAEA;IACA;IACA4F,iBAAA,WAAAA,kBAAA,EACA;MAAA,IAAAC,MAAA;MACA,IACA,KAAAhG,KAAA,CAAAC,SAAA,YACA,KAAAD,KAAA,CAAAC,SAAA,UACA,KAAAD,KAAA,CAAAE,OAAA,YACA,KAAAF,KAAA,CAAAE,OAAA,QACA;QACA,KAAAuF,OAAA,CAAAC,KAAA;UACAvF,KAAA;UACAwF,OAAA;QACA;QACA;MACA;MACA,KAAA3F,KAAA,CAAAiB,QAAA,QAAAA,QAAA;MACA,KAAAgF,YAAA,CACA,iDAAAJ,cAAA,CAAAC,OAAA,MAEA,KAAA9F,KAAA,GAEA,KAAAN,kBAAA,cAAAqB,aAAA,GACA,yBAEA,EAAAgB,IAAA,WAAAmE,IAAA;QACA,IAAAC,MAAA,OAAAC,UAAA;QACAD,MAAA,CAAAE,iBAAA,CAAAH,IAAA;QACAC,MAAA,CAAAG,MAAA,aAAAC,GAAA;UACAP,MAAA,CAAAQ,iBAAA,GAAAL,MAAA,CAAAM,MAAA;UACA,IAAAC,IAAA,OAAAC,UAAA,CAAAJ,GAAA,CAAA9E,MAAA,CAAAgF,MAAA;UACAC,IAAA,GAAAA,IAAA,CAAAE,KAAA,IAAAV,IAAA,CAAAW,IAAA;UACA,IAAAC,QAAA,GAAApI,IAAA,CAAAqI,IAAA,CAAAL,IAAA;YAAAM,IAAA;UAAA;UACA,IAAAC,UAAA,GAAAH,QAAA,CAAAI,UAAA;UACA,IAAAC,SAAA,GAAAF,UAAA;UACA,IAAAG,SAAA,GAAAN,QAAA,CAAAO,MAAA,CAAAF,SAAA;UACA;UACA,IAAAG,UAAA,GAAA5I,IAAA,CAAA6I,KAAA,CAAAC,aAAA,CAAAJ,SAAA;UACA;UACA,IAAAK,UAAA,GAAAC,KAAA,CAAAC,IAAA,CAAAC,MAAA,CAAAC,IAAA,CAAAP,UAAA,MAAAQ,GAAA,CACA,UAAAC,IAAA;YACA,OAAAA,IAAA;UACA,CACA;UACA/B,MAAA,CAAAgC,SAAA,GAAAV,UAAA;UACAtB,MAAA,CAAAiC,UAAA,GAAAR,UAAA;UACAzB,MAAA,CAAAkC,SAAA,GAAAZ,UAAA;UACAtB,MAAA,CAAA5G,UAAA;QACA;MACA;IACA;IACA+I,sBAAA,WAAAA,uBAAA;MAAA,IAAAC,MAAA;MACA,IACA,KAAApI,KAAA,CAAAC,SAAA,YACA,KAAAD,KAAA,CAAAC,SAAA,UACA,KAAAD,KAAA,CAAAE,OAAA,YACA,KAAAF,KAAA,CAAAE,OAAA,QACA;QACA,KAAAuF,OAAA,CAAAC,KAAA;UACAvF,KAAA;UACAwF,OAAA;QACA;QACA;MACA;MACA,KAAA3F,KAAA,CAAAF,MAAA,QAAAkB,gBAAA;MACA,KAAAhB,KAAA,CAAAgH,IAAA;MACA,KAAAf,YAAA,CACA,yCAAAJ,cAAA,CAAAC,OAAA,MAEA,KAAA9F,KAAA,GAEA,KAAAN,kBAAA,cAAAqB,aAAA,GACA,yBAEA,EAAAgB,IAAA,WAAAmE,IAAA;QACA,IAAAC,MAAA,OAAAC,UAAA;QACAD,MAAA,CAAAE,iBAAA,CAAAH,IAAA;QAEAC,MAAA,CAAAG,MAAA,aAAAC,GAAA;UACA6B,MAAA,CAAA5B,iBAAA,GAAAL,MAAA,CAAAM,MAAA;UACA,IAAAC,IAAA,OAAAC,UAAA,CAAAJ,GAAA,CAAA9E,MAAA,CAAAgF,MAAA;UACAC,IAAA,GAAAA,IAAA,CAAAE,KAAA,IAAAV,IAAA,CAAAW,IAAA;UACA,IAAAC,QAAA,GAAApI,IAAA,CAAAqI,IAAA,CAAAL,IAAA;YAAAM,IAAA;UAAA;UACA,IAAAC,UAAA,GAAAH,QAAA,CAAAI,UAAA;UACA,IAAAC,SAAA,GAAAF,UAAA;UACA,IAAAG,SAAA,GAAAN,QAAA,CAAAO,MAAA,CAAAF,SAAA;UACA;UACA,IAAAG,UAAA,GAAA5I,IAAA,CAAA6I,KAAA,CAAAC,aAAA,CAAAJ,SAAA;UACA;UACA,IAAAK,UAAA,GAAAC,KAAA,CAAAC,IAAA,CAAAC,MAAA,CAAAC,IAAA,CAAAP,UAAA,MAAAQ,GAAA,CACA,UAAAC,IAAA;YACA,OAAAA,IAAA;UACA,CACA;UACAK,MAAA,CAAAJ,SAAA,GAAAV,UAAA;UACAc,MAAA,CAAAH,UAAA,GAAAR,UAAA;UACAW,MAAA,CAAAF,SAAA,GAAAZ,UAAA;UACAc,MAAA,CAAAhJ,UAAA;QACA;MACA;IACA;IACAiJ,eAAA,WAAAA,gBAAA;MACA,IACA,KAAArI,KAAA,CAAAC,SAAA,YACA,KAAAD,KAAA,CAAAC,SAAA,UACA,KAAAD,KAAA,CAAAE,OAAA,YACA,KAAAF,KAAA,CAAAE,OAAA,QACA;QACA,KAAAuF,OAAA,CAAAC,KAAA;UACAvF,KAAA;UACAwF,OAAA;QACA;QACA;MACA;MACA,KAAA3F,KAAA,CAAAF,MAAA,QAAAkB,gBAAA;MACA,KAAAhB,KAAA,CAAAgH,IAAA;MACA,KAAApB,YAAA,CACA,yCAAAC,cAAA,CAAAC,OAAA,MAEA,KAAA9F,KAAA,GAEA,MACA,KAAAA,KAAA,CAAAC,SAAA,GACA,MACA,KAAAD,KAAA,CAAAE,OAAA,GACA,MACA,KAAAR,kBAAA,UAEA;IACA;IACA4I,YAAA,WAAAA,aAAA;MACA3F,OAAA,CAAAC,GAAA,MAAAnC,SAAA;MACA,SAAAA,SAAA,iBAAAA,SAAA;QACA,KAAAT,KAAA,CAAAC,SAAA,QAAAQ,SAAA;QACA,KAAAT,KAAA,CAAAE,OAAA,QAAAO,SAAA;MACA;QACA,KAAAT,KAAA,CAAAC,SAAA;QACA,KAAAD,KAAA,CAAAE,OAAA;MACA;IACA;IACAqI,aAAA,WAAAA,cAAAzF,GAAA;MACA,IAAA9B,gBAAA,GAAA8B,GAAA,CAAAV,EAAA;MACA,KAAAoG,OAAA,CAAAC,IAAA,+DAAAzH,gBAAA;MACA;IACA;IACA0H,gBAAA,WAAAA,iBAAA;MACA,KAAAvH,OAAA;IACA;IACAwH,gBAAA,WAAAA,iBAAA7F,GAAA;MAAA,IAAA8F,MAAA;MAEA,IAAA5H,gBAAA,GAAA8B,GAAA,CAAAV,EAAA;MACA,IAAAyG,wCAAA;QAAA7H,gBAAA,EAAAA;MAAA,GAAAe,IAAA,WAAA4C,QAAA;QACAiE,MAAA,CAAAhI,QAAA,GAAA+D,QAAA,CAAA1C,IAAA;QACA;QACA;QACA2G,MAAA,CAAA/H,SAAA;MACA;;MAEA;MACA;MACA;IACA;IACAiI,UAAA,WAAAA,WAAAhG,GAAA;MACA;MACA;MACA,IAAA9B,gBAAA,GAAA8B,GAAA,CAAAV,EAAA;MACA,IAAA2G,MAAA,QAAAxJ,WAAA,CAAAwJ,MAAA;MACA,IAAArJ,kBAAA,GAAAoD,GAAA,CAAApD,kBAAA;MACA,KAAA8I,OAAA,CAAAC,IAAA;QAAAxF,IAAA,kDAAAjC,gBAAA;QAAAhB,KAAA;UAAAgB,gBAAA,EAAAA,gBAAA;UAAA+H,MAAA,EAAAA,MAAA;UAAArJ,kBAAA,EAAAA;QAAA;MAAA;IACA;IACAsJ,YAAA,WAAAA,aAAAlG,GAAA;MACA;MACA;MACA,IAAA9B,gBAAA,GAAA8B,GAAA,CAAAV,EAAA;MACA,IAAA2G,MAAA,QAAAxJ,WAAA,CAAAwJ,MAAA;MACA,IAAArJ,kBAAA,GAAAoD,GAAA,CAAApD,kBAAA;MACA,KAAA8I,OAAA,CAAAC,IAAA;QAAAxF,IAAA,8CAAAjC,gBAAA;QAAAhB,KAAA;UAAAgB,gBAAA,EAAAA,gBAAA;UAAA+H,MAAA,EAAAA,MAAA;UAAArJ,kBAAA,EAAAA;QAAA;MAAA;IACA;IAEAuJ,aAAA,WAAAA,cAAAnG,GAAA;MACA;MACA,KAAApD,kBAAA,GAAAoD,GAAA,CAAApD,kBAAA;MACA,KAAAsB,gBAAA,GAAA8B,GAAA,CAAAV,EAAA;MACA,KAAAlD,iBAAA;IAEA;IACAgK,WAAA,WAAAA,YAAApG,GAAA;MACA;MACA,KAAApD,kBAAA,GAAAoD,GAAA,CAAApD,kBAAA;MACA,KAAAsB,gBAAA,GAAA8B,GAAA,CAAAV,EAAA;MACA,KAAAgD,eAAA;MACA,KAAAjG,eAAA;IACA;IACAgK,8BAAA,WAAAA,+BAAA;MAAA,IAAAC,MAAA;MACA,SAAArI,aAAA;QACA,KAAAA,aAAA,QAAAxB,WAAA,CAAAwJ,MAAA;MACA;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAAM,WAAA;MACAA,WAAA,CAAAvJ,MAAA,QAAAkB,gBAAA;MACAqI,WAAA,CAAAN,MAAA,QAAAhI,aAAA;MACAsI,WAAA,CAAArC,IAAA;MACA,IAAAsC,GAAA;MACA,SAAA5J,kBAAA,iBACA;QACA4J,GAAA;MACA,OAEA;QACAA,GAAA;MACA;MACA,KAAArD,YAAA,CACAqD,GAAA,MAAAzD,cAAA,CAAAC,OAAA,MAEAuD,WAAA,GAEA,KAAA3J,kBAAA,cAAAqB,aAAA,GACA,yBAEA,EAAAgB,IAAA,WAAAmE,IAAA;QACA,IAAAC,MAAA,OAAAC,UAAA;QACAD,MAAA,CAAAE,iBAAA,CAAAH,IAAA;QAEAC,MAAA,CAAAG,MAAA,aAAAC,GAAA;UACA6C,MAAA,CAAA5C,iBAAA,GAAAL,MAAA,CAAAM,MAAA;UACA,IAAAC,IAAA,OAAAC,UAAA,CAAAJ,GAAA,CAAA9E,MAAA,CAAAgF,MAAA;UACAC,IAAA,GAAAA,IAAA,CAAAE,KAAA,IAAAV,IAAA,CAAAW,IAAA;UACA,IAAAC,QAAA,GAAApI,IAAA,CAAAqI,IAAA,CAAAL,IAAA;YAAAM,IAAA;UAAA;UACA,IAAAC,UAAA,GAAAH,QAAA,CAAAI,UAAA;UACA,IAAAC,SAAA,GAAAF,UAAA;UACA,IAAAG,SAAA,GAAAN,QAAA,CAAAO,MAAA,CAAAF,SAAA;UACA;UACA,IAAAG,UAAA,GAAA5I,IAAA,CAAA6I,KAAA,CAAAC,aAAA,CAAAJ,SAAA;UACA;UACA,IAAAK,UAAA,GAAAC,KAAA,CAAAC,IAAA,CAAAC,MAAA,CAAAC,IAAA,CAAAP,UAAA,MAAAQ,GAAA,CACA,UAAAC,IAAA;YACA,OAAAA,IAAA;UACA,CACA;UACAqB,MAAA,CAAApB,SAAA,GAAAV,UAAA;UACA8B,MAAA,CAAAnB,UAAA,GAAAR,UAAA;UACA2B,MAAA,CAAAlB,SAAA,GAAAZ,UAAA;UACA8B,MAAA,CAAAhK,UAAA;QACA;MACA;IACA;IACAmK,uBAAA,WAAAA,wBAAA;MACA,SAAAxI,aAAA;QACA,KAAAA,aAAA,QAAAxB,WAAA,CAAAwJ,MAAA;MACA;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAAM,WAAA;MACAA,WAAA,CAAAvJ,MAAA,QAAAkB,gBAAA;MACAqI,WAAA,CAAAN,MAAA,QAAAhI,aAAA;MACAsI,WAAA,CAAArC,IAAA;MACA,IAAAsC,GAAA;MACA,SAAA5J,kBAAA,iBACA;QACA4J,GAAA;MACA,OAEA;QACAA,GAAA;MACA;MACA,KAAA1D,YAAA,CACA0D,GAAA,MAAAzD,cAAA,CAAAC,OAAA,MAEAuD,WAAA,GAEA,KAAA3J,kBAAA,cAAAqB,aAAA,GACA,yBAEA;IACA;IAEA;IACAsB,iBAAA,WAAAA,kBAAAmH,SAAA,EAAAC,MAAA,EACA;MACA,OAAAA,MAAA,CAAAC,QAAA,CAAAF,SAAA;IACA;IACA;IACAG,UAAA,WAAAA,WAAAF,MAAA,EACA;MACA,IAAAA,MAAA,YACA;QACA;MACA;MACA,IAAAA,MAAA,mBACA;QACA;MACA;MACA,IAAAA,MAAA,sBACA;QACA;MACA;MACA,IAAAA,MAAA,mBACA;QACA;MACA;MACA;IACA;IACA;IACAG,SAAA,WAAAA,UAAAH,MAAA;MACA,IAAAA,MAAA,cACA;QACA;MACA;MACA,IAAAA,MAAA,kBACA;QACA;MACA;MACA,IAAAA,MAAA,gBACA;QACA;MACA;MACA,IAAAA,MAAA,eACA;QACA;MACA;MACA,IAAAA,MAAA,aACA;QACA;MACA;MACA,IAAAA,MAAA,2BACA;QACA;MACA;MACA,IAAAA,MAAA,iBACA;QACA;MACA;MACA,IAAAA,MAAA,iBACA;QACA;MACA;MACA,IAAAA,MAAA,kBACA;QACA;MACA;MACA,IAAAA,MAAA,eACA;QACA;MACA;MACA;IACA;IAEA;IACAI,aAAA,WAAAA,cAAA;MAAA,IAAAC,MAAA;MACA,IAAAT,WAAA;MACAA,WAAA,CAAAvJ,MAAA,QAAAP,WAAA,CAAAyB,gBAAA;MACAqI,WAAA,CAAAN,MAAA,QAAAxJ,WAAA,CAAAwJ,MAAA;MACAM,WAAA,CAAArC,IAAA;MACA,SAAAtH,kBAAA,iBACA;QACA,KAAAuG,YAAA,CACA,2CAAAJ,cAAA,CAAAC,OAAA,MAEAuD,WAAA,GAEA,KAAA3J,kBAAA,cAAAqB,aAAA,GACA,yBAEA,EAAAgB,IAAA,WAAAmE,IAAA;UACA,IAAAC,MAAA,OAAAC,UAAA;UACAD,MAAA,CAAAE,iBAAA,CAAAH,IAAA;UAEAC,MAAA,CAAAG,MAAA,aAAAC,GAAA;YACAuD,MAAA,CAAAtD,iBAAA,GAAAL,MAAA,CAAAM,MAAA;YACA,IAAAC,IAAA,OAAAC,UAAA,CAAAJ,GAAA,CAAA9E,MAAA,CAAAgF,MAAA;YACAC,IAAA,GAAAA,IAAA,CAAAE,KAAA,IAAAV,IAAA,CAAAW,IAAA;YACA,IAAAC,QAAA,GAAApI,IAAA,CAAAqI,IAAA,CAAAL,IAAA;cAAAM,IAAA;YAAA;YACA,IAAAC,UAAA,GAAAH,QAAA,CAAAI,UAAA;YACA,IAAAC,SAAA,GAAAF,UAAA;YACA,IAAAG,SAAA,GAAAN,QAAA,CAAAO,MAAA,CAAAF,SAAA;YACA;YACA,IAAAG,UAAA,GAAA5I,IAAA,CAAA6I,KAAA,CAAAC,aAAA,CAAAJ,SAAA;YACA;YACA,IAAAK,UAAA,GAAAC,KAAA,CAAAC,IAAA,CAAAC,MAAA,CAAAC,IAAA,CAAAP,UAAA,MAAAQ,GAAA,CACA,UAAAC,IAAA;cACA,OAAAA,IAAA;YACA,CACA;YACA+B,MAAA,CAAA9B,SAAA,GAAAV,UAAA;YACAwC,MAAA,CAAA7B,UAAA,GAAAR,UAAA;YACAqC,MAAA,CAAA5B,SAAA,GAAAZ,UAAA;YACAwC,MAAA,CAAA1K,UAAA;UACA;QACA;MACA,OAEA;QACA,KAAA6G,YAAA,CACA,8CAAAJ,cAAA,CAAAC,OAAA,MAEAuD,WAAA,GAEA,KAAA3J,kBAAA,cAAAqB,aAAA,GACA,yBAEA,EAAAgB,IAAA,WAAAmE,IAAA;UACA,IAAAC,MAAA,OAAAC,UAAA;UACAD,MAAA,CAAAE,iBAAA,CAAAH,IAAA;UACAC,MAAA,CAAAG,MAAA,aAAAC,GAAA;YACAuD,MAAA,CAAAtD,iBAAA,GAAAL,MAAA,CAAAM,MAAA;YACA,IAAAC,IAAA,OAAAC,UAAA,CAAAJ,GAAA,CAAA9E,MAAA,CAAAgF,MAAA;YACAC,IAAA,GAAAA,IAAA,CAAAE,KAAA,IAAAV,IAAA,CAAAW,IAAA;YACA,IAAAC,QAAA,GAAApI,IAAA,CAAAqI,IAAA,CAAAL,IAAA;cAAAM,IAAA;YAAA;YACA,IAAAC,UAAA,GAAAH,QAAA,CAAAI,UAAA;YACA,IAAAC,SAAA,GAAAF,UAAA;YACA,IAAAG,SAAA,GAAAN,QAAA,CAAAO,MAAA,CAAAF,SAAA;YACA;YACA,IAAAG,UAAA,GAAA5I,IAAA,CAAA6I,KAAA,CAAAC,aAAA,CAAAJ,SAAA;YACA;YACA,IAAAK,UAAA,GAAAC,KAAA,CAAAC,IAAA,CAAAC,MAAA,CAAAC,IAAA,CAAAP,UAAA,MAAAQ,GAAA,CACA,UAAAC,IAAA;cACA,OAAAA,IAAA;YACA,CACA;YACA+B,MAAA,CAAA9B,SAAA,GAAAV,UAAA;YACAwC,MAAA,CAAA7B,UAAA,GAAAR,UAAA;YACAqC,MAAA,CAAA5B,SAAA,GAAAZ,UAAA;YACAwC,MAAA,CAAA1K,UAAA;UACA;QACA;MACA;IACA;IACA;IACA2K,cAAA,WAAAA,eAAA;MAAA,IAAAC,MAAA;MACA;MACA;MACA;MACA;MACA,IACA,KAAAX,WAAA,CAAApJ,SAAA,YACA,KAAAoJ,WAAA,CAAApJ,SAAA,UACA,KAAAoJ,WAAA,CAAAnJ,OAAA,YACA,KAAAmJ,WAAA,CAAAnJ,OAAA,QACA;QACA,KAAAuF,OAAA,CAAAC,KAAA;UACAvF,KAAA;UACAwF,OAAA;QACA;QACA;MACA;MACA,KAAA0D,WAAA,CAAAvJ,MAAA,QAAAP,WAAA,CAAAyB,gBAAA;MACA,KAAAqI,WAAA,CAAArC,IAAA;MACA,KAAAf,YAAA,CACA,6CAAAJ,cAAA,CAAAC,OAAA,MAEA,KAAAuD,WAAA,GAEA,KAAA3J,kBAAA,cAAAqB,aAAA,GACA,yBAEA,EAAAgB,IAAA,WAAAmE,IAAA;QACA,IAAAC,MAAA,OAAAC,UAAA;QACAD,MAAA,CAAAE,iBAAA,CAAAH,IAAA;QAEAC,MAAA,CAAAG,MAAA,aAAAC,GAAA;UACAyD,MAAA,CAAAxD,iBAAA,GAAAL,MAAA,CAAAM,MAAA;UACA,IAAAC,IAAA,OAAAC,UAAA,CAAAJ,GAAA,CAAA9E,MAAA,CAAAgF,MAAA;UACAC,IAAA,GAAAA,IAAA,CAAAE,KAAA,IAAAV,IAAA,CAAAW,IAAA;UACA,IAAAC,QAAA,GAAApI,IAAA,CAAAqI,IAAA,CAAAL,IAAA;YAAAM,IAAA;UAAA;UACA,IAAAC,UAAA,GAAAH,QAAA,CAAAI,UAAA;UACA,IAAAC,SAAA,GAAAF,UAAA;UACA,IAAAG,SAAA,GAAAN,QAAA,CAAAO,MAAA,CAAAF,SAAA;UACA;UACA,IAAAG,UAAA,GAAA5I,IAAA,CAAA6I,KAAA,CAAAC,aAAA,CAAAJ,SAAA;UACA;UACA,IAAAK,UAAA,GAAAC,KAAA,CAAAC,IAAA,CAAAC,MAAA,CAAAC,IAAA,CAAAP,UAAA,MAAAQ,GAAA,CACA,UAAAC,IAAA;YACA,OAAAA,IAAA;UACA,CACA;UACAiC,MAAA,CAAAhC,SAAA,GAAAV,UAAA;UACA0C,MAAA,CAAA/B,UAAA,GAAAR,UAAA;UACAuC,MAAA,CAAA9B,SAAA,GAAAZ,UAAA;UACA0C,MAAA,CAAA5K,UAAA;QACA;MACA;IACA;IAEAgG,eAAA,WAAAA,gBAAA,EACA;MACA,IAAA6E,GAAA,OAAAC,IAAA;MACA,KAAAlK,KAAA,CAAAC,SAAA,QAAAkK,cAAA,CAAAF,GAAA;MACA,KAAAjK,KAAA,CAAAE,OAAA,QAAAkK,eAAA,CAAAH,GAAA;MACA,KAAAxJ,SAAA;MACA,KAAAA,SAAA,CAAAgI,IAAA,MAAAzI,KAAA,CAAAC,SAAA;MACA,KAAAQ,SAAA,CAAAgI,IAAA,MAAAzI,KAAA,CAAAE,OAAA;IACA;IACA;IACAiK,cAAA,WAAAA,eAAAF,GAAA,EACA;MACA,IAAAI,cAAA,OAAAH,IAAA,CAAAD,GAAA,CAAAK,WAAA;MACA,YAAAC,UAAA,CAAAF,cAAA;IACA;IACAD,eAAA,WAAAA,gBAAAH,GAAA,EACA;MACA,IAAAO,eAAA,OAAAN,IAAA,CAAAD,GAAA,CAAAK,WAAA,IAAAL,GAAA,CAAAQ,QAAA;MACA,YAAAF,UAAA,CAAAC,eAAA;IACA;IACA;IACAD,UAAA,WAAAA,WAAAG,IAAA,EACA;MACA,IAAAC,IAAA,GAAAD,IAAA,CAAAJ,WAAA;MACA,IAAAM,KAAA,GAAAC,MAAA,CAAAH,IAAA,CAAAD,QAAA,QAAAK,QAAA;MACA,IAAAC,GAAA,GAAAF,MAAA,CAAAH,IAAA,CAAAM,OAAA,IAAAF,QAAA;MACA,UAAAG,MAAA,CAAAN,IAAA,OAAAM,MAAA,CAAAL,KAAA,OAAAK,MAAA,CAAAF,GAAA;IACA;EAEA;AACA", "ignoreList": []}]}