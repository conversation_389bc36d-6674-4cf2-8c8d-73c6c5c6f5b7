import request from '@/utils/request'

// 查询出门证任务列表
export function listTask(query) {
  return request({
    url: '/leave/task/list',
    method: 'get',
    params: query
  })
}

// 查询出门证任务详细
export function getTask(id) {
  return request({
    url: '/leave/task/' + id,
    method: 'get'
  })
}

// 新增出门证任务
export function addTask(data) {
  return request({
    url: '/leave/task',
    method: 'post',
    data: data
  })
}

// 修改出门证任务
export function updateTask(data) {
  return request({
    url: '/leave/task',
    method: 'put',
    data: data
  })
}

// 删除出门证任务
export function delTask(id) {
  return request({
    url: '/leave/task/' + id,
    method: 'delete'
  })
}

// 导出出门证任务
export function exportTask(query) {
  return request({
    url: '/leave/task/export',
    method: 'get',
    params: query
  })
}