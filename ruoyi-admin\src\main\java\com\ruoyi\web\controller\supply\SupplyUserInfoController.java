package com.ruoyi.web.controller.supply;

import java.util.List;

import com.ruoyi.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.app.supply.domain.SupplyUserInfo;
import com.ruoyi.app.supply.domain.SupplyUserImportDto;
import com.ruoyi.app.supply.service.ISupplyUserInfoService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 相关方人员Controller
 * 
 * <AUTHOR>
 * @date 2023-3-22
 */
@RestController
@RequestMapping("/web/supply/userinfo")
public class SupplyUserInfoController extends BaseController
{
    @Autowired
    private ISupplyUserInfoService supplyUserInfoService;

    /**
     * 查询相关方人员列表
     */
    @GetMapping("/get/list")
    public TableDataInfo getList(SupplyUserInfo supplyUserInfo)
    {
        startPage();
        List<SupplyUserInfo> list = supplyUserInfoService.selectSupplyUserInfoList(supplyUserInfo);
        return getDataTable(list);
    }

    /**
     * 导出相关方人员列表
     */
    @Log(title = "相关方人员", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public AjaxResult export(SupplyUserInfo supplyUserInfo)
    {
        List<SupplyUserInfo> list = supplyUserInfoService.selectSupplyUserInfoList(supplyUserInfo);
        ExcelUtil<SupplyUserInfo> util = new ExcelUtil<SupplyUserInfo>(SupplyUserInfo.class);
        return util.exportExcel(list, "相关方人员数据");
    }

    /**
     * 获取相关方人员详细信息
     */
    @GetMapping(value = "/get/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id)
    {
        return success(supplyUserInfoService.selectSupplyUserInfoById(id));
    }

    /**
     * 新增相关方人员
     */
    @Log(title = "相关方人员", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public AjaxResult add(@RequestBody SupplyUserInfo supplyUserInfo)
    {
        return toAjax(supplyUserInfoService.insertSupplyUserInfo(supplyUserInfo));
    }

    /**
     * 修改相关方人员
     */
    @Log(title = "相关方人员", businessType = BusinessType.UPDATE)
    @PutMapping("/update")
    public AjaxResult update(@RequestBody SupplyUserInfo supplyUserInfo)
    {
        return toAjax(supplyUserInfoService.updateSupplyUserInfo(supplyUserInfo));
    }

    /**
     * 删除相关方人员
     */
    @Log(title = "相关方人员", businessType = BusinessType.DELETE)
	@DeleteMapping("/delete/{ids}")
    public AjaxResult delete(@PathVariable Integer[] ids)
    {
        return toAjax(supplyUserInfoService.deleteSupplyUserInfoByIds(ids));
    }

    /**
     * 导入相关方人员数据
     */
    @Log(title = "相关方人员", businessType = BusinessType.IMPORT)
    @PostMapping("/import")
    public AjaxResult importData(MultipartFile file) throws Exception
    {
        ExcelUtil<SupplyUserImportDto> util = new ExcelUtil<>(SupplyUserImportDto.class);
        List<SupplyUserImportDto> importList = util.importExcel(file.getInputStream());

        if (importList == null || importList.isEmpty()) {
            return error("导入数据不能为空");
        }

        String currentUser = SecurityUtils.getUsername();
        List<SupplyUserImportDto> resultList = supplyUserInfoService.importSupplyUserInfo(importList, currentUser);

        // 统计导入结果
        long successCount = resultList.stream().filter(item -> "成功".equals(item.getResult())).count();
        long partialSuccessCount = resultList.stream().filter(item -> "部分成功".equals(item.getResult())).count();
        long failCount = resultList.stream().filter(item -> "失败".equals(item.getResult())).count();

        String message = String.format("导入完成！成功：%d条，部分成功：%d条，失败：%d条",
                                     successCount, partialSuccessCount, failCount);

        // 如果有失败的记录，返回详细信息
        if (failCount > 0 || partialSuccessCount > 0) {
            return AjaxResult.success(message, resultList);
        } else {
            return AjaxResult.success(message);
        }
    }

    /**
     * 下载导入模板
     */
    @PostMapping("/importTemplate")
    public AjaxResult importTemplate()
    {
        ExcelUtil<SupplyUserImportDto> util = new ExcelUtil<>(SupplyUserImportDto.class);
        return util.importTemplateExcel("供应商用户信息导入模板");
    }
}