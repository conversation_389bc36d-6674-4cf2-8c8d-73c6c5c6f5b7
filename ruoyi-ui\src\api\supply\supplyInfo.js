import request from '@/utils/request'

// 查询供应商信息列表
export function listSupplyInfo(query) {
  return request({
    url: '/web/supply/info/list',
    method: 'get',
    params: query
  })
}

// 根据供应商代码查询供应商信息
export function getSupplyInfoByCode(supplyCode) {
  return request({
    url: '/web/supply/info/getByCode/' + supplyCode,
    method: 'get'
  })
}

// 查询供应商信息详细
export function getSupplyInfo(supplyCode) {
  return request({
    url: '/web/supply/info/' + supplyCode,
    method: 'get'
  })
}

// 新增供应商信息
export function addSupplyInfo(data) {
  return request({
    url: '/web/supply/info',
    method: 'post',
    data: data
  })
}

// 修改供应商信息
export function updateSupplyInfo(data) {
  return request({
    url: '/web/supply/info',
    method: 'put',
    data: data
  })
}

// 删除供应商信息
export function delSupplyInfo(supplyCodes) {
  return request({
    url: '/web/supply/info/' + supplyCodes,
    method: 'delete'
  })
}

// 导出供应商信息
export function exportSupplyInfo(query) {
  return request({
    url: '/web/supply/info/export',
    method: 'post',
    data: query
  })
}
