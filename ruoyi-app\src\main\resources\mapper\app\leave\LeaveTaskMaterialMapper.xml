<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.app.leave.mapper.LeaveTaskMaterialMapper">
    
    <resultMap type="LeaveTaskMaterial" id="LeaveTaskMaterialResult">
        <result property="id"    column="id"    />
        <result property="taskNo"    column="task_no"    />
        <result property="materialId"    column="material_id"    />
        <result property="materialNo"    column="material_no"    />
        <result property="materialType"    column="material_type"    />
        <result property="materialName"    column="material_name"    />
        <result property="materialSpec"    column="material_spec"    />
        <result property="measureUnit"    column="measure_unit"    />
        <result property="measureFlag"    column="measure_flag"    />
        <result property="planNum"    column="plan_num"    />
        <result property="doormanReceiveNum"    column="doorman_receive_num"    />
        <result property="factoryReceiveNum"    column="factory_receive_num"    />
        <result property="gross"    column="gross"    />
        <result property="tare"    column="tare"    />
        <result property="netWeight"    column="net_weight"    />
        <result property="grossTime"    column="gross_time"    />
        <result property="tareTime"    column="tare_time"    />
        <result property="secGross"    column="sec_gross"    />
        <result property="secTare"    column="sec_tare"    />
        <result property="secNetWeight"    column="sec_net_weight"    />
        <result property="secTareTime"    column="sec_tare_time"    />
        <result property="secGrossTime"    column="sec_gross_time"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="selectLeaveTaskMaterialVo">
        select id, task_no, material_id, material_no, material_type, material_name, material_spec, measure_unit, measure_flag, plan_num, doorman_receive_num, factory_receive_num, gross, tare, net_weight, gross_time, tare_time, sec_gross, sec_tare, sec_net_weight, sec_tare_time, sec_gross_time, create_time, create_by, update_time, update_by from leave_task_material
    </sql>

    <select id="selectLeaveTaskMaterialList" parameterType="LeaveTaskMaterial" resultMap="LeaveTaskMaterialResult">
        <include refid="selectLeaveTaskMaterialVo"/>
        <where>  
            <if test="taskNo != null  and taskNo != ''"> and task_no = #{taskNo}</if>
            <if test="materialId != null "> and material_id = #{materialId}</if>
            <if test="materialNo != null  and materialNo != ''"> and material_no = #{materialNo}</if>
            <if test="materialType != null "> and material_type = #{materialType}</if>
            <if test="materialName != null  and materialName != ''"> and material_name like concat('%', #{materialName}, '%')</if>
            <if test="materialSpec != null  and materialSpec != ''"> and material_spec = #{materialSpec}</if>
            <if test="measureUnit != null  and measureUnit != ''"> and measure_unit = #{measureUnit}</if>
            <if test="measureFlag != null "> and measure_flag = #{measureFlag}</if>
            <if test="planNum != null "> and plan_num = #{planNum}</if>
            <if test="doormanReceiveNum != null "> and doorman_receive_num = #{doormanReceiveNum}</if>
            <if test="factoryReceiveNum != null "> and factory_receive_num = #{factoryReceiveNum}</if>
            <if test="gross != null "> and gross = #{gross}</if>
            <if test="tare != null "> and tare = #{tare}</if>
            <if test="netWeight != null "> and net_weight = #{netWeight}</if>
            <if test="grossTime != null "> and gross_time = #{grossTime}</if>
            <if test="tareTime != null "> and tare_time = #{tareTime}</if>
            <if test="secGross != null "> and sec_gross = #{secGross}</if>
            <if test="secTare != null "> and sec_tare = #{secTare}</if>
            <if test="secNetWeight != null "> and sec_net_weight = #{secNetWeight}</if>
            <if test="secTareTime != null "> and sec_tare_time = #{secTareTime}</if>
            <if test="secGrossTime != null "> and sec_gross_time = #{secGrossTime}</if>
        </where>
    </select>
    
    <select id="selectLeaveTaskMaterialById" parameterType="Long" resultMap="LeaveTaskMaterialResult">
        <include refid="selectLeaveTaskMaterialVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertLeaveTaskMaterial" parameterType="LeaveTaskMaterial" useGeneratedKeys="true" keyProperty="id">
        insert into leave_task_material
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskNo != null">task_no,</if>
            <if test="materialId != null">material_id,</if>
            <if test="materialNo != null">material_no,</if>
            <if test="materialType != null">material_type,</if>
            <if test="materialName != null">material_name,</if>
            <if test="materialSpec != null">material_spec,</if>
            <if test="measureUnit != null">measure_unit,</if>
            <if test="measureFlag != null">measure_flag,</if>
            <if test="planNum != null">plan_num,</if>
            <if test="doormanReceiveNum != null">doorman_receive_num,</if>
            <if test="factoryReceiveNum != null">factory_receive_num,</if>
            <if test="gross != null">gross,</if>
            <if test="tare != null">tare,</if>
            <if test="netWeight != null">net_weight,</if>
            <if test="grossTime != null">gross_time,</if>
            <if test="tareTime != null">tare_time,</if>
            <if test="secGross != null">sec_gross,</if>
            <if test="secTare != null">sec_tare,</if>
            <if test="secNetWeight != null">sec_net_weight,</if>
            <if test="secTareTime != null">sec_tare_time,</if>
            <if test="secGrossTime != null">sec_gross_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskNo != null">#{taskNo},</if>
            <if test="materialId != null">#{materialId},</if>
            <if test="materialNo != null">#{materialNo},</if>
            <if test="materialType != null">#{materialType},</if>
            <if test="materialName != null">#{materialName},</if>
            <if test="materialSpec != null">#{materialSpec},</if>
            <if test="measureUnit != null">#{measureUnit},</if>
            <if test="measureFlag != null">#{measureFlag},</if>
            <if test="planNum != null">#{planNum},</if>
            <if test="doormanReceiveNum != null">#{doormanReceiveNum},</if>
            <if test="factoryReceiveNum != null">#{factoryReceiveNum},</if>
            <if test="gross != null">#{gross},</if>
            <if test="tare != null">#{tare},</if>
            <if test="netWeight != null">#{netWeight},</if>
            <if test="grossTime != null">#{grossTime},</if>
            <if test="tareTime != null">#{tareTime},</if>
            <if test="secGross != null">#{secGross},</if>
            <if test="secTare != null">#{secTare},</if>
            <if test="secNetWeight != null">#{secNetWeight},</if>
            <if test="secTareTime != null">#{secTareTime},</if>
            <if test="secGrossTime != null">#{secGrossTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
         </trim>
    </insert>

    <update id="updateLeaveTaskMaterial" parameterType="LeaveTaskMaterial">
        update leave_task_material
        <trim prefix="SET" suffixOverrides=",">
            <if test="taskNo != null">task_no = #{taskNo},</if>
            <if test="materialId != null">material_id = #{materialId},</if>
            <if test="materialNo != null">material_no = #{materialNo},</if>
            <if test="materialType != null">material_type = #{materialType},</if>
            <if test="materialName != null">material_name = #{materialName},</if>
            <if test="materialSpec != null">material_spec = #{materialSpec},</if>
            <if test="measureUnit != null">measure_unit = #{measureUnit},</if>
            <if test="measureFlag != null">measure_flag = #{measureFlag},</if>
            <if test="planNum != null">plan_num = #{planNum},</if>
            <if test="doormanReceiveNum != null">doorman_receive_num = #{doormanReceiveNum},</if>
            <if test="factoryReceiveNum != null">factory_receive_num = #{factoryReceiveNum},</if>
            <if test="gross != null">gross = #{gross},</if>
            <if test="tare != null">tare = #{tare},</if>
            <if test="netWeight != null">net_weight = #{netWeight},</if>
            <if test="grossTime != null">gross_time = #{grossTime},</if>
            <if test="tareTime != null">tare_time = #{tareTime},</if>
            <if test="secGross != null">sec_gross = #{secGross},</if>
            <if test="secTare != null">sec_tare = #{secTare},</if>
            <if test="secNetWeight != null">sec_net_weight = #{secNetWeight},</if>
            <if test="secTareTime != null">sec_tare_time = #{secTareTime},</if>
            <if test="secGrossTime != null">sec_gross_time = #{secGrossTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteLeaveTaskMaterialById" parameterType="Long">
        delete from leave_task_material where id = #{id}
    </delete>

    <delete id="deleteLeaveTaskMaterialByIds" parameterType="String">
        delete from leave_task_material where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
</mapper>