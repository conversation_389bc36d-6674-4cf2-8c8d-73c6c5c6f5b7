package com.ruoyi.app.v1.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.ruoyi.app.domain.CopyUser;
import com.ruoyi.app.domain.FileApproval;
import com.ruoyi.app.domain.FileVerifyRelation;
import com.ruoyi.app.mapper.FileApprovalMapper;
import com.ruoyi.app.mapper.FileVerifyRelationMapper;
import com.ruoyi.app.sale.domain.SaleUser;
import com.ruoyi.app.sale.mapper.SaleRoleMapper;
import com.ruoyi.app.sale.service.ISaleRoleService;
import com.ruoyi.app.v1.domain.FileStaff;
import com.ruoyi.app.v1.enums.FileType;
import com.ruoyi.app.v1.mapper.AppCommonV1Mapper;
import com.ruoyi.app.v1.service.IFileApprovalV1Service;
import com.ruoyi.app.v2.service.IOfficialUserService;
import com.ruoyi.common.constant.HttpStatus;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysDictData;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.text.GetUpperString;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.spring.SpringUtils;
import com.ruoyi.framework.manager.AsyncManager;
import com.ruoyi.framework.manager.factory.AsyncFactory;
import com.ruoyi.system.service.ISysDictDataService;
import com.ruoyi.system.service.ISysDictTypeService;
import com.ruoyi.system.service.impl.SysConfigServiceImpl;
import org.apache.commons.lang3.StringEscapeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 文件报批Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-05-25
 */
@Service
public class FileApprovalV1ServiceImpl implements IFileApprovalV1Service {
    @Autowired
    private FileApprovalMapper fileApprovalMapper;

    @Autowired
    private AppCommonV1Mapper commonMapper;

    @Autowired
    private FileVerifyRelationMapper fileVerifyRelationMapper;

    @Autowired
    private IOfficialUserService officialUserService;

    @Autowired
    private ISaleRoleService saleRoleService;

    @Autowired
    private SaleRoleMapper saleRoleMapper;

    @Autowired
    private ISysDictTypeService dictTypeService;

    //板块总经理
    public static final String GENERAL_MANAGER_ROLE_KEY = "sale.general";

    //营销管理部环节
    public static final String MARKET_MANAGER_STAGE_ROLE_KEY = "sale.manager.stage";

    //营销管理部-兴澄
    public static final String MARKET_MANAGER_XC_ROLE_KEY = "sale.manager.xc";

    //营销管理部-冶钢
    public static final String MARKET_MANAGER_YG_ROLE_KEY = "sale.manager.yg";

    //营销管理部-青钢
    public static final String MARKET_MANAGER_QG_ROLE_KEY = "sale.manager.qg";


    //营销管理部-青钢
    public static final String MARKET_MANAGER_YZ_ROLE_KEY = "sale.manager.yz";

    /**
     * 查询文件报批
     *
     * @param id 文件报批ID
     * @return 文件报批
     */
    @Override
    public FileApproval selectFileApprovalById(Long id) {
        FileApproval res = fileApprovalMapper.selectFileApprovalById(id);
        if (!Optional.ofNullable(res.getFileType()).isPresent()) {
            res.setFileType("0");
        }
        res.setFileTypeDesc(FileType.getDescriptionByCode(res.getFileType()));
        //翻译类型
        if (!Optional.ofNullable(res.getVarietyCode()).isPresent()) {
            res.setVarietyCode("");
        }
        res.setVarietyLabel("");
        List<SysDictData> typeList = dictTypeService.selectDictDataByType("sale_variety_type");
        if (StringUtils.isNull(typeList)) {
            typeList = new ArrayList<SysDictData>();
        }
        if (Optional.ofNullable(res.getVarietyCode()).isPresent()) {
            for (SysDictData dictData : typeList) {
                if (dictData.getDictValue().equals(res.getVarietyCode())) {
                    res.setVarietyLabel(dictData.getDictLabel());
                }
            }
        }
        return res;
    }

    /**
     * 查询文件报批列表
     *
     * @param fileApproval 文件报批
     * @return 文件报批
     */
    @Override
    public List<FileApproval> selectFileApprovalList(FileApproval fileApproval) {
        return fileApprovalMapper.selectFileApprovalList(fileApproval);
    }

    /**
     * 新增文件报批
     *
     * @param fileApproval 文件报批
     * @return 结果
     */
    @Override
    public int insertFileApproval(FileApproval fileApproval) {
        StringBuffer noticeType = new StringBuffer();
        noticeType.append("文件报批");

        if (Optional.ofNullable(fileApproval.getFileType()).isPresent()) {
            String fileType = fileApproval.getFileType();
            if (!fileType.equals("0")) {
                noticeType.append("(");
                noticeType.append(FileType.getDescriptionByCode(fileType));
                noticeType.append(")");
            }
        }

        Date now = DateUtils.getNowDate();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        fileApproval.setHistory(simpleDateFormat.format(now) + " " + fileApproval.getWorkName() + " 提交审批;\n");
        fileApproval.setCreateTime(now);
        int result = fileApprovalMapper.insertFileApproval(fileApproval);
        String openId = commonMapper.selectOpenIdByWorkNo(fileApproval.getApprover());
        if (StringUtils.isNotBlank(openId)) {
            String officialOpenId = officialUserService.getOpenIdsByOpenId(openId);
            if (StringUtils.isNotBlank(officialOpenId)) {
                AsyncManager.me().execute(AsyncFactory.sendTemplateMessage("有一个文件报批需要您审核", noticeType.toString(), fileApproval.getWorkName(), simpleDateFormat.format(now), officialOpenId, "packageA/pages/file/examine/list", "请点击链接登录小程序开始审核"));
            }
        }
        return result;
    }

    /**
     * 修改文件报批
     *
     * @param fileApproval 文件报批
     * @return 结果
     */
    @Override
    public int updateFileApproval(FileApproval fileApproval) {
        Date now = DateUtils.getNowDate();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String history = fileApproval.getHistory() + simpleDateFormat.format(now) + " " + fileApproval.getWorkName() + " 提交审批;\n";
        fileApproval.setHistory(history);
        fileApproval.setUpdateTime(now);

        StringBuffer noticeType = new StringBuffer();
        noticeType.append("文件报批");

        if (Optional.ofNullable(fileApproval.getFileType()).isPresent()) {
            String fileType = fileApproval.getFileType();
            if (!fileType.equals("0")) {
                noticeType.append("(");
                noticeType.append(FileType.getDescriptionByCode(fileType));
                noticeType.append(")");
            }
        }

        String openId = commonMapper.selectOpenIdByWorkNo(fileApproval.getApprover());
        if (StringUtils.isNotBlank(openId)) {
            String officialOpenId = officialUserService.getOpenIdsByOpenId(openId);
            if (StringUtils.isNotBlank(officialOpenId)) {
                AsyncManager.me().execute(AsyncFactory.sendTemplateMessage("有一个文件报批需要您审核", noticeType.toString(), fileApproval.getWorkName(), simpleDateFormat.format(now), officialOpenId, "packageA/pages/file/examine/list", "请点击链接登录小程序开始审核"));
            }
        }
        return fileApprovalMapper.updateFileApproval(fileApproval);
    }

    @Override
    public int editApprovers(FileApproval fileApproval) {
        Date now = DateUtils.getNowDate();
        fileApproval.setUpdateTime(now);
        return fileApprovalMapper.updateFileApproval(fileApproval);
    }

    /**
     * 批量删除文件报批
     *
     * @param ids 需要删除的文件报批ID
     * @return 结果
     */
    @Override
    public int deleteFileApprovalByIds(Long[] ids) {
        return fileApprovalMapper.deleteFileApprovalByIds(ids);
    }

    /**
     * 删除文件报批信息
     *
     * @param fileApproval 文件报批ID
     * @return 结果
     */
    @Override
    public int deleteFileApprovalById(FileApproval fileApproval) {
        Date now = DateUtils.getNowDate();
        fileApproval.setUpdateTime(now);
        fileApproval.setDeleteTime(now);
        return fileApprovalMapper.updateFileApproval(fileApproval);
    }

    @Override
    public TableDataInfo selectMyApprovalList(FileApproval fileApproval) {
        List<Map<String, Object>> result = new ArrayList<Map<String, Object>>();
        List<FileApproval> approvalList = fileApprovalMapper.selectFileApprovalList(fileApproval);
        long total = new PageInfo(approvalList).getTotal();
        for (FileApproval item : approvalList) {
            Map<String, Object> map = new HashMap<>();
            map.put("id", item.getId() == null ? "" : item.getId());
            map.put("title", item.getTitle() == null ? "" : item.getTitle());
            map.put("workNo", item.getWorkNo() == null ? "" : item.getWorkNo());
            map.put("workName", item.getWorkName() == null ? "" : item.getWorkName());
            map.put("fileType", item.getFileType() == null ? "" : item.getFileType());
            map.put("fileTypeDesc", FileType.getDescriptionByCode(item.getFileType()));
            if (Optional.ofNullable(item.getCreateTime()).isPresent()) {
                map.put("createTime", DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, item.getCreateTime()));
            } else {
                map.put("createTime", "");
            }
            result.add(map);
        }
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setMsg("查询成功");
        rspData.setRows(result);
        rspData.setTotal(total);
        return rspData;
    }

    @Override
    public int examineFileApproval(FileApproval fileApproval) {
        FileApproval examineFile = fileApprovalMapper.selectFileApprovalById(fileApproval.getId());

        StringBuffer noticeType = new StringBuffer();
        noticeType.append("文件报批");
        if (Optional.ofNullable(examineFile.getFileType()).isPresent()) {
            String fileType = examineFile.getFileType();
            if (!fileType.equals("0")) {
                noticeType.append("(");
                noticeType.append(FileType.getDescriptionByCode(fileType));
                noticeType.append(")");
            }
        }

        if (fileApproval.getOpt().equals("Y")) {
            JSONArray jsonArr = JSONArray.parseArray(examineFile.getApprovers());
            Date now = DateUtils.getNowDate();
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

            //判断是否为营销管理部确认环节
            String roleStaff = examineFile.getApprovers();
            List<FileStaff> roleStaffList = JSONArray.parseArray(roleStaff, FileStaff.class);
            int currentIndex = Integer.parseInt(examineFile.getPoint()); //当前审核人
            String currentUserId = roleStaffList.get(currentIndex).getUserId();
            boolean isMarketRole = saleRoleService.selectRoleExistByUserName(currentUserId, MARKET_MANAGER_STAGE_ROLE_KEY);
            String currentLevel = roleStaffList.get(currentIndex).getLevel(); //是否营销管理部人员且当前为确认环节
            boolean isMarketConfirmStage = isMarketRole && currentLevel.equals("5");
            if (!isMarketConfirmStage) {
                //添加审批履历
                String history = examineFile.getHistory();
                history += simpleDateFormat.format(now) + " " + examineFile.getApproverName() + " 通过了审批";
                if (!fileApproval.getRemark().equals("") && fileApproval.getRemark() != null) {
                    history += "，审批意见：" + fileApproval.getRemark();
                }
                //通过日志
                history += ";\n";
                examineFile.setHistory(history);
            }

            //判断审批是否结束  修改状态
            int idx = Integer.parseInt(examineFile.getPoint());
            if (jsonArr.size() == (idx + 1)) {

                String originalApprove = examineFile.getApprovers();
                List<FileStaff> originalApproveList = JSONArray.parseArray(originalApprove, FileStaff.class);
                //价格或合同审批，提报板块销售总经理(姚总)审核，审核通过转至营销管理部确认
                boolean isTransferToMarket = false;

                //是否板块经理环节
                boolean isGeneralStage = originalApproveList.get(idx).getLevel().equals("4");

                //是否价格或合同审批
                boolean isPriceOrContract = false;
                String fileType = examineFile.getFileType();
                if (fileType.equals("2") || fileType.equals("3")) {
                    isPriceOrContract = true;
                }
                //是否兴澄销售提报
                boolean isXcStaff = false;
                String presenter = examineFile.getWorkNo();
                if (presenter.length() > 0) {
                    if ("X".equals(presenter.substring(0, 1)) || "Y".equals(presenter.substring(0, 1)) || "Q".equals(presenter.substring(0, 1)) || "J".equals(presenter.substring(0, 1))) {
                        isXcStaff = true;
                    }
                }
                //是否姚总审核
                boolean isGeneralManager = saleRoleService.selectRoleExistByUserName(examineFile.getApprover(), GENERAL_MANAGER_ROLE_KEY);
                isTransferToMarket = isGeneralStage && isPriceOrContract && isXcStaff && isGeneralManager;

                if (isTransferToMarket) {
                    //推送至营销管理部确认
                    if (isXcStaff) {
                        //兴澄转兴澄负责人(孙海红)确认
                        //提交给谁谁确认
                        List<FileStaff> confirmUsers = originalApproveList.stream().filter(x -> x.getLevel().equals("3")).collect(Collectors.toList());
                        if (confirmUsers.size() > 0) {
                            examineFile.setStatus("1");
                            examineFile.setPoint("" + (idx + 1));
                            examineFile.setApprover(confirmUsers.get(0).getUserId());
                            examineFile.setApproverName(confirmUsers.get(0).getUserName());
                            //添加营销管理部确认人
                            FileStaff fileStaff = new FileStaff();
                            fileStaff.setLevel("5");
                            fileStaff.setUserId(examineFile.getApprover());
                            fileStaff.setUserName(examineFile.getApproverName());
                            originalApproveList.add(fileStaff);
                            examineFile.setApprovers(JSONArray.toJSONString(originalApproveList));
                            //确认日志，移除
//                            history += ";转营销管理部" + examineFile.getApproverName() + "确认";

                            //推送给营销管理部确认
                            String openId = commonMapper.selectOpenIdByWorkNo(examineFile.getApprover());
                            if (StringUtils.isNotBlank(openId)) {
                                String officialOpenId = officialUserService.getOpenIdsByOpenId(openId);
                                if (StringUtils.isNotBlank(officialOpenId)) {
                                    AsyncManager.me().execute(AsyncFactory.sendTemplateMessage("有一个文件报批需要您审核", noticeType.toString() + "确认", examineFile.getWorkName(), simpleDateFormat.format(now), officialOpenId, "packageA/pages/file/examine/list", "请点击链接登录小程序开始审核"));
                                }
                            }
                        }
                    }
                } else {
                    examineFile.setStatus("2");
                    examineFile.setPoint("E");
                    // 此处获取到的集团openId 兴澄无法读取config配置 config为null
                    String openId = commonMapper.selectOpenIdByWorkNo(examineFile.getWorkNo());
                    if (StringUtils.isNotBlank(openId)) {
                        String officialOpenId = officialUserService.getOpenIdsByOpenId(openId);
                        if (StringUtils.isNotBlank(officialOpenId)) {
                            AsyncManager.me().execute(AsyncFactory.sendTemplateMessage("有一个文件报批需要您审核", "文件报批(审核通过)", examineFile.getWorkName(), simpleDateFormat.format(now), officialOpenId, "packageA/pages/file/history/list", "请点击链接登录小程序开始审核"));
                        }
                    }
                }
            } else {
                examineFile.setPoint("" + (idx + 1));
                JSONObject jo = JSONObject.parseObject(jsonArr.get(idx + 1).toString());
                examineFile.setApprover(jo.getString("userId"));
                examineFile.setApproverName(jo.getString("userName"));
                // 此处获取到的集团openId 兴澄无法读取config配置 config为null
                String openId = commonMapper.selectOpenIdByWorkNo(examineFile.getApprover());
                if (StringUtils.isNotBlank(openId)) {
                    String officialOpenId = officialUserService.getOpenIdsByOpenId(openId);
                    if (StringUtils.isNotBlank(officialOpenId)) {
                        AsyncManager.me().execute(AsyncFactory.sendTemplateMessage("有一个文件报批需要您审核", noticeType.toString(), examineFile.getWorkName(), simpleDateFormat.format(now), officialOpenId, "packageA/pages/file/examine/list", "请点击链接登录小程序开始审核"));
                    }
                }
            }

        } else if (fileApproval.getOpt().equals("N")) {
            //添加审批履历
            String history = examineFile.getHistory();
            Date now = DateUtils.getNowDate();
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            history += simpleDateFormat.format(now) + " " + examineFile.getApproverName() + " 驳回了审批";
            history += "，驳回意见：" + fileApproval.getRemark() + ";\n";
            examineFile.setHistory(history);

            JSONArray jsonArr = JSONArray.parseArray(examineFile.getApprovers());
            JSONObject jo = JSONObject.parseObject(jsonArr.get(0).toString());

            examineFile.setApprover(jo.getString("userId"));
            examineFile.setApproverName(jo.getString("userName"));


            examineFile.setStatus("0");
            examineFile.setPoint("B");

            String openId = commonMapper.selectOpenIdByWorkNo(examineFile.getWorkNo());
            if (StringUtils.isNotBlank(openId)) {
                String officialOpenId = officialUserService.getOpenIdsByOpenId(openId);
                if (StringUtils.isNotBlank(officialOpenId)) {
                    AsyncManager.me().execute(AsyncFactory.sendTemplateMessage("您的上报被驳回", "文件报批驳回", examineFile.getWorkName(), simpleDateFormat.format(now), officialOpenId, "packageA/pages/file/history/list", "请点击链接登录小程序查看详情"));
                }
            }
        }
        return fileApprovalMapper.updateFileApproval(examineFile);
    }

    @Override
    public TableDataInfo selectHistoryFileApprovalList(FileApproval fileApproval) {
        List<FileApproval> fileApprovalList = fileApprovalMapper.selectHistoryFileApprovalList(fileApproval);
        long total = new PageInfo(fileApprovalList).getTotal();
        List<Map<String, Object>> result = new ArrayList<Map<String, Object>>();
        for (FileApproval item : fileApprovalList) {
            Map<String, Object> map = new HashMap<>();
            map.put("id", item.getId() == null ? "" : item.getId());
            map.put("title", item.getTitle() == null ? "" : item.getTitle());
            map.put("point", item.getPoint() == null ? "" : item.getPoint());
            map.put("workName", item.getWorkName() == null ? "" : item.getWorkName());
            map.put("fileType", item.getFileType() == null ? "" : item.getFileType());
            map.put("fileTypeDesc", FileType.getDescriptionByCode(item.getFileType()));
            if (Optional.ofNullable(item.getCreateTime()).isPresent()) {
                map.put("createTime", DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, item.getCreateTime()));
            } else {
                map.put("createTime", "");
            }
            result.add(map);
        }
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setMsg("查询成功");
        rspData.setRows(result);
        rspData.setTotal(total);
        return rspData;
    }

    @Override
    public List<Map<String, Object>> getFileVerifyStaffList(FileVerifyRelation fileVerifyRelation, String workNo) {
        List<Map<String, Object>> result = new ArrayList<Map<String, Object>>();
        //根据分公司选择营销管理部审核人员
        if (Optional.ofNullable(fileVerifyRelation.getWorkFlowNodeSort()).isPresent()) {
            if (fileVerifyRelation.getWorkFlowNodeSort().equals("7")) {
                if (Optional.ofNullable(workNo).isPresent()) {
                    if (workNo.length() > 0) {
                        List<SaleUser> saleUsers = new ArrayList<>();
                        //兴澄销售选择兴澄负责人
                        if ("X".equals(workNo.substring(0, 1))) {
                            saleUsers = saleRoleMapper.selectSaleUserByRoleKey(MARKET_MANAGER_XC_ROLE_KEY);
                        }
                        //冶钢
                        if ("Y".equals(workNo.substring(0, 1))) {
                            saleUsers = saleRoleMapper.selectSaleUserByRoleKey(MARKET_MANAGER_YG_ROLE_KEY);
                        }
                        //青钢
                        if ("Q".equals(workNo.substring(0, 1))) {
                            saleUsers = saleRoleMapper.selectSaleUserByRoleKey(MARKET_MANAGER_QG_ROLE_KEY);
                        }

                        //青钢
                        if ("J".equals(workNo.substring(0, 1))) {
                            saleUsers = saleRoleMapper.selectSaleUserByRoleKey(MARKET_MANAGER_YZ_ROLE_KEY);
                        }
                        for (SaleUser item : saleUsers) {
                            Map<String, Object> map = new HashMap<>();
                            map.put("supervisorNo", item.getUserName());
                            map.put("supervisorName", item.getNickName());
                            map.put("workFlowNodeSort", fileVerifyRelation.getWorkFlowNodeSort());
                            map.put("upperStr", GetUpperString.getUpperStr(item.getNickName()));
                            result.add(map);
                        }
                        return result;
                    }
                }
            }
        }
        List<FileVerifyRelation> fileVerifyStaff = fileVerifyRelationMapper.selectFileVerifyStaffList(fileVerifyRelation);
        for (FileVerifyRelation item : fileVerifyStaff) {
            Map<String, Object> map = new HashMap<>();
            map.put("supervisorNo", item.getSupervisorNo());
            map.put("supervisorName", item.getSupervisorName());
            map.put("workFlowNodeSort", item.getWorkFlowNodeSort());
            map.put("upperStr", GetUpperString.getUpperStr(item.getSupervisorName()));
            result.add(map);
        }
        return result;
    }

    @Override
    public int withdrawFileApproval(FileApproval fileApproval) {
        //根据id获取记录详情
        FileApproval examineFile = fileApprovalMapper.selectFileApprovalById(fileApproval.getId());

        //添加审批履历
        String history = examineFile.getHistory();
        Date now = DateUtils.getNowDate();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        history += simpleDateFormat.format(now) + " " + examineFile.getWorkName() + " 撤回了上报信息;\n";
        examineFile.setHistory(history);

        JSONArray jsonArr = JSONArray.parseArray(examineFile.getApprovers());
        JSONObject jo = JSONObject.parseObject(jsonArr.get(0).toString());

        examineFile.setApprover(jo.getString("userId"));
        examineFile.setApproverName(jo.getString("userName"));

        examineFile.setUpdateTime(now);
        examineFile.setStatus("0");
        examineFile.setPoint("S");

        int result = fileApprovalMapper.updateFileApproval(examineFile);

        return result;

    }


    @Override
    public int insertFileCopy(String fileNo, String users, String userName) {
        Date nowDate = DateUtils.getNowDate();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        // 前端json字符串转化为List
        List<CopyUser> userList = JSONArray.parseArray(users, CopyUser.class);
        // 推送人
        List<String> openIds = new ArrayList<>();
        int res = 1;
        // 查询出原先已推送的用户
        List<Map> formerUser = fileApprovalMapper.selectFileCopyStaff(fileNo);
        if (formerUser.size() > 0) {
            Map<String, Integer> formerUserMap = new HashMap<>();
            // 去重
            List<Map> tempList = new ArrayList<>(formerUser.size());
            formerUser.forEach(x -> {
                if (!tempList.contains(x)) { // 如果新集合中不存在则插入
                    tempList.add(x);
                }
            });
            //排除已经推送的用户
            for (int i = 0; i < tempList.size(); i++) {
                formerUserMap.put(tempList.get(i).get("userId").toString(), i);
            }
            List<CopyUser> pushUserList = new ArrayList<>();
            userList.forEach(x -> {
                if (null == formerUserMap.get(x.getUserId())) {
                    pushUserList.add(x);
                }
            });
            List<String> workNos = pushUserList.stream().map(x -> x.getUserId()).collect(Collectors.toList());
            if (workNos.size() > 0) openIds = officialUserService.getOpenIdsByWorkNos(workNos);

        } else {

            List<String> workNos = userList.stream().map(x -> x.getUserId()).collect(Collectors.toList());
            if (workNos.size() > 0) openIds = officialUserService.getOpenIdsByWorkNos(workNos);

        }
        AsyncManager.me().execute(AsyncFactory.circulateSendTemplateMessage(openIds, "文件抄送", "文件抄送", userName, simpleDateFormat.format(nowDate), "packageA/pages/file/history/list", "请点击链接登录小程序查看详情"));
        // 保存新的记录
        res = fileApprovalMapper.insertFileCopy(fileNo, userList, nowDate);
        return res;
    }

    @Override
    public List<Map<String, Object>> getFileCopyStaff(String fileNo) {
        List<Map> fileCopyStaff = fileApprovalMapper.selectFileCopyStaff(fileNo);
        List<Map<String, Object>> result = new ArrayList<Map<String, Object>>();
        for (Map item : fileCopyStaff) {
            Map<String, Object> map = new HashMap<>();
            map.put("copyStaffNo", item.get("userId"));
            map.put("copyStaffName", item.get("userName"));
            result.add(map);
        }
        return result;
    }

    @Override
    public TableDataInfo selectFileCopyList(FileApproval fileApproval) {
        List<FileApproval> fileApprovalList = fileApprovalMapper.selectFileCopyList(fileApproval);
        long total = new PageInfo(fileApprovalList).getTotal();
        List<Map<String, Object>> result = new ArrayList<Map<String, Object>>();
        for (FileApproval item : fileApprovalList) {
            Map<String, Object> map = new HashMap<>();
            map.put("id", item.getId() == null ? "" : item.getId());
            map.put("title", item.getTitle() == null ? "" : item.getTitle());
            map.put("point", item.getPoint() == null ? "" : item.getPoint());
            map.put("workName", item.getWorkName() == null ? "" : item.getWorkName());
            map.put("fileType", item.getFileType() == null ? "" : item.getFileType());
            map.put("fileTypeDesc", FileType.getDescriptionByCode(item.getFileType()));
            if (Optional.ofNullable(item.getCreateTime()).isPresent()) {
                map.put("createTime", DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, item.getCreateTime()));
            } else {
                map.put("createTime", "");
            }
            result.add(map);
        }
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setMsg("查询成功");
        rspData.setRows(result);
        rspData.setTotal(total);
        return rspData;
    }


    @Override
    public String changeFH(String content) {
        String fileTSList = SpringUtils.getBean(SysConfigServiceImpl.class).selectConfigByKey("fileApprove.tsList");
        List<String> tsList = new ArrayList<>(Arrays.asList(fileTSList.split(",")));
        String res = "";
        for (int i = 0; i < content.length(); i++) {

            if (tsList.contains(String.valueOf(content.charAt(i)))) {
                String left = (i == 0 ? "" : StringEscapeUtils.escapeHtml4(changeFH(content.substring(0, i))));
                String right = (i == content.length() ? "" : changeFH(content.substring(i + 1)));
                return left + content.charAt(i) + right;
            }
        }
        return StringEscapeUtils.escapeHtml4(content);

    }

    @Override
    public boolean judgeIsMarketManager(Long id, String userId) {
        boolean isMarketManager = false;
        FileApproval detail = fileApprovalMapper.selectFileApprovalById(id);
        if (Optional.ofNullable(detail).isPresent()) {
            //审核通过价格、合同文件处理(兴澄小程序仅面向兴澄人员)
            String status = detail.getStatus();
            String fileType = detail.getFileType();
            String presenter = detail.getWorkNo();
            //兴澄员
            boolean isSpecialStaff = false;
            if (presenter.length() > 0) {
                if ("X".equals(presenter.substring(0, 1)) || "Y".equals(presenter.substring(0, 1)) || "Q".equals(presenter.substring(0, 1)) || "J".equals(presenter.substring(0, 1))) {
                    isSpecialStaff = true;
                }
            }
            boolean judgingCondition = isSpecialStaff && Optional.ofNullable(status).isPresent() && Optional.ofNullable(fileType).isPresent();
            if (judgingCondition) {
                if (fileType.equals("2") || fileType.equals("3")) {
                    if (status.equals("2")) {
                        //判断是否已有集团总裁审批
                        boolean leaderCondition = false;
                        String originalApprovers = detail.getApprovers();
                        List<FileStaff> originalApproverList = JSONArray.parseArray(originalApprovers, FileStaff.class);
                        FileVerifyRelation approverQuery = new FileVerifyRelation();
                        approverQuery.setWorkFlowNodeSort("6");
                        List<FileVerifyRelation> leaderList = fileVerifyRelationMapper.selectFileVerifyStaffList(approverQuery);
                        for (FileStaff staff : originalApproverList) {
                            for (FileVerifyRelation leader : leaderList) {
                                if (staff.getUserId().equals(leader.getSupervisorNo())) {
                                    leaderCondition = true;
                                }
                            }
                        }
                        //若集团总裁尚未审批
                        if (!leaderCondition) {
                            //若为营销管理部管理员
                            isMarketManager = saleRoleService.selectRoleExistByUserName(userId, MARKET_MANAGER_STAGE_ROLE_KEY);
                        }

                    }
                }
            }
        }
        return isMarketManager;
    }

    @Override
    public AjaxResult adjustCompletedFile(FileApproval fileApproval, String hanlePerson) {
        FileApproval detail = fileApprovalMapper.selectFileApprovalById(fileApproval.getId());
        String originalApprovers = detail.getApprovers();
        String currentApprovers = fileApproval.getApprovers();
        if (Optional.ofNullable(originalApprovers).isPresent() && Optional.ofNullable(currentApprovers).isPresent()) {
            List<FileStaff> originalApproverList = JSONArray.parseArray(originalApprovers, FileStaff.class);
            int point = originalApproverList.size();
            List<FileStaff> currentApproverList = JSONArray.parseArray(currentApprovers, FileStaff.class);
            //判断是否修改审核人
            if (originalApproverList.size() == currentApproverList.size()) {
                return AjaxResult.error("请选择领导审批");
            }
            FileApproval updateParam = new FileApproval();
            updateParam.setId(fileApproval.getId());
            updateParam.setApprovers(currentApprovers);
            updateParam.setPoint(String.valueOf(point));
            updateParam.setStatus("1");
            String userId = currentApproverList.get(point).getUserId();
            String userName = currentApproverList.get(point).getUserName();
            updateParam.setApprover(userId);
            updateParam.setApproverName(userName);
            updateParam.setUpdateTime(DateUtils.getNowDate());
            //审批履历
            Date now = DateUtils.getNowDate();
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//            String history = detail.getHistory();
//            history += simpleDateFormat.format(now) + " " + hanlePerson + " 上报审批";
//            history += ";\n";
//            updateParam.setHistory(history);
            fileApprovalMapper.updateFileApproval(updateParam);

            //发起通知
            String openId = commonMapper.selectOpenIdByWorkNo(userId);
            if (StringUtils.isNotBlank(openId)) {
                String officialOpenId = officialUserService.getOpenIdsByOpenId(openId);
                if (StringUtils.isNotBlank(officialOpenId)) {
                    AsyncManager.me().execute(AsyncFactory.sendTemplateMessage("有一个文件报批需要您审核", "文件报批", userName, simpleDateFormat.format(now), officialOpenId, "packageA/pages/file/examine/list", "请点击链接登录小程序开始审核"));
                }
            }

            return AjaxResult.success("已提交");
        } else {
            return AjaxResult.error("审批人为空");
        }
    }

    @Override
    public boolean judgeCanBackToMarket(Long id, String userId) {
        boolean canBackToCertainNode = false;
        FileApproval detail = fileApprovalMapper.selectFileApprovalById(id);
        if (Optional.ofNullable(detail).isPresent()) {
            String status = detail.getStatus();
            String fileType = detail.getFileType();
            String presenter = detail.getWorkNo();
            //是否兴澄销售提报
            boolean isXcStaff = false;
            if (presenter.length() > 0) {
                if ("X".equals(presenter.substring(0, 1)) || "Y".equals(presenter.substring(0, 1)) || "Q".equals(presenter.substring(0, 1)) || "J".equals(presenter.substring(0, 1))) {
                    isXcStaff = true;
                }
            }
            //是否营销管理部审核人员
            boolean isMarketManager = saleRoleService.selectRoleExistByUserName(userId, MARKET_MANAGER_STAGE_ROLE_KEY);
            //是否价格或合同审批
            boolean isPriceOrContract = false;
            if (fileType.equals("2") || fileType.equals("3")) {
                isPriceOrContract = true;
            }
            canBackToCertainNode = isPriceOrContract && isMarketManager && isXcStaff;
        }

        return canBackToCertainNode;
    }

    @Override
    public AjaxResult backToMarket(FileApproval fileApproval, String userId, String hanlePerson) {
        FileApproval detail = fileApprovalMapper.selectFileApprovalById(fileApproval.getId());
        //获取当前审批人
        String currentApprovers = detail.getApprovers();
        int idx = fileApproval.getIdx();
        List<FileStaff> currentApproverList = JSONArray.parseArray(currentApprovers, FileStaff.class);
        FileStaff currentUser = currentApproverList.get(idx);
        //更新参数
        FileApproval updateParam = new FileApproval();
        Date now = DateUtils.getNowDate();
        updateParam.setId(fileApproval.getId());
        updateParam.setPoint(String.valueOf(idx));
        updateParam.setStatus("1");
        updateParam.setApprover(currentUser.getUserId());
        updateParam.setApproverName(currentUser.getUserName());
        updateParam.setUpdateTime(now);
        //审批履历
        String history = detail.getHistory();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        history += simpleDateFormat.format(now) + " " + hanlePerson + " 退回至" + currentUser.getUserName() + "审批";
        history += ";\n";
        updateParam.setHistory(history);
        fileApprovalMapper.updateFileApproval(updateParam);
        return AjaxResult.success("退回成功");
    }

    @Override
    public boolean judgeCanHandleToSuperior(Long id) {
        boolean canHandleToSuperior = false;
        FileApproval detail = fileApprovalMapper.selectFileApprovalById(id);
        String currentApprovers = detail.getApprovers();
        List<FileStaff> currentApproverList = JSONArray.parseArray(currentApprovers, FileStaff.class);
        if (detail.getStatus().equals("1")) {
            int idx = Integer.parseInt(detail.getPoint());
            //价格或合同审批营销管理部环节
            boolean condition = (detail.getFileType().equals("2") || detail.getFileType().equals("3"));
            if (condition) {
                String userId = currentApproverList.get(idx).getUserId();
                //是否营销管理部环节
                boolean isGeneralStage = currentApproverList.get(idx).getLevel().equals("3") || currentApproverList.get(idx).getLevel().equals("5");
                canHandleToSuperior = isGeneralStage && saleRoleService.selectRoleExistByUserName(userId, MARKET_MANAGER_STAGE_ROLE_KEY);
            }
            //板块总经理以下
            String currentLevel = currentApproverList.get(idx).getLevel();
            int currentIndex = Integer.parseInt(currentLevel);
            if (currentIndex <= 2) {
                canHandleToSuperior = true;
            }
        }

        return canHandleToSuperior;
    }

    @Override
    public int judgeApproveStage(Long id) {
        //普通审批返回1
        int res = 1;
        FileApproval detail = fileApprovalMapper.selectFileApprovalById(id);
        String fileType = detail.getFileType();

        //是否兴澄销售提报
        String presenter = detail.getWorkNo();
        boolean isXcStaff = false;
        if (presenter.length() > 0) {
            if ("X".equals(presenter.substring(0, 1)) || "Y".equals(presenter.substring(0, 1)) || "Q".equals(presenter.substring(0, 1)) || "J".equals(presenter.substring(0, 1))) {
                isXcStaff = true;
            }
        }

        //价格或合同审批
        if (fileType.equals("2") || fileType.equals("3")) {
            if (isXcStaff) {
                res = 2;
                //判断是否有营销管理部确认环节
                String currentApprovers = detail.getApprovers();
                List<FileStaff> currentApproverList = JSONArray.parseArray(currentApprovers, FileStaff.class);
                int count = 0;
                List<FileStaff> marketManagers = currentApproverList.stream().filter(el -> el.getLevel().equals("3")).collect(Collectors.toList());
                List<FileStaff> confirmManagers = currentApproverList.stream().filter(el -> el.getLevel().equals("5")).collect(Collectors.toList());
                for (FileStaff x : marketManagers) {
                    for (FileStaff y : confirmManagers) {
                        if (x.getUserId().equals(y.getUserId())) {
                            count++;
                        }
                    }
                }
                if (count > 0) {
                    //有营销管理部确认则置为3
                    res = 3;
                }
            }
        }
        //资金类审批
        if (fileType.equals("5") || fileType.equals("6")) {
            if (isXcStaff) {
                res = 4;
            }
        }
        return res;
    }
}
