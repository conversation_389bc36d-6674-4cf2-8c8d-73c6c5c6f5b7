{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\assess\\self\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\assess\\self\\index.vue", "mtime": 1756099891057}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGdldEluZm9CeURhdGUsIHNhdmVJbmZvLCBzdWJtaXRJbmZvLCBkZWxJbmZvLCBsaXN0QmVBc3Nlc3NlZCB9IGZyb20gIkAvYXBpL2Fzc2Vzcy9zZWxmL2luZm8iOwovLyBpbXBvcnQgeyBiYXRjaFRhcmdldCwgbGlzdFNlbGZUYXJnZXRBbGwgfSBmcm9tICJAL2FwaS9hc3Nlc3Mvc2VsZi90YXJnZXQiOwppbXBvcnQgeyBnZXRSZXBvcnREZXB0TGlzdCwgZ2V0QnlXb3JrTm9EZXB0SWQgfSBmcm9tICJAL2FwaS9hc3Nlc3Mvc2VsZi91c2VyIjsKaW1wb3J0IHsgVnVlU2lnbmF0dXJlUGFkIH0gZnJvbSAndnVlLXNpZ25hdHVyZS1wYWQnOwoKZXhwb3J0IGRlZmF1bHQgewogIGNvbXBvbmVudHM6IHsKICAgIFZ1ZVNpZ25hdHVyZVBhZAogIH0sCiAgbmFtZTogIlNlbGZBc3Nlc3NSZXBvcnQiLAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICAvLyDpga7nvanlsYIKICAgICAgbG9hZGluZzogdHJ1ZSwKICAgICAgLy8g5pi+56S65pCc57Si5p2h5Lu2CiAgICAgIHNob3dTZWFyY2g6IHRydWUsCiAgICAgIG9wZW5TaWduOmZhbHNlLAogICAgICAvLyDnu6nmlYjogIPmoLgt6Ieq6K+E5oyH5qCH6YWN572u6KGo5qC85pWw5o2uCiAgICAgIGxpc3Q6IFtdLAogICAgICAvLyDlvLnlh7rlsYLmoIfpopgKICAgICAgdGl0bGU6ICIiLAogICAgICAvLyDmmK/lkKbmmL7npLrlvLnlh7rlsYIKICAgICAgb3BlbjogZmFsc2UsCiAgICAgIC8vIOafpeivouWPguaVsAogICAgICBxdWVyeVBhcmFtczogewogICAgICAgIHVzZXJJZDpudWxsLAogICAgICAgIHdvcmtObzogbnVsbCwKICAgICAgICBkZXB0SWQ6bnVsbCwKICAgICAgICBhc3Nlc3NEYXRlOiBudWxsLAogICAgICB9LAogICAgICAvLyDogIPmoLjlubTmnIjmlofmnKzmmL7npLoKICAgICAgYXNzZXNzRGF0ZVRleHQ6bnVsbCwKICAgICAgLy8g6YOo6Zeo5pi+56S6CiAgICAgIGRlcHROYW1lOm51bGwsCiAgICAgIC8vIOihqOWNleWPguaVsAogICAgICBmb3JtOiB7fSwKICAgICAgLy8g6KGo5Y2V5qCh6aqMCiAgICAgIHJ1bGVzOiB7CiAgICAgIH0sCiAgICAgIC8vIOeUqOaIt+S/oeaBrwogICAgICB1c2VySW5mbzp7fSwKICAgICAgLy8g5ZCI5bm25Y2V5YWD5qC85L+h5oGvCiAgICAgIHNwYW5MaXN0OnsKICAgICAgICBpdGVtTGlzdDpbXSwKICAgICAgICBzdGFuZGFyZExpc3Q6W10KICAgICAgfSwKICAgICAgLy8g5piv5ZCm5pi+56S66YeN572u5oyJ6ZKuCiAgICAgIHJlc2V0U2hvdzpmYWxzZSwKICAgICAgLy8g5piv5ZCm5Y+q6K+7CiAgICAgIHJlYWRPbmx5OmZhbHNlLAogICAgICAvLyDpg6jpl6jpgInpobkKICAgICAgZGVwdE9wdGlvbnM6W10sCiAgICAgIC8vIOiHquivhOS/oeaBr0lkCiAgICAgIGlkOm51bGwsCiAgICAgIC8vIOiHquivhOWIhuaVsAogICAgICBzZWxmU2NvcmU6MTAwLAogICAgICAvLyDnirbmgIEKICAgICAgc3RhdHVzOiIwIiwKICAgICAgLy8g6Ieq6K+E5L+h5oGvCiAgICAgIGluZm86e30sCiAgICAgIC8vIOaoquWQkeiiq+iAg+ivhOS/oeaBrwogICAgICBiZUFzc2Vzc2VkTGlzdDpbXSwKICAgICAgLy8g6YCA5Zue55CG55SxCiAgICAgIHJlamVjdFJlYXNvbjoiIiwKICAgICAgLy8g6Ieq6K+E562+5ZCNCiAgICAgIHNlbGZTaWduOiIiLAogICAgICAvLyDnrb7lkI3mnb/phY3nva4KICAgICAgc2lnbk9wdGlvbnM6IHsKICAgICAgICBvbkJlZ2luOiAoKSA9PiB0aGlzLiRyZWZzLnNpZ25hdHVyZVBhZC5yZXNpemVDYW52YXMoKSwKICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6ICdyZ2JhKDI1NSwgMjU1LCAyNTUsIDEpJwogICAgICB9LAogICAgICBzaWduOiIiLAogICAgICBmaWxlOm51bGwsCiAgICAgIGZpbGVMaXN0OltdLAogICAgICB1cGxvYWQ6IHsKICAgICAgICAvLyDkuIrkvKDnmoTlnLDlnYAKICAgICAgICB1cmw6IHByb2Nlc3MuZW52LlZVRV9BUFBfQkFTRV9BUEkgKyAiL2FwcC9jb21tb24vdXBsb2FkTWluaW8iLAogICAgICAgIGlzVXBsb2FkaW5nOiBmYWxzZSwKICAgICAgfSwKICAgIH07CiAgfSwKICBjcmVhdGVkKCkgewogICAgdGhpcy5xdWVyeVBhcmFtcy5hc3Nlc3NEYXRlID0gdGhpcy5nZXREZWZhdWx0QXNzZXNzRGF0ZSgpCiAgICB0aGlzLmFzc2Vzc0RhdGVUZXh0ID0gdGhpcy5xdWVyeVBhcmFtcy5hc3Nlc3NEYXRlLnJlcGxhY2UoIi0iLCIg5bm0ICIpICsgIiDmnIgiOwogICAgLy8gdGhpcy5nZXRTZWxmQXNzZXNzVXNlcigpOwogICAgdGhpcy5nZXRSZXBvcnREZXB0TGlzdCgpOwogIH0sCiAgbWV0aG9kczogewoKICAgIC8vIOiOt+WPlum7mOiupOiAg+aguOaXpeacnwogICAgZ2V0RGVmYXVsdEFzc2Vzc0RhdGUoKSB7CiAgICAgIGNvbnN0IG5vdyA9IG5ldyBEYXRlKCk7CiAgICAgIGNvbnN0IGN1cnJlbnREYXkgPSBub3cuZ2V0RGF0ZSgpOwoKICAgICAgbGV0IHRhcmdldERhdGU7CiAgICAgIGlmIChjdXJyZW50RGF5IDwgMTApIHsKICAgICAgICAvLyDlvZPliY3ml6XmnJ/lsI/kuo4xMOaXpe+8jOm7mOiupOS4uuS4iuS4quaciAogICAgICAgIHRhcmdldERhdGUgPSBuZXcgRGF0ZShub3cuZ2V0RnVsbFllYXIoKSwgbm93LmdldE1vbnRoKCkgLSAxLCAxKTsKICAgICAgfSBlbHNlIHsKICAgICAgICAvLyDlvZPliY3ml6XmnJ/lpKfkuo7nrYnkuo4xMOaXpe+8jOm7mOiupOS4uuW9k+aciAogICAgICAgIHRhcmdldERhdGUgPSBuZXcgRGF0ZShub3cuZ2V0RnVsbFllYXIoKSwgbm93LmdldE1vbnRoKCksIDEpOwogICAgICB9CgogICAgICAvLyDmoLzlvI/ljJbkuLogWVlZWS1NIOagvOW8jwogICAgICBjb25zdCB5ZWFyID0gdGFyZ2V0RGF0ZS5nZXRGdWxsWWVhcigpOwogICAgICBjb25zdCBtb250aCA9IHRhcmdldERhdGUuZ2V0TW9udGgoKSArIDE7CiAgICAgIHJldHVybiBgJHt5ZWFyfS0ke21vbnRofWA7CiAgICB9LAoKICAgIC8vIOiOt+WPlumDqOmXqOS/oeaBrwogICAgZ2V0UmVwb3J0RGVwdExpc3QoKXsKICAgICAgZ2V0UmVwb3J0RGVwdExpc3QoKS50aGVuKHJlcyA9PiB7CiAgICAgICAgY29uc29sZS5sb2cocmVzKQogICAgICAgIGlmKHJlcy5jb2RlID09IDIwMCl7CiAgICAgICAgICB0aGlzLmhhbmRsZURlcHRMaXN0KHJlcy5kYXRhKTsKICAgICAgICAgIC8vIOagueaNrumDqOmXqOiOt+WPlueUqOaIt+S/oeaBrwogICAgICAgICAgdGhpcy5nZXRCeVdvcmtOb0RlcHRJZCgpOwogICAgICAgIH0KICAgICAgfSkKICAgIH0sCiAgICAvLyDojrflj5bnlKjmiLfkv6Hmga8KICAgIGdldEJ5V29ya05vRGVwdElkKCl7CiAgICAgIGdldEJ5V29ya05vRGVwdElkKHtkZXB0SWQ6dGhpcy5xdWVyeVBhcmFtcy5kZXB0SWR9KS50aGVuKHJlcyA9PiB7CiAgICAgICAgY29uc29sZS5sb2cocmVzKQogICAgICAgIGlmKHJlcy5jb2RlID09IDIwMCl7CiAgICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnVzZXJJZCA9IHJlcy5kYXRhLmlkOwogICAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy53b3JrTm8gPSByZXMuZGF0YS53b3JrTm87CiAgICAgICAgICB0aGlzLnVzZXJJbmZvID0gcmVzLmRhdGE7CiAgICAgICAgICB0aGlzLmdldExpc3QoKTsKICAgICAgICAgIC8vIOiOt+WPluiiq+iAg+aguOS/oeaBrwogICAgICAgICAgdGhpcy5nZXRCZUFzc2Vzc2VkTGlzdCgpOwogICAgICAgIH0KICAgICAgfSkKICAgIH0sCiAgICAKICAgIC8vIOiOt+WPluiiq+iAg+aguOS/oeaBrwogICAgZ2V0QmVBc3Nlc3NlZExpc3QoKXsKICAgICAgbGlzdEJlQXNzZXNzZWQoe2RlcHRJZDp0aGlzLnF1ZXJ5UGFyYW1zLmRlcHRJZCxhc3Nlc3NEYXRlOnRoaXMucXVlcnlQYXJhbXMuYXNzZXNzRGF0ZX0pLnRoZW4ocmVzID0+ewogICAgICAgIGxldCBiZUFzc2Vzc2VkTGlzdCA9IFtdOwogICAgICAgIGlmKHJlcy5jb2RlID09IDIwMCl7CiAgICAgICAgICBpZihyZXMuZGF0YS5sZW5ndGggPiAwKXsKICAgICAgICAgICAgcmVzLmRhdGEuZm9yRWFjaChpdGVtID0+IHsKICAgICAgICAgICAgICBiZUFzc2Vzc2VkTGlzdCA9IFsuLi5iZUFzc2Vzc2VkTGlzdCwuLi5pdGVtLmhyTGF0ZXJhbEFzc2Vzc0luZm9MaXN0XQogICAgICAgICAgICB9KQogICAgICAgICAgICB0aGlzLmJlQXNzZXNzZWRMaXN0ID0gYmVBc3Nlc3NlZExpc3Q7CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICAgIGNvbnNvbGUubG9nKGJlQXNzZXNzZWRMaXN0KQogICAgICB9KQogICAgfSwKICAgIC8qKiDmn6Xor6Lnu6nmlYjogIPmoLgt6Ieq6K+E5oyH5qCH6YWN572u5YiX6KGoICovCiAgICBnZXRMaXN0KCkgewogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOwogICAgICBnZXRJbmZvQnlEYXRlKHRoaXMucXVlcnlQYXJhbXMpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIGNvbnNvbGUubG9nKHJlc3BvbnNlLmRhdGEpOwogICAgICAgIC8vIGNvbnNvbGUubG9nKHR5cGVvZiByZXNwb25zZS5kYXRhKTsKICAgICAgICBpZiAoQXJyYXkuaXNBcnJheShyZXNwb25zZS5kYXRhKSkgewogICAgICAgICAgLy8g5oyH5qCH6YWN572u5pWw5o2uCiAgICAgICAgICB0aGlzLmhhbmRsZVNwYW5MaXN0KHJlc3BvbnNlLmRhdGEpOwogICAgICAgICAgdGhpcy5saXN0ID0gcmVzcG9uc2UuZGF0YS5tYXAoaXRlbSA9PiB7aXRlbS5wZXJmb3JtYW5jZSA9ICIiO2l0ZW0uZGVQb2ludHMgPSBudWxsOyByZXR1cm4gaXRlbX0pOwogICAgICAgICAgdGhpcy5zdGF0dXMgPSAiMCI7CiAgICAgICAgICB0aGlzLnJlYWRPbmx5ID0gZmFsc2U7CiAgICAgICAgICB0aGlzLnJlc2V0U2hvdyA9IGZhbHNlOwogICAgICAgICAgdGhpcy5yZWplY3RSZWFzb24gPSBudWxsOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAvLyDoh6ror4Tkv6Hmga8KICAgICAgICAgIGxldCBpbmZvID0gcmVzcG9uc2UuZGF0YTsKICAgICAgICAgIGxldCBsaXN0ID0gSlNPTi5wYXJzZShpbmZvLmNvbnRlbnQpOwogICAgICAgICAgdGhpcy5oYW5kbGVTcGFuTGlzdChsaXN0KTsKICAgICAgICAgIHRoaXMubGlzdCA9IGxpc3Q7CiAgICAgICAgICB0aGlzLmlkID0gaW5mby5pZDsKICAgICAgICAgIHRoaXMuc2VsZlNjb3JlID0gaW5mby5zZWxmU2NvcmU7CiAgICAgICAgICB0aGlzLnJlamVjdFJlYXNvbiA9IGluZm8ucmVqZWN0UmVhc29uOwogICAgICAgICAgdGhpcy5zdGF0dXMgPSBpbmZvLnN0YXR1czsKICAgICAgICAgIGlmKGluZm8uc2lnbil7CiAgICAgICAgICAgIHRoaXMuc2VsZlNpZ24gPSBKU09OLnBhcnNlKGluZm8uc2lnbik7CiAgICAgICAgICB9CiAgICAgICAgICB0aGlzLmluZm8gPSBpbmZvOwogICAgICAgICAgaWYoaW5mby5zdGF0dXMgPT0gIjAiKXsKICAgICAgICAgICAgdGhpcy5yZWFkT25seSA9IGZhbHNlOwogICAgICAgICAgICB0aGlzLnJlc2V0U2hvdyA9IHRydWU7CiAgICAgICAgICB9ZWxzZXsKICAgICAgICAgICAgdGhpcy5yZWFkT25seSA9IHRydWU7CiAgICAgICAgICAgIHRoaXMucmVzZXRTaG93ID0gZmFsc2U7CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOwogICAgICB9KTsKICAgIH0sCgogICAgLy8g5aSE55CG5YiX6KGoCiAgICBoYW5kbGVTcGFuTGlzdChkYXRhKXsKICAgICAgbGV0IGl0ZW1MaXN0ID0gW107CiAgICAgIGxldCBzdGFuZGFyZExpc3QgPSBbXTsKICAgICAgbGV0IGl0ZW1GbGFnID0gMDsKICAgICAgbGV0IHN0YW5kYXJkRmxhZyA9IDA7CiAgICAgIGZvcihsZXQgaSA9IDA7IGkgPCBkYXRhLmxlbmd0aDsgaSsrKXsKICAgICAgICAvLyDnm7jlkIzogIPmoLjpobnjgIHor4TliIbmoIflh4blkIjlubYKICAgICAgICBpZihpID09IDApewogICAgICAgICAgaXRlbUxpc3QucHVzaCh7CiAgICAgICAgICAgIHJvd3NwYW46IDEsCiAgICAgICAgICAgIGNvbHNwYW46IDEKICAgICAgICAgIH0pCiAgICAgICAgICBzdGFuZGFyZExpc3QucHVzaCh7CiAgICAgICAgICAgIHJvd3NwYW46IDEsCiAgICAgICAgICAgIGNvbHNwYW46IDEKICAgICAgICAgIH0pCiAgICAgICAgfWVsc2V7CiAgICAgICAgICAvLyDogIPmoLjpobkKICAgICAgICAgIGlmKGRhdGFbaSAtIDFdLml0ZW0gPT0gZGF0YVtpXS5pdGVtKXsKICAgICAgICAgICAgaXRlbUxpc3QucHVzaCh7CiAgICAgICAgICAgICAgcm93c3BhbjogMCwKICAgICAgICAgICAgICBjb2xzcGFuOiAwCiAgICAgICAgICAgIH0pCiAgICAgICAgICAgIGl0ZW1MaXN0W2l0ZW1GbGFnXS5yb3dzcGFuICs9IDE7CiAgICAgICAgICB9ZWxzZXsKICAgICAgICAgICAgaXRlbUxpc3QucHVzaCh7CiAgICAgICAgICAgICAgcm93c3BhbjogMSwKICAgICAgICAgICAgICBjb2xzcGFuOiAxCiAgICAgICAgICAgIH0pCiAgICAgICAgICAgIGl0ZW1GbGFnID0gaTsKICAgICAgICAgIH0KICAgICAgICAgIC8vIOivhOWIhuagh+WHhgogICAgICAgICAgaWYoZGF0YVtpIC0gMV0uc3RhbmRhcmQgPT0gZGF0YVtpXS5zdGFuZGFyZCl7CiAgICAgICAgICAgIHN0YW5kYXJkTGlzdC5wdXNoKHsKICAgICAgICAgICAgICByb3dzcGFuOiAwLAogICAgICAgICAgICAgIGNvbHNwYW46IDAKICAgICAgICAgICAgfSkKICAgICAgICAgICAgc3RhbmRhcmRMaXN0W3N0YW5kYXJkRmxhZ10ucm93c3BhbiArPSAxOwogICAgICAgICAgfWVsc2V7CiAgICAgICAgICAgIHN0YW5kYXJkTGlzdC5wdXNoKHsKICAgICAgICAgICAgICByb3dzcGFuOiAxLAogICAgICAgICAgICAgIGNvbHNwYW46IDEKICAgICAgICAgICAgfSkKICAgICAgICAgICAgc3RhbmRhcmRGbGFnID0gaTsKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0KICAgICAgdGhpcy5zcGFuTGlzdC5pdGVtTGlzdCA9IGl0ZW1MaXN0OwogICAgICB0aGlzLnNwYW5MaXN0LnN0YW5kYXJkTGlzdCA9IHN0YW5kYXJkTGlzdDsKICAgIH0sCgoKICAgIC8vIOWkhOeQhumDqOmXqOS4i+aLiemAiemhuQogICAgaGFuZGxlRGVwdExpc3QoZGF0YSl7CiAgICAgIC8vIGxldCBzeWIgPSBbIueCvOmTgeS6i+S4mumDqCIsIueCvOmSouS6i+S4mumDqCIsIui9p+mSouS6i+S4mumDqCIsIueJueadv+S6i+S4mumDqCIsIuWKqOWKm+S6i+S4mumDqCIsIueJqea1geS6i+S4mumDqCIsIueglOeptumZoiJdOwogICAgICBsZXQgZGVwdExpc3QgPSBbXTsKICAgICAgZGF0YS5mb3JFYWNoKGl0ZW0gPT4gewogICAgICAgIC8vaWYoc3liLmluZGV4T2YoaXRlbS5kZXB0TmFtZSkgPT0gLTEpewogICAgICAgICAgZGVwdExpc3QucHVzaCh7CiAgICAgICAgICAgIGRlcHROYW1lOml0ZW0uZGVwdE5hbWUsCiAgICAgICAgICAgIGRlcHRJZDppdGVtLmRlcHRJZAogICAgICAgICAgfSkKICAgICAgICAvL30KICAgICAgfSkKICAgICAgdGhpcy5kZXB0T3B0aW9ucyA9IGRlcHRMaXN0OwogICAgICBpZihkZXB0TGlzdC5sZW5ndGggPiAwKXsKICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmRlcHRJZCA9IGRlcHRMaXN0WzBdLmRlcHRJZDsKICAgICAgICB0aGlzLmRlcHROYW1lID0gZGVwdExpc3RbMF0uZGVwdE5hbWU7CiAgICAgIH0KICAgIH0sCgoKICAgIC8qKiDmkJzntKLmjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZVF1ZXJ5KCkgewogICAgICB0aGlzLmFzc2Vzc0RhdGVUZXh0ID0gdGhpcy5xdWVyeVBhcmFtcy5hc3Nlc3NEYXRlLnJlcGxhY2UoIi0iLCIg5bm0ICIpICsgIiDmnIgiOwogICAgICB0aGlzLmRlcHRPcHRpb25zLmZvckVhY2goaXRlbSA9PiB7CiAgICAgICAgaWYoaXRlbS5kZXB0SWQgPT0gdGhpcy5xdWVyeVBhcmFtcy5kZXB0SWQpewogICAgICAgICAgdGhpcy5kZXB0TmFtZSA9IGl0ZW0uZGVwdE5hbWU7CiAgICAgICAgfQogICAgICB9KQogICAgICB0aGlzLmlkID0gbnVsbDsKICAgICAgdGhpcy5pbmZvID0gbnVsbDsKICAgICAgdGhpcy5zZWxmU2NvcmUgPSAiIjsKICAgICAgdGhpcy5nZXRCeVdvcmtOb0RlcHRJZCgpOwogICAgfSwKCiAgICAvLyDkv53lrZgKICAgIHNhdmUoKXsKICAgICAgaWYodGhpcy5saXN0Lmxlbmd0aCA9PSAwKXsKICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgdHlwZTogJ3dhcm5pbmcnLAogICAgICAgICAgICBtZXNzYWdlOiAn5pyq6YWN572u55u45YWz5L+h5oGv77yM6K+35YWI6YWN572u5oyH5qCHJwogICAgICAgICAgfSk7CiAgICAgICAgICByZXR1cm4gZmFsc2U7CiAgICAgIH0KICAgICAgbGV0IGRhdGEgPSB0aGlzLmhhbmRsZURhdGEodGhpcy5saXN0KTsKICAgICAgbGV0IGZvcm0gPSB7CiAgICAgICAgaWQ6dGhpcy5pZCwKICAgICAgICB3b3JrTm86dGhpcy51c2VySW5mby53b3JrTm8sCiAgICAgICAgYXNzZXNzRGF0ZTp0aGlzLnF1ZXJ5UGFyYW1zLmFzc2Vzc0RhdGUsCiAgICAgICAgZGVwdElkOnRoaXMucXVlcnlQYXJhbXMuZGVwdElkLAogICAgICAgIGNvbnRlbnQ6SlNPTi5zdHJpbmdpZnkoZGF0YSksCiAgICAgICAgc3RhdHVzOiIwIiwKICAgICAgICB1c2VySWQ6dGhpcy5xdWVyeVBhcmFtcy51c2VySWQsCiAgICAgICAgZGVwdE5hbWU6dGhpcy5kZXB0TmFtZSwKICAgICAgICBuYW1lOnRoaXMudXNlckluZm8ubmFtZSwKICAgICAgICBzZWxmU2NvcmU6dGhpcy5zZWxmU2NvcmUsCiAgICAgICAgam9iOnRoaXMudXNlckluZm8uam9iLAogICAgICAgIHBvc3RUeXBlOnRoaXMudXNlckluZm8ucG9zdFR5cGUKICAgICAgfQogICAgICBzYXZlSW5mbyhmb3JtKS50aGVuKHJlcyA9PiB7CiAgICAgICAgaWYocmVzLmNvZGUgPT0gMjAwKXsKICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgIHR5cGU6ICdzdWNjZXNzJywKICAgICAgICAgICAgbWVzc2FnZTogJ+S/neWtmOaIkOWKnyEnCiAgICAgICAgICB9KTsKICAgICAgICB9CiAgICAgIH0pCiAgICB9LAoKICAgIC8vIOehruiupOaPkOS6pOeCueWHu+S6i+S7tgogICAgc3VibWl0KCl7CiAgICAgICAgdGhpcy4kY29uZmlybSgn56Gu6K6k5ZCO5bCG5rWB6L2s6Iez5LiL5LiA6IqC54K5LCDmmK/lkKbnu6fnu60/JywgJ+aPkOekuicsIHsKICAgICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJywKICAgICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLAogICAgICAgICAgdHlwZTogJ3dhcm5pbmcnCiAgICAgICAgfSkudGhlbigoKSA9PiB7CiAgICAgICAgICB0aGlzLnN1Ym1pdERhdGEoKTsKICAgICAgICB9KS5jYXRjaCgoKSA9PiB7CgogICAgICAgIH0pOwogICAgfSwKCiAgICBvblN1Ym1pdCgpewogICAgICBpZih0aGlzLnZlcmlmeUluc2VydCgpKXsKICAgICAgICB0aGlzLm9wZW5TaWduID0gdHJ1ZTsKICAgICAgfQogICAgfSwKCiAgICBjbGVhclNpZ24oKXsKICAgICAgdGhpcy4kcmVmcy5zaWduYXR1cmVQYWQuY2xlYXJTaWduYXR1cmUoKTsKICAgIH0sCgogICAgLy8g5o+Q5Lqk5pWw5o2u6aqM6K+BCiAgICB2ZXJpZnlJbnNlcnQoKXsKICAgICAgaWYodGhpcy5saXN0Lmxlbmd0aCA9PSAwKXsKICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgdHlwZTogJ3dhcm5pbmcnLAogICAgICAgICAgICBtZXNzYWdlOiAn5pyq6YWN572u55u45YWz5L+h5oGv77yM6K+35YWI6YWN572u5oyH5qCHJwogICAgICAgICAgfSk7CiAgICAgICAgICByZXR1cm4gZmFsc2U7CiAgICAgIH0KICAgICAgZm9yKGxldCBpID0gMDsgaSA8IHRoaXMubGlzdC5sZW5ndGg7IGkrKyl7CiAgICAgICAgaWYoIXRoaXMubGlzdFtpXS5wZXJmb3JtYW5jZSB8fCAhdGhpcy5saXN0W2ldLmRlUG9pbnRzKXsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICB0eXBlOiAnd2FybmluZycsCiAgICAgICAgICAgIG1lc3NhZ2U6ICfkv6Hmga/mnKrloavlhpnlrozmlbQnCiAgICAgICAgICB9KTsKICAgICAgICAgIHJldHVybiBmYWxzZTsKICAgICAgICB9ZWxzZSBpZih0aGlzLmxpc3RbaV0uZGVQb2ludHMgIT0gMCAmJiAhdGhpcy5saXN0W2ldLnBvaW50c1JlYXNvbil7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgdHlwZTogJ3dhcm5pbmcnLAogICAgICAgICAgICBtZXNzYWdlOiAn5pyJ5Yqg5YeP5YiG55qE6K+35aGr5YaZ5Y6f5ZugJwogICAgICAgICAgfSk7CiAgICAgICAgICByZXR1cm4gZmFsc2U7CiAgICAgICAgfQogICAgICB9CiAgICAgIGlmKCF0aGlzLnNlbGZTY29yZSl7CiAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJywKICAgICAgICAgICAgbWVzc2FnZTogJ+ivt+Whq+WGmeiHquivhOWIhuaVsCcKICAgICAgICAgIH0pOwogICAgICAgICAgcmV0dXJuIGZhbHNlOwogICAgICB9CiAgICAgIHJldHVybiB0cnVlOwogICAgfSwKCiAgICAvLyDmlrDlop7mlbDmja4KICAgIHN1Ym1pdERhdGEoKXsKICAgICAgbGV0IGRhdGEgPSB0aGlzLmhhbmRsZURhdGEodGhpcy5saXN0KTsKICAgICAgbGV0IGZvcm0gPSB7CiAgICAgICAgaWQ6dGhpcy5pZCwKICAgICAgICB3b3JrTm86dGhpcy51c2VySW5mby53b3JrTm8sCiAgICAgICAgYXNzZXNzRGF0ZTp0aGlzLnF1ZXJ5UGFyYW1zLmFzc2Vzc0RhdGUsCiAgICAgICAgZGVwdElkOnRoaXMucXVlcnlQYXJhbXMuZGVwdElkLAogICAgICAgIGNvbnRlbnQ6SlNPTi5zdHJpbmdpZnkoZGF0YSksCiAgICAgICAgc3RhdHVzOiIxIiwKICAgICAgICB1c2VySWQ6dGhpcy5xdWVyeVBhcmFtcy51c2VySWQsCiAgICAgICAgZGVwdE5hbWU6dGhpcy5kZXB0TmFtZSwKICAgICAgICBuYW1lOnRoaXMudXNlckluZm8ubmFtZSwKICAgICAgICBzZWxmU2NvcmU6dGhpcy5zZWxmU2NvcmUsCiAgICAgICAgam9iOnRoaXMudXNlckluZm8uam9iLAogICAgICAgIHBvc3RUeXBlOnRoaXMudXNlckluZm8ucG9zdFR5cGUsCiAgICAgICAgYXZlcmFnZUxpbmtGbGFnOnRoaXMudXNlckluZm8uYXZlcmFnZUxpbmtGbGFnLAogICAgICAgIGJlbmVmaXRMaW5rRmxhZzp0aGlzLnVzZXJJbmZvLmJlbmVmaXRMaW5rRmxhZywKICAgICAgICBzaWduOkpTT04uc3RyaW5naWZ5KHRoaXMuc2lnbikKICAgICAgfQogICAgICBzdWJtaXRJbmZvKGZvcm0pLnRoZW4ocmVzID0+IHsKICAgICAgICBpZihyZXMuY29kZSA9PSAyMDApewogICAgICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICAgICAgICB0aGlzLnNpZ24gPSAiIjsKICAgICAgICAgIHRoaXMuJHJlZnMuc2lnbmF0dXJlUGFkLmNsZWFyU2lnbmF0dXJlKCk7CiAgICAgICAgICB0aGlzLm9wZW5TaWduID0gZmFsc2U7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnLAogICAgICAgICAgICBtZXNzYWdlOiAn5o+Q5Lqk5oiQ5YqfIScKICAgICAgICAgIH0pOwogICAgICAgIH1lbHNlewoKICAgICAgICB9CiAgICAgIH0pCiAgICB9LAoKICAgIC8vIOS/neWtmOmHjee9rgogICAgcmVzZXRJbmZvKCl7CiAgICAgIC8vIOWIoOmZpOS/neWtmOS/oeaBrwogICAgICBkZWxJbmZvKHtpZDp0aGlzLmlkfSkudGhlbihyZXMgPT4gewogICAgICAgIGlmKHJlcy5jb2RlID09IDIwMCl7CiAgICAgICAgICB0aGlzLmlkID0gbnVsbDsKICAgICAgICAgIHRoaXMuc2VsZlNjb3JlID0gbnVsbDsKICAgICAgICAgIC8vIOiOt+WPlumFjee9ruS/oeaBrwogICAgICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICAgICAgfQogICAgICB9KQogICAgfSwKCiAgICAvLyDlpITnkIbmj5DkuqTmlbDmja4KICAgIGhhbmRsZURhdGEoZGF0YSl7CiAgICAgIGxldCByZXN1bHQgPSBbXQogICAgICBkYXRhLmZvckVhY2goaXRlbSA9PiB7CiAgICAgICAgbGV0IGZvcm0gPSB7CiAgICAgICAgICBpdGVtOiBpdGVtLml0ZW0sCiAgICAgICAgICBjYXRlZ29yeTogaXRlbS5jYXRlZ29yeSwKICAgICAgICAgIHRhcmdldDogaXRlbS50YXJnZXQsCiAgICAgICAgICBzdGFuZGFyZDogaXRlbS5zdGFuZGFyZCwKICAgICAgICAgIHBlcmZvcm1hbmNlOiBpdGVtLnBlcmZvcm1hbmNlLAogICAgICAgICAgZGVQb2ludHM6IGl0ZW0uZGVQb2ludHMsCiAgICAgICAgICBwb2ludHNSZWFzb246aXRlbS5wb2ludHNSZWFzb24KICAgICAgICB9OwogICAgICAgIHJlc3VsdC5wdXNoKGZvcm0pOwogICAgICB9KQogICAgICByZXR1cm4gcmVzdWx0CiAgICB9LAoKICAgIC8qKiDmoIflh4bphY3nva7ot7PovawgKi8KICAgIGhhbmRsZUNvbmZpZygpewogICAgICBnZXRCeVdvcmtOb0RlcHRJZCh7ZGVwdElkOnRoaXMucXVlcnlQYXJhbXMuZGVwdElkfSkudGhlbihyZXMgPT4gewogICAgICAgIGNvbnNvbGUubG9nKHJlcykKICAgICAgICBpZihyZXMuY29kZSA9PSAyMDApewogICAgICAgICAgaWYocmVzLmRhdGEuaWQpewogICAgICAgICAgdGhpcy4kcm91dGVyLnB1c2goewogICAgICAgICAgICBwYXRoOiIvYXNzZXNzL3NlbGYvdXNlci9kZXRhaWwiLAogICAgICAgICAgICBxdWVyeTp7CiAgICAgICAgICAgICAgdXNlcklkOnJlcy5kYXRhLmlkCiAgICAgICAgICAgIH0KICAgICAgICAgIH0pCiAgICAgICAgfQogICAgICAgIH0KICAgICAgfSkKICAgICAgCiAgICB9LAoKCiAgICAvLyDlkIjlubbljZXlhYPmoLzmlrnms5UKICAgIG9iamVjdFNwYW5NZXRob2QoeyByb3csIGNvbHVtbiwgcm93SW5kZXgsIGNvbHVtbkluZGV4IH0pIHsKICAgICAgLy8g56ys5LiA5YiX55u45ZCM6aG55ZCI5bm2CiAgICAgIGlmIChjb2x1bW5JbmRleCA9PT0gMCkgewogICAgICAgIHJldHVybiB0aGlzLnNwYW5MaXN0Lml0ZW1MaXN0W3Jvd0luZGV4XTsKICAgICAgfQogICAgICAvLyDor4TliIbmoIflh4bnm7jlkIzlkIjlubYKICAgICAgaWYoY29sdW1uSW5kZXggPT09IDMpewogICAgICAgIHJldHVybiB0aGlzLnNwYW5MaXN0LnN0YW5kYXJkTGlzdFtyb3dJbmRleF07CiAgICAgIH0KICAgICAgLy8g57G75Yir5peg5YaF5a65IOWQiOW5tgogICAgICBpZihjb2x1bW5JbmRleCA9PT0gMSl7CiAgICAgICAgaWYoIXJvdy5jYXRlZ29yeSl7CiAgICAgICAgICByZXR1cm4gewogICAgICAgICAgICByb3dzcGFuOiAwLAogICAgICAgICAgICBjb2xzcGFuOiAwCiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9CiAgICAgIGlmKGNvbHVtbkluZGV4ID09PSAyKXsKICAgICAgICBpZighcm93LmNhdGVnb3J5KXsKICAgICAgICAgIHJldHVybiB7CiAgICAgICAgICAgIHJvd3NwYW46IDEsCiAgICAgICAgICAgIGNvbHNwYW46IDIKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0KICAgIH0sCgogICAgLy8g6KKr6ICD5qC45LqL6aG554K55Ye75LqL5Lu2CiAgICBoYW5kbGVCZUFzc2Vzc2VkQ2xpY2socm93LG9wdGlvblJvdyxpbmRleCl7CiAgICAgIGNvbnNvbGUubG9nKHJvdykKICAgICAgLy8g5bCG5LqL6aG55aGr5YWl5a6M5oiQ5a6e57up5YiX77yI5byD55So77yJCiAgICAgIC8vIGlmKHJvdy5wZXJmb3JtYW5jZSl7CiAgICAgIC8vICAgdGhpcy4kc2V0KHJvdywgJ3BlcmZvcm1hbmNlJywgcm93LnBlcmZvcm1hbmNlICsgIu+8myIgKyBvcHRpb25Sb3cuYXNzZXNzQ29udGVudCk7CiAgICAgIC8vIH1lbHNlewogICAgICAvLyAgIHRoaXMuJHNldChyb3csICdwZXJmb3JtYW5jZScsIG9wdGlvblJvdy5hc3Nlc3NDb250ZW50KTsKICAgICAgLy8gfQogICAgICAKICAgICAgLy8g5bCG5YiG5pWw5aGr5YWl5Yqg5YeP5YiG5YiXCiAgICAgIGlmKHJvdy5kZVBvaW50cyl7CiAgICAgICAgdGhpcy4kc2V0KHJvdywgJ2RlUG9pbnRzJywgTnVtYmVyKHJvdy5kZVBvaW50cykgKyBOdW1iZXIob3B0aW9uUm93LmRlZHVjdGlvbk9mUG9pbnQpKTsKICAgICAgfWVsc2V7CiAgICAgICAgdGhpcy4kc2V0KHJvdywgJ2RlUG9pbnRzJywgTnVtYmVyKG9wdGlvblJvdy5kZWR1Y3Rpb25PZlBvaW50KSk7CiAgICAgIH0KICAgICAgCiAgICAgIC8vIOWwhuS6i+mhuSvliIbmlbDloavlhaXliqDlh4/liIbnkIbnlLHliJcKICAgICAgbGV0IHJlYXNvbkNvbnRlbnQgPSBvcHRpb25Sb3cuYXNzZXNzQ29udGVudCArICIoIiArIG9wdGlvblJvdy5kZWR1Y3Rpb25PZlBvaW50ICsgIuWIhikiOwogICAgICBpZihyb3cucG9pbnRzUmVhc29uKXsKICAgICAgICB0aGlzLiRzZXQocm93LCAncG9pbnRzUmVhc29uJywgcm93LnBvaW50c1JlYXNvbiArICLvvJsiICsgcmVhc29uQ29udGVudCk7CiAgICAgIH1lbHNlewogICAgICAgIHRoaXMuJHNldChyb3csICdwb2ludHNSZWFzb24nLCByZWFzb25Db250ZW50KTsKICAgICAgfQogICAgICAKICAgICAgdGhpcy4kcmVmc1tgcG9wb3ZlciR7aW5kZXh9YF0uc2hvd1BvcHBlciA9IGZhbHNlOwogICAgICAvLyDph43mlrDorqHnrpfoh6ror4TliIbmlbAKICAgICAgdGhpcy5zY29yZUlucHV0KCk7CiAgICB9LAoKICAgIC8vIOWKoOWHj+WIhui+k+WFpQogICAgc2NvcmVJbnB1dChyb3cgPSBudWxsKXsKICAgICAgLy8g6aqM6K+B5pyI5bqm6YeN54K55bel5L2c55qE5Yqg5YeP5YiG5Y+q6IO95Li6MeOAgTPmiJY177yI5LuF5b2T5Lyg5YWlcm935Y+C5pWw5pe26L+b6KGM6aqM6K+B77yJCiAgICAgIGlmIChyb3cgJiYgcm93Lml0ZW0pIHsKICAgICAgICBsZXQgbm9TcGFjZVN0ciA9IHJvdy5pdGVtLnJlcGxhY2UoL1xzKy9nLCAnJyk7CiAgICAgICAgaWYgKG5vU3BhY2VTdHIuaW5jbHVkZXMoIuaciOW6pumHjeeCueW3peS9nCIpKSB7CiAgICAgICAgICBsZXQgdmFsdWUgPSByb3cuZGVQb2ludHM7CiAgICAgICAgICBpZiAodmFsdWUgIT09IG51bGwgJiYgdmFsdWUgIT09IHVuZGVmaW5lZCAmJiB2YWx1ZSAhPT0gJycpIHsKICAgICAgICAgICAgbGV0IG51bVZhbHVlID0gTnVtYmVyKHZhbHVlKTsKICAgICAgICAgICAgaWYgKCFbMSwgMywgNV0uaW5jbHVkZXMobnVtVmFsdWUpKSB7CiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgICB0eXBlOiAnd2FybmluZycsCiAgICAgICAgICAgICAgICBtZXNzYWdlOiAn5pyI5bqm6YeN54K55bel5L2c55qE5Yqg5YeP5YiG5Y+q6IO95Li6MeWIhuOAgTPliIbmiJY15YiGJwogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgIC8vIOmHjee9ruS4uuepuuWAvAogICAgICAgICAgICAgIHRoaXMuJHNldChyb3csICdkZVBvaW50cycsIG51bGwpOwogICAgICAgICAgICAgIHJldHVybjsKICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfQoKICAgICAgLy8g6YeN5paw6K6h566X6Ieq6K+E5YiG5pWwCiAgICAgIGxldCBkZVBvaW50cyA9IDA7CiAgICAgIGxldCBwb2ludHMgPSAwOwogICAgICB0aGlzLmxpc3QuZm9yRWFjaChpdGVtID0+IHsKICAgICAgICBsZXQgbm9TcGFjZVN0ciA9IGl0ZW0uaXRlbS5yZXBsYWNlKC9ccysvZywgJycpOwogICAgICAgIGlmKGl0ZW0uZGVQb2ludHMgJiYgbm9TcGFjZVN0ci5pbmNsdWRlcygi5pyI5bqm6YeN54K55bel5L2cIikpewogICAgICAgICAgcG9pbnRzICs9IE51bWJlcihpdGVtLmRlUG9pbnRzKTsKICAgICAgICB9ZWxzZSBpZihpdGVtLmRlUG9pbnRzKXsKICAgICAgICAgIGRlUG9pbnRzICs9IE51bWJlcihpdGVtLmRlUG9pbnRzKTsKICAgICAgICB9CiAgICAgIH0pCiAgICAgIHRoaXMuc2VsZlNjb3JlID0gODUgKyBkZVBvaW50cyArIHBvaW50czsKICAgIH0sCgogICAgLy8g562+5ZCN5LiK5Lyg55u45YWzCiAgICB1cGxvYWRTaWduYXR1cmUoKXsKICAgICAgY29uc3QgeyBpc0VtcHR5LCBkYXRhIH0gPSB0aGlzLiRyZWZzLnNpZ25hdHVyZVBhZC5zYXZlU2lnbmF0dXJlKCk7CiAgICAgIGNvbnNvbGUubG9nKGlzRW1wdHksZGF0YSkKICAgICAgaWYoaXNFbXB0eSl7CiAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICB0eXBlOiAnd2FybmluZycsCiAgICAgICAgICBtZXNzYWdlOiAn6K+3562+5ZCNIScKICAgICAgICB9KTsKICAgICAgICByZXR1cm4gZmFsc2U7CiAgICAgIH1lbHNlewogICAgICAgIGNvbnN0IGJsb2JCaW4gPSBhdG9iKGRhdGEuc3BsaXQoJywnKVsxXSk7CiAgICAgICAgbGV0IGFycmF5ID0gW107CiAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBibG9iQmluLmxlbmd0aDsgaSsrKSB7CiAgICAgICAgICBhcnJheS5wdXNoKGJsb2JCaW4uY2hhckNvZGVBdChpKSk7CiAgICAgICAgfQogICAgICAgIGNvbnN0IGZpbGVCbG9iID0gbmV3IEJsb2IoW25ldyBVaW50OEFycmF5KGFycmF5KV0sIHsgdHlwZTogJ2ltYWdlL3BuZycgfSk7CiAgICAgICAgY29uc3QgZm9ybURhdGEgPSBuZXcgRm9ybURhdGEoKTsKICAgICAgICBmb3JtRGF0YS5hcHBlbmQoJ2ZpbGUnLCBmaWxlQmxvYiwgYCR7RGF0ZS5ub3coKX0ucG5nYCk7CiAgICAgICAgZmV0Y2godGhpcy51cGxvYWQudXJsLCB7CiAgICAgICAgICBtZXRob2Q6ICdQT1NUJywKICAgICAgICAgIGJvZHk6IGZvcm1EYXRhLAogICAgICAgIH0pCiAgICAgICAgLnRoZW4ocmVzcG9uc2UgPT4gcmVzcG9uc2UuanNvbigpKQogICAgICAgIC50aGVuKGRhdGEgPT4gewogICAgICAgICAgY29uc29sZS5sb2coJ1N1Y2Nlc3M6JywgZGF0YSk7CiAgICAgICAgICBpZihkYXRhLmNvZGUgPT0gMjAwKXsKICAgICAgICAgICAgdGhpcy5zaWduID0ge2ZpbGVOYW1lOnRoaXMudXNlckluZm8ubmFtZSArICIucG5nIix1cmw6ZGF0YS51cmx9OwogICAgICAgICAgICB0aGlzLnN1Ym1pdCgpOwogICAgICAgICAgfWVsc2V7CiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICAgIHR5cGU6ICdlcnJvcicsCiAgICAgICAgICAgICAgbWVzc2FnZTogJ+etvuWQjeS4iuS8oOWksei0pScKICAgICAgICAgICAgfSk7CiAgICAgICAgICB9CiAgICAgICAgfSkKICAgICAgICAuY2F0Y2goKGVycm9yKSA9PiB7CiAgICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvcjonLCBlcnJvcik7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgdHlwZTogJ2Vycm9yJywKICAgICAgICAgICAgbWVzc2FnZTogJ+etvuWQjeS4iuS8oOW8guW4uCcKICAgICAgICAgIH0pOwogICAgICAgIH0pOwogICAgICB9CiAgICAgIAogICAgfSwKICB9LAp9Owo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2LA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/assess/self", "sourcesContent": ["<template>\r\n    <div class=\"app-container\">\r\n      <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"80px\">\r\n      <el-row>\r\n        <el-form-item label=\"考核年月\" prop=\"assessDate\">\r\n          <el-date-picker\r\n            v-model=\"queryParams.assessDate\"\r\n            type=\"month\"\r\n            value-format=\"yyyy-M\"\r\n            format=\"yyyy 年 M 月\"\r\n            placeholder=\"选择考核年月\"\r\n            :clearable=\"false\">\r\n          </el-date-picker>\r\n        </el-form-item>\r\n        <el-form-item label=\"部门\" prop=\"deptId\">\r\n          <el-select v-model=\"queryParams.deptId\" placeholder=\"请选择部门\">\r\n            <el-option\r\n              v-for=\"item in deptOptions\"\r\n              :key=\"item.deptId\"\r\n              :label=\"item.deptName\"\r\n              :value=\"item.deptId\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n          <el-button type=\"warning\" icon=\"el-icon-s-tools\" size=\"mini\" @click=\"handleConfig\">指标配置</el-button>\r\n        </el-form-item>\r\n      </el-row>\r\n      <el-row>\r\n        <el-form-item label=\"当前状态\">\r\n          <el-tag v-if=\"status == '0' && rejectReason\" type=\"danger\">退 回</el-tag>\r\n          <el-tag v-if=\"status == '0' && !rejectReason\" type=\"info\">未提交</el-tag>\r\n          <el-tag v-if=\"status == '1'\" type=\"warning\">部门领导评分</el-tag>\r\n          <el-tag v-if=\"status == '2'\" type=\"warning\">事业部评分</el-tag>\r\n          <el-tag v-if=\"status == '3'\" type=\"warning\">运改部/组织部审核</el-tag>\r\n          <el-tag v-if=\"status == '4'\" type=\"warning\">总经理部评分</el-tag>\r\n          <el-tag v-if=\"status == '5'\" type=\"success\">已完成</el-tag>\r\n        </el-form-item>\r\n      </el-row>\r\n      <el-row v-if=\"rejectReason\">\r\n        <el-form-item label=\"退回原因\" prop=\"rejectReason\">\r\n            <span class=\"el-icon-warning\" style=\"color: #f56c6c;\"></span>\r\n            {{ rejectReason }}\r\n        </el-form-item>\r\n      </el-row>\r\n    </el-form>\r\n      <h3 style=\"text-align: center;\">月度业绩考核表</h3>\r\n      <el-descriptions class=\"margin-top\" :column=\"3\">\r\n        <el-descriptions-item>\r\n          <template slot=\"label\">\r\n            姓名\r\n          </template>\r\n          {{ userInfo.name }}\r\n        </el-descriptions-item>\r\n        <!-- <el-descriptions-item>\r\n          <template slot=\"label\">\r\n            工号\r\n          </template>\r\n          {{ userInfo.workNo }}\r\n        </el-descriptions-item> -->\r\n        <!-- <el-descriptions-item>\r\n          <template slot=\"label\">\r\n            身份\r\n          </template>\r\n          <span v-if=\"userInfo.assessRole == '0'\">干部</span>\r\n          <span v-if=\"userInfo.assessRole == '1'\">一把手</span>\r\n          <span v-if=\"userInfo.assessRole == '2'\">条线领导</span>\r\n        </el-descriptions-item> -->\r\n\r\n        <el-descriptions-item>\r\n          <template slot=\"label\">\r\n            部门\r\n          </template>\r\n          {{ deptName }}\r\n        </el-descriptions-item>\r\n        <el-descriptions-item>\r\n          <template slot=\"label\">\r\n            考核年月\r\n          </template>\r\n          {{ assessDateText }}\r\n        </el-descriptions-item>\r\n      </el-descriptions>\r\n      <el-table v-loading=\"loading\" :data=\"list\"\r\n        :span-method=\"objectSpanMethod\" border>\r\n        <el-table-column label=\"类型\" align=\"center\" prop=\"item\" width=\"120\"/>\r\n        <el-table-column label=\"指标\" align=\"center\" prop=\"category\" width=\"140\"/>\r\n        <el-table-column label=\"目标\" align=\"center\" prop=\"target\" width=\"150\" />\r\n        <el-table-column label=\"评分标准\" align=\"center\" prop=\"standard\" />\r\n        <el-table-column label=\"完成实绩（若扣分，写明原因）\" align=\"center\" prop=\"performance\" width=\"440\">\r\n            <template slot-scope=\"scope\">\r\n                <span v-if=\"readOnly\">{{ scope.row.performance }}</span>\r\n                <div v-else style=\"display: flex\">\r\n                  <!-- <el-button icon=\"el-icon-search\" size=\"small\"></el-button> -->\r\n                  <el-popover\r\n                    placement=\"left\"\r\n                    width=\"636\"\r\n                    trigger=\"click\"\r\n                    :ref=\"'popover' + scope.$index\">\r\n                    <el-table :data=\"beAssessedList\">\r\n                      <el-table-column width=\"150\" property=\"assessDeptName\" label=\"提出考核单位\"></el-table-column>\r\n                      <el-table-column width=\"300\" property=\"assessContent\" label=\"事项\"></el-table-column>\r\n                      <el-table-column width=\"80\" property=\"deductionOfPoint\" label=\"加减分\"></el-table-column>\r\n                      <el-table-column width=\"80\" label=\"操作\">\r\n                        <template slot-scope=\"beAssessed\">\r\n                          <el-button\r\n                            size=\"mini\"\r\n                            type=\"text\"\r\n                            icon=\"el-icon-edit\"\r\n                            @click=\"handleBeAssessedClick(scope.row,beAssessed.row,scope.$index)\"\r\n                          >填入</el-button>\r\n                        </template>\r\n                      </el-table-column>\r\n                    </el-table>\r\n                    <el-button slot=\"reference\" icon=\"el-icon-search\" size=\"small\"></el-button>\r\n                  </el-popover>\r\n                  <el-input class=\"table-input\" type=\"textarea\" autosize v-model=\"scope.row.performance\" placeholder=\"请输入完成实绩\" />\r\n                </div>\r\n            </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"加减分\" align=\"center\" prop=\"dePoints\" width=\"108\">\r\n            <template slot-scope=\"scope\">\r\n                <span v-if=\"readOnly\">{{ scope.row.dePoints }}</span>\r\n                <el-input v-else class=\"table-input\" type=\"number\" autosize v-model=\"scope.row.dePoints\" placeholder=\"请输入加减分\" @input=\"scoreInput(scope.row)\"></el-input>\r\n            </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"加减分原因\" align=\"center\" prop=\"pointsReason\"  width=\"440\">\r\n            <template slot-scope=\"scope\">\r\n                <span v-if=\"readOnly\">{{ scope.row.pointsReason }}</span>\r\n                <el-input v-else class=\"table-input\" type=\"textarea\" autosize v-model=\"scope.row.pointsReason\" placeholder=\"请输入加减分原因\"></el-input>\r\n            </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <el-form size=\"small\" :inline=\"false\" label-width=\"200px\" style=\"margin-top: 10px;\" label-position=\"left\">\r\n            <el-form-item v-if=\"readOnly\" label=\"自评分数 / 签名：\" prop=\"deptId\">\r\n              <div style=\"display: flex;\">\r\n                <span >{{ selfScore + \" 分 / \" }}</span>\r\n                <span v-if=\"!selfSign\">{{info.name}}</span>\r\n                <el-image v-else\r\n                  style=\"width: 100px; height: 46px\"\r\n                  :src=\"selfSign.url\"\r\n                  :fit=\"fit\"></el-image>\r\n              </div>\r\n              \r\n            </el-form-item>\r\n            <el-form-item v-else label=\"自评分数：\" prop=\"selfScore\">\r\n              <div style=\"display: flex;width: 180px;\">\r\n                <el-input type=\"number\" autosize v-model=\"selfScore\" placeholder=\"请输入分数\" :readonly=\"readOnly\" />\r\n                <span style=\"margin-left: 8px;\">分</span>\r\n              </div>\r\n            </el-form-item>\r\n            <el-form-item v-if=\"status > '1' && info.deptScore && info.deptUserName\" label=\"部门领导评分 / 签名：\">\r\n              <span >{{ info.deptScore + \" 分 / \" + info.deptUserName }}</span>\r\n            </el-form-item>\r\n            <el-form-item v-if=\"status > '2' && info.businessUserName && info.businessScore\" label=\"事业部领导评分 / 签名：\">\r\n              <span>{{ info.businessScore + \" 分 / \" + info.businessUserName }}</span>\r\n            </el-form-item>\r\n            <!-- <el-form-item v-if=\"status > '4' && info.leaderScore && info.leaderName\" label=\"总经理部领导评分 / 签名：\" prop=\"deptId\">\r\n              <span >{{ info.leaderScore + \" 分 / \" + info.leaderName }}</span>\r\n            </el-form-item> -->\r\n          </el-form>\r\n      <div v-if=\"!readOnly\" style=\"text-align: center;\">\r\n        <el-button v-if=\"resetShow\" plain type=\"info\" @click=\"resetInfo\">重 置 </el-button>\r\n        <el-button type=\"success\" @click=\"save\">保 存</el-button>\r\n        <el-button type=\"primary\" @click=\"onSubmit\">提 交</el-button>\r\n      </div>\r\n\r\n      <!-- 签名板 -->\r\n      <el-dialog title=\"签字确认\" :visible.sync=\"openSign\" width=\"760px\" append-to-body>\r\n        <div style=\"border: 1px #ccc solid;width: 702px;\">\r\n            <vue-signature-pad\r\n            width=\"700px\"\r\n            height=\"300px\"\r\n            ref=\"signaturePad\"\r\n            :options=\"signOptions\"\r\n          />\r\n        </div>\r\n        <div style=\"text-align: center;padding: 10px 10px;\">\r\n          <el-button style=\"margin-right: 20px;\" type=\"success\" @click=\"clearSign\">清除</el-button>\r\n          <el-button type=\"primary\" @click=\"uploadSignature\">确认</el-button>\r\n        </div>\r\n      </el-dialog>\r\n    </div>\r\n  </template>\r\n\r\n  <script>\r\n  import { getInfoByDate, saveInfo, submitInfo, delInfo, listBeAssessed } from \"@/api/assess/self/info\";\r\n  // import { batchTarget, listSelfTargetAll } from \"@/api/assess/self/target\";\r\n  import { getReportDeptList, getByWorkNoDeptId } from \"@/api/assess/self/user\";\r\n  import { VueSignaturePad } from 'vue-signature-pad';\r\n\r\n  export default {\r\n    components: {\r\n      VueSignaturePad\r\n    },\r\n    name: \"SelfAssessReport\",\r\n    data() {\r\n      return {\r\n        // 遮罩层\r\n        loading: true,\r\n        // 显示搜索条件\r\n        showSearch: true,\r\n        openSign:false,\r\n        // 绩效考核-自评指标配置表格数据\r\n        list: [],\r\n        // 弹出层标题\r\n        title: \"\",\r\n        // 是否显示弹出层\r\n        open: false,\r\n        // 查询参数\r\n        queryParams: {\r\n          userId:null,\r\n          workNo: null,\r\n          deptId:null,\r\n          assessDate: null,\r\n        },\r\n        // 考核年月文本显示\r\n        assessDateText:null,\r\n        // 部门显示\r\n        deptName:null,\r\n        // 表单参数\r\n        form: {},\r\n        // 表单校验\r\n        rules: {\r\n        },\r\n        // 用户信息\r\n        userInfo:{},\r\n        // 合并单元格信息\r\n        spanList:{\r\n          itemList:[],\r\n          standardList:[]\r\n        },\r\n        // 是否显示重置按钮\r\n        resetShow:false,\r\n        // 是否只读\r\n        readOnly:false,\r\n        // 部门选项\r\n        deptOptions:[],\r\n        // 自评信息Id\r\n        id:null,\r\n        // 自评分数\r\n        selfScore:100,\r\n        // 状态\r\n        status:\"0\",\r\n        // 自评信息\r\n        info:{},\r\n        // 横向被考评信息\r\n        beAssessedList:[],\r\n        // 退回理由\r\n        rejectReason:\"\",\r\n        // 自评签名\r\n        selfSign:\"\",\r\n        // 签名板配置\r\n        signOptions: {\r\n          onBegin: () => this.$refs.signaturePad.resizeCanvas(),\r\n          backgroundColor: 'rgba(255, 255, 255, 1)'\r\n        },\r\n        sign:\"\",\r\n        file:null,\r\n        fileList:[],\r\n        upload: {\r\n          // 上传的地址\r\n          url: process.env.VUE_APP_BASE_API + \"/app/common/uploadMinio\",\r\n          isUploading: false,\r\n        },\r\n      };\r\n    },\r\n    created() {\r\n      this.queryParams.assessDate = this.getDefaultAssessDate()\r\n      this.assessDateText = this.queryParams.assessDate.replace(\"-\",\" 年 \") + \" 月\";\r\n      // this.getSelfAssessUser();\r\n      this.getReportDeptList();\r\n    },\r\n    methods: {\r\n\r\n      // 获取默认考核日期\r\n      getDefaultAssessDate() {\r\n        const now = new Date();\r\n        const currentDay = now.getDate();\r\n\r\n        let targetDate;\r\n        if (currentDay < 10) {\r\n          // 当前日期小于10日，默认为上个月\r\n          targetDate = new Date(now.getFullYear(), now.getMonth() - 1, 1);\r\n        } else {\r\n          // 当前日期大于等于10日，默认为当月\r\n          targetDate = new Date(now.getFullYear(), now.getMonth(), 1);\r\n        }\r\n\r\n        // 格式化为 YYYY-M 格式\r\n        const year = targetDate.getFullYear();\r\n        const month = targetDate.getMonth() + 1;\r\n        return `${year}-${month}`;\r\n      },\r\n\r\n      // 获取部门信息\r\n      getReportDeptList(){\r\n        getReportDeptList().then(res => {\r\n          console.log(res)\r\n          if(res.code == 200){\r\n            this.handleDeptList(res.data);\r\n            // 根据部门获取用户信息\r\n            this.getByWorkNoDeptId();\r\n          }\r\n        })\r\n      },\r\n      // 获取用户信息\r\n      getByWorkNoDeptId(){\r\n        getByWorkNoDeptId({deptId:this.queryParams.deptId}).then(res => {\r\n          console.log(res)\r\n          if(res.code == 200){\r\n            this.queryParams.userId = res.data.id;\r\n            this.queryParams.workNo = res.data.workNo;\r\n            this.userInfo = res.data;\r\n            this.getList();\r\n            // 获取被考核信息\r\n            this.getBeAssessedList();\r\n          }\r\n        })\r\n      },\r\n      \r\n      // 获取被考核信息\r\n      getBeAssessedList(){\r\n        listBeAssessed({deptId:this.queryParams.deptId,assessDate:this.queryParams.assessDate}).then(res =>{\r\n          let beAssessedList = [];\r\n          if(res.code == 200){\r\n            if(res.data.length > 0){\r\n              res.data.forEach(item => {\r\n                beAssessedList = [...beAssessedList,...item.hrLateralAssessInfoList]\r\n              })\r\n              this.beAssessedList = beAssessedList;\r\n            }\r\n          }\r\n          console.log(beAssessedList)\r\n        })\r\n      },\r\n      /** 查询绩效考核-自评指标配置列表 */\r\n      getList() {\r\n        this.loading = true;\r\n        getInfoByDate(this.queryParams).then(response => {\r\n          console.log(response.data);\r\n          // console.log(typeof response.data);\r\n          if (Array.isArray(response.data)) {\r\n            // 指标配置数据\r\n            this.handleSpanList(response.data);\r\n            this.list = response.data.map(item => {item.performance = \"\";item.dePoints = null; return item});\r\n            this.status = \"0\";\r\n            this.readOnly = false;\r\n            this.resetShow = false;\r\n            this.rejectReason = null;\r\n          } else {\r\n            // 自评信息\r\n            let info = response.data;\r\n            let list = JSON.parse(info.content);\r\n            this.handleSpanList(list);\r\n            this.list = list;\r\n            this.id = info.id;\r\n            this.selfScore = info.selfScore;\r\n            this.rejectReason = info.rejectReason;\r\n            this.status = info.status;\r\n            if(info.sign){\r\n              this.selfSign = JSON.parse(info.sign);\r\n            }\r\n            this.info = info;\r\n            if(info.status == \"0\"){\r\n              this.readOnly = false;\r\n              this.resetShow = true;\r\n            }else{\r\n              this.readOnly = true;\r\n              this.resetShow = false;\r\n            }\r\n          }\r\n          this.loading = false;\r\n        });\r\n      },\r\n\r\n      // 处理列表\r\n      handleSpanList(data){\r\n        let itemList = [];\r\n        let standardList = [];\r\n        let itemFlag = 0;\r\n        let standardFlag = 0;\r\n        for(let i = 0; i < data.length; i++){\r\n          // 相同考核项、评分标准合并\r\n          if(i == 0){\r\n            itemList.push({\r\n              rowspan: 1,\r\n              colspan: 1\r\n            })\r\n            standardList.push({\r\n              rowspan: 1,\r\n              colspan: 1\r\n            })\r\n          }else{\r\n            // 考核项\r\n            if(data[i - 1].item == data[i].item){\r\n              itemList.push({\r\n                rowspan: 0,\r\n                colspan: 0\r\n              })\r\n              itemList[itemFlag].rowspan += 1;\r\n            }else{\r\n              itemList.push({\r\n                rowspan: 1,\r\n                colspan: 1\r\n              })\r\n              itemFlag = i;\r\n            }\r\n            // 评分标准\r\n            if(data[i - 1].standard == data[i].standard){\r\n              standardList.push({\r\n                rowspan: 0,\r\n                colspan: 0\r\n              })\r\n              standardList[standardFlag].rowspan += 1;\r\n            }else{\r\n              standardList.push({\r\n                rowspan: 1,\r\n                colspan: 1\r\n              })\r\n              standardFlag = i;\r\n            }\r\n          }\r\n        }\r\n        this.spanList.itemList = itemList;\r\n        this.spanList.standardList = standardList;\r\n      },\r\n\r\n\r\n      // 处理部门下拉选项\r\n      handleDeptList(data){\r\n        // let syb = [\"炼铁事业部\",\"炼钢事业部\",\"轧钢事业部\",\"特板事业部\",\"动力事业部\",\"物流事业部\",\"研究院\"];\r\n        let deptList = [];\r\n        data.forEach(item => {\r\n          //if(syb.indexOf(item.deptName) == -1){\r\n            deptList.push({\r\n              deptName:item.deptName,\r\n              deptId:item.deptId\r\n            })\r\n          //}\r\n        })\r\n        this.deptOptions = deptList;\r\n        if(deptList.length > 0){\r\n          this.queryParams.deptId = deptList[0].deptId;\r\n          this.deptName = deptList[0].deptName;\r\n        }\r\n      },\r\n\r\n\r\n      /** 搜索按钮操作 */\r\n      handleQuery() {\r\n        this.assessDateText = this.queryParams.assessDate.replace(\"-\",\" 年 \") + \" 月\";\r\n        this.deptOptions.forEach(item => {\r\n          if(item.deptId == this.queryParams.deptId){\r\n            this.deptName = item.deptName;\r\n          }\r\n        })\r\n        this.id = null;\r\n        this.info = null;\r\n        this.selfScore = \"\";\r\n        this.getByWorkNoDeptId();\r\n      },\r\n\r\n      // 保存\r\n      save(){\r\n        if(this.list.length == 0){\r\n          this.$message({\r\n              type: 'warning',\r\n              message: '未配置相关信息，请先配置指标'\r\n            });\r\n            return false;\r\n        }\r\n        let data = this.handleData(this.list);\r\n        let form = {\r\n          id:this.id,\r\n          workNo:this.userInfo.workNo,\r\n          assessDate:this.queryParams.assessDate,\r\n          deptId:this.queryParams.deptId,\r\n          content:JSON.stringify(data),\r\n          status:\"0\",\r\n          userId:this.queryParams.userId,\r\n          deptName:this.deptName,\r\n          name:this.userInfo.name,\r\n          selfScore:this.selfScore,\r\n          job:this.userInfo.job,\r\n          postType:this.userInfo.postType\r\n        }\r\n        saveInfo(form).then(res => {\r\n          if(res.code == 200){\r\n            this.getList();\r\n            this.$message({\r\n              type: 'success',\r\n              message: '保存成功!'\r\n            });\r\n          }\r\n        })\r\n      },\r\n\r\n      // 确认提交点击事件\r\n      submit(){\r\n          this.$confirm('确认后将流转至下一节点, 是否继续?', '提示', {\r\n            confirmButtonText: '确定',\r\n            cancelButtonText: '取消',\r\n            type: 'warning'\r\n          }).then(() => {\r\n            this.submitData();\r\n          }).catch(() => {\r\n\r\n          });\r\n      },\r\n\r\n      onSubmit(){\r\n        if(this.verifyInsert()){\r\n          this.openSign = true;\r\n        }\r\n      },\r\n\r\n      clearSign(){\r\n        this.$refs.signaturePad.clearSignature();\r\n      },\r\n\r\n      // 提交数据验证\r\n      verifyInsert(){\r\n        if(this.list.length == 0){\r\n          this.$message({\r\n              type: 'warning',\r\n              message: '未配置相关信息，请先配置指标'\r\n            });\r\n            return false;\r\n        }\r\n        for(let i = 0; i < this.list.length; i++){\r\n          if(!this.list[i].performance || !this.list[i].dePoints){\r\n            this.$message({\r\n              type: 'warning',\r\n              message: '信息未填写完整'\r\n            });\r\n            return false;\r\n          }else if(this.list[i].dePoints != 0 && !this.list[i].pointsReason){\r\n            this.$message({\r\n              type: 'warning',\r\n              message: '有加减分的请填写原因'\r\n            });\r\n            return false;\r\n          }\r\n        }\r\n        if(!this.selfScore){\r\n          this.$message({\r\n              type: 'warning',\r\n              message: '请填写自评分数'\r\n            });\r\n            return false;\r\n        }\r\n        return true;\r\n      },\r\n\r\n      // 新增数据\r\n      submitData(){\r\n        let data = this.handleData(this.list);\r\n        let form = {\r\n          id:this.id,\r\n          workNo:this.userInfo.workNo,\r\n          assessDate:this.queryParams.assessDate,\r\n          deptId:this.queryParams.deptId,\r\n          content:JSON.stringify(data),\r\n          status:\"1\",\r\n          userId:this.queryParams.userId,\r\n          deptName:this.deptName,\r\n          name:this.userInfo.name,\r\n          selfScore:this.selfScore,\r\n          job:this.userInfo.job,\r\n          postType:this.userInfo.postType,\r\n          averageLinkFlag:this.userInfo.averageLinkFlag,\r\n          benefitLinkFlag:this.userInfo.benefitLinkFlag,\r\n          sign:JSON.stringify(this.sign)\r\n        }\r\n        submitInfo(form).then(res => {\r\n          if(res.code == 200){\r\n            this.getList();\r\n            this.sign = \"\";\r\n            this.$refs.signaturePad.clearSignature();\r\n            this.openSign = false;\r\n            this.$message({\r\n              type: 'success',\r\n              message: '提交成功!'\r\n            });\r\n          }else{\r\n\r\n          }\r\n        })\r\n      },\r\n\r\n      // 保存重置\r\n      resetInfo(){\r\n        // 删除保存信息\r\n        delInfo({id:this.id}).then(res => {\r\n          if(res.code == 200){\r\n            this.id = null;\r\n            this.selfScore = null;\r\n            // 获取配置信息\r\n            this.getList();\r\n          }\r\n        })\r\n      },\r\n\r\n      // 处理提交数据\r\n      handleData(data){\r\n        let result = []\r\n        data.forEach(item => {\r\n          let form = {\r\n            item: item.item,\r\n            category: item.category,\r\n            target: item.target,\r\n            standard: item.standard,\r\n            performance: item.performance,\r\n            dePoints: item.dePoints,\r\n            pointsReason:item.pointsReason\r\n          };\r\n          result.push(form);\r\n        })\r\n        return result\r\n      },\r\n\r\n      /** 标准配置跳转 */\r\n      handleConfig(){\r\n        getByWorkNoDeptId({deptId:this.queryParams.deptId}).then(res => {\r\n          console.log(res)\r\n          if(res.code == 200){\r\n            if(res.data.id){\r\n            this.$router.push({\r\n              path:\"/assess/self/user/detail\",\r\n              query:{\r\n                userId:res.data.id\r\n              }\r\n            })\r\n          }\r\n          }\r\n        })\r\n        \r\n      },\r\n\r\n\r\n      // 合并单元格方法\r\n      objectSpanMethod({ row, column, rowIndex, columnIndex }) {\r\n        // 第一列相同项合并\r\n        if (columnIndex === 0) {\r\n          return this.spanList.itemList[rowIndex];\r\n        }\r\n        // 评分标准相同合并\r\n        if(columnIndex === 3){\r\n          return this.spanList.standardList[rowIndex];\r\n        }\r\n        // 类别无内容 合并\r\n        if(columnIndex === 1){\r\n          if(!row.category){\r\n            return {\r\n              rowspan: 0,\r\n              colspan: 0\r\n            }\r\n          }\r\n        }\r\n        if(columnIndex === 2){\r\n          if(!row.category){\r\n            return {\r\n              rowspan: 1,\r\n              colspan: 2\r\n            }\r\n          }\r\n        }\r\n      },\r\n\r\n      // 被考核事项点击事件\r\n      handleBeAssessedClick(row,optionRow,index){\r\n        console.log(row)\r\n        // 将事项填入完成实绩列（弃用）\r\n        // if(row.performance){\r\n        //   this.$set(row, 'performance', row.performance + \"；\" + optionRow.assessContent);\r\n        // }else{\r\n        //   this.$set(row, 'performance', optionRow.assessContent);\r\n        // }\r\n        \r\n        // 将分数填入加减分列\r\n        if(row.dePoints){\r\n          this.$set(row, 'dePoints', Number(row.dePoints) + Number(optionRow.deductionOfPoint));\r\n        }else{\r\n          this.$set(row, 'dePoints', Number(optionRow.deductionOfPoint));\r\n        }\r\n        \r\n        // 将事项+分数填入加减分理由列\r\n        let reasonContent = optionRow.assessContent + \"(\" + optionRow.deductionOfPoint + \"分)\";\r\n        if(row.pointsReason){\r\n          this.$set(row, 'pointsReason', row.pointsReason + \"；\" + reasonContent);\r\n        }else{\r\n          this.$set(row, 'pointsReason', reasonContent);\r\n        }\r\n        \r\n        this.$refs[`popover${index}`].showPopper = false;\r\n        // 重新计算自评分数\r\n        this.scoreInput();\r\n      },\r\n\r\n      // 加减分输入\r\n      scoreInput(row = null){\r\n        // 验证月度重点工作的加减分只能为1、3或5（仅当传入row参数时进行验证）\r\n        if (row && row.item) {\r\n          let noSpaceStr = row.item.replace(/\\s+/g, '');\r\n          if (noSpaceStr.includes(\"月度重点工作\")) {\r\n            let value = row.dePoints;\r\n            if (value !== null && value !== undefined && value !== '') {\r\n              let numValue = Number(value);\r\n              if (![1, 3, 5].includes(numValue)) {\r\n                this.$message({\r\n                  type: 'warning',\r\n                  message: '月度重点工作的加减分只能为1分、3分或5分'\r\n                });\r\n                // 重置为空值\r\n                this.$set(row, 'dePoints', null);\r\n                return;\r\n              }\r\n            }\r\n          }\r\n        }\r\n\r\n        // 重新计算自评分数\r\n        let dePoints = 0;\r\n        let points = 0;\r\n        this.list.forEach(item => {\r\n          let noSpaceStr = item.item.replace(/\\s+/g, '');\r\n          if(item.dePoints && noSpaceStr.includes(\"月度重点工作\")){\r\n            points += Number(item.dePoints);\r\n          }else if(item.dePoints){\r\n            dePoints += Number(item.dePoints);\r\n          }\r\n        })\r\n        this.selfScore = 85 + dePoints + points;\r\n      },\r\n\r\n      // 签名上传相关\r\n      uploadSignature(){\r\n        const { isEmpty, data } = this.$refs.signaturePad.saveSignature();\r\n        console.log(isEmpty,data)\r\n        if(isEmpty){\r\n          this.$message({\r\n            type: 'warning',\r\n            message: '请签名!'\r\n          });\r\n          return false;\r\n        }else{\r\n          const blobBin = atob(data.split(',')[1]);\r\n          let array = [];\r\n          for (let i = 0; i < blobBin.length; i++) {\r\n            array.push(blobBin.charCodeAt(i));\r\n          }\r\n          const fileBlob = new Blob([new Uint8Array(array)], { type: 'image/png' });\r\n          const formData = new FormData();\r\n          formData.append('file', fileBlob, `${Date.now()}.png`);\r\n          fetch(this.upload.url, {\r\n            method: 'POST',\r\n            body: formData,\r\n          })\r\n          .then(response => response.json())\r\n          .then(data => {\r\n            console.log('Success:', data);\r\n            if(data.code == 200){\r\n              this.sign = {fileName:this.userInfo.name + \".png\",url:data.url};\r\n              this.submit();\r\n            }else{\r\n              this.$message({\r\n                type: 'error',\r\n                message: '签名上传失败'\r\n              });\r\n            }\r\n          })\r\n          .catch((error) => {\r\n            console.error('Error:', error);\r\n            this.$message({\r\n              type: 'error',\r\n              message: '签名上传异常'\r\n            });\r\n          });\r\n        }\r\n        \r\n      },\r\n    },\r\n  };\r\n</script>\r\n<style>\r\n  .table-striped{\r\n    margin-top: 10px;\r\n    margin-bottom: 10px;\r\n    width: 100%;\r\n    text-align: center;\r\n    border: 1px #888;\r\n    border-collapse: collapse;\r\n  }\r\n  .table-striped th{\r\n    height: 32px;\r\n    border: 1px solid #888;\r\n    background-color: #dedede;\r\n  }\r\n  .table-striped td{\r\n    min-height: 32px;\r\n    border: 1px solid #888;\r\n  }\r\n  .table-input .el-textarea__inner{\r\n    border: 0 !important;\r\n    resize: none !important;\r\n  }\r\n  .table-input .el-input__inner{\r\n    border: 0 !important;\r\n  }\r\n  /* .myUpload .el-upload--picture-card{\r\n    display:none !important; \r\n  } */\r\n  </style>\r\n"]}]}