package com.ruoyi.web.controller.leave;

import com.ruoyi.app.leave.domain.LeaveDepartment;
import com.ruoyi.app.leave.dto.LeaveUserPermitDto;
import com.ruoyi.app.leave.dto.LeaveUserQueryDto;
import com.ruoyi.app.leave.dto.LeaveUserSaveDto;
import com.ruoyi.app.leave.enums.LeaveRoleEnum;
import com.ruoyi.app.leave.service.ILeaveDepartmentService;
import com.ruoyi.app.leave.service.ILeaveDeptAssignmentService;
import com.ruoyi.app.truck.common.domain.QueryUserDetailDTO;
import com.ruoyi.app.truck.common.domain.TruckUserDTO;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Description: 出门证用户相关
 * @CreateTime: 2025-03-24 10:38
 * @Author: liekkas
 */
@RestController
@RequestMapping("/leave/user")
public class PassPermitUserController extends BaseController {

    @Autowired
    private ILeaveDeptAssignmentService assignmentService;

    @Autowired
    private ILeaveDepartmentService departmentService;

    @Autowired
    private ISysUserService userService;

    @GetMapping("/permit/getAllUserList")
    public TableDataInfo getAllUserList(SysUser user) {
        startPage();
        List<SysUser> list = userService.selectUsersExcludeSpecial(user);
        return getDataTable(list);
    }

    @GetMapping("/permit/getDeptList")
    public AjaxResult getDeptList() {
        return AjaxResult.success(departmentService.selectLeaveDepartmentList(new LeaveDepartment()));
    }

    @GetMapping("/permit/getRoleList")
    public AjaxResult getRoleList() {
        return AjaxResult.success(LeaveRoleEnum.getRoleList());
    }

    /**
     * 查询某个用户的具体信息
     */
    @GetMapping("/permit/userInfo")
    public AjaxResult getUserInfo(LeaveUserQueryDto queryDto) {
        LeaveUserPermitDto userInfo = assignmentService.getUserInfo(queryDto);
        return AjaxResult.success(userInfo);
    }

    @GetMapping("/permit/getHasPermitUserList")
    public TableDataInfo getHasPermitUserList(LeaveUserQueryDto queryDto) {
        startPage();
        List<LeaveUserPermitDto> list = assignmentService.getHasPermitUserList(queryDto);
        return getDataTable(list);
    }

    /**
     * 出门证用户部门角色分配
     */
    @PostMapping("/permit/save")
    public AjaxResult save(@RequestBody LeaveUserSaveDto userDto) {
        userDto.setHandleNo(SecurityUtils.getUsername());
        assignmentService.save(userDto);
        return AjaxResult.success();
    }

    /**
     * 出门证用户部门角色删除
     */
    @PostMapping("/permit/delete")
    public AjaxResult delete(@RequestBody LeaveUserSaveDto userDto) {
        userDto.setHandleNo(SecurityUtils.getUsername());
        assignmentService.delete(userDto);
        return AjaxResult.success();
    }

}
