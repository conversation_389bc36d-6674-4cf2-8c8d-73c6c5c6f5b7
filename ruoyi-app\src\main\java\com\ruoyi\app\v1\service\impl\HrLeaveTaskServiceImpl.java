package com.ruoyi.app.v1.service.impl;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;

import com.google.common.collect.Lists;
import com.ruoyi.app.v1.domain.*;
import com.ruoyi.app.v1.enums.LeaveNode;
import com.ruoyi.app.v1.enums.LeaveType;
import com.ruoyi.app.v1.mapper.*;
import com.ruoyi.app.v1.service.IHrLeaveFlowService;
import com.ruoyi.app.v1.service.IHrLeaveInfoService;
import com.ruoyi.app.v2.service.IOfficialUserService;
import com.ruoyi.app.vehicleAccess.service.impl.XctgVehicleAuditFlowServiceImpl;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.FastJsonUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.http.HttpUtil;
import com.ruoyi.framework.manager.AsyncManager;
import com.ruoyi.framework.web.service.TemplateMessageService;
import com.ruoyi.system.domain.HrUser;
import com.ruoyi.system.service.ISysUserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ruoyi.common.utils.StringUtils;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.app.v1.service.IHrLeaveTaskService;

/**
 * 请假审批任务Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-11-27
 */
@Service
public class HrLeaveTaskServiceImpl implements IHrLeaveTaskService
{
    private static final Logger log = LoggerFactory.getLogger(XctgVehicleAuditFlowServiceImpl.class);

    /**
     * 请假时间段信息，包含开始时间、结束时间和请假天数
     */
    public static class LeavePeriod {
        private Date startTime;
        private Date endTime;
        private BigDecimal leaveDays;
        
        public LeavePeriod(Date startTime, Date endTime, BigDecimal leaveDays) {
            this.startTime = startTime;
            this.endTime = endTime;
            this.leaveDays = leaveDays;
        }
        
        // Getters and setters
        public Date getStartTime() { return startTime; }
        public void setStartTime(Date startTime) { this.startTime = startTime; }
        
        public Date getEndTime() { return endTime; }
        public void setEndTime(Date endTime) { this.endTime = endTime; }
        
        public BigDecimal getLeaveDays() { return leaveDays; }
        public void setLeaveDays(BigDecimal leaveDays) { this.leaveDays = leaveDays; }
    }

    @Autowired
    private HrLeaveTaskMapper hrLeaveTaskMapper;
    @Autowired
    private HrLeaveInfoMapper hrLeaveInfoMapper;
    @Autowired
    private IHrLeaveInfoService hrLeaveInfoService;
    @Autowired
    private HrLeaveNodeMapper hrLeaveNodeMapper;
    @Autowired
    private HrLeaveUserMapper hrLeaveUserMapper;
    @Autowired
    private IHrLeaveFlowService hrLeaveFlowService;
    @Autowired
    private ISysUserService userService;
    @Autowired
    private AppCommonV1Mapper commonMapper;
    @Autowired
    private IOfficialUserService officialUserService;
    @Autowired
    private TemplateMessageService templateMessageService;
    @Autowired
    private HrAbnormalAttendancePushMapper hrAbnormalAttendancePushMapper;
    @Autowired
    private HrLeavePushMapper hrLeavePushMapper;

    /**
     * 查询请假审批任务
     * 
     * @param id 请假审批任务主键
     * @return 请假审批任务
     */
    @Override
    public HrLeaveTask selectHrLeaveTaskById(Long id)
    {
        HrLeaveTask task = hrLeaveTaskMapper.selectHrLeaveTaskById(id);
        HrLeaveInfo info = hrLeaveInfoService.get(task.getLeaveId());
//        LeaveType leaveType = LeaveType.getByCode(info.getLeaveType());
//        if(StringUtils.isNotNull(leaveType)){
//            info.setLeaveTypeDes(leaveType.getInfo());
//        }
//        LeaveNode[] flowList = hrLeaveFlowService.getLeaveFlow(info);
//        info = handleCheckUsers(info);
//        info.setFlowDetailList(hrLeaveFlowService.getFlowDetail(flowList,info.getId()));

        task.setInfo(info);
        return task;
    }

    /**
     * 查询请假审批任务列表
     * 
     * @param hrLeaveTask 请假审批任务
     * @return 请假审批任务
     */
    @Override
    public List<HrLeaveTask> selectHrLeaveTaskList(HrLeaveTask hrLeaveTask)
    {
        return hrLeaveTaskMapper.selectHrLeaveTaskList(hrLeaveTask);
    }

    @Override
    public HrLeaveTask selectLatestTask(String leaveId) {
        return hrLeaveTaskMapper.selectLatestTask(leaveId);
    }

    /**
     * 新增请假审批任务
     * 
     * @param hrLeaveTask 请假审批任务
     * @return 结果
     */
    @Transactional
    @Override
    public int insertHrLeaveTask(HrLeaveTask hrLeaveTask)
    {
        hrLeaveTask.setCreateTime(DateUtils.getNowDate());
        int rows = hrLeaveTaskMapper.insertHrLeaveTask(hrLeaveTask);
        insertHrLeaveTaskUser(hrLeaveTask);
        return rows;
    }

    /**
     * 修改请假审批任务
     * 
     * @param hrLeaveTask 请假审批任务
     * @return 结果
     */
    @Transactional
    @Override
    public int updateHrLeaveTask(HrLeaveTask hrLeaveTask)
    {
        hrLeaveTaskMapper.deleteHrLeaveTaskUserByTaskId(hrLeaveTask.getId());
        insertHrLeaveTaskUser(hrLeaveTask);
        return hrLeaveTaskMapper.updateHrLeaveTask(hrLeaveTask);
    }

    /**
     * 批量删除请假审批任务
     * 
     * @param ids 需要删除的请假审批任务主键
     * @return 结果
     */
    @Transactional
    @Override
    public int deleteHrLeaveTaskByIds(Long[] ids)
    {
        hrLeaveTaskMapper.deleteHrLeaveTaskUserByTaskIds(ids);
        return hrLeaveTaskMapper.deleteHrLeaveTaskByIds(ids);
    }

    /**
     * 删除请假审批任务信息
     * 
     * @param id 请假审批任务主键
     * @return 结果
     */
    @Transactional
    @Override
    public int deleteHrLeaveTaskById(Long id)
    {
        hrLeaveTaskMapper.deleteHrLeaveTaskUserByTaskId(id);
        return hrLeaveTaskMapper.deleteHrLeaveTaskById(id);
    }

    @Override
    public List<HrLeaveTask> selectUserTaskList(HrLeaveInfo hrLeaveInfo) {
        List<HrLeaveTask> list = hrLeaveTaskMapper.selectUserTaskList(hrLeaveInfo);
        if(StringUtils.isNotNull(list)){
            for(HrLeaveTask task:list){
                HrLeaveInfo info = hrLeaveInfoMapper.get(task.getLeaveId());
                LeaveType leaveType = LeaveType.getByCode(info.getLeaveType());
                if(StringUtils.isNotNull(leaveType)){
                    info.setLeaveTypeDes(leaveType.getInfo());
                }
//                info = handleCheckUsers(info);
                if(task.getFinishType().equals("1")){
                    // 多人审核
                    List<HrLeaveTaskUser> taskUser = hrLeaveTaskMapper.selectTaskUserByTaskId(task.getId());
                    task.setHrLeaveTaskUserList(taskUser);
                    for(HrLeaveTaskUser user:taskUser){
                        if(user.getWorkNo().equals(hrLeaveInfo.getWorkNo())){
                            task.setCheckType(user.getStatus());
                            task.setFinishTime(user.getHandleTime());
                        }
                    }
                }
                LeaveNode taskNode = LeaveNode.getByCode(task.getNodeSign());
                if(StringUtils.isNotNull(taskNode)){
                    task.setNodeSignDes(taskNode.getInfo());
                }
                task.setInfo(info);
            }
        }
        return list;
    }

    @Override
    public HrLeaveTask selectLatestTaskByNode(HrLeaveTask hrLeaveTask) {
        return hrLeaveTaskMapper.selectLatestTaskByNode(hrLeaveTask);
    }

    @Override
    public List<HrLeaveTaskUser> selectTaskUserWorkNoListById(Long taskId) {
        return hrLeaveTaskMapper.selectTaskUserWorkNoListById(taskId);
    }

    /**
     * 新增请假审批任务人员信息
     * 
     * @param hrLeaveTask 请假审批任务对象
     */
    public void insertHrLeaveTaskUser(HrLeaveTask hrLeaveTask)
    {
        List<HrLeaveTaskUser> hrLeaveTaskUserList = hrLeaveTask.getHrLeaveTaskUserList();
        Long id = hrLeaveTask.getId();
        if (StringUtils.isNotNull(hrLeaveTaskUserList))
        {
            List<HrLeaveTaskUser> list = new ArrayList<HrLeaveTaskUser>();
            for (HrLeaveTaskUser hrLeaveTaskUser : hrLeaveTaskUserList)
            {
                hrLeaveTaskUser.setTaskId(id);
                list.add(hrLeaveTaskUser);
            }
            if (list.size() > 0)
            {
                hrLeaveTaskMapper.batchHrLeaveTaskUser(list);
            }
        }
    }

    @Transactional
    @Override
    public boolean checkPass(HrLeaveTask hrLeaveTask) throws Exception {
        boolean result;
        String leaveId = hrLeaveTask.getLeaveId();
        // 更新请假信息
        LeaveNode currentNode = LeaveNode.getByCode(hrLeaveTask.getNodeSign());
        HrLeaveInfo info = hendleCheckUser(hrLeaveTask.getInfo(),currentNode,hrLeaveTask.getFinishUser());
        // 获取下一流程节点
        LeaveNode nextNode = hrLeaveFlowService.getNextNode(currentNode,info);
        if(!nextNode.equals(LeaveNode.Finish)){
            // 生成下一节点审核信息
            if(StringUtils.isNotNull(hrLeaveTask.getNextCheckUser())){
                String[] user = hrLeaveTask.getNextCheckUser();
                result = genNodeTask(nextNode,leaveId,user);
            }else {
                result = genNodeTask(nextNode,leaveId);
            }
        }else{
            // 审核完成
            info.setStatus("1");
            result = true;
        }
        // 更新当前审核任务信息
        hrLeaveTask.setFinishFlag("1");
        hrLeaveTask.setFinishTime(DateUtils.getNowDate());
        hrLeaveTask.setCheckType("0");
        hrLeaveTaskMapper.updateHrLeaveTask(hrLeaveTask);
        hrLeaveInfoMapper.update(info);
        if(info.getStatus().equals("1")){
            // 流程结束发送通知
            this.sendPassMessage(info.getWorkNo());
            this.sendCollectMessage(info.getDeptCode(),info.getId());
            // 传考勤系统
            List<String> depts = new ArrayList<>();
            depts.add("XZ11");
            depts.add("XZ12");
            depts.add("XO02");
            depts.add("XF01");
            if(depts.contains(info.getDeptCode())){
                this.sendToKQ(info);
            }
            // 病假、产假、产前假、哺乳假、工伤假  通知部门一把手
//            String[] typeArray = {LeaveType.SickLeave.getCode(),LeaveType.MaternityLeave.getCode(),LeaveType.PrenatalLeave.getCode(),LeaveType.BreastfeedingLeave.getCode(),LeaveType.WorkInjuryLeave.getCode()};
//            if(Arrays.stream(typeArray).anyMatch(x -> x.equals(info.getLeaveType()))){
//                this.sendDeptLeaderMessage(info.getDeptCode(),info.getId());
//            }
        }
        return result;
    }

    @Transactional
    @Override
    public boolean checkPassSpecial(HrLeaveTask hrLeaveTask) throws Exception {
        boolean result;
        List<HrLeaveTaskUser> taskUser = hrLeaveTask.getHrLeaveTaskUserList();
        int count = 0;
        HrLeaveTaskUser currentUser = new HrLeaveTaskUser();
        for(HrLeaveTaskUser user:taskUser){
            if(StringUtils.isNotNull(user.getStatus())){
                count += 1;
            }
            if(user.getWorkNo().equals(hrLeaveTask.getFinishUser())){
                currentUser = user;
                currentUser.setStatus("0");
                currentUser.setHandleTime(DateUtils.getNowDate());
            }
        }
        // 更新审批用户信息
        if(StringUtils.isNotNull(currentUser.getId())){
            hrLeaveTaskMapper.updateHrLeaveTaskUser(currentUser);
            result = true;
        }else{
            throw new Exception("审核失败，审核人员不匹配");
        }
        if(StringUtils.isNotNull(taskUser) && count == taskUser.size() - 1){
            // 最后一位审核人
            result = this.checkPass(hrLeaveTask);
        }
        return result;
    }

    @Transactional
    @Override
    public boolean checkReject(HrLeaveTask hrLeaveTask) {
        // 更新当前审核任务信息
        hrLeaveTask.setFinishFlag("1");
        hrLeaveTask.setFinishTime(DateUtils.getNowDate());
        hrLeaveTask.setCheckType("1");
        hrLeaveTaskMapper.updateHrLeaveTask(hrLeaveTask);
        // 更新请假信息
        HrLeaveInfo info = cleanCheckUser(hrLeaveTask.getInfo());
        info.setRejectReason(hrLeaveTask.getRejectReason());
        info.setStatus("3");  // 请假信息设为 暂存状态
        hrLeaveInfoMapper.update(info);
        // 发送驳回通知
        this.sendRejectMessage(info.getWorkNo());
        return false;
    }
    @Transactional
    @Override
    public boolean checkRejectSpecial(HrLeaveTask hrLeaveTask){
        List<HrLeaveTaskUser> taskUser = hrLeaveTask.getHrLeaveTaskUserList();
        HrLeaveTaskUser currentUser = new HrLeaveTaskUser();
        for(HrLeaveTaskUser user:taskUser){
            if(user.getWorkNo().equals(hrLeaveTask.getFinishUser())){
                currentUser = user;
                currentUser.setStatus("1");
                currentUser.setHandleTime(DateUtils.getNowDate());
            }
        }
        // 更新审批用户信息
        if(StringUtils.isNotNull(currentUser.getId())){
            hrLeaveTaskMapper.updateHrLeaveTaskUser(currentUser);
        }
        return this.checkReject(hrLeaveTask);
    }

    // 生成流程节点审批任务
    public boolean genNodeTask(LeaveNode node,String leaveId) throws Exception {
        HrLeaveTask hrLeaveTask = new HrLeaveTask();
        hrLeaveTask.setNodeSign(node.getCode());
        hrLeaveTask.setLeaveId(leaveId);
        if(node.equals(LeaveNode.DeptLeaderCheck)){
            // 部门/分厂 审批
            HrLeaveInfo info = hrLeaveInfoMapper.get(leaveId);
            String deptCode = info.getDeptCode();
            if(StringUtils.isNotNull(deptCode)){
                HrLeaveUser param = new HrLeaveUser();
                param.setDeptCode(deptCode);
                param.setNodeSign(node.getCode());
                String[] users = hrLeaveNodeMapper.selectNodeSignUserByDeptCode(param);
                if(StringUtils.isNotNull(users) && users.length > 0){
                    List<HrLeaveTaskUser> taskUser = new ArrayList<>();
                    for(String workNo : users){
                        HrLeaveTaskUser user = new HrLeaveTaskUser();
                        user.setWorkNo(workNo);
                        taskUser.add(user);
                    }
                    hrLeaveTask.setHrLeaveTaskUserList(taskUser);
                    int rows = insertHrLeaveTask(hrLeaveTask);
                    // 发送审批通知
                    this.sendApproveMessage(taskUser);
                    return rows > 0;
                }else {
                    throw new Exception("下一步流程流转失败，未获取到下一流程审批人员");
                }
            }else {
                throw new Exception("下一步流程流转失败");
            }
        }else {
            // 无特殊情况 获取节点配置人员
            String[] users = hrLeaveNodeMapper.selectNodeSignUser(node.getCode());
            if(StringUtils.isNotNull(users) && users.length > 0){
                List<HrLeaveTaskUser> taskUser = new ArrayList<>();
                for(String workNo : users){
                    HrLeaveTaskUser user = new HrLeaveTaskUser();
                    user.setWorkNo(workNo);
                    taskUser.add(user);
                }
                hrLeaveTask.setHrLeaveTaskUserList(taskUser);
                int rows = insertHrLeaveTask(hrLeaveTask);
                // 发送审批通知
                this.sendApproveMessage(taskUser);
                return rows > 0;
            }else {
                throw new Exception("下一步流程流转失败，未获取到下一流程审批人员");
            }
        }
    }


    @Override
    public boolean genNodeTask(LeaveNode node,String leaveId,String[] users){
        HrLeaveTask hrLeaveTask = new HrLeaveTask();
        hrLeaveTask.setNodeSign(node.getCode());
        hrLeaveTask.setLeaveId(leaveId);
        if(node.equals(LeaveNode.TeamGroupCheck)){
            if(users.length > 1){
                hrLeaveTask.setFinishType("1"); // 设置所有人审批才通过
            }
        }
        List<HrLeaveTaskUser> taskUser = new ArrayList<>();
        if(StringUtils.isNotNull(users) && users.length > 0){
            for(String workNo : users){
                HrLeaveTaskUser user = new HrLeaveTaskUser();
                user.setWorkNo(workNo);
                taskUser.add(user);
            }
            hrLeaveTask.setHrLeaveTaskUserList(taskUser);
            int rows = insertHrLeaveTask(hrLeaveTask);
            // 发送审批通知
            this.sendApproveMessage(taskUser);
            return rows > 0;
        }else {
            return false;
        }
    }

    // 处理请假审批人员
    public HrLeaveInfo hendleCheckUser(HrLeaveInfo hrLeaveInfo,LeaveNode node,String workNo){
        HrLeaveUser user = hrLeaveUserMapper.selectUserByWorkNo(workNo);
        String signature = null;
        if(StringUtils.isNotNull(user)){
            signature = user.getSignature();
        }
        if(node.equals(LeaveNode.TeamGroupCheck)){
            // 班组确认
            hrLeaveInfo.setTeamCheckUser(workNo);
            if(StringUtils.isNotNull(signature)){
                hrLeaveInfo.setTeamCheckSign(signature);
            }
        }else if(node.equals(LeaveNode.DeptLeaderCheck)){
            // 部门分厂审批
            hrLeaveInfo.setDeptCheckUser(workNo);
            if(StringUtils.isNotNull(signature)){
                hrLeaveInfo.setDeptCheckSign(signature);
            }
        }else if(node.equals(LeaveNode.HRCheck)){
            // 人力资源部审批
            hrLeaveInfo.setHrCheckUser(workNo);
            if(StringUtils.isNotNull(signature)){
                hrLeaveInfo.setHrCheckSign(signature);
            }
        }else if(node.equals(LeaveNode.LUCheck)){
            // 工会审批
            hrLeaveInfo.setLuCheckUser(workNo);
            if(StringUtils.isNotNull(signature)){
                hrLeaveInfo.setLuCheckSign(signature);
            }
        }else if(node.equals(LeaveNode.SMCheck)){
            // 安全管理部审批
            hrLeaveInfo.setSmCheckUser(workNo);
            if(StringUtils.isNotNull(signature)){
                hrLeaveInfo.setSmCheckSign(signature);
            }
        }
        return hrLeaveInfo;
    }

    public HrLeaveInfo cleanCheckUser(HrLeaveInfo hrLeaveInfo){
        hrLeaveInfo.setTeamCheckSign("");
        hrLeaveInfo.setTeamCheckUser("");
        hrLeaveInfo.setDeptCheckSign("");
        hrLeaveInfo.setDeptCheckUser("");
        hrLeaveInfo.setHrCheckSign("");
        hrLeaveInfo.setHrCheckUser("");
        hrLeaveInfo.setLuCheckSign("");
        hrLeaveInfo.setLuCheckUser("");
        hrLeaveInfo.setSmCheckSign("");
        hrLeaveInfo.setSmCheckUser("");
        return hrLeaveInfo;
    }

    public HrLeaveInfo handleCheckUsers(HrLeaveInfo hrLeaveInfo){
        String workNo = hrLeaveInfo.getWorkNo();
        String hrCheckUser = hrLeaveInfo.getHrCheckUser();
        String teamCheckUser = hrLeaveInfo.getTeamCheckUser();
        String deptCheckUser = hrLeaveInfo.getDeptCheckUser();
        String smCheckUser = hrLeaveInfo.getSmCheckUser();
        String luCheckUser = hrLeaveInfo.getLuCheckUser();
        if(StringUtils.isNotNull(workNo) && StringUtils.isNotBlank(workNo)){
            HrUser user = userService.selectHrUserByWorkNo(workNo);
            hrLeaveInfo.setDeptName(user.getCodeItemDesc());
        }
        if(StringUtils.isNotNull(hrCheckUser) && StringUtils.isNotBlank(hrCheckUser)){
            hrLeaveInfo.setHrCheckUserDes(userService.selectUserByUserName(hrCheckUser).getNickName());
        }
        if(StringUtils.isNotNull(teamCheckUser) && StringUtils.isNotBlank(teamCheckUser)){
            hrLeaveInfo.setTeamCheckUserDes(userService.selectUserByUserName(teamCheckUser).getNickName());
        }
        if(StringUtils.isNotNull(deptCheckUser) && StringUtils.isNotBlank(deptCheckUser)){
            hrLeaveInfo.setDeptCheckUserDes(userService.selectUserByUserName(deptCheckUser).getNickName());
        }
        if(StringUtils.isNotNull(smCheckUser) && StringUtils.isNotBlank(smCheckUser)){
            hrLeaveInfo.setSmCheckUserDes(userService.selectUserByUserName(smCheckUser).getNickName());
        }
        if(StringUtils.isNotNull(luCheckUser) && StringUtils.isNotBlank(luCheckUser)){
            hrLeaveInfo.setLuCheckUserDes(userService.selectUserByUserName(luCheckUser).getNickName());
        }
        return hrLeaveInfo;
    }



    // 审批通知
    private void sendApproveMessage(List<HrLeaveTaskUser> taskUser){
        String path = "packageB/pages/leaveapprove/approve/list";
        String type = "请假审批";
        String time = DateUtils.dateTimeNow("yyyy-MM-dd HH:mm:ss");
        String message = "您有新的请假申请待审批";
        for(HrLeaveTaskUser data : taskUser){
            String workNo = data.getWorkNo();
            HrLeaveUser hrLeaveUser=new HrLeaveUser();
            hrLeaveUser.setWorkNo(workNo);
            List<HrLeaveUser> list = hrLeaveUserMapper.findList(hrLeaveUser);
            if(list.size()>0)
            {
                if(list.get(0).getNotifyFlag().equals("1"))
                {
                    this.sendToDoMessage(time,type,message,workNo,path);
                }
            }
            else
            {
                this.sendToDoMessage(time,type,message,workNo,path);
            }
        }
    }

    // 审批通过通知
    private void sendPassMessage(String workNo){
        String path = "packageB/pages/leaveapprove/leaveInfoSearch/leaveInfoSearch";
        String type = "线上请假";
        String time = DateUtils.dateTimeNow("yyyy-MM-dd HH:mm:ss");
        String message = "您提交的请假申请已通过";
        this.sendToDoMessage(time,type,message,workNo,path);
    }

    // 审批驳回通知
    private void sendRejectMessage(String workNo){
        String path = "packageB/pages/leaveapprove/leaveInfoSearch/leaveInfoSearch";
        String type = "线上请假";
        String time = DateUtils.dateTimeNow("yyyy-MM-dd HH:mm:ss");
        String message = "您提交的请假申请被驳回";
        this.sendToDoMessage(time,type,message,workNo,path);
    }


    // 考勤员通知
    private void sendCollectMessage(String deptCode,String leaveId){
        String[] workNos = hrLeaveUserMapper.selectCollectDeptUser(deptCode);
        String path = "packageB/pages/leaveapprove/leaveInfoSearch/leaveInfoDetail?id="+leaveId;
        String type = "线上请假";
        String time = DateUtils.dateTimeNow("yyyy-MM-dd HH:mm:ss");
        String message = "您有新的员工请假信息";
        for(String workNo : workNos){
            this.sendToDoMessage(time,type,message,workNo,path);
        }
    }

    // 部门一把手通知
    private void sendDeptLeaderMessage(String deptCode,String leaveId){
        HrLeaveUser user = new HrLeaveUser();
        user.setDeptCode(deptCode);
        user.setNodeSign(LeaveNode.DeptLeaderCheck.getCode());
        String[] workNos = hrLeaveUserMapper.selectDeptLeaderArrayByDeptCode(user);
        String path = "packageB/pages/leaveapprove/leaveInfoSearch/leaveInfoDetail?id="+leaveId;
        String type = "线上请假";
        String time = DateUtils.dateTimeNow("yyyy-MM-dd HH:mm:ss");
        String message = "您有新的员工请假信息";
        for(String workNo : workNos){
            this.sendToDoMessage(time,type,message,workNo,path);
        }
    }


    // 发送微信通知
    private void sendToDoMessage(String time,String type,String message,String workNo,String path){
        String appOpenId = commonMapper.selectOpenIdByWorkNo(workNo);
        if(StringUtils.isNotBlank(appOpenId)){
            String openId = officialUserService.getOpenIdsByOpenId(appOpenId);
            if(StringUtils.isNotBlank(openId)){
                AsyncManager.me().execute(new TimerTask() {
                    @Override
                    public void run() {
                        templateMessageService.sendToDoMessage(time,type,message,openId,path);
                    }
                });
            }
        }
    }

    // 发考勤系统
    private void sendToKQ(HrLeaveInfo info){
        try{
            log.info("请假信息推考勤系统-保存信息记录开始！");
            LeaveType type = LeaveType.getByCode(info.getLeaveType());
            if(StringUtils.isNotNull(type)){
                if(type.equals(LeaveType.DeferredLeave)){

                    HrAbnormalAttendancePush hrAbnormalAttendancePush = new HrAbnormalAttendancePush();
                    hrAbnormalAttendancePush.setaNumber(info.getWorkNo()); // 工号
                    hrAbnormalAttendancePush.setFlag("0"); // 混合
                    hrAbnormalAttendancePush.setManuaType("因公"); // 混合
                    hrAbnormalAttendancePush.setCardDateTime(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS,info.getLeaveStartTime()));
                    hrAbnormalAttendancePush.setRemark("线上请假调休");
                    hrAbnormalAttendancePush.setInfoId(Long.parseLong(info.getId()));
                    hrAbnormalAttendancePushMapper.insertHrAbnormalAttendancePush(hrAbnormalAttendancePush);
                }else {
                    // 请假
                    List<LeavePeriod> periods = this.splitDateRanges(info.getLeaveStartTime(), info.getLeaveEndTime(), info.getLeaveDateNum());
                    for (LeavePeriod period : periods) {
                        HrLeavePush hrLeavePush = new HrLeavePush();
                        hrLeavePush.setaNumber(info.getWorkNo());
                        hrLeavePush.setBeginDate(period.getStartTime());
                        hrLeavePush.setEndDate(period.getEndTime());
                        hrLeavePush.setLeaveType(getKQLeaveType(type.getInfo()));
                        hrLeavePush.setLeaveDay(period.getLeaveDays());
                        hrLeavePush.setLeaveHour(period.getLeaveDays().multiply(new BigDecimal("8")));
                        hrLeavePush.setEatCount(0);
                        hrLeavePush.setMiddleWorkDay(new BigDecimal("0"));
                        hrLeavePush.setMorningWorkDay(new BigDecimal("0"));
                        hrLeavePush.setNightWorkDay(new BigDecimal("0"));
                        hrLeavePush.setWorkHour(new BigDecimal("0"));
                        hrLeavePush.setLeaveId(Long.parseLong(info.getId()));
                        hrLeavePush.setRemark("线上请假推送");
                        hrLeavePushMapper.insertHrLeavePush(hrLeavePush);
                    }
                }
            }
        }catch (Exception e){
            log.info("请假信息推送考勤系统-保存信息异常:" + e.getMessage());
        }
    }

    private String getKQLeaveType(String leaveType){
        if(leaveType.equals(LeaveType.BereavementLeave.getInfo())){
            return "丧假";
        }else if(leaveType.equals(LeaveType.PersonalLeave.getInfo())){
            return "事假1";
        }else if(leaveType.equals(LeaveType.MarriageLeave.getInfo())){
            return "婚假";
        }else if(leaveType.equals(LeaveType.HomeLeave.getInfo())){
            return "探亲假";
        }else if(leaveType.equals(LeaveType.ReunionLeave.getInfo())){
            return "团聚假";
        }else if(leaveType.equals(LeaveType.AnnualLeave.getInfo())){
            return "年假";
        }else if(leaveType.equals(LeaveType.SickLeave.getInfo())){
            return "病假1";
        }else if(leaveType.equals(LeaveType.MaternityLeave.getInfo())){
            return "产假/陪产假";
        }else if(leaveType.equals(LeaveType.PrenatalLeave.getInfo())){
            return "产假/陪产假";
        }else if(leaveType.equals(LeaveType.BreastfeedingLeave.getInfo())){
            return "哺乳假";
        }else if(leaveType.equals(LeaveType.WorkInjuryLeave.getInfo())){
            return "工伤1";
        }else if(leaveType.equals(LeaveType.ChildBirthLeave.getInfo())){
            return "产假/陪产假";
        }else{
            return leaveType;
        }
    }

    // 拆分请假数据，同时计算每个时间段的请假天数
    private List<LeavePeriod> splitDateRanges(Date startTime, Date endTime, BigDecimal totalLeaveDays) {
        List<LeavePeriod> result = new ArrayList<>();
        LocalDateTime start = startTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        LocalDateTime end = endTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();

        LocalDate startDate = start.toLocalDate();
        LocalDate endDate = end.toLocalDate();

        boolean found26 = false;

        // 构造当前月的26号
        LocalDateTime target26 = LocalDateTime.of(startDate.getYear(), startDate.getMonth(), 26,0,0,0);

        if (!target26.isAfter(end) && !target26.isBefore(start)) {
            // 当前时间段包含26号
            LocalDateTime splitTime = target26;
            
            Date firstPeriodEnd = toDate(splitTime);
            Date secondPeriodStart = toDate(splitTime.plusSeconds(1)); // 第二段开始时间加1秒
            
            // 计算第一段的天数
            BigDecimal firstPeriodDays = calculateLeaveDays(startTime, firstPeriodEnd, totalLeaveDays, startTime, endTime);
            // 计算第二段的天数
//            BigDecimal secondPeriodDays = calculateLeaveDays(secondPeriodStart, endTime, totalLeaveDays, startTime, endTime);
            BigDecimal secondPeriodDays = totalLeaveDays.subtract(firstPeriodDays);
            log.info("时间段包含26号，已拆分：");
            log.info("第一段：" + format(startTime) + " - " + format(firstPeriodEnd) + "，天数：" + firstPeriodDays);
            log.info("第二段：" + format(secondPeriodStart) + " - " + format(endTime) + "，天数：" + secondPeriodDays);
            
            result.add(new LeavePeriod(startTime, firstPeriodEnd, firstPeriodDays));
            result.add(new LeavePeriod(secondPeriodStart, endTime, secondPeriodDays));
            found26 = true;
        } else {
            // 跨月的情况，检查中间月份是否有26号
            LocalDate current = startDate.withDayOfMonth(1).plusMonths(1);
            while (!current.isAfter(endDate.withDayOfMonth(1))) {
                try {
                    target26 = LocalDateTime.of(current.getYear(), current.getMonth(), 26,0,0,0);
                    if (!target26.isAfter(end)) {
                        LocalDateTime splitTime = target26;
                        
                        Date firstPeriodEnd = toDate(splitTime);
                        Date secondPeriodStart = toDate(splitTime.plusSeconds(1)); // 第二段开始时间加1秒
                        
                        // 计算第一段的天数
                        BigDecimal firstPeriodDays = calculateLeaveDays(startTime, firstPeriodEnd, totalLeaveDays, startTime, endTime);
                        // 计算第二段的天数
//                        BigDecimal secondPeriodDays = calculateLeaveDays(secondPeriodStart, endTime, totalLeaveDays, startTime, endTime);
                        BigDecimal secondPeriodDays = totalLeaveDays.subtract(firstPeriodDays);

                        log.info("时间段包含26号，已拆分：");
                        log.info("第一段：" + format(startTime) + " - " + format(firstPeriodEnd) + "，天数：" + firstPeriodDays);
                        log.info("第二段：" + format(secondPeriodStart) + " - " + format(endTime) + "，天数：" + secondPeriodDays);
                        
                        result.add(new LeavePeriod(startTime, firstPeriodEnd, firstPeriodDays));
                        result.add(new LeavePeriod(secondPeriodStart, endTime, secondPeriodDays));
                        found26 = true;
                        break;
                    }
                } catch (Exception ignored) {}
                current = current.plusMonths(1);
            }
        }

        if (!found26) {
            result.add(new LeavePeriod(startTime, endTime, totalLeaveDays));
            log.info("时间段不包含26号，天数：" + totalLeaveDays);
        }
        return result;
    }

    /**
     * 计算指定时间段的请假天数（按比例分配，最小单位0.5天）
     */
    private BigDecimal calculateLeaveDays(Date periodStart, Date periodEnd, BigDecimal totalLeaveDays, Date originalStart, Date originalEnd) {
        // 计算时间段的小时数
        long periodHours = Math.abs(periodEnd.getTime() - periodStart.getTime()) / (1000 * 60 * 60);
        long totalHours = Math.abs(originalEnd.getTime() - originalStart.getTime()) / (1000 * 60 * 60);
        
        if (totalHours == 0) {
            return BigDecimal.ZERO;
        }
        
        // 按小时比例计算天数
        BigDecimal ratio = new BigDecimal(periodHours).divide(new BigDecimal(totalHours), 4, BigDecimal.ROUND_HALF_UP);
        BigDecimal periodDays = totalLeaveDays.multiply(ratio);
        
        // 按0.5天为最小单位进行舍入
        // 将天数乘以2，四舍五入，再除以2，确保结果是0.5的整数倍
        BigDecimal doubled = periodDays.multiply(new BigDecimal("2"));
        BigDecimal rounded = doubled.setScale(0, BigDecimal.ROUND_HALF_UP);
        BigDecimal finalDays = rounded.divide(new BigDecimal("2"), 1, BigDecimal.ROUND_HALF_UP);
        
        // 如果结果为0但原始计算值大于0，则设为最小单位0.5天
        if (finalDays.compareTo(BigDecimal.ZERO) == 0 && periodDays.compareTo(BigDecimal.ZERO) > 0) {
            finalDays = new BigDecimal("0.5");
        }
        
        return finalDays;
    }

    // 将 LocalDateTime 转换为 Date
    private static Date toDate(LocalDateTime dateTime) {
        return Date.from(dateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    // 格式化输出 Date
    private static String format(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return sdf.format(date);
    }

    private LocalDateTime dateToLocalDateTime(Date date) {
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
    }

    private Date localDateTimeToDate(LocalDateTime localDateTime) {
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }


}
