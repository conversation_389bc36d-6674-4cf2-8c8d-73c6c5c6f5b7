package com.ruoyi.app.supply.service.impl;

import com.github.pagehelper.PageInfo;
import com.ruoyi.app.supply.domain.SupplyUserInfo;
import com.ruoyi.app.supply.domain.SupplyUserImportDto;
import com.ruoyi.app.supply.domain.SupplyUserFac;
import com.ruoyi.app.supply.domain.SupplyUserTec;
import com.ruoyi.app.supply.mapper.SupplyUserInfoMapper;
import com.ruoyi.app.supply.service.ISupplyUserInfoService;
import com.ruoyi.app.supply.service.ISupplyUserFacService;
import com.ruoyi.app.supply.service.ISupplyUserTecService;
import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.constant.HttpStatus;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 相关方人员信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-3-22
 */
@Service
public class SupplyUserInfoServiceImpl implements ISupplyUserInfoService
{
    @Autowired
    private SupplyUserInfoMapper supplyUserInfoMapper;

    @Autowired
    private ISupplyUserFacService supplyUserFacService;

    @Autowired
    private ISupplyUserTecService supplyUserTecService;

    /**
     * 查询相关方人员信息
     *
     * @param id 人员ID
     * @return 人员信息
     */
    @Override
    @DataSource(value = DataSourceType.SUPPLY)
    public SupplyUserInfo selectSupplyUserInfoById(Integer id)
    {
        return supplyUserInfoMapper.selectSupplyUserInfoById(id);
    }

    /**
     * 查询人员信息列表
     *
     * @param supplyUserInfo 人员信息
     * @return 人员信息集合
     */
    @Override
    @DataSource(value = DataSourceType.SUPPLY)
    public List<SupplyUserInfo> selectSupplyUserInfoList(SupplyUserInfo supplyUserInfo)
    {
        return supplyUserInfoMapper.selectSupplyUserInfoList(supplyUserInfo);
    }

    /**
     * 查询人员信息分页列表
     *
     * @param supplyUserInfo 人员信息
     * @return 人员信息分页数据
     */
    @Override
    @DataSource(value = DataSourceType.SUPPLY)
    public TableDataInfo selectSupplyUserInfoTable(SupplyUserInfo supplyUserInfo) {
        List<SupplyUserInfo> list = supplyUserInfoMapper.selectSupplyUserInfoList(supplyUserInfo);
        long total = new PageInfo(list).getTotal();
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setMsg("查询成功");
        rspData.setRows(list);
        rspData.setTotal(total);
        return rspData;
    }

    /**
     * 新增人员信息
     *
     * @param supplyUserInfo 人员信息
     * @return 结果
     */
    @Override
    @DataSource(value = DataSourceType.SUPPLY)
    public int insertSupplyUserInfo(SupplyUserInfo supplyUserInfo)
    {
        return supplyUserInfoMapper.insertSupplyUserInfo(supplyUserInfo);
    }

    /**
     * 修改人员信息
     *
     * @param supplyUserInfo 人员信息
     * @return 结果
     */
    @Override
    @DataSource(value = DataSourceType.SUPPLY)
    public int updateSupplyUserInfo(SupplyUserInfo supplyUserInfo)
    {
        return supplyUserInfoMapper.updateSupplyUserInfo(supplyUserInfo);
    }

    /**
     * 批量删除人员信息
     *
     * @param ids 需要删除的人员信息主键
     * @return 结果
     */
    @Override
    @DataSource(value = DataSourceType.SUPPLY)
    public int deleteSupplyUserInfoByIds(Integer[] ids)
    {
        return supplyUserInfoMapper.deleteSupplyUserInfoByIds(ids);
    }

    /**
     * 删除人员信息信息
     *
     * @param id 人员信息主键
     * @return 结果
     */
    @Override
    @DataSource(value = DataSourceType.SUPPLY)
    public int deleteSupplyUserInfoById(Integer id)
    {
        return supplyUserInfoMapper.deleteSupplyUserInfoById(id);
    }

    /**
     * 根据身份证号和状态查询用户信息
     *
     * @param idcard 身份证号
     * @param state 状态
     * @return 用户信息
     */
    @Override
    @DataSource(value = DataSourceType.SUPPLY)
    public SupplyUserInfo selectSupplyUserInfoByIdcardAndState(String idcard, Integer state)
    {
        return supplyUserInfoMapper.selectSupplyUserInfoByIdcardAndState(idcard, state);
    }

    /**
     * 导入供应商用户信息
     *
     * @param importList 导入数据列表
     * @param currentUser 当前操作用户
     * @return 导入结果列表
     */
    @Override
    @DataSource(value = DataSourceType.SUPPLY)
    public List<SupplyUserImportDto> importSupplyUserInfo(List<SupplyUserImportDto> importList, String currentUser)
    {
        List<SupplyUserImportDto> resultList = new ArrayList<>();
        Date currentTime = DateUtils.getNowDate();

        for (SupplyUserImportDto importDto : importList) {
            try {
                // 数据验证
                String validationResult = validateImportData(importDto);
                if (StringUtils.isNotEmpty(validationResult)) {
                    importDto.setResult("失败");
                    importDto.setReason(validationResult);
                    resultList.add(importDto);
                    continue;
                }

                // 检查唯一性：根据身份证号和状态判断数据唯一性
                SupplyUserInfo existingUser = selectSupplyUserInfoByIdcardAndState(importDto.getIdcard(), importDto.getState());
                if (existingUser != null) {
                    importDto.setResult("失败");
                    importDto.setReason("身份证号[" + importDto.getIdcard() + "]和状态[" + importDto.getState() + "]的用户已存在");
                    resultList.add(importDto);
                    continue;
                }

                // 创建SupplyUserInfo对象
                SupplyUserInfo supplyUserInfo = createSupplyUserInfo(importDto, currentUser, currentTime);

                // 插入用户信息
                int userResult = insertSupplyUserInfo(supplyUserInfo);
                if (userResult <= 0) {
                    importDto.setResult("失败");
                    importDto.setReason("用户信息插入失败");
                    resultList.add(importDto);
                    continue;
                }

                // 记录插入结果
                boolean facSuccess = true;
                boolean tecSuccess = true;
                StringBuilder failureReason = new StringBuilder();

                // 如果有岗位识别卡相关信息，则创建岗位识别卡记录
                if (hasUserFacInfo(importDto)) {
                    SupplyUserFac supplyUserFac = createSupplyUserFac(importDto, supplyUserInfo.getId(), currentUser, currentTime);
                    int facResult = supplyUserFacService.insertSupplyUserFac(supplyUserFac);
                    if (facResult <= 0) {
                        facSuccess = false;
                        failureReason.append("岗位识别卡信息插入失败；");
                    }
                }

                // 如果有三级教育卡相关信息，则创建三级教育卡记录
                if (hasUserTecInfo(importDto)) {
                    SupplyUserTec supplyUserTec = createSupplyUserTec(importDto, supplyUserInfo.getId(), currentUser, currentTime);
                    int tecResult = supplyUserTecService.insertSupplyUserTec(supplyUserTec);
                    if (tecResult <= 0) {
                        tecSuccess = false;
                        failureReason.append("三级教育卡信息插入失败；");
                    }
                }

                // 根据插入结果设置导入状态
                if (facSuccess && tecSuccess) {
                    importDto.setResult("成功");
                    importDto.setReason("导入成功");
                } else if (!facSuccess || !tecSuccess) {
                    importDto.setResult("部分成功");
                    importDto.setReason("用户信息导入成功，但" + failureReason.toString());
                }

                resultList.add(importDto);

            } catch (Exception e) {
                importDto.setResult("失败");
                importDto.setReason("导入异常：" + e.getMessage());
                resultList.add(importDto);
            }
        }

        return resultList;
    }

    /**
     * 验证导入数据
     */
    private String validateImportData(SupplyUserImportDto importDto) {
        if (StringUtils.isEmpty(importDto.getUserName())) {
            return "用户姓名不能为空";
        }
        if (StringUtils.isEmpty(importDto.getIdcard())) {
            return "身份证号不能为空";
        }
        if (importDto.getIdcard().length() != 18) {
            return "身份证号格式不正确，应为18位";
        }
        if (StringUtils.isEmpty(importDto.getSupplyCode())) {
            return "供应商代码不能为空";
        }
        if (StringUtils.isEmpty(importDto.getSupplyName())) {
            return "供应商名称不能为空";
        }
        if (importDto.getState() == null) {
            return "状态不能为空";
        }
        if (importDto.getState() != 0 && importDto.getState() != 1) {
            return "状态值只能为0(无效)或1(有效)";
        }
        return null;
    }

    /**
     * 创建SupplyUserInfo对象
     */
    private SupplyUserInfo createSupplyUserInfo(SupplyUserImportDto importDto, String currentUser, Date currentTime) {
        SupplyUserInfo supplyUserInfo = new SupplyUserInfo();
        supplyUserInfo.setUserCode(importDto.getUserCode());
        supplyUserInfo.setUserName(importDto.getUserName());
        supplyUserInfo.setSupplyCode(importDto.getSupplyCode());
        supplyUserInfo.setSupplyName(importDto.getSupplyName());
        supplyUserInfo.setIdcard(importDto.getIdcard());
        supplyUserInfo.setState(importDto.getState());
        supplyUserInfo.setAddUser(currentUser);
        supplyUserInfo.setAddTime(currentTime);
        return supplyUserInfo;
    }

    /**
     * 检查是否有岗位识别卡相关信息
     */
    private boolean hasUserFacInfo(SupplyUserImportDto importDto) {
        return StringUtils.isNotEmpty(importDto.getUserPost()) ||
               StringUtils.isNotEmpty(importDto.getUserDeptC()) ||
               StringUtils.isNotEmpty(importDto.getUserDeptName()) ||
               importDto.getUserTimeBegin() != null ||
               importDto.getUserTimeEnd() != null ||
               importDto.getUserFacTime() != null ||
               StringUtils.isNotEmpty(importDto.getUserFacWork()) ||
               StringUtils.isNotEmpty(importDto.getUserFacClass());
    }

    /**
     * 检查是否有三级教育卡相关信息
     */
    private boolean hasUserTecInfo(SupplyUserImportDto importDto) {
        return StringUtils.isNotEmpty(importDto.getSex()) ||
               StringUtils.isNotEmpty(importDto.getBirth()) ||
               StringUtils.isNotEmpty(importDto.getEducationLevel()) ||
               StringUtils.isNotEmpty(importDto.getSafetyTraining()) ||
               StringUtils.isNotEmpty(importDto.getExamRes()) ||
               StringUtils.isNotEmpty(importDto.getAddress()) ||
               importDto.getEmploymentDate() != null ||
               importDto.getFirstArtTime() != null ||
               importDto.getFirEndTime() != null ||
               StringUtils.isNotEmpty(importDto.getFirHours()) ||
               importDto.getSecStartTime() != null ||
               importDto.getSecEndTime() != null ||
               StringUtils.isNotEmpty(importDto.getSecHours()) ||
               importDto.getThiStartTime() != null ||
               importDto.getThiEndTime() != null ||
               StringUtils.isNotEmpty(importDto.getThiHours());
    }

    /**
     * 创建SupplyUserFac对象
     */
    private SupplyUserFac createSupplyUserFac(SupplyUserImportDto importDto, Integer userId, String currentUser, Date currentTime) {
        SupplyUserFac supplyUserFac = new SupplyUserFac();
        supplyUserFac.setUserId(userId);
        supplyUserFac.setUserCode(importDto.getUserCode());
        supplyUserFac.setUserName(importDto.getUserName());
        supplyUserFac.setIdcard(importDto.getIdcard());
        supplyUserFac.setSupplyCode(importDto.getSupplyCode());
        supplyUserFac.setSupplyName(importDto.getSupplyName());
        supplyUserFac.setUserPost(importDto.getUserPost());
        supplyUserFac.setUserDeptC(importDto.getUserDeptC());
        supplyUserFac.setUserDeptName(importDto.getUserDeptName());
        supplyUserFac.setUserTimeBegin(importDto.getUserTimeBegin());
        supplyUserFac.setUserTimeEnd(importDto.getUserTimeEnd());
        supplyUserFac.setUserFacTime(importDto.getUserFacTime());
        supplyUserFac.setUserFacWork(importDto.getUserFacWork());
        supplyUserFac.setUserFacClass(importDto.getUserFacClass());
        supplyUserFac.setIsChange(importDto.getIsChange());
        supplyUserFac.setRetireDate(importDto.getRetireDate());
        supplyUserFac.setState(importDto.getState());
        supplyUserFac.setAddUserID(currentUser);
        supplyUserFac.setAddUserName(currentUser);
        supplyUserFac.setAddTime(currentTime);
        return supplyUserFac;
    }

    /**
     * 创建SupplyUserTec对象
     */
    private SupplyUserTec createSupplyUserTec(SupplyUserImportDto importDto, Integer userId, String currentUser, Date currentTime) {
        SupplyUserTec supplyUserTec = new SupplyUserTec();
        supplyUserTec.setUserId(userId);
        supplyUserTec.setUserCode(importDto.getUserCode());
        supplyUserTec.setUserName(importDto.getUserName());
        supplyUserTec.setIdcard(importDto.getIdcard());
        supplyUserTec.setSupplyCode(importDto.getSupplyCode());
        supplyUserTec.setSupplyName(importDto.getSupplyName());
        supplyUserTec.setUserPost(importDto.getUserPost());
        supplyUserTec.setUserDeptC(importDto.getUserDeptC());
        supplyUserTec.setUserDeptName(importDto.getUserDeptName());
        supplyUserTec.setSex(importDto.getSex());
        supplyUserTec.setBirth(importDto.getBirth());
        supplyUserTec.setEducationLevel(importDto.getEducationLevel());
        supplyUserTec.setSafetyTraining(importDto.getSafetyTraining());
        supplyUserTec.setExamRes(importDto.getExamRes());
        supplyUserTec.setAddress(importDto.getAddress());
        supplyUserTec.setEmploymentDate(importDto.getEmploymentDate());
        supplyUserTec.setFirstArtTime(importDto.getFirstArtTime());
        supplyUserTec.setFirEndTime(importDto.getFirEndTime());
        supplyUserTec.setFirHours(importDto.getFirHours());
        supplyUserTec.setSecStartTime(importDto.getSecStartTime());
        supplyUserTec.setSecEndTime(importDto.getSecEndTime());
        supplyUserTec.setSecHours(importDto.getSecHours());
        supplyUserTec.setThiStartTime(importDto.getThiStartTime());
        supplyUserTec.setThiEndTime(importDto.getThiEndTime());
        supplyUserTec.setThiHours(importDto.getThiHours());
        supplyUserTec.setState(importDto.getState().toString());
        supplyUserTec.setAddTime(currentTime);
        return supplyUserTec;
    }

}