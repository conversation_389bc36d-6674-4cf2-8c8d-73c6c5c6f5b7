package com.ruoyi.app.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.ruoyi.app.domain.WXUser;
import com.ruoyi.app.mapper.CommonMapper;
import com.ruoyi.app.service.ICommonService;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.constant.HttpStatus;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginBody;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.enums.UserStatus;
import com.ruoyi.common.utils.MessageUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.file.FileUploadUtils;
import com.ruoyi.common.utils.file.FileUtils;
import com.ruoyi.common.utils.http.HttpUtils;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.common.utils.uuid.UUID;
import com.ruoyi.framework.manager.AsyncManager;
import com.ruoyi.framework.manager.factory.AsyncFactory;
import com.ruoyi.system.service.impl.SysUserServiceImpl;
import com.ruoyi.xcerp.domain.XctgSaleStock;
import com.ruoyi.xcerp.mapper.XctgSaleStockMapper;
import net.coobird.thumbnailator.Thumbnails;
import org.apache.commons.lang3.tuple.Triple;
import org.apache.http.entity.ContentType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 通用功能业务层处理
 *
 * <AUTHOR>
 * @date 2021-03-15
 */
@Service
public class CommonServiceImpl implements ICommonService {

    private static final Logger logger = LoggerFactory.getLogger(CommonServiceImpl.class);

    /**
     * 配置文件中appid
     */
    @Value("${wx.xctg.appid}")
    private String appid;

    /**
     * 配置文件中secret
     */
    @Value("${wx.xctg.secret}")
    private String secret;

    /**
     * 微信获取openId
     */
    @Value("${wx.jsCodeUrl}")
    private String jsCodeUrl;


    @Autowired
    private RedisCache redisCache;

    @Autowired
    private CommonMapper commonMapper;

    @Autowired
    private XctgSaleStockMapper saleStockMapper;

    @Autowired
    private SysUserServiceImpl sysUserService;

    @Value("${minio.domain}")
    private String minioDomain;


    /**
     * 使用code换取token
     *
     * @param code
     * @return
     */
    public AjaxResult getToken(String oldToken, String code) {
        try {
            if (code == null || StringUtils.isEmpty(code)) {
                return AjaxResult.error("code为空");
            }
            // 使用code换取openId
            String rspStr = HttpUtils.sendGet(jsCodeUrl, "appid=" + appid + "&secret=" + secret + "&js_code=" + code + "&grant_type=authorization_code", Constants.UTF8);
            JSONObject obj = JSONObject.parseObject(rspStr);
            if (obj.get("errcode") == null) {
                String openId = obj.getString("openid");
                // 检测openId是否存在，不存在则新建
                WXUser user = commonMapper.selectWXUserByOpenId(openId);

                String nickName = "";
                String workNo = "";
                if (user == null) {
                    commonMapper.insertWXUserByOpenId(openId);
                } else {
                    commonMapper.updateTimeByOpenId(openId);
                    workNo = user.getWorkNo();
                    nickName = commonMapper.selectSysUserByWorkNo(user.getWorkNo());
                    if (user.getStatus().equals("1")) {
                        AsyncManager.me().execute(AsyncFactory.recordLogininfor((workNo == null ? openId : (workNo + "(" + nickName + ")")), Constants.LOGIN_FAIL, MessageUtils.message("user.login.app.forbid")));
                        return AjaxResult.error(HttpStatus.NOT_ALLOW_LOGIN, "禁止访问该小程序");
                    } else {
                        AsyncManager.me().execute(AsyncFactory.recordLogininfor((workNo == null ? openId : (workNo + "(" + nickName + ")")), Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.app.success")));
                    }
                }
                // 生成UUID 用于后续的业务操作 token有效期1天
                String UUID = IdUtils.simpleUUID();
                redisCache.setCacheObject("UUID:" + UUID, openId, 12, TimeUnit.HOURS);
                JSONObject dataJson = new JSONObject();
                dataJson.put("token", UUID);
                dataJson.put("nickName", nickName);
                dataJson.put("workNo", workNo);


                // 删除之前的token记录
                redisCache.deleteObject("UUID:" + oldToken);
                return AjaxResult.success(dataJson);
            } else {
                return AjaxResult.error(obj.getString("errmsg"));
            }
        } catch (Exception e) {
            return AjaxResult.error("发生错误");
        }

    }

    public AjaxResult loginByXC(String token, LoginBody loginBody) {
        try {
            // 获取真实的openId
            String openId = redisCache.getCacheObject("UUID:" + token);
            if (token == null || openId == null)
                return AjaxResult.error(HttpStatus.NOT_IDENTIFY, "身份无法识别，请在设置中重新进入小程序");
            // 登录的工号
            String workNo = loginBody.getUsername().trim();
            // 密码
            String password = loginBody.getPassword();
            // 移动应用系统用户校验
            SysUser sysUser = sysUserService.selectUserByUserName(loginBody.getUsername());
            if (StringUtils.isNull(sysUser)) {
                return AjaxResult.error("登录工号：{} 不存在.", workNo);
            } else if (UserStatus.DELETED.getCode().equals(sysUser.getDelFlag())) {
                return AjaxResult.error("登录工号：{} 已被删除.", workNo);
            } else if (UserStatus.DISABLE.getCode().equals(sysUser.getStatus())) {
                return AjaxResult.error("登录工号：{} 已被停用.", workNo);
            }
            // 校验工号是否已被其他账号使用
            List<WXUser> wxUsers = commonMapper.selectWXUserListByWorkNo(workNo);
            if (wxUsers.size() > 1) {
                return AjaxResult.error("该工号已被其他多个微信号绑定");
            } else if (wxUsers.size() == 1) {
                WXUser user = wxUsers.get(0);
                if (!user.getOpenId().equals(openId)) {
                    return AjaxResult.error("该工号已被其他微信号绑定");
                }

            }
            // 密码校验
            if (!SecurityUtils.matchesPassword(password, sysUser.getPassword())) {
                if (sysUserService.loginInMes(workNo, password).equals("PASS")) {
                    // 密码重置为产销密码
                    sysUserService.resetUserPwd(workNo, SecurityUtils.encryptPassword(password));
                } else {
                    return AjaxResult.error("账号密码不匹配");
                }
            }
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(workNo + "(" + sysUser.getNickName() + ")", Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.app.success")));

            // 匹配到合适的账户 与openId进行关联
            WXUser user = new WXUser();
            user.setOpenId(openId);
            user.setWorkNo(workNo);
            commonMapper.updateWorkNoByOpenId(user);
            // 拼接返回数据
            JSONObject dataJson = new JSONObject();
            dataJson.put("nickName", sysUser.getNickName());
            dataJson.put("workNo", workNo);
            return AjaxResult.success(dataJson);
        } catch (Exception e) {
            return AjaxResult.error("发生错误");
        }
    }

    public AjaxResult getPermission(String token) {
        try {
            // 获取真实的openId
            String openId = redisCache.getCacheObject("UUID:" + token);
            if (token == null || openId == null)
                return AjaxResult.error(HttpStatus.NOT_IDENTIFY, "身份无法识别，请在设置中重新进入小程序");
            String workNo = commonMapper.selectWorkNoByOpenId(openId);
            if (workNo == null)
                return AjaxResult.error(HttpStatus.NOT_WORKNO, "工号识别失败");
            List<String> list = commonMapper.getPermissionList(workNo);
            Map<String, List<String>> result = new HashMap<>();
            if (list.contains("xcerp")) {
                List<String> hashResult = new ArrayList<>();
                if (list.contains("guarantee"))
                    hashResult.add("guarantee");
                if (list.contains("guaranteedeal"))
                    hashResult.add("guaranteedeal");
                if (list.contains("stock"))
                    hashResult.add("stock");
                if (list.contains("saleDetail"))
                    hashResult.add("saleDetail");
                if (list.contains("freeAmt"))
                    hashResult.add("freeAmt");
                if (list.contains("goodsInStock"))
                    hashResult.add("goodsInStock");
                if (list.contains("contractTrack)"))
                    hashResult.add("contractTrack");

                result.put("xcerp", hashResult);
            }

            // 经营分析权限
            if (list.contains("prodForm")) {
                List<String> hashResult = new ArrayList<>();
                // 产销跟踪日报权限
                if (list.contains("xcprodnew"))
                    hashResult.add("xcprodnew");
                // 生产经营日报
                if (list.contains("outputform"))
                    hashResult.add("outputform");
                result.put("prodForm", hashResult);
            }

            // 客户走访栏目权限
            if (list.contains("visit")) {
                List<String> hashResult = new ArrayList<>();
                // 客户走访编制权限
                if (list.contains("visitPlan"))
                    hashResult.add("visitPlan");
                // 客户走访审核权限
                if (list.contains("visitVerify"))
                    hashResult.add("visitVerify");
                result.put("visit", hashResult);
            }

            //文件报批模块
            if (list.contains("fileApprove")) {
                List<String> hashResult = new ArrayList<>();
                if (list.contains("fileApproveAdd"))
                    hashResult.add("fileApproveAdd");
                if (list.contains("fileExamine"))
                    hashResult.add("fileExamine");
                if (list.contains("fileHistory"))
                    hashResult.add("fileHistory");
                result.put("fileApprove", hashResult);
            }

            //计算机点检模块
            if (list.contains("xctgDevice")) {
                List<String> hashResult = new ArrayList<>();
                if (list.contains("myItDevice"))
                    hashResult.add("myItDevice");
                result.put("xctgDevice", hashResult);
            }


            // 其他模块
            List<String> hashResult = new ArrayList<>();
            if (list.contains("sign")) hashResult.add("sign");
            hashResult.add("wages");
            result.put("other", hashResult);

            return AjaxResult.success(result);
        } catch (Exception e) {
            return AjaxResult.error("发生错误");
        }
    }

    /**
     * 根据token 获得openid
     */
    public String getOpenid(String token) {
        String openId = redisCache.getCacheObject("UUID:" + token);
        return openId;
    }

    /**
     * 根据token 获得work_no
     */
    public String getWorkNoByToken(String token) {
        String openId = redisCache.getCacheObject("UUID:" + token);
        String workNo = null;
        if (token != null && openId != null) workNo = commonMapper.selectWorkNoByOpenId(openId);
        return workNo;
    }

    /**
     * 根据工号 获得姓名
     */
    public String getUserNameByWorkNo(String workNo) {
        return commonMapper.getUserNameByWorkNo(workNo);
    }

    @Override
    public AjaxResult uploadAndTestFace(MultipartFile file) {
        try {
            //压缩图片至指定大小(200KB)以下
            byte[] fileBytes = file.getBytes();
            byte[] imageBytes = compressPicForScale(fileBytes, 200);
            InputStream inputStream = new ByteArrayInputStream(imageBytes);
            //构造唯一文件名uuid
            String originalFilename = file.getOriginalFilename();
            int index = originalFilename.lastIndexOf(".");
            String extName = originalFilename.substring(index);
            String newFileName = UUID.randomUUID().toString() + extName;
            MultipartFile newFile = new MockMultipartFile(newFileName, file.getOriginalFilename(), ContentType.APPLICATION_OCTET_STREAM.toString(), inputStream);
            //照片验证
            String verifyUrl = "http://*************:8080/asseapi/api/v1/face/verify/facepass";
            String fileKey = "picture";
            Map<String, String> param = new HashMap<>();
            param.put("ip", "************");
            param.put("port", "4001");
            param.put("posId", "1");
            //MultipartFile转File，用于上传文件
            File verifyFile = new File(newFile.getOriginalFilename());
            FileUtils.writeByteArrayToFile(verifyFile, newFile.getBytes());
            //返回值，若验证成功则上传文件，否则提示重新上传人脸
            int responseCode = HttpUtils.doPostFormData(verifyUrl, verifyFile, fileKey, param);
            //删除临时文件
            if (verifyFile.exists()) {
                if (!verifyFile.delete()) {
                    logger.error("访客临时文件删除失败！");
                }
            }
            if (responseCode == HttpStatus.SUCCESS) {
                // 上传并返回新文件名称
                String fileName = FileUploadUtils.uploadMinio(newFile, "guest");
                String url = minioDomain + "/" + fileName;
                AjaxResult ajax = AjaxResult.success();
                ajax.put("fileName", fileName);
                ajax.put("url", url);
                return ajax;
            } else {
                return AjaxResult.error("请重新上传图片");
            }
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    @Override
    public AjaxResult uploadVerifyVisitor(MultipartFile file) {
        try {
            //1.压缩
            //1.1压缩图片至指定大小(200KB)以下
            byte[] fileBytes = file.getBytes();
            byte[] imageBytes = compressPicForScale(fileBytes, 200);
            InputStream inputStream = new ByteArrayInputStream(imageBytes);
            //1.2构造唯一文件名uuid
            String originalFilename = file.getOriginalFilename();
            int index = originalFilename.lastIndexOf(".");
            String extName = originalFilename.substring(index);
            String newFileName = UUID.randomUUID().toString() + extName;
            MultipartFile newFile = new MockMultipartFile("picture", newFileName, ContentType.APPLICATION_OCTET_STREAM.toString(), inputStream);
            //1.3MultipartFile转File，用于上传文件
            File compressedFile = new File(newFileName);
            FileUtils.writeByteArrayToFile(compressedFile, newFile.getBytes());
            //2.照片验证
            String verifyUrl = "http://*************:8080/asseapi/api/v1/face/verify/facepass";
            Map<String, String> param = new HashMap<>();
            param.put("ip", "************");
            param.put("port", "4001");
            param.put("posId", "1");
            int responseCode = HttpUtils.doPostFormData(verifyUrl, compressedFile, "picture", param);
            if (responseCode == HttpStatus.SUCCESS) {
                //3.截取
                //3.1若截取成功则上传
//                String facialSegmentationUrl = "http://*************:8080/asseapi/api/v1/face/cut";
//                String cutFileKey = "picture";
//                Map<String, String> cutParam = new HashMap<>();
//                cutParam.put("onlyFace", "true");
//                cutParam.put("maxWidth", "600");
//                cutParam.put("maxHeight", "800");
//                Triple<Integer, File, String> faceCutRes = HttpUtils.doPostSpecialForm(facialSegmentationUrl, compressedFile, cutFileKey, cutParam);
//                //3.2删除压缩文件
//                if (compressedFile.exists()) {
//                    if (!compressedFile.delete()) {
//                        logger.error("访客->压缩文件删除失败！");
//                    }
//                }
                if (responseCode == HttpStatus.SUCCESS) {
                    //3.4上传并返回新文件名称
                    FileInputStream input = new FileInputStream(compressedFile);
                    MultipartFile multipartFile = new MockMultipartFile("picture",
                            newFileName, ContentType.APPLICATION_OCTET_STREAM.toString(), input);
                    String fileName = FileUploadUtils.uploadMinio(multipartFile, "visitor");
                    String url = minioDomain + "/" + fileName;
                    AjaxResult ajax = AjaxResult.success();
                    ajax.put("fileName", fileName);
                    ajax.put("url", url);
                    //3.5删除临时文件
                    if (compressedFile.exists()) {
                        if (!compressedFile.delete()) {
                            logger.error("访客->截取图片删除失败！");
                        }
                    }
                    return ajax;
                } else {
                    return AjaxResult.error("照片截取失败");
                }
            } else {
                //3.2删除压缩文件
                if (compressedFile.exists()) {
                    if (!compressedFile.delete()) {
                        logger.error("访客->压缩文件删除错误！");
                    }
                }
                return AjaxResult.error("请重新上传图片");
            }


        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    //图片常量
    private static final Integer ZERO = 0;
    private static final Integer ONE_ZERO_TWO_FOUR = 1024;
    private static final Integer NINE_ZERO_ZERO = 900;
    private static final Integer THREE_TWO_SEVEN_FIVE = 3275;
    private static final Integer TWO_ZERO_FOUR_SEVEN = 2047;
    private static final Double ZERO_EIGHT_FIVE = 0.85;
    private static final Double ZERO_SIX = 0.6;
    private static final Double ZERO_FOUR_FOUR = 0.44;
    private static final Double ZERO_FIVE = 0.5;

    /**
     * 根据指定大小压缩图片
     *
     * @param imageBytes  源图片字节数组
     * @param desFileSize 指定图片大小，单位kb
     * @return 压缩质量后的图片字节数组
     */
    public static byte[] compressPicForScale(byte[] imageBytes, long desFileSize) {
        if (imageBytes == null || imageBytes.length <= ZERO || imageBytes.length < desFileSize * ONE_ZERO_TWO_FOUR) {
            return imageBytes;
        }
        long srcSize = imageBytes.length;
        double accuracy = getAccuracy(srcSize / ONE_ZERO_TWO_FOUR);
        try {
            while (imageBytes.length > desFileSize * ONE_ZERO_TWO_FOUR) {
                ByteArrayInputStream inputStream = new ByteArrayInputStream(imageBytes);
                ByteArrayOutputStream outputStream = new ByteArrayOutputStream(imageBytes.length);
                Thumbnails.of(inputStream)
                        .scale(accuracy)
                        .outputQuality(accuracy)
                        .toOutputStream(outputStream);
                imageBytes = outputStream.toByteArray();
            }
            logger.info("图片原大小={}kb | 压缩后大小={}kb",
                    srcSize / ONE_ZERO_TWO_FOUR, imageBytes.length / ONE_ZERO_TWO_FOUR);
        } catch (Exception e) {
            logger.error("【图片压缩】msg=图片压缩失败!", e);
        }
        return imageBytes;
    }

    /**
     * 自动调节精度(经验数值)
     *
     * @param size 源图片大小
     * @return 图片压缩质量比
     */
    private static double getAccuracy(long size) {
        double accuracy;
        if (size < NINE_ZERO_ZERO) {
            accuracy = ZERO_EIGHT_FIVE;
        } else if (size < TWO_ZERO_FOUR_SEVEN) {
            accuracy = ZERO_SIX;
        } else if (size < THREE_TWO_SEVEN_FIVE) {
            accuracy = ZERO_FOUR_FOUR;
        } else {
            accuracy = ZERO_FIVE;
        }
        return accuracy;
    }

    public AjaxResult getSubCompanyAndSubOperator() {
        List<Map<String, Object>> result = new ArrayList<Map<String, Object>>();
        List<Map> companyList = saleStockMapper.selectSubCompany();
        List<XctgSaleStock> list = saleStockMapper.selectSubCompanyAndSubOperator();
        for (Map map : companyList) {
            Map<String, Object> company = new HashMap<>();
            company.put("companyName", map.get("NAME"));
            company.put("companyNo", map.get("VALUE"));
            List<Map<String, Object>> operators = new ArrayList<Map<String, Object>>();
            for (XctgSaleStock stock : list) {
                if (stock.getCompanyNo().equals(map.get("VALUE"))) {
                    Map<String, Object> operator = new HashMap<>();
                    operator.put("operatorNo", stock.getOperatorNo());
                    operator.put("operatorName", stock.getOperatorName());
                    operators.add(operator);
                }
            }
            company.put("operators", operators);
            result.add(company);
        }
        return AjaxResult.success(result);
    }

    public String getNotice() {
        return commonMapper.getNotice();
    }

}
