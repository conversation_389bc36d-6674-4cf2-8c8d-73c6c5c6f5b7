package com.ruoyi.app.v1.service;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.ruoyi.app.v1.domain.HrAbnormalAttendancePush;
import com.ruoyi.app.v1.domain.HrLeavePush;
import com.ruoyi.app.v1.mapper.HrAbnormalAttendancePushMapper;
import com.ruoyi.app.v1.mapper.HrLeavePushMapper;
import com.ruoyi.app.vehicleAccess.service.impl.XctgVehicleAuditFlowServiceImpl;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.http.HttpUtil;
import com.ruoyi.common.utils.http.HttpUtils;
import com.ruoyi.common.utils.ssl.SslUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.net.URLEncoder;
import java.util.HashMap;


@Service
public class KQInfoPush {
    private static final Logger log = LoggerFactory.getLogger(XctgVehicleAuditFlowServiceImpl.class);
    // 推送考勤手工补卡地址
//    private static final String MANUALCARD_URL = "http://172.16.15.133:8080/xckqjob/api/v1/kq/manualcard";
    private static final String MANUALCARD_URL = "https://172.16.15.133:9228/xckqjob/api/v1/kq/manualcard";
    // 推送考勤请假数据地址
//    private static final String LEAVE_URL = "http://172.16.15.133:8080/xckqjob/api/v1/kq/leave";
    private static final String LEAVE_URL = "https://172.16.15.133:9228/xckqjob/api/v1/kq/leave";

    @Autowired
    private HrAbnormalAttendancePushMapper hrAbnormalAttendancePushMapper;
    @Autowired
    private HrLeavePushMapper hrLeavePushMapper;

    /**
     * 每隔10秒推送-请假信息
     */
    @Scheduled(fixedRate = 10000)
    public void sendLeaveInfoTask() {
        HrLeavePush hrLeavePush = hrLeavePushMapper.selectHrLeavePushInfo();
        if (hrLeavePush != null) {
            sendLeavePush(hrLeavePush);
        } else {
            log.debug("无推送请假信息");
        }
    }

    /**
     * 每隔13秒推送-异常考勤信息
     */
    @Scheduled(fixedRate = 13000)
    public void sendAbnormalAttendanceInfoTask() {
        HrAbnormalAttendancePush hrAbnormalAttendancePush = hrAbnormalAttendancePushMapper.selectHrAbnormalAttendancePushInfo();
        if (hrAbnormalAttendancePush != null) {
            sendAbnormalAttendancePush(hrAbnormalAttendancePush);
        } else {
            log.debug("无推送异常考勤信息");
        }
    }

    /**
     * 异步推送请假数据
     */
    @Async("threadPoolTaskExecutor")
    public void sendLeavePush(HrLeavePush info) {
        try{
            log.debug("请假推送开始");
            String sb = "a_Number=" +
                    info.getaNumber() +
                    "&beginDate=" +
                    URLEncoder.encode(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS,info.getBeginDate()),"UTF-8") +
                    "&endDate=" +
                    URLEncoder.encode(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS,info.getEndDate()),"UTF-8") +
                    "&leaveType=" +
                    URLEncoder.encode(info.getLeaveType(),"UTF-8") +
                    "&leaveDay=" +
                    info.getLeaveDay() +
                    "&workHour=" +
                    info.getWorkHour() +
                    "&morningWorkDay=" +
                    info.getMorningWorkDay() +
                    "&middleWorkDay=" +
                    info.getMiddleWorkDay() +
                    "&nightWorkDay=" +
                    info.getNightWorkDay() +
                    "&leaveHour=" +
                    info.getLeaveHour() +
                    "&eatCount=" +
                    info.getEatCount() +
                    "&remark=" +
                    URLEncoder.encode(info.getRemark(),"UTF-8");
//                    info.getRemark();
            log.info("请假推送信息：" + sb);
            String result = HttpUtils.sendSSLPost(LEAVE_URL,sb);
            log.info("请假推送结果：" + result);
            JSONObject jsonObject = JSON.parseObject(result);
            Long keyId = jsonObject.getLong("keyId");
            if(keyId != 0){
                log.info("请假推送成功");
                info.setKeyId(keyId);
                info.setPushFlag("1");
            }else{
                log.info("请假推送失败");
                info.setPushFlag("2");
            }
            hrLeavePushMapper.updateHrLeavePush(info);
        }catch (Exception e) {
            log.error("请假推送发生异常:"+ e);
            info.setPushFlag("2");
            hrLeavePushMapper.updateHrLeavePush(info);
        }
    }

    /**
     * 异步推送异常考勤数据
     */
    @Async("threadPoolTaskExecutor")
    public void sendAbnormalAttendancePush(HrAbnormalAttendancePush info) {
        try{
            log.debug("异常考勤推送开始");
            String sb = "a_Number=" +
                    info.getaNumber() +
                    "&flag=" +
                    info.getFlag() +
                    "&cardDateTime=" +
                    URLEncoder.encode(info.getCardDateTime(),"UTF-8")+
                    "&manualType=" +
                    URLEncoder.encode(info.getManuaType(),"UTF-8") +
                    "&remark=" +
                    URLEncoder.encode(info.getRemark(),"UTF-8");
            log.info("异常考勤推送信息：" + sb);
            String result = HttpUtils.sendSSLPost(MANUALCARD_URL ,sb);
            log.info("异常考勤推送结果：" + result);
            JSONObject jsonObject = JSON.parseObject(result);
            Long keyId = jsonObject.getLong("keyId");
            if(keyId != 0){
                log.info("异常考勤推送成功");
                info.setKeyId(keyId);
                info.setPushFlag("1");
            }else{
                log.info("异常考勤推送失败");
                info.setPushFlag("2");
            }
            hrAbnormalAttendancePushMapper.updateHrAbnormalAttendancePush(info);
        }catch (Exception e) {
            log.error("异常考勤推送发生异常:"+ e);
            info.setPushFlag("2");
            hrAbnormalAttendancePushMapper.updateHrAbnormalAttendancePush(info);
        }
    }
}
