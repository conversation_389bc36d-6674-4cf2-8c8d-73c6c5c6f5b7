<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.app.supply.mapper.SupplyUserInfoMapper">
    
    <resultMap type="SupplyUserInfo" id="SupplyUserInfoResult">
        <result property="id"    column="ID"    />
        <result property="userCode"    column="USERCODE"    />
        <result property="userName"    column="USERNAME"    />
        <result property="supplyCode"    column="SUPPLYCODE"    />
        <result property="supplyName"    column="SUPPLYNAME"    />
        <result property="idcard"    column="IDCARD"    />
        <result property="state"    column="STATE"    />
        <result property="addUser"    column="ADDUSER"    />
        <result property="addTime"    column="ADDTIME"    />
        <result property="editUser"    column="EDITUSER"    />
        <result property="editTime"    column="EDITTIME"    />
        <result property="delUser"    column="DELUSER"    />
        <result property="delTime"    column="DELTIME"    />
    </resultMap>

    <sql id="selectSupplyUserInfo">
        select ID, USERCODE, USERNAME, SUPPLYCODE, SUPPLYNAME, IDCARD, STATE, ADDUSER, ADDTIME, EDITUSER, EDITTIME, DELUSER, DELTIME FROM EMPHEALTH.SUPPLY_USERINFO
    </sql>

    <select id="selectSupplyUserInfoList" parameterType="SupplyUserInfo" resultMap="SupplyUserInfoResult">
        <include refid="selectSupplyUserInfo"/>
        <where>  
            <if test="id != null"> and ID = #{id}</if>
            <if test="userCode != null and userCode != ''"> and USERCODE like '%'||#{userCode}||'%'</if>
            <if test="userName != null and userName != ''"> and USERNAME like '%'||#{userName}||'%'</if>
            <if test="supplyCode != null and supplyCode != ''"> and SUPPLYCODE like '%'||#{supplyCode}||'%'</if>
            <if test="supplyName != null and supplyName != ''"> and SUPPLYNAME like '%'||#{supplyName}||'%'</if>
            <if test="idcard != null and idcard != ''"> and IDCARD like '%'||#{idcard}||'%'</if>
            <if test="state != null"> and STATE = #{state}</if>
            <if test="addUser != null and addUser != ''"> and ADDUSER like '%'||#{addUser}||'%'</if>
            <if test="addTime != null"> and ADDTIME = #{addTime}</if>
            <if test="editUser != null and editUser != ''"> and EDITUSER like '%'||#{editUser}||'%'</if>
            <if test="editTime != null"> and EDITTIME = #{editTime}</if>
            <if test="delUser != null and delUser != ''"> and DELUSER like '%'||#{delUser}||'%'</if>
            <if test="delTime != null"> and DELTIME = #{delTime}</if>
        </where>
        order by ID desc
    </select>
    
    <select id="selectSupplyUserInfoById" parameterType="Integer" resultMap="SupplyUserInfoResult">
        <include refid="selectSupplyUserInfo"/>
        where ID = #{id}
    </select>

    <!-- 新增人员信息 -->
    <insert id="insertSupplyUserInfo" parameterType="SupplyUserInfo">
        insert into EMPHEALTH.SUPPLY_USERINFO
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userCode != null">USERCODE,</if>
            <if test="userName != null">USERNAME,</if>
            <if test="supplyCode != null">SUPPLYCODE,</if>
            <if test="supplyName != null">SUPPLYNAME,</if>
            <if test="idcard != null">IDCARD,</if>
            <if test="state != null">STATE,</if>
            <if test="addUser != null">ADDUSER,</if>
            <if test="addTime != null">ADDTIME,</if>
            <if test="editUser != null">EDITUSER,</if>
            <if test="editTime != null">EDITTIME,</if>
            <if test="delUser != null">DELUSER,</if>
            <if test="delTime != null">DELTIME,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userCode != null">#{userCode},</if>
            <if test="userName != null">#{userName},</if>
            <if test="supplyCode != null">#{supplyCode},</if>
            <if test="supplyName != null">#{supplyName},</if>
            <if test="idcard != null">#{idcard},</if>
            <if test="state != null">#{state},</if>
            <if test="addUser != null">#{addUser},</if>
            <if test="addTime != null">#{addTime},</if>
            <if test="editUser != null">#{editUser},</if>
            <if test="editTime != null">#{editTime},</if>
            <if test="delUser != null">#{delUser},</if>
            <if test="delTime != null">#{delTime},</if>
         </trim>
    </insert>

    <!-- 修改人员信息 -->
    <update id="updateSupplyUserInfo" parameterType="SupplyUserInfo">
        update EMPHEALTH.SUPPLY_USERINFO
        <trim prefix="SET" suffixOverrides=",">
            <if test="userCode != null">USERCODE = #{userCode},</if>
            <if test="userName != null">USERNAME = #{userName},</if>
            <if test="supplyCode != null">SUPPLYCODE = #{supplyCode},</if>
            <if test="supplyName != null">SUPPLYNAME = #{supplyName},</if>
            <if test="idcard != null">IDCARD = #{idcard},</if>
            <if test="state != null">STATE = #{state},</if>
            <if test="addUser != null">ADDUSER = #{addUser},</if>
            <if test="addTime != null">ADDTIME = #{addTime},</if>
            <if test="editUser != null">EDITUSER = #{editUser},</if>
            <if test="editTime != null">EDITTIME = #{editTime},</if>
            <if test="delUser != null">DELUSER = #{delUser},</if>
            <if test="delTime != null">DELTIME = #{delTime},</if>
        </trim>
        where ID = #{id}
    </update>

    <!-- 删除人员信息 -->
    <delete id="deleteSupplyUserInfoById" parameterType="Integer">
        delete from EMPHEALTH.SUPPLY_USERINFO where ID = #{id}
    </delete>

    <!-- 批量删除人员信息 -->
    <delete id="deleteSupplyUserInfoByIds" parameterType="Integer">
        delete from EMPHEALTH.SUPPLY_USERINFO where ID in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 根据身份证号和状态查询用户信息 -->
    <select id="selectSupplyUserInfoByIdcardAndState" resultMap="SupplyUserInfoResult">
        <include refid="selectSupplyUserInfo"/>
        <where>
            <if test="idcard != null and idcard != ''"> and IDCARD = #{idcard}</if>
            <if test="state != null"> and STATE = #{state}</if>
        </where>
        AND ROWNUM = 1
    </select>

</mapper>