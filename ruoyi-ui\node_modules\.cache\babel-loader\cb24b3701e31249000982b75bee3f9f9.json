{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\leave\\department\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\leave\\department\\index.vue", "mtime": 1756099891069}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\babel.config.js", "mtime": 1688548084091}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_department", "require", "name", "components", "data", "loading", "ids", "single", "multiple", "showSearch", "total", "departmentList", "title", "open", "queryParams", "pageNum", "pageSize", "validFlag", "storeCode", "storeName", "queryWord", "type", "unitName", "position", "lowerLimit", "upLimit", "memo", "fStoreCode", "fStoreName", "levelName", "form", "rules", "created", "getList", "methods", "_this", "listDepartment", "then", "response", "rows", "cancel", "reset", "id", "createTime", "createBy", "updateTime", "updateBy", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleSelectionChange", "selection", "map", "item", "length", "handleAdd", "handleUpdate", "row", "_this2", "getDepartment", "submitForm", "_this3", "$refs", "validate", "valid", "updateDepartment", "msgSuccess", "addDepartment", "handleDelete", "_this4", "$confirm", "confirmButtonText", "cancelButtonText", "delDepartment", "handleExport", "_this5", "exportDepartment", "download", "msg"], "sources": ["src/views/leave/department/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n      <el-form-item label=\"状态\" prop=\"validFlag\">\r\n        <el-input\r\n          v-model=\"queryParams.validFlag\"\r\n          placeholder=\"请输入状态\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"库房编码\" prop=\"storeCode\">\r\n        <el-input\r\n          v-model=\"queryParams.storeCode\"\r\n          placeholder=\"请输入库房编码\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"库房名称\" prop=\"storeName\">\r\n        <el-input\r\n          v-model=\"queryParams.storeName\"\r\n          placeholder=\"请输入库房名称\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"拼音头缩写\" prop=\"queryWord\">\r\n        <el-input\r\n          v-model=\"queryParams.queryWord\"\r\n          placeholder=\"请输入拼音头缩写\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"原料库0，港口库为1，车站库2；3-投料库，4-完工库，5-销售库\" prop=\"type\">\r\n        <el-select v-model=\"queryParams.type\" placeholder=\"请选择原料库0，港口库为1，车站库2；3-投料库，4-完工库，5-销售库\" clearable size=\"small\">\r\n          <el-option label=\"请选择字典生成\" value=\"\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"单位名称\" prop=\"unitName\">\r\n        <el-input\r\n          v-model=\"queryParams.unitName\"\r\n          placeholder=\"请输入单位名称\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"库房位置\" prop=\"position\">\r\n        <el-input\r\n          v-model=\"queryParams.position\"\r\n          placeholder=\"请输入库房位置\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"最小限制\" prop=\"lowerLimit\">\r\n        <el-input\r\n          v-model=\"queryParams.lowerLimit\"\r\n          placeholder=\"请输入最小限制\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"最大限制\" prop=\"upLimit\">\r\n        <el-input\r\n          v-model=\"queryParams.upLimit\"\r\n          placeholder=\"请输入最大限制\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"备注信息\" prop=\"memo\">\r\n        <el-input\r\n          v-model=\"queryParams.memo\"\r\n          placeholder=\"请输入备注信息\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"上级单位编码\" prop=\"fStoreCode\">\r\n        <el-input\r\n          v-model=\"queryParams.fStoreCode\"\r\n          placeholder=\"请输入上级单位编码\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"上级单位名称\" prop=\"fStoreName\">\r\n        <el-input\r\n          v-model=\"queryParams.fStoreName\"\r\n          placeholder=\"请输入上级单位名称\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"单位层级\" prop=\"levelName\">\r\n        <el-input\r\n          v-model=\"queryParams.levelName\"\r\n          placeholder=\"请输入单位层级\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"cyan\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          icon=\"el-icon-plus\"\r\n          size=\"mini\"\r\n          @click=\"handleAdd\"\r\n          v-hasPermi=\"['leave:department:add']\"\r\n        >新增</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"success\"\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"single\"\r\n          @click=\"handleUpdate\"\r\n          v-hasPermi=\"['leave:department:edit']\"\r\n        >修改</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n          v-hasPermi=\"['leave:department:remove']\"\r\n        >删除</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n          v-hasPermi=\"['leave:department:export']\"\r\n        >导出</el-button>\r\n      </el-col>\r\n\t  <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table v-loading=\"loading\" :data=\"departmentList\" @selection-change=\"handleSelectionChange\">\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column label=\"主键\" align=\"center\" prop=\"id\" />\r\n      <el-table-column label=\"状态\" align=\"center\" prop=\"validFlag\" />\r\n      <el-table-column label=\"库房编码\" align=\"center\" prop=\"storeCode\" />\r\n      <el-table-column label=\"库房名称\" align=\"center\" prop=\"storeName\" />\r\n      <el-table-column label=\"拼音头缩写\" align=\"center\" prop=\"queryWord\" />\r\n      <el-table-column label=\"原料库0，港口库为1，车站库2；3-投料库，4-完工库，5-销售库\" align=\"center\" prop=\"type\" />\r\n      <el-table-column label=\"单位名称\" align=\"center\" prop=\"unitName\" />\r\n      <el-table-column label=\"库房位置\" align=\"center\" prop=\"position\" />\r\n      <el-table-column label=\"最小限制\" align=\"center\" prop=\"lowerLimit\" />\r\n      <el-table-column label=\"最大限制\" align=\"center\" prop=\"upLimit\" />\r\n      <el-table-column label=\"备注信息\" align=\"center\" prop=\"memo\" />\r\n      <el-table-column label=\"上级单位编码\" align=\"center\" prop=\"fStoreCode\" />\r\n      <el-table-column label=\"上级单位名称\" align=\"center\" prop=\"fStoreName\" />\r\n      <el-table-column label=\"单位层级\" align=\"center\" prop=\"levelName\" />\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleUpdate(scope.row)\"\r\n            v-hasPermi=\"['leave:department:edit']\"\r\n          >修改</el-button>\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleDelete(scope.row)\"\r\n            v-hasPermi=\"['leave:department:remove']\"\r\n          >删除</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n    \r\n    <pagination\r\n      v-show=\"total>0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 添加或修改出门证部门（厂内单位）对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"500px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\r\n        <el-form-item label=\"状态\" prop=\"validFlag\">\r\n          <el-input v-model=\"form.validFlag\" placeholder=\"请输入状态\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"库房编码\" prop=\"storeCode\">\r\n          <el-input v-model=\"form.storeCode\" placeholder=\"请输入库房编码\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"库房名称\" prop=\"storeName\">\r\n          <el-input v-model=\"form.storeName\" placeholder=\"请输入库房名称\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"拼音头缩写\" prop=\"queryWord\">\r\n          <el-input v-model=\"form.queryWord\" placeholder=\"请输入拼音头缩写\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"原料库0，港口库为1，车站库2；3-投料库，4-完工库，5-销售库\" prop=\"type\">\r\n          <el-select v-model=\"form.type\" placeholder=\"请选择原料库0，港口库为1，车站库2；3-投料库，4-完工库，5-销售库\">\r\n            <el-option label=\"请选择字典生成\" value=\"\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"单位名称\" prop=\"unitName\">\r\n          <el-input v-model=\"form.unitName\" placeholder=\"请输入单位名称\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"库房位置\" prop=\"position\">\r\n          <el-input v-model=\"form.position\" placeholder=\"请输入库房位置\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"最小限制\" prop=\"lowerLimit\">\r\n          <el-input v-model=\"form.lowerLimit\" placeholder=\"请输入最小限制\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"最大限制\" prop=\"upLimit\">\r\n          <el-input v-model=\"form.upLimit\" placeholder=\"请输入最大限制\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"备注信息\" prop=\"memo\">\r\n          <el-input v-model=\"form.memo\" placeholder=\"请输入备注信息\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"上级单位编码\" prop=\"fStoreCode\">\r\n          <el-input v-model=\"form.fStoreCode\" placeholder=\"请输入上级单位编码\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"上级单位名称\" prop=\"fStoreName\">\r\n          <el-input v-model=\"form.fStoreName\" placeholder=\"请输入上级单位名称\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"单位层级\" prop=\"levelName\">\r\n          <el-input v-model=\"form.levelName\" placeholder=\"请输入单位层级\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listDepartment, getDepartment, delDepartment, addDepartment, updateDepartment, exportDepartment } from \"@/api/leave/department\";\r\n\r\nexport default {\r\n  name: \"Department\",\r\n  components: {\r\n  },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 出门证部门（厂内单位）表格数据\r\n      departmentList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        validFlag: null,\r\n        storeCode: null,\r\n        storeName: null,\r\n        queryWord: null,\r\n        type: null,\r\n        unitName: null,\r\n        position: null,\r\n        lowerLimit: null,\r\n        upLimit: null,\r\n        memo: null,\r\n        fStoreCode: null,\r\n        fStoreName: null,\r\n        levelName: null,\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    /** 查询出门证部门（厂内单位）列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listDepartment(this.queryParams).then(response => {\r\n        this.departmentList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        id: null,\r\n        validFlag: null,\r\n        storeCode: null,\r\n        storeName: null,\r\n        queryWord: null,\r\n        type: null,\r\n        unitName: null,\r\n        position: null,\r\n        lowerLimit: null,\r\n        upLimit: null,\r\n        memo: null,\r\n        fStoreCode: null,\r\n        fStoreName: null,\r\n        levelName: null,\r\n        createTime: null,\r\n        createBy: null,\r\n        updateTime: null,\r\n        updateBy: null\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.id)\r\n      this.single = selection.length!==1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加出门证部门（厂内单位）\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const id = row.id || this.ids\r\n      getDepartment(id).then(response => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"修改出门证部门（厂内单位）\";\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.id != null) {\r\n            updateDepartment(this.form).then(response => {\r\n              this.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addDepartment(this.form).then(response => {\r\n              this.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const ids = row.id || this.ids;\r\n      this.$confirm('是否确认删除出门证部门（厂内单位）编号为\"' + ids + '\"的数据项?', \"警告\", {\r\n          confirmButtonText: \"确定\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\"\r\n        }).then(function() {\r\n          return delDepartment(ids);\r\n        }).then(() => {\r\n          this.getList();\r\n          this.msgSuccess(\"删除成功\");\r\n        })\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      const queryParams = this.queryParams;\r\n      this.$confirm('是否确认导出所有出门证部门（厂内单位）数据项?', \"警告\", {\r\n          confirmButtonText: \"确定\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\"\r\n        }).then(function() {\r\n          return exportDepartment(queryParams);\r\n        }).then(response => {\r\n          this.download(response.msg);\r\n        })\r\n    }\r\n  }\r\n};\r\n</script>\r\n"], "mappings": ";;;;;;;;;;AAsQA,IAAAA,WAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,UAAA,GACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,cAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,SAAA;QACAC,SAAA;QACAC,SAAA;QACAC,SAAA;QACAC,IAAA;QACAC,QAAA;QACAC,QAAA;QACAC,UAAA;QACAC,OAAA;QACAC,IAAA;QACAC,UAAA;QACAC,UAAA;QACAC,SAAA;MACA;MACA;MACAC,IAAA;MACA;MACAC,KAAA,GACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACA,sBACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MACA,KAAA9B,OAAA;MACA,IAAA+B,0BAAA,OAAAtB,WAAA,EAAAuB,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAAxB,cAAA,GAAA2B,QAAA,CAAAC,IAAA;QACAJ,KAAA,CAAAzB,KAAA,GAAA4B,QAAA,CAAA5B,KAAA;QACAyB,KAAA,CAAA9B,OAAA;MACA;IACA;IACA;IACAmC,MAAA,WAAAA,OAAA;MACA,KAAA3B,IAAA;MACA,KAAA4B,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAAX,IAAA;QACAY,EAAA;QACAzB,SAAA;QACAC,SAAA;QACAC,SAAA;QACAC,SAAA;QACAC,IAAA;QACAC,QAAA;QACAC,QAAA;QACAC,UAAA;QACAC,OAAA;QACAC,IAAA;QACAC,UAAA;QACAC,UAAA;QACAC,SAAA;QACAc,UAAA;QACAC,QAAA;QACAC,UAAA;QACAC,QAAA;MACA;MACA,KAAAC,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAAlC,WAAA,CAAAC,OAAA;MACA,KAAAkB,OAAA;IACA;IACA,aACAgB,UAAA,WAAAA,WAAA;MACA,KAAAF,SAAA;MACA,KAAAC,WAAA;IACA;IACA;IACAE,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAA7C,GAAA,GAAA6C,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAX,EAAA;MAAA;MACA,KAAAnC,MAAA,GAAA4C,SAAA,CAAAG,MAAA;MACA,KAAA9C,QAAA,IAAA2C,SAAA,CAAAG,MAAA;IACA;IACA,aACAC,SAAA,WAAAA,UAAA;MACA,KAAAd,KAAA;MACA,KAAA5B,IAAA;MACA,KAAAD,KAAA;IACA;IACA,aACA4C,YAAA,WAAAA,aAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAAjB,KAAA;MACA,IAAAC,EAAA,GAAAe,GAAA,CAAAf,EAAA,SAAApC,GAAA;MACA,IAAAqD,yBAAA,EAAAjB,EAAA,EAAAL,IAAA,WAAAC,QAAA;QACAoB,MAAA,CAAA5B,IAAA,GAAAQ,QAAA,CAAAlC,IAAA;QACAsD,MAAA,CAAA7C,IAAA;QACA6C,MAAA,CAAA9C,KAAA;MACA;IACA;IACA,WACAgD,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAH,MAAA,CAAA/B,IAAA,CAAAY,EAAA;YACA,IAAAuB,4BAAA,EAAAJ,MAAA,CAAA/B,IAAA,EAAAO,IAAA,WAAAC,QAAA;cACAuB,MAAA,CAAAK,UAAA;cACAL,MAAA,CAAAhD,IAAA;cACAgD,MAAA,CAAA5B,OAAA;YACA;UACA;YACA,IAAAkC,yBAAA,EAAAN,MAAA,CAAA/B,IAAA,EAAAO,IAAA,WAAAC,QAAA;cACAuB,MAAA,CAAAK,UAAA;cACAL,MAAA,CAAAhD,IAAA;cACAgD,MAAA,CAAA5B,OAAA;YACA;UACA;QACA;MACA;IACA;IACA,aACAmC,YAAA,WAAAA,aAAAX,GAAA;MAAA,IAAAY,MAAA;MACA,IAAA/D,GAAA,GAAAmD,GAAA,CAAAf,EAAA,SAAApC,GAAA;MACA,KAAAgE,QAAA,2BAAAhE,GAAA;QACAiE,iBAAA;QACAC,gBAAA;QACAnD,IAAA;MACA,GAAAgB,IAAA;QACA,WAAAoC,yBAAA,EAAAnE,GAAA;MACA,GAAA+B,IAAA;QACAgC,MAAA,CAAApC,OAAA;QACAoC,MAAA,CAAAH,UAAA;MACA;IACA;IACA,aACAQ,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,IAAA7D,WAAA,QAAAA,WAAA;MACA,KAAAwD,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAnD,IAAA;MACA,GAAAgB,IAAA;QACA,WAAAuC,4BAAA,EAAA9D,WAAA;MACA,GAAAuB,IAAA,WAAAC,QAAA;QACAqC,MAAA,CAAAE,QAAA,CAAAvC,QAAA,CAAAwC,GAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}