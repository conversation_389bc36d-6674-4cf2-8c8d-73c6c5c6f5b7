{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\dataReport\\form\\dimensionalityOverview.vue?vue&type=style&index=0&id=14876a6c&scoped=true&lang=css", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\dataReport\\form\\dimensionalityOverview.vue", "mtime": 1756099891064}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\@vue\\cli-service\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KLmFwcC1jb250YWluZXIgew0KICBwYWRkaW5nOiAyMHB4Ow0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjVmN2ZhOw0KfQ0KDQoucGFnZS10aXRsZSB7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgbWFyZ2luLWJvdHRvbTogMjBweDsNCn0NCg0KLnBhZ2UtdGl0bGUgaDIgew0KICBmb250LXNpemU6IDI0cHg7DQogIGNvbG9yOiAjMzAzMTMzOw0KICBtYXJnaW46IDA7DQp9DQoNCi5ib3gtY2FyZCB7DQogIG1hcmdpbi1ib3R0b206IDIwcHg7DQogIGJvcmRlci1yYWRpdXM6IDhweDsNCiAgdHJhbnNpdGlvbjogYWxsIDAuM3M7DQp9DQoNCi5ib3gtY2FyZDpob3ZlciB7DQogIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtNXB4KTsNCiAgYm94LXNoYWRvdzogMCAxMHB4IDIwcHggcmdiYSgwLCAwLCAwLCAwLjEpOw0KfQ0KDQouc2VjdGlvbi1oZWFkZXIgew0KICBmb250LXdlaWdodDogYm9sZDsNCiAgZm9udC1zaXplOiAxOHB4Ow0KICBjb2xvcjogIzMzMzsNCiAgcGFkZGluZzogNXB4IDA7DQogIGJvcmRlci1ib3R0b206IDJweCBzb2xpZCByZ2JhKDAsIDAsIDAsIDAuMSk7DQp9DQoNCi50ZWNoLWVjb25vbWljLXNlY3Rpb24gLnNlY3Rpb24taGVhZGVyIHsNCiAgY29sb3I6ICM0YTZlZTA7DQogIGJvcmRlci1ib3R0b20tY29sb3I6ICM0YTZlZTA7DQp9DQoNCi5lbmVyZ3ktc2VjdGlvbiAuc2VjdGlvbi1oZWFkZXIgew0KICBjb2xvcjogIzQ3YjQ3NTsNCiAgYm9yZGVyLWJvdHRvbS1jb2xvcjogIzQ3YjQ3NTsNCn0NCg0KLmNoYXJ0LWNvbnRhaW5lciB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogIG1hcmdpbjogMCAtMTBweDsNCn0NCg0KLnRvcC1jaGFydHMtcm93LA0KLmVuZXJneS1jaGFydHMtcm93IHsNCiAgZGlzcGxheTogZmxleDsNCiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICBmbGV4LXdyYXA6IG5vd3JhcDsNCiAgd2lkdGg6IDEwMCU7DQogIGhlaWdodDogNjAwcHghaW1wb3J0YW50Ow0KfQ0KDQouY2hhcnQtaXRlbSB7DQogIGZsZXg6IDE7DQogIG1pbi13aWR0aDogMzIlOw0KICBtYXJnaW46IDEwcHg7DQogIGJhY2tncm91bmQtY29sb3I6ICNmZmY7DQogIGJvcmRlci1yYWRpdXM6IDZweDsNCiAgcGFkZGluZzogMTVweDsNCiAgYm94LXNoYWRvdzogMCAycHggMTJweCAwIHJnYmEoMCwgMCwgMCwgMC4wNSk7DQp9DQoNCi5jaGFydC10aXRsZSB7DQogIGZvbnQtc2l6ZTogMTZweDsNCiAgZm9udC13ZWlnaHQ6IGJvbGQ7DQogIG1hcmdpbi1ib3R0b206IDE1cHg7DQogIGNvbG9yOiAjMzAzMTMzOw0KICBkaXNwbGF5OiBmbGV4Ow0KICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQp9DQoNCi50cmVuZC1jb250cm9scyB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQp9DQoNCi5jaGFydCB7DQogIGhlaWdodDogMzQwcHg7DQogIHdpZHRoOiAxMDAlOw0KfQ0KDQouc2Nyb2xsLXRhYmxlLWNvbnRhaW5lciB7DQogIGhlaWdodDogMzQwcHg7DQogIG92ZXJmbG93OiBoaWRkZW47DQogIHBvc2l0aW9uOiByZWxhdGl2ZTsNCn0NCg0KLnNjcm9sbC10YWJsZSB7DQogIGhlaWdodDogMTAwJTsNCiAgb3ZlcmZsb3cteTogaGlkZGVuOyAvKiDkv67mlLnkuLpoaWRkZW7vvIzkvb/nlKhKU+aOp+WItua7muWKqCAqLw0KICBzY3JvbGxiYXItd2lkdGg6IHRoaW47DQp9DQoNCi5jb21wbGV0aW9uLXJhdGUgew0KICBmb250LXdlaWdodDogYm9sZDsNCn0NCg0KLmNvbXBsZXRpb24tcmF0ZS5yZWQgew0KICBjb2xvcjogI0Y1NkM2QzsNCn0NCg0KLmNvbXBsZXRpb24tcmF0ZS5kZWVwLXJlZCB7DQogIGNvbG9yOiAjZmYwMDAwOw0KICBmb250LXdlaWdodDogYm9sZGVyOw0KfQ0KDQouY29tcGxldGlvbi1yYXRlLmdyZWVuIHsNCiAgY29sb3I6ICM2N0MyM0E7DQp9DQoNCi5lbmVyZ3ktc3RhdHMgew0KICBwYWRkaW5nOiAxMHB4Ow0KfQ0KDQouc3RhdC1jYXJkIHsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZjsNCiAgcGFkZGluZzogMTVweDsNCiAgYm9yZGVyLXJhZGl1czogNnB4Ow0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogIG1hcmdpbi1ib3R0b206IDE1cHg7DQogIGJvcmRlci1sZWZ0OiA0cHggc29saWQgIzQwOUVGRjsNCiAgdHJhbnNpdGlvbjogYWxsIDAuM3M7DQogIGhlaWdodDogMTAwcHg7DQogIGRpc3BsYXk6IGZsZXg7DQogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCn0NCg0KLnN0YXQtY2FyZDpob3ZlciB7DQogIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtM3B4KTsNCiAgYm94LXNoYWRvdzogMCA1cHggMTVweCByZ2JhKDAsIDAsIDAsIDAuMSk7DQp9DQoNCi5zdGF0LWNhcmQuZ29vZCB7DQogIGJvcmRlci1sZWZ0LWNvbG9yOiAjNjdDMjNBOw0KfQ0KDQouc3RhdC1jYXJkLndhcm5pbmcgew0KICBib3JkZXItbGVmdC1jb2xvcjogI0U2QTIzQzsNCn0NCg0KLnN0YXQtY2FyZC5kYW5nZXIgew0KICBib3JkZXItbGVmdC1jb2xvcjogI0Y1NkM2QzsNCn0NCg0KLnN0YXQtdGl0bGUgew0KICBmb250LXNpemU6IDE0cHg7DQogIGNvbG9yOiAjNjA2MjY2Ow0KfQ0KDQouc3RhdC12YWx1ZSB7DQogIGZvbnQtc2l6ZTogMjBweDsNCiAgZm9udC13ZWlnaHQ6IGJvbGQ7DQogIGNvbG9yOiAjMzAzMTMzOw0KICBtYXJnaW46IDVweCAwOw0KfQ0KDQouc3RhdC1jaGFuZ2Ugew0KICBmb250LXNpemU6IDEycHg7DQogIGNvbG9yOiAjOTA5Mzk5Ow0KfQ0KDQoucGFnaW5hdGlvbi13cmFwcGVyIHsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCn0NCg0KLyog5Yqo55S75pWI5p6cICovDQouYm94LWNhcmQgew0KICBhbmltYXRpb246IGZhZGVJbiAwLjZzIGVhc2UtaW4tb3V0Ow0KfQ0KDQpAa2V5ZnJhbWVzIGZhZGVJbiB7DQogIGZyb20gew0KICAgIG9wYWNpdHk6IDA7DQogICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKDIwcHgpOw0KICB9DQogIHRvIHsNCiAgICBvcGFjaXR5OiAxOw0KICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgwKTsNCiAgfQ0KfQ0KDQoudGVjaC1lY29ub21pYy1zZWN0aW9uIHsNCiAgYW5pbWF0aW9uLWRlbGF5OiAwLjFzOw0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZTZmMGZmOyAvKiDkv67mlLnmioDnu4/mjIfmoIfpg6jliIbnmoTog4zmma/oibLvvIzmm7Tmt7HnmoTok53oibLog4zmma8gKi8NCn0NCg0KLmVuZXJneS1zZWN0aW9uIHsNCiAgYW5pbWF0aW9uLWRlbGF5OiAwLjNzOw0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZTZmZmYwOyAvKiDkv67mlLnog73mupDmjIfmoIfpg6jliIbnmoTog4zmma/oibLvvIzmm7Tmt7HnmoTnu7/oibLog4zmma8gKi8NCn0NCg0KLyog5L+u5pS55Y2h54mH6IOM5pmv6ImyICovDQouY2hhcnQtaXRlbSB7DQogIGJhY2tncm91bmQtY29sb3I6ICNmZmZmZmY7IC8qIOeZveiJsuiDjOaZryAqLw0KICBib3gtc2hhZG93OiAwIDRweCAxMnB4IHJnYmEoMCwgMCwgMCwgMC4xKTsgLyog5aKe5by66Zi05b2xICovDQogIGJvcmRlcjogMXB4IHNvbGlkIHJnYmEoMCwgMCwgMCwgMC4wNSk7IC8qIOa3u+WKo<PERSON><PERSON>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"}, {"version": 3, "sources": ["dimensionalityOverview.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwxFA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "dimensionalityOverview.vue", "sourceRoot": "src/views/dataReport/form", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <!-- 页面标题 -->\r\n    <div class=\"page-title\">\r\n      <h2>技经能源指标报表</h2>\r\n    </div>\r\n\r\n\r\n        <!-- 能源指标部分 -->\r\n    <el-card class=\"box-card energy-section\" shadow=\"hover\">\r\n      <div slot=\"header\" class=\"clearfix section-header\">\r\n        <span>能源指标概览</span>\r\n      </div>\r\n      <div class=\"chart-container\">\r\n        <!-- 能源报表一行显示 -->\r\n        <div class=\"energy-charts-row\">\r\n          <!-- 部门能源消耗详情（原图四，现在放在第一位置） -->\r\n          <div class=\"chart-item energy-chart-half\">\r\n            <div class=\"chart-title\">\r\n              <span>部门能源消耗详情</span>\r\n              <div class=\"energy-dept-selector\">\r\n                <el-select v-model=\"currentEnergyDept\" size=\"small\" placeholder=\"选择部门\" @change=\"initEnergyDetailCharts\">\r\n                  <el-option\r\n                    v-for=\"factory in allFactories\"\r\n                    :key=\"factory\"\r\n                    :label=\"factory\"\r\n                    :value=\"factory\">\r\n                  </el-option>\r\n                </el-select>\r\n              </div>\r\n            </div>\r\n            <div class=\"energy-subchart-container\">\r\n              <div v-for=\"type in energyTypes\" :key=\"type.value\" \r\n                  class=\"energy-subchart\">\r\n                <div class=\"subchart-title\">{{ type.label }}</div>\r\n                <div :id=\"'energySubchart_' + type.value\" class=\"subchart\"></div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 部门能源消耗趋势（原图一，现在放在第三位置） -->\r\n          <div class=\"chart-item energy-chart-half\">\r\n            <div class=\"chart-title\">\r\n              <span>能源数据监控</span>\r\n            </div>\r\n            <div class=\"key-indicators-container\">\r\n              <div \r\n                v-for=\"(indicator, index) in keyEnergyIndicators\" \r\n                :key=\"index\" \r\n                class=\"indicator-card\"\r\n                :class=\"[indicator.status, {'danger': indicator.change < -50 || indicator.change > 50}]\"\r\n              >\r\n                <div class=\"indicator-title\">{{ indicator.name }}</div>\r\n                <div class=\"indicator-value\">{{ indicator.today }} <span class=\"indicator-unit\">{{ indicator.unit }}</span></div>\r\n                <div class=\"compare-compare\"> <span >今日吨钢消耗:{{ indicator.todayrate+\" \"+indicator.unit }}</span></div>\r\n                <!-- <div class=\"indicator-target\">\r\n                  目标范围: {{ indicator.targetMin }} ~ {{ indicator.targetMax }} {{ indicator.unit }}\r\n                </div> -->\r\n                <div class=\"indicator-compare\">\r\n                  <span>昨日: {{ indicator.yesterday }}</span>\r\n                  \r\n                  <!-- <span \r\n                    class=\"indicator-change\" \r\n                    :class=\"{ 'up': indicator.change > 0, 'down': indicator.change < 0 }\"\r\n                  >\r\n                    {{ indicator.change > 0 ? '+' : '' }}{{ indicator.change }}%\r\n                  </span> -->\r\n                </div>\r\n                <div class=\"indicator-compare\">\r\n                  <span>昨日吨钢消耗: {{ indicator.yesterdayrate+\" \"+indicator.unit }}</span>\r\n                </div>\r\n                \r\n                <div class=\"indicator-target\">\r\n                  <span \r\n                    class=\"indicator-change\" \r\n                    :class=\"{ 'up': indicator.change > 0, 'down': indicator.change < 0 }\"\r\n                  >\r\n                   环比变化:{{ indicator.change > 0 ? '+' : '' }}{{ indicator.change }}%\r\n                  </span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </el-card>\r\n\r\n    <el-card class=\"box-card tech-economic-section\" shadow=\"hover\">\r\n      <div slot=\"header\" class=\"clearfix\">\r\n        <span>技经指标数据表格</span>\r\n        <el-button\r\n          size=\"mini\"\r\n          type=\"primary\"\r\n          style=\"float: right; margin-left: 10px\"\r\n          @click=\"loadExcelFromRemote\"\r\n          :loading=\"excelLoading\"\r\n        >\r\n          {{ excelLoading ? \"正在加载...\" : \"重新加载数据\" }}\r\n        </el-button>\r\n      </div>\r\n      <el-table\r\n        :data=\"techIndicators\"\r\n        border\r\n        style=\"width: 100%\"\r\n        :header-cell-style=\"headerCellStyle\"\r\n        :span-method=\"objectSpanMethod\"\r\n      >\r\n        <el-table-column\r\n          prop=\"factory\"\r\n          label=\"分厂\"\r\n          width=\"250\"\r\n          align=\"center\"\r\n        ></el-table-column>\r\n        <el-table-column\r\n          prop=\"name\"\r\n          label=\"指标名称\"\r\n          width=\"280\"\r\n          align=\"center\"\r\n        >\r\n          <template slot-scope=\"scope\">\r\n            <span\r\n              :style=\"{\r\n                color: '#409EFF',\r\n                fontWeight: 'bold',\r\n                backgroundColor: scope.row.highlight\r\n                  ? '#a9d3ff'\r\n                  : 'transparent',\r\n              }\"\r\n              >{{ scope.row.name }}</span\r\n            >\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column\r\n          prop=\"target\"\r\n          label=\"目标\"\r\n          width=\"100\"\r\n          align=\"center\"\r\n        ></el-table-column>\r\n        <el-table-column\r\n          prop=\"unit\"\r\n          label=\"单位\"\r\n          width=\"150\"\r\n          align=\"center\"\r\n        ></el-table-column>\r\n        <!-- <el-table-column prop=\"jan\" label=\"01月实绩\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <span\r\n              :style=\"{\r\n                color: scope.row.janStatus === 1 ? '#67C23A' : '#F56C6C',\r\n                fontWeight: 'bold',\r\n              }\"\r\n            >\r\n              {{ scope.row.jan }}\r\n            </span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"feb\" label=\"02月实绩\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <span\r\n              :style=\"{\r\n                color: scope.row.febStatus === 1 ? '#67C23A' : '#F56C6C',\r\n                fontWeight: 'bold',\r\n              }\"\r\n            >\r\n              {{ scope.row.feb }}\r\n            </span>\r\n          </template>\r\n        </el-table-column> -->\r\n        <el-table-column prop=\"mar\" label=\"03月实绩\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <span\r\n              :style=\"{\r\n                color: scope.row.marStatus === 1 ? '#67C23A' : '#F56C6C',\r\n                fontWeight: 'bold',\r\n              }\"\r\n            >\r\n              {{ scope.row.mar }}\r\n            </span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"apr\" label=\"04月实绩\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <span\r\n              :style=\"{\r\n                color: scope.row.aprStatus === 1 ? '#67C23A' : '#F56C6C',\r\n                fontWeight: 'bold',\r\n              }\"\r\n            >\r\n              {{ scope.row.apr }}\r\n            </span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"may\" label=\"05月实绩\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <span\r\n              :style=\"{\r\n                color: scope.row.mayStatus === 1 ? '#67C23A' : '#F56C6C',\r\n                fontWeight: 'bold',\r\n              }\"\r\n            >\r\n              {{ scope.row.may }}\r\n            </span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"操作\" width=\"120\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button\r\n              size=\"mini\"\r\n              type=\"primary\"\r\n              icon=\"el-icon-view\"\r\n              circle\r\n              @click=\"showDetails(scope.row)\"\r\n            ></el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n    </el-card>\r\n\r\n        <!-- 指标详情对话框 -->\r\n    <el-dialog\r\n      title=\"指标详情\"\r\n      :visible.sync=\"detailDialogVisible\"\r\n      width=\"70%\"\r\n      :before-close=\"handleDialogClose\"\r\n    >\r\n      <div v-if=\"currentIndicator\">\r\n        <h3>{{ currentIndicator.name }} ({{ currentIndicator.unit }})</h3>\r\n        <div class=\"indicator-info\">\r\n          <p>分厂: {{ currentIndicator.factory }}</p>\r\n          <p>目标值: {{ currentIndicator.target }}</p>\r\n          <p>当前值: {{ currentIndicator.may }}</p>\r\n        </div>\r\n        <div id=\"indicatorChart\" style=\"width: 100%; height: 400px\"></div>\r\n      </div>\r\n    </el-dialog>\r\n\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from 'echarts'\r\nimport axios from \"axios\";\r\nimport * as XLSX from \"xlsx\";\r\nimport {\r\n  dimensionalitylistPermissionList\r\n} from \"@/api/tYjy/dimensionality\";\r\nimport { dateUpdateList } from \"@/api/tYjy/answer\";\r\nexport default {\r\n  name: 'DimensionalityOverview',\r\n  data() {\r\n    return {\r\n\r\n      // 技经指标数据\r\n      techIndicators: [],\r\n\r\n      detailDialogVisible: false,\r\n      currentIndicator: null,\r\n      // Excel文件加载状态\r\n      excelLoading: false,\r\n      adminShow:\"0\",\r\n\r\n      // 当前显示的部门（用于月度趋势图切换）\r\n      // currentBusinessUnit: '炼铁事业部', // 当前选择的事业部\r\n      // currentDepartment: '',\r\n      // currentIndicator: '',\r\n      // autoSwitchDepartment: true,\r\n      // 定时器\r\n      trendChartTimer: null, // 用于分厂切换\r\n      businessUnitTimer: null, // 用于事业部切换\r\n      scrollTimer: null,\r\n      tableScrollPaused: false, // 是否暂停表格自动滚动\r\n      completionChartTimer: null, // 用于完成率图表滚动\r\n      indicatorCardsScrollTimer: null, // 用于指标卡片滚动\r\n      completionChartScrollDirection: 'down', // 滚动方向：'up' 或 'down'\r\n      completionChartScrollPaused: false, // 是否暂停自动滚动\r\n      indicatorCardsScrollPaused: false, // 是否暂停指标卡片自动滚动\r\n  \r\n      energyDeptTimer: null, // 能源部门切换定时器\r\n      scrollSpeed: 50, // 滚动速度，数值越大速度越慢\r\n      \r\n      // 事件处理器引用\r\n      chartMouseOverHandler: null,\r\n      chartMouseOutHandler: null,\r\n      tableMouseEnterHandler: null, // 表格鼠标进入处理器\r\n      tableMouseLeaveHandler: null, // 表格鼠标离开处理器\r\n      \r\n      // 当前选择的能源类型和部门\r\n      currentEnergyType: 'electricity',\r\n      currentEnergyDept: '炼铁分厂', // 修改为分厂名称\r\n      \r\n      // 部门列表\r\n      departments: ['炼铁事业部', '炼钢事业部', '轧钢事业部', '马科托钢球', '特板事业部', '动力事业部', '物流事业部', '检修事业部'],\r\n      \r\n      // 指标完成情况数据 - 从图片中提取的数据\r\n      completionData: [\r\n        { department: '炼铁事业部-炼铁分厂', indicators: [\r\n          { name: '综合燃料比', target: 512.33, actual: 523.45, unit: 'kg/t', isHigherBetter: false, values: [520.26, 523.67, 523.45, 519.06], completionRates: ['-1.55%', '-2.00%', '-2.17%', '-1.31%'] },\r\n          { name: '工序能耗', target: 369.91, actual: 369.69, unit: 'kgCe/t', isHigherBetter: false, values: [369.74, 367.98, 369.69, 363.76], completionRates: ['0.05%', '0.52%', '0.06%', '1.66%'] },\r\n          { name: '400高炉工序能耗', target: 43.70, actual: 43.56, unit: 'kgCe/t', isHigherBetter: false, values: [43.67, 43.67, 43.56, 43.67], completionRates: ['0.07%', '0.07%', '0.32%', '0.07%'] },\r\n          { name: '360高炉工序能耗', target: 45.01, actual: 44.84, unit: 'kgCe/t', isHigherBetter: false, values: [45.00, 44.93, 44.84, 44.99], completionRates: ['0.02%', '0.07%', '0.38%', '0.04%'] }\r\n        ]},\r\n        { department: '炼铁事业部-烧结分厂', indicators: [\r\n          { name: '工序能耗', target: 154.65, actual: 154.56, unit: 'kgCe/t', isHigherBetter: false, values: [154.65, 154.91, 154.56, 154.57], completionRates: ['0.01%', '0.03%', '0.06%', '0.05%'] },\r\n          { name: '矿渣增幅', target: 16.00, actual: 15.80, unit: 'kgCe/t', isHigherBetter: false, values: [15.82, 15.94, 15.80, 15.85], completionRates: ['0.50%', '0.38%', '1.25%', '0.94%'] },\r\n        ]},\r\n        { department: '炼钢事业部-一炼钢', indicators: [\r\n          { name: '综合石灰消耗', target: 42.95, actual: 42.89, unit: 'kg/t', isHigherBetter: false, values: [42.94, 42.82, 42.89, 40.55], completionRates: ['0.02%', '0.30%', '0.14%', '5.59%'] },\r\n          { name: '氧气消耗', target: 44.56, actual: 44.45, unit: 'm3/t', isHigherBetter: false, values: [44.50, 44.34, 44.45, 44.37], completionRates: ['0.13%', '0.49%', '0.25%', '0.43%'] },\r\n          { name: '电炉工序能耗', target: 57.50, actual: 57.32, unit: 'kgCe/t', isHigherBetter: false, values: [57.34, 57.33, 57.32, 57.19], completionRates: ['0.28%', '0.30%', '0.31%', '0.54%'] },\r\n          { name: '钢铁料收得率', target: 91.50, actual: 91.32, unit: '%', isHigherBetter: true, values: [91.51, 91.32, 91.32, 91.32], completionRates: ['0.01%', '-0.20%', '-0.20%', '-0.20%'] }\r\n        ]},\r\n        { department: '炼钢事业部-二炼钢', indicators: [\r\n          { name: '综合石灰消耗', target: 50.00, actual: 48.95, unit: 'kg/t', isHigherBetter: false, values: [49.90, 49.40, 48.95, 49.00], completionRates: ['0.20%', '1.08%', '2.10%', '2.00%'] },\r\n          { name: '氧气消耗', target: 45.65, actual: 45.26, unit: 'm3/t', isHigherBetter: false, values: [45.49, 45.37, 45.26, 45.35], completionRates: ['0.35%', '0.39%', '0.85%', '0.66%'] },\r\n          { name: '转炉工序能耗', target: -28.50, actual: -29.52, unit: 'kgCe/t', isHigherBetter: true, values: [29.78, 29.57, 29.52, 29.61], completionRates: ['-0.98%', '-3.75%', '-3.58%', '-3.89%'] },\r\n          { name: '钢铁料收得率', target: 91.50, actual: 91.16, unit: '%', isHigherBetter: true, values: [91.50, 91.32, 91.16, 91.15], completionRates: ['0.00%', '-0.20%', '-0.37%', '-0.38%'] }\r\n        ]},\r\n        { department: '轧钢事业部-综合利用', indicators: [\r\n          { name: '废金金属量', target: 0.65, actual: 0.64, unit: '%', isHigherBetter: false, values: [0.65, 0.53, 0.64, 0.49], completionRates: ['0.00%', '18.40%', '1.54%', '-26.15%'] },\r\n          { name: '电耗', target: 22.33, actual: 23.81, unit: 'kWh/t', isHigherBetter: false, values: [22.33, 23.07, 23.81, 21.19], completionRates: ['0.45%', '-3.31%', '-6.62%', '5.11%'] }\r\n        ]},\r\n        { department: '轧钢事业部-一轧钢', indicators: [\r\n          { name: '热轧综合成材率', target: 96.35, actual: 96.22, unit: '%', isHigherBetter: true, values: [96.23, 96.25, 96.22, 96.24], completionRates: ['-0.12%', '-0.10%', '-0.13%', '-0.11%'] },\r\n          { name: '热轧钢材工序能耗', target: 58.55, actual: 58.51, unit: 'kgCe/t', isHigherBetter: false, values: [58.47, 58.52, 58.51, 58.45], completionRates: ['0.14%', '0.05%', '0.07%', '0.17%'] }\r\n        ]},\r\n        { department: '轧钢事业部-二轧钢', indicators: [\r\n          { name: '热轧综合成材率(大棒)', target: 95.37, actual: 95.37, unit: '%', isHigherBetter: true, values: [95.37, 95.22, 95.37, 95.37], completionRates: ['0.00%', '-0.15%', '0.00%', '0.00%'] },\r\n          { name: '热轧综合成材率(小棒)', target: 96.56, actual: 96.56, unit: '%', isHigherBetter: true, values: [96.39, 96.56, 96.56, 96.56], completionRates: ['-0.17%', '0.00%', '0.00%', '0.00%'] },\r\n          { name: '热轧钢材工序能耗(大棒)', target: 67.28, actual: 72.49, unit: 'kgCe/t', isHigherBetter: false, values: [71.35, 73.80, 72.49, 66.24], completionRates: ['-0.93%', '0.75%', '0.00%', '-0.25%'] },\r\n          { name: '热轧钢材工序能耗(小棒)', target: 42.05, actual: 42.02, unit: 'kgCe/t', isHigherBetter: false, values: [42.03, 42.05, 42.02, 45.68], completionRates: ['0.05%', '0.05%', '0.05%', '0.34%'] }\r\n        ]},\r\n        { department: '轧钢事业部-三轧钢', indicators: [\r\n          { name: '热轧综合成材率', target: 96.04, actual: 95.50, unit: '%', isHigherBetter: true, values: [95.76, 96.30, 95.50, 95.51], completionRates: ['-0.28%', '0.44%', '-0.50%', '-0.50%'] },\r\n          { name: '热轧钢材工序能耗', target: 56.31, actual: 54.67, unit: 'kgCe/t', isHigherBetter: false, values: [55.26, 56.34, 54.67, 55.19], completionRates: ['-0.79%', '0.71%', '-1.33%', '-1.18%'] }\r\n        ]},\r\n        { department: '轧钢事业部-特殊钢轧材', indicators: [\r\n          { name: '热轧钢材工序能耗', target: 67.04, actual: 68.64, unit: 'kgCe/t', isHigherBetter: false, values: [67.35, 64.09, 68.64, 64.77], completionRates: ['0.46%', '-3.26%', '0.00%', '-0.23%'] },\r\n          { name: '综合成材率', target: 96.73, actual: 96.73, unit: '%', isHigherBetter: true, values: [96.73, 96.79, 96.73, 96.45], completionRates: ['0.00%', '0.06%', '0.00%', '-0.28%'] }\r\n        ]},\r\n        { department: '轧钢事业部-棒材轧制厂', indicators: [\r\n          { name: '热轧钢材工序能耗(棒扁)', target: 56.93, actual: 61.81, unit: 'kgCe/t', isHigherBetter: false, values: [66.14, 60.00, 61.81, 59.96], completionRates: ['-0.91%', '-1.35%', '0.00%', '-0.24%'] },\r\n          { name: '热轧钢材工序能耗(大盘)', target: 57.08, actual: 61.28, unit: 'kgCe/t', isHigherBetter: false, values: [64.30, 60.29, 61.28, 60.02], completionRates: ['-0.19%', '-0.15%', '0.00%', '-0.26%'] },\r\n          { name: '综合成材率(棒扁轧材)', target: 96.45, actual: 96.12, unit: '%', isHigherBetter: true, values: [96.14, 96.11, 96.12, 96.03], completionRates: ['-0.31%', '-0.33%', '0.00%', '-0.29%'] },\r\n          { name: '综合成材率(大盘卷)', target: 95.85, actual: 95.84, unit: '%', isHigherBetter: true, values: [95.86, 95.90, 95.84, 95.87], completionRates: ['0.01%', '0.04%', '0.00%', '0.02%'] }\r\n        ]},\r\n        { department: '轧钢事业部-棒线材深加工(棒)', indicators: [\r\n          { name: '综合成材率', target: 92.60, actual: 92.60, unit: '%', isHigherBetter: true, values: [92.60, 92.60, 92.60, 92.60], completionRates: ['0.00%', '0.00%', '0.00%', '0.00%'] }\r\n        ]},\r\n        { department: '轧钢事业部-棒线材深加工(线)', indicators: [\r\n          { name: '控线材综合成材率', target: 98.55, actual: 98.56, unit: '%', isHigherBetter: true, values: [98.55, 98.55, 98.56, 98.55], completionRates: ['0.00%', '0.00%', '0.00%', '0.00%'] }\r\n        ]},\r\n        { department: '轧钢事业部-线材深加工', indicators: [\r\n          { name: '冷镦材综合成材率', target: 96.36, actual: 96.02, unit: '%', isHigherBetter: true, values: [96.36, 96.36, 96.02, 94.44], completionRates: ['0.00%', '0.00%', '0.00%', '-1.94%'] }\r\n        ]},\r\n        { department: '马科托钢球-马科托钢球', indicators: [\r\n          { name: '综合成材率', target: 93.19, actual: 93.61, unit: '%', isHigherBetter: true, values: [93.13, 93.54, 93.61, 93.80], completionRates: ['-0.06%', '0.42%', '0.00%', '0.20%'] },\r\n          { name: '氧气消耗', target: 44.64, actual: 44.63, unit: 'm3/t', isHigherBetter: false, values: [44.64, 44.64, 44.63, 44.64], completionRates: ['0.00%', '0.00%', '0.00%', '0.00%'] }\r\n        ]},\r\n        { department: '特板事业部-特钢炼钢分厂', indicators: [\r\n          { name: '氧气消耗', target: 44.64, actual: 44.63, unit: 'm3/t', isHigherBetter: false, values: [44.64, 44.64, 44.63, 44.64], completionRates: ['0.00%', '0.00%', '0.00%', '0.00%'] },\r\n          { name: '转炉工序能耗', target: -28.50, actual: -29.91, unit: 'kgCe/t', isHigherBetter: true, values: [28.93, 29.67, 29.91, 29.55], completionRates: ['-0.98%', '-3.75%', '-3.58%', '-3.89%'] },\r\n          { name: '钢铁料收得率', target: 91.60, actual: 91.27, unit: '%', isHigherBetter: true, values: [91.80, 91.31, 91.27, 91.27], completionRates: ['0.22%', '-0.33%', '-0.37%', '-0.37%'] },\r\n          { name: '综合石灰消耗', target: 43.67, actual: 46.07, unit: 'kg/t', isHigherBetter: false, values: [46.01, 47.19, 46.07, 43.97], completionRates: ['2.84%', '3.27%', '2.14%', '2.07%'] }\r\n        ]},\r\n        { department: '特板事业部-中板分厂', indicators: [\r\n          { name: '综合命中率', target: 98.62, actual: 98.63, unit: '%', isHigherBetter: true, values: [98.63, 98.65, 98.63, 98.63], completionRates: ['0.00%', '0.02%', '0.00%', '0.00%'] },\r\n          { name: '综合成材率', target: 92.55, actual: 92.04, unit: '%', isHigherBetter: true, values: [92.63, 92.03, 92.04, 92.65], completionRates: ['0.09%', '-0.51%', '-0.51%', '0.02%'] },\r\n          { name: '整客户交付率', target: 98.75, actual: 98.78, unit: '%', isHigherBetter: true, values: [98.75, 98.77, 98.78, 98.75], completionRates: ['0.00%', '0.02%', '0.00%', '0.00%'] },\r\n          { name: '热轧工序能耗', target: 45.02, actual: 43.32, unit: 'kgCe/t', isHigherBetter: false, values: [44.60, 44.15, 43.32, 43.80], completionRates: ['-0.93%', '-1.25%', '-1.68%', '-0.70%'] },\r\n          { name: '热装比', target: 75.00, actual: 75.85, unit: '%', isHigherBetter: true, values: [75.40, 77.64, 75.85, 74.05], completionRates: ['0.53%', '2.18%', '0.00%', '-1.95%'] }\r\n        ]},\r\n        { department: '特板事业部-厚板分厂', indicators: [\r\n          { name: '综合命中率', target: 97.49, actual: 97.27, unit: '%', isHigherBetter: true, values: [97.49, 97.53, 97.27, 97.52], completionRates: ['0.00%', '0.04%', '-0.26%', '0.05%'] },\r\n          { name: '综合成材率', target: 90.91, actual: 90.76, unit: '%', isHigherBetter: true, values: [90.41, 90.79, 90.76, 90.78], completionRates: ['-0.55%', '0.32%', '-0.26%', '0.02%'] },\r\n          { name: '整客户交付率', target: 96.34, actual: 96.34, unit: '%', isHigherBetter: true, values: [96.37, 96.35, 96.34, 96.31], completionRates: ['0.03%', '-0.02%', '0.00%', '-0.03%'] },\r\n          { name: '热轧工序能耗', target: 48.62, actual: 45.85, unit: 'kgCe/t', isHigherBetter: false, values: [46.27, 46.01, 45.85, 47.11], completionRates: ['-2.79%', '-2.56%', '-2.34%', '-1.54%'] },\r\n          { name: '热装比(200℃)', target: 50.00, actual: 31.23, unit: '%', isHigherBetter: true, values: [50.60, 51.28, 31.23, 50.28], completionRates: ['1.20%', '2.56%', '0.00%', '-1.56%'] }\r\n        ]},\r\n        { department: '特板事业部-钢材深加工', indicators: [\r\n          { name: '整客户交付率', target: 99.11, actual: 99.12, unit: '%', isHigherBetter: true, values: [99.11, 99.10, 99.12, 99.12], completionRates: ['0.00%', '-0.01%', '0.00%', '0.00%'] },\r\n          { name: '综合命中率', target: 99.73, actual: 99.75, unit: '%', isHigherBetter: true, values: [99.73, 99.74, 99.75, 99.74], completionRates: ['0.00%', '-0.01%', '0.00%', '0.00%'] }\r\n        ]},\r\n        { department: '动力事业部-热电', indicators: [\r\n          { name: '标煤产汽率', target: 10.85, actual: 10.90, unit: 't/tCe', isHigherBetter: true, values: [10.87, 10.89, 10.90, 10.92], completionRates: ['0.19%', '0.20%', '0.00%', '0.00%'] }\r\n        ]},\r\n        { department: '动力事业部-供电工区', indicators: [\r\n          { name: '供电功率因数', target: 95.00, actual: 98.00, unit: '%(95.3-100)', isHigherBetter: true, values: [98.66, 98.66, 98.00, 98.00], completionRates: ['3.20%', '3.20%', '0.00%', '0.00%'] }\r\n        ]},\r\n        { department: '动力事业部-水处理分厂', indicators: [\r\n          { name: '吨钢软水水', target: 1.62, actual: 1.63, unit: 'm3/t', isHigherBetter: false, values: [1.62, 1.61, 1.63, 1.69], completionRates: ['0.62%', '-0.62%', '0.00%', '0.62%'] },\r\n          { name: '吨钢热水处理量', target: 21.20, actual: 20.25, unit: 't/t钢', isHigherBetter: false, values: [19.89, 20.14, 20.25, 20.28], completionRates: ['-6.19%', '-3.57%', '0.00%', '1.40%'] }\r\n        ]},\r\n        { department: '动力事业部-制氧分厂', indicators: [\r\n          { name: '氧气就耗率', target: 0.02, actual: 0.00, unit: '%', isHigherBetter: false, values: [0.00, 0.00, 0.00, 0.00], completionRates: ['0.00%', '0.00%', '0.00%', '0.00%'] }\r\n        ]},\r\n        { department: '动力事业部-煤气分厂', indicators: [\r\n          { name: '高炉煤气就耗率', target: 0.02, actual: 0.00, unit: '%', isHigherBetter: false, values: [0.00, 0.00, 0.00, 0.00], completionRates: ['0.00%', '0.00%', '0.00%', '0.00%'] }\r\n        ]},\r\n        { department: '物流事业部-储运公司', indicators: [\r\n          { name: '360混匀矿水分合格率', target: 90.56, actual: 91.32, unit: '%', isHigherBetter: true, values: [90.63, 90.68, 91.32, 90.68], completionRates: ['0.07%', '0.05%', '0.00%', '0.00%'] },\r\n          { name: '混匀矿稳定率', target: 97.82, actual: 97.89, unit: '%', isHigherBetter: true, values: [97.83, 98.01, 97.89, 98.01], completionRates: ['0.18%', '0.17%', '0.00%', '0.00%'] }\r\n        ]},\r\n        { department: '检修事业部-检修分厂', indicators: [\r\n          { name: '热修机率', target: 0.10, actual: 0.00, unit: '%', isHigherBetter: false, values: [0.00, 0.00, 0.00, 0.00], completionRates: ['0.00%', '0.00%', '0.00%', '0.00%'] }\r\n        ]}\r\n      ],\r\n      completionData1: \r\n      [\r\n      {department:'钙业分厂'},\r\n      {department:'矿渣微粉'},\r\n      {department:'烧结分厂-1#烧结'},\r\n      {department:'烧结分厂-2#烧结'},\r\n      {department:'炼铁分厂-1#高炉'},\r\n      {department:'炼铁分厂-2#高炉'},\r\n      {department:'炼铁分厂-3#高炉'},\r\n      {department:'炼铁分厂-小喷煤'},\r\n      {department:'炼铁分厂-大喷煤'},\r\n      {department:'一炼钢'},\r\n      {department:'二炼钢'},\r\n      {department:'一轧钢'},\r\n      {department:'二轧-大棒'},\r\n      {department:'二轧-小棒'},\r\n      {department:'特板炼钢'},\r\n      {department:'3500中板'},\r\n      {department:'4300厚板'},\r\n      {department:'4300水处理'},\r\n      {department:'热处理'},\r\n      {department:'高线分厂'},\r\n      {department:'线材深加工'},\r\n      {department:'棒材深加工'},\r\n      {department:'热电分厂-热电'},\r\n      {department:'热电分厂-亚临界'},\r\n      {department:'热电分厂-余热'},\r\n      {department:'热电分厂-鼓风'},\r\n      {department:'制氧分厂'},\r\n      {department:'制氧分厂-一期'},\r\n      {department:'制氧分厂-三期'},\r\n      {department:'制氧分厂-空压站'},\r\n      {department:'水处理-一期'},\r\n      {department:'水处理-二期'},\r\n      {department:'水处理-三期'},\r\n      {department:'煤气分厂'},\r\n      {department:'供电一区'},\r\n      {department:'兴澄钢球'},\r\n      {department:'兴澄港务'},\r\n      {department:'储运公司'},\r\n      {department:'综合利用'},\r\n      {department:'合金炉分厂'},\r\n      {department:'物管部'},\r\n      {department:'后勤部'},\r\n      {department:'其他'},\r\n      {department:'损耗'},\r\n      {department:'合计'},\r\n\r\n      ],\r\n      // 未完成指标数据\r\n      incompleteData: [],\r\n      \r\n      // 能源指标统计数据\r\n      energyStats: [\r\n        { title: '综合能耗', value: '5.21吨标煤/吨钢', change: -2.3, status: 'good' },\r\n        { title: '水资源消耗', value: '3.8立方米/吨钢', change: -1.5, status: 'good' },\r\n        { title: '电力消耗', value: '485千瓦时/吨钢', change: 0.8, status: 'warning' },\r\n        { title: '煤气回收率', value: '98.5%', change: 1.2, status: 'good' },\r\n        { title: '余热回收率', value: '75.2%', change: 2.5, status: 'good' },\r\n        { title: '二氧化碳排放', value: '1.85吨/吨钢', change: -3.2, status: 'good' }\r\n      ],\r\n      \r\n      // 能源消耗数据\r\n      energyConsumptionData: [\r\n        { name: '电力', value: 35 },\r\n        { name: '煤炭', value: 25 },\r\n        { name: '天然气', value: 15 },\r\n        { name: '蒸汽', value: 10 },\r\n        { name: '其他', value: 15 }\r\n      ],\r\n      \r\n      // 部门能源消耗数据（新增）- 按月统计\r\n      departmentEnergyData: {\r\n        months: ['1月', '2月', '3月', '4月'],\r\n        departments: ['炼铁事业部', '炼钢事业部', '轧钢事业部', '马科托钢球', '特板事业部', '动力事业部', '物流事业部', '检修事业部'],\r\n        electricity: { // 电力消耗 (万千瓦时)\r\n          '炼铁事业部': [1250, 1180, 1220, 1260],\r\n          '炼钢事业部': [1850, 1790, 1810, 1880],\r\n          '轧钢事业部': [1450, 1420, 1480, 1440],\r\n          '马科托钢球': [420, 410, 430, 425],\r\n          '特板事业部': [980, 950, 970, 990],\r\n          '动力事业部': [320, 310, 330, 325],\r\n          '物流事业部': [180, 175, 185, 182],\r\n          '检修事业部': [150, 145, 155, 152]\r\n        },\r\n        water: { // 水资源消耗 (万吨)\r\n          '炼铁事业部': [85, 82, 86, 88],\r\n          '炼钢事业部': [120, 115, 118, 122],\r\n          '轧钢事业部': [95, 92, 96, 94],\r\n          '马科托钢球': [28, 27, 29, 28.5],\r\n          '特板事业部': [65, 63, 66, 67],\r\n          '动力事业部': [180, 175, 185, 182],\r\n          '物流事业部': [15, 14, 16, 15.5],\r\n          '检修事业部': [12, 11.5, 12.5, 12.2]\r\n        },\r\n        gas: { // 天然气消耗 (万立方米)\r\n          '炼铁事业部': [320, 310, 325, 330],\r\n          '炼钢事业部': [480, 470, 485, 490],\r\n          '轧钢事业部': [380, 370, 385, 375],\r\n          '马科托钢球': [110, 105, 112, 108],\r\n          '特板事业部': [250, 245, 255, 260],\r\n          '动力事业部': [85, 82, 87, 86],\r\n          '物流事业部': [45, 43, 46, 44],\r\n          '检修事业部': [35, 34, 36, 35.5]\r\n        },\r\n        steam: { // 蒸汽消耗 (万吨)\r\n          '炼铁事业部': [45, 43, 46, 47],\r\n          '炼钢事业部': [65, 63, 66, 67],\r\n          '轧钢事业部': [52, 50, 53, 51],\r\n          '马科托钢球': [15, 14.5, 15.5, 15.2],\r\n          '特板事业部': [35, 34, 36, 37],\r\n          '动力事业部': [12, 11.5, 12.5, 12.2],\r\n          '物流事业部': [8, 7.8, 8.2, 8.1],\r\n          '检修事业部': [6, 5.8, 6.2, 6.1]\r\n        }\r\n      },\r\n      \r\n      // 能源类型选项\r\n      energyTypes: [\r\n        { label: '电力消耗', value: 'electricity', unit: '千瓦时', color: '#2f80ed' },\r\n        { label: '水资源消耗', value: 'water', unit: '吨', color: '#2d9cdb' },\r\n        { label: '天然气消耗', value: 'gas', unit: '立方米', color: '#1a73e8' },\r\n        { label: '蒸汽消耗', value: 'steam', unit: '吨', color: '#27ae60' }\r\n      ],\r\n      \r\n      // 能源成本数据\r\n      energyCostData: {\r\n        months: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],\r\n        electricity: [320, 332, 301, 334, 390, 330, 320, 315, 310, 325, 315, 318],\r\n        coal: [220, 182, 191, 234, 290, 330, 310, 295, 300, 285, 270, 275],\r\n        gas: [150, 232, 201, 154, 190, 180, 165, 175, 190, 195, 205, 210],\r\n        steam: [98, 77, 101, 99, 120, 125, 110, 100, 105, 115, 110, 120]\r\n      },\r\n      \r\n      // 能源详情数据 - 当日消耗\r\n      energyDetailData: [\r\n        { \r\n          name: '转炉煤气', \r\n          category: 'gas',\r\n          value: 125.6, \r\n          unit: '万m³', \r\n          target: 130, \r\n          warning: 140, \r\n          danger: 150, \r\n          status: 'normal',\r\n          trend: -2.1 // 相比昨日变化百分比\r\n        },\r\n        { \r\n          name: '高炉煤气', \r\n          category: 'gas',\r\n          value: 287.3, \r\n          unit: '万m³', \r\n          target: 280, \r\n          warning: 300, \r\n          danger: 320, \r\n          status: 'warning',\r\n          trend: 5.3\r\n        },\r\n        { \r\n          name: '焦炉煤气', \r\n          category: 'gas',\r\n          value: 98.4, \r\n          unit: '万m³', \r\n          target: 100, \r\n          warning: 110, \r\n          danger: 120, \r\n          status: 'normal',\r\n          trend: -1.2\r\n        },\r\n        { \r\n          name: '天然气', \r\n          category: 'gas',\r\n          value: 45.7, \r\n          unit: '万m³', \r\n          target: 40, \r\n          warning: 45, \r\n          danger: 50, \r\n          status: 'danger',\r\n          trend: 12.5\r\n        },\r\n        { \r\n          name: '饱和蒸汽', \r\n          category: 'steam',\r\n          value: 56.2, \r\n          unit: '万吨', \r\n          target: 55, \r\n          warning: 60, \r\n          danger: 65, \r\n          status: 'normal',\r\n          trend: 1.8\r\n        },\r\n        { \r\n          name: '过热蒸汽', \r\n          category: 'steam',\r\n          value: 32.8, \r\n          unit: '万吨', \r\n          target: 30, \r\n          warning: 35, \r\n          danger: 40, \r\n          status: 'warning',\r\n          trend: 7.2\r\n        },\r\n        { \r\n          name: '工业用水', \r\n          category: 'water',\r\n          value: 142.5, \r\n          unit: '万吨', \r\n          target: 140, \r\n          warning: 150, \r\n          danger: 160, \r\n          status: 'normal',\r\n          trend: 1.5\r\n        },\r\n        { \r\n          name: '循环冷却水', \r\n          category: 'water',\r\n          value: 285.3, \r\n          unit: '万吨', \r\n          target: 280, \r\n          warning: 300, \r\n          danger: 320, \r\n          status: 'warning',\r\n          trend: 3.8\r\n        },\r\n        { \r\n          name: '高压电力', \r\n          category: 'electricity',\r\n          value: 1856.4, \r\n          unit: '万kWh', \r\n          target: 1800, \r\n          warning: 1900, \r\n          danger: 2000, \r\n          status: 'warning',\r\n          trend: 4.2\r\n        },\r\n        { \r\n          name: '中压电力', \r\n          category: 'electricity',\r\n          value: 945.2, \r\n          unit: '万kWh', \r\n          target: 950, \r\n          warning: 1000, \r\n          danger: 1050, \r\n          status: 'normal',\r\n          trend: -0.8\r\n        }\r\n      ],\r\n      \r\n      // 分厂能源消耗数据 - 按月统计\r\n      factoryEnergyData: {\r\n        months: ['1月', '2月', '3月', '4月', '5月', '6月'],\r\n        electricity: { // 电力消耗 (万千瓦时)\r\n          '钙业分厂':[1400884,1434005,1435766,1376319,1301095,1269630],\r\n          '矿渣微粉':[5805794,2131847,6089046,6100999,6417335,6478262],\r\n          '烧结分厂-1#烧结':[13117902,12943568,11061444,10812393,11623702,11032539],\r\n          '烧结分厂-2#烧结':[13033926,10436634,9287884,9769316,9879397,10565760],\r\n          '炼铁分厂-1#高炉':[0,0,0,0,0,0],\r\n          '炼铁分厂-2#高炉':[66940,0,0,0,0,0],\r\n          '炼铁分厂-3#高炉':[0,0,0,0,0,0],\r\n          '炼铁分厂-小喷煤':[0,0,0,0,0,0],\r\n          '一炼钢':[30768167,30125781,30297463,28980497,29774159,31343397],\r\n          '二炼钢':[22488495,21968943,21787916,22171067,21242115,21788119],\r\n          '一轧钢':[5795777,5452204,5711051,5648575,5496447,5733403],\r\n          '二轧-大棒':[3246250,3195091,3268836,3363082,3262553,3466935],\r\n          '二轧-小棒':[5059018,4954511,4811987,5053456,4687922,4852370],\r\n          '特板炼钢':[30387862,29842019,26431716,29469372,29271035,29035520],\r\n          '3500中板':[7706300,6644420,7397716,7328102,7206215,7421179],\r\n          '4300厚板':[13519112,12464662,10028536,12881286,12674940,13166679],\r\n          '热处理':[2813937,2726501,2275425,2384412,2206548,3135105],\r\n          '高线分厂':[7621452,7822538,7920470,7152446,7071412,7342066],\r\n          '线材深加工':[968654,878075,925284,902573,915434,915053],\r\n          '棒材深加工':[3330295,3280875,3350518,3481898,3304357,3326433],\r\n          '热电分厂-热电':[8709519,9757840,10102720,9660480,9307680,7473520],\r\n          '热电分厂-亚临界':[2842245,4042080,3634400,4309800,4358435,4033588],\r\n          '热电分厂-余热':[133500,247280,112640,112640,218560,136480],\r\n          '热电分厂-鼓风':[12146140,21995616,21969803,23377762,28428976,37036933],\r\n          '制氧分厂-一期':[26203549,26456523,20347464,26646735,25523888,27290746],\r\n          '制氧分厂-三期':[29239632,27886434,29274657,26601919,26162015,26865678],\r\n          '制氧分厂-空压站':[6092759,6483609,6455930,6661039,6369297,6464792],\r\n          '水处理-一期':[2467240,2470442,2515829,2549457,3222031,2884069],\r\n          '水处理-二期':[4951897,4902986,4843723,5040984,5021708,5263224],\r\n          '水处理-三期':[5224649,5320012,5060813,5407588,5488715,5816560],\r\n          '煤气分厂':[643132,693466,657052,659543,624830,620973],\r\n          '供电一区':[100415,103537,103906,133611,166027,180227],\r\n          '兴澄钢球':[1087981,840818,981751,1057275,909275,1188557],\r\n          '兴澄港务':[68023,59481,69918,63336,44541,65389],\r\n          '储运公司':[5759324,5859975,5352206,5316640,5378337,5644938],\r\n          '综合利用':[1046763,1048737,1103178,1006943,1082975,1068868],\r\n          '合金炉分厂':[2769092,4321005,3221559,4932761,4789800,5878980],\r\n          '物管部':[39902,43498,34953,29662,24373,24227],\r\n          '后勤部':[46436,51144,39739,36459,36817,36596],\r\n          '其他':[824375,775107,749943,688365,764337,978520],\r\n          '物管部':[1295140,664000,621920,541480,576620,836960],\r\n          '合计':[278822476,280325264,269335161,281710272,284833903,300662274]\r\n          // '炼铁分厂': [850, 820, 840, 860],\r\n          // '烧结分厂': [400, 380, 390, 410],\r\n          // '一炼钢': [950, 920, 940, 960],\r\n          // '二炼钢': [900, 880, 890, 920],\r\n          // '综合利用': [280, 270, 290, 275],\r\n          // '一轧钢': [380, 370, 390, 375],\r\n          // '二轧钢': [360, 350, 370, 355],\r\n          // '三轧钢': [340, 330, 350, 335],\r\n          // '特殊钢轧材': [320, 310, 330, 315],\r\n          // '棒材轧制厂': [300, 290, 310, 295],\r\n          // '棒线材深加工(棒)': [180, 170, 190, 175],\r\n          // '棒线材深加工(线)': [160, 150, 170, 155],\r\n          // '线材深加工': [140, 130, 150, 135],\r\n          // '马科托钢球': [420, 410, 430, 425],\r\n          // '特钢炼钢分厂': [380, 370, 390, 375],\r\n          // '中板分厂': [360, 350, 370, 355],\r\n          // '厚板分厂': [340, 330, 350, 335],\r\n          // '钢材深加工': [320, 310, 330, 315],\r\n          // '热电': [180, 170, 190, 175],\r\n          // '供电工区': [160, 150, 170, 155],\r\n          // '水处理分厂': [140, 130, 150, 135],\r\n          // '制氧分厂': [120, 110, 130, 115],\r\n          // '煤气分厂': [100, 90, 110, 95],\r\n          // '储运公司': [180, 175, 185, 182],\r\n          // '检修分厂': [150, 145, 155, 152]\r\n        },\r\n        water: { // 水资源消耗 (万吨)\r\n          '钙业分厂':[2517,2376,2253,2173,2259,2301],\r\n          '矿渣微粉':[2890,2591,2478,2358,2381,2256],\r\n          '烧结分厂-1#烧结':[41290,40443,39591,40136,40852,40146],\r\n          '烧结分厂-2#烧结':[71804,67539,69009,69947,72050,70344],\r\n          '炼铁分厂-1#高炉':[20870,21082,21231,26729,24702,28188],\r\n          '炼铁分厂-2#高炉':[61032,65615,56218,65690,63567,67033],\r\n          '炼铁分厂-3#高炉':[70604,69964,78613,85914,85358,99016],\r\n          '炼铁分厂-喷煤（0#2#高炉）':[2308,2457,2897,3017,2851,3597],\r\n          '一炼钢':[4631152,4529160,4609510,4645449,4536932,4618563],\r\n          '二炼钢':[9104629,8974584,8707241,8864449,8574110,8780493],\r\n          '一轧钢':[1613416,1566303,1603660,1604021,1574222,1641956],\r\n          '二轧-大棒':[1549976,1473652,1448998,1482840,1420848,1461097],\r\n          '二轧-小棒':[1761922,1636518,1599618,1707777,1611290,1566453],\r\n          '特板炼钢':[11065202,10861897,10078370,10798271,10539246,10414072],\r\n          '3500中板':[2528768,2531102,2577106,2614333,2509852,2746164],\r\n          '4300厚板':[53602,52973,47100,51727,48993,52439],\r\n          '4300水处理':[51957,59997,58691,55515,61891,66474],\r\n          '热处理':[15873,13257,11536,10213,10796,10018],\r\n          '高线分厂':[24783,21328,19082,23117,24548,23518],\r\n          '热电分厂-热电':[32654,35991,46976,34907,35831,30211],\r\n          '热电分厂-鼓风':[66781,60578,74506,84715,56847,60222],\r\n          '热电分厂-亚临界':[50702,42898,53360,80937,75090,83220],\r\n          '热电分厂-净水厂直供原水':[82710,66641,58670,59022,51034,51739],\r\n          '制氧分厂-一期':[48107,89953,40665,51367,56605,55866],\r\n          '制氧分厂-三期':[27136,13397,10726,21896,25682,11716],\r\n          '制氧分厂-空压站':[3928,3563,4385,4983,3343,3542],\r\n          '水处理-一期':[666457,684861,699017,706374,703497,737291],\r\n          '水处理-二期':[283733,290660,245217,243883,245300,285485],\r\n          '水处理-三期':[162012,140737,143298,211329,206303,245132],\r\n          '供电一区':[2273,2357,2502,2597,2385,2153],\r\n          '储运公司':[41906,40362,41980,42361,41963,40779],\r\n          '综合利用':[25117,24586,19931,25926,22555,15964],\r\n          '合金炉分厂':[939944,1406061,1129690,1591196,1392990,1834398],\r\n          '后勤综合楼':[606264,473027,520855,233384,168146,365997],\r\n          '公用':[79065,79871,79634,79871,80971,81341],\r\n          '合计':[35793384,35448381,34204614,35628424,34375290,35599184]\r\n          // '炼铁分厂': [55, 53, 56, 57],\r\n          // '烧结分厂': [30, 29, 31, 32],\r\n          // '一炼钢': [65, 63, 66, 67],\r\n          // '二炼钢': [60, 58, 61, 62],\r\n          // '综合利用': [20, 19, 21, 20.5],\r\n          // '一轧钢': [25, 24, 26, 25.5],\r\n          // '二轧钢': [23, 22, 24, 23.5],\r\n          // '三轧钢': [22, 21, 23, 22.5],\r\n          // '特殊钢轧材': [21, 20, 22, 21.5],\r\n          // '棒材轧制厂': [20, 19, 21, 20.5],\r\n          // '棒线材深加工(棒)': [12, 11, 13, 12.5],\r\n          // '棒线材深加工(线)': [11, 10, 12, 11.5],\r\n          // '线材深加工': [9, 8, 10, 9.5],\r\n          // '马科托钢球': [28, 27, 29, 28.5],\r\n          // '特钢炼钢分厂': [25, 24, 26, 25.5],\r\n          // '中板分厂': [23, 22, 24, 23.5],\r\n          // '厚板分厂': [22, 21, 23, 22.5],\r\n          // '钢材深加工': [21, 20, 22, 21.5],\r\n          // '热电': [65, 63, 67, 66],\r\n          // '供电工区': [45, 43, 47, 46],\r\n          // '水处理分厂': [70, 68, 72, 71],\r\n          // '制氧分厂': [55, 53, 57, 56],\r\n          // '煤气分厂': [45, 43, 47, 46],\r\n          // '储运公司': [15, 14, 16, 15.5],\r\n          // '检修分厂': [12, 11.5, 12.5, 12.2]\r\n        },\r\n        gas: {\r\n          '钙业分厂':[2080,14317,12875,6240,0,0],\r\n          '矿渣微粉':[0,0,0,0,0,0],\r\n          '烧结分厂-1#烧结':[928,358,1054,2476,1040,1040],\r\n          '烧结分厂-2#烧结':[13471,3440,11592,14448,19094,38272],\r\n          '炼铁分厂-1#高炉':[4264,2392,1832,3952,1456,2184],\r\n          '炼铁分厂-2#高炉':[3744,6344,14808,4104,3432,5616],\r\n          '炼铁分厂-3#高炉':[48880,74483,62541,92272,17992,47889],\r\n          '炼铁分厂-小喷煤':[936,1144,1040,1212,936,1040],\r\n          '炼铁分厂-大喷煤':[42432,35464,32200,20696,15288,17784],\r\n          '一炼钢':[321044,441179,438423,450993,381575,351275],\r\n          '二炼钢':[150844,208889,212605,212274,204372,195425],\r\n          '一轧钢':[2857,12084,12120,6818,6230,16217],\r\n          '二轧-大棒':[23824,30000,14520,10630,24036,24489],\r\n          '二轧-小棒':[16000,12487,8926,4555,16024,10496],\r\n          '特板炼钢':[154790,218832,190568,135757,143405,140886],\r\n          '3500中板':[619233,644217,767563,908906,890002,926636],\r\n          '4300厚板':[492656,621775,785247,500988,563886,792919],\r\n          '热处理':[1093740,1370389,1296365,1408988,1170360,1299076],\r\n          '高线分厂':[214,96,298,207,100,204],\r\n          '棒材深加工':[1133600,1177473,1139440,1214108,1141737,1153930],\r\n          '线材深加工':[614092,501810,509750,552280,559440,517720],\r\n          '热电分厂-热电':[15713,33472,183549,21279,82525,33965],\r\n          '热电分厂-鼓风':[58638,35678,204014,28303,60770,58240],\r\n          '热电分厂-亚临界':[94063,60326,27315,63941,90389,93837],\r\n          '煤气分厂':[6285,7289,7616,7537,7561,3075],\r\n          '兴澄钢球':[194970,206307,208890,225422,209463,215730],\r\n          '综合利用':[0,0,0,0,0,0],\r\n          '合计':[5109298,5720245,6145151,5898386,5611113,5947945]\r\n          \r\n           // 天然气消耗 (万立方米)\r\n          // '炼铁分厂': [220, 210, 225, 230],\r\n          // '烧结分厂': [100, 95, 105, 110],\r\n          // '一炼钢': [250, 240, 255, 260],\r\n          // '二炼钢': [230, 220, 235, 240],\r\n          // '综合利用': [75, 70, 78, 76],\r\n          // '一轧钢': [95, 90, 98, 96],\r\n          // '二轧钢': [90, 85, 93, 91],\r\n          // '三轧钢': [85, 80, 88, 86],\r\n          // '特殊钢轧材': [80, 75, 83, 81],\r\n          // '棒材轧制厂': [75, 70, 78, 76],\r\n          // '棒线材深加工(棒)': [45, 40, 48, 46],\r\n          // '棒线材深加工(线)': [40, 35, 43, 41],\r\n          // '线材深加工': [35, 30, 38, 36],\r\n          // '马科托钢球': [110, 105, 112, 108],\r\n          // '特钢炼钢分厂': [95, 90, 98, 96],\r\n          // '中板分厂': [90, 85, 93, 91],\r\n          // '厚板分厂': [85, 80, 88, 86],\r\n          // '钢材深加工': [80, 75, 83, 81],\r\n          // '热电': [35, 30, 38, 36],\r\n          // '供电工区': [25, 20, 28, 26],\r\n          // '水处理分厂': [20, 15, 23, 21],\r\n          // '制氧分厂': [15, 10, 18, 16],\r\n          // '煤气分厂': [10, 5, 13, 11],\r\n          // '储运公司': [45, 43, 46, 44],\r\n          // '检修分厂': [35, 34, 36, 35.5]\r\n        },\r\n        steam: { // 蒸汽消耗 (万吨)\r\n          '钙业分厂':[0,0,0,0,0,0],\r\n          '矿渣微粉':[0,0,0,0,0,0],\r\n          '烧结分厂-1#烧结':[0,0,0,0,0,0],\r\n          '烧结分厂-2#烧结':[2368,2379,1765,1615,1422,1663],\r\n          '炼铁分厂-1#高炉':[578,637,485,554,388,671],\r\n          '炼铁分厂-2#高炉':[295,141,109,265,419,312],\r\n          '炼铁分厂-3#高炉':[1445,2143,1633,124,0,127],\r\n          '一炼钢':[116,388,50,50,96,164],\r\n          '二炼钢':[12927,16443,17638,17071,16843,16351],\r\n          '一轧钢':[0,0,0,0,0,0],\r\n          '二轧-大棒':[209,222,253,133,198,116],\r\n          '二轧-小棒':[193,204,233,123,182,108],\r\n          '特板炼钢':[22968,22336,18819,21647,21604,21708],\r\n          '3500中板':[0,0,0,0,0,0],\r\n          '4300厚板':[0,0,0,0,0,0],\r\n          '高线分厂':[0,0,0,0,0,0],\r\n          '棒材深加工':[0,0,0,0,0,0],\r\n          '线材深加工':[2240,1616,1364,1121,1082,920],\r\n          '热电分厂-热电':[85199,90583,83075,93108,89615,95156],\r\n          '热电分厂-鼓风':[0,0,0,0,0,0],\r\n          '热电分厂-热水':[0,0,0,0,0,0],\r\n          '煤气分厂':[0,0,0,0,0,0],\r\n          '制氧分厂':[400,400,400,400,400,400],\r\n          '后勤部':[0,0,0,0,0,0],\r\n          '外销蒸汽':[32568,17534,27334,24311,21126,19504],\r\n          '综合利用':[216,31,16,19,32,46],\r\n          '损耗':[1270,1166,1088,907,838,825],\r\n          '合计':[162992,156223,154262,161448,154245,158071]\r\n          // '炼铁分厂': [30, 28, 31, 32],\r\n          // '烧结分厂': [15, 13, 16, 17],\r\n          // '一炼钢': [35, 33, 36, 37],\r\n          // '二炼钢': [30, 28, 31, 32],\r\n          // '综合利用': [10, 9, 11, 10.5],\r\n          // '一轧钢': [13, 12, 14, 13.5],\r\n          // '二轧钢': [12, 11, 13, 12.5],\r\n          // '三轧钢': [11, 10, 12, 11.5],\r\n          // '特殊钢轧材': [10, 9, 11, 10.5],\r\n          // '棒材轧制厂': [9, 8, 10, 9.5],\r\n          // '棒线材深加工(棒)': [5, 4, 6, 5.5],\r\n          // '棒线材深加工(线)': [4, 3, 5, 4.5],\r\n          // '线材深加工': [3, 2, 4, 3.5],\r\n          // '马科托钢球': [15, 14.5, 15.5, 15.2],\r\n          // '特钢炼钢分厂': [13, 12, 14, 13.5],\r\n          // '中板分厂': [12, 11, 13, 12.5],\r\n          // '厚板分厂': [11, 10, 12, 11.5],\r\n          // '钢材深加工': [10, 9, 11, 10.5],\r\n          // '热电': [5, 4, 6, 5.5],\r\n          // '供电工区': [3, 2, 4, 3.5],\r\n          // '水处理分厂': [2, 1, 3, 2.5],\r\n          // '制氧分厂': [1.5, 0.5, 2.5, 2],\r\n          // '煤气分厂': [1, 0.8, 1.2, 1.1],\r\n          // '储运公司': [8, 7.8, 8.2, 8.1],\r\n          // '检修分厂': [6, 5.8, 6.2, 6.1]\r\n        }\r\n      },\r\n      \r\n      // 关键能源指标当日值与昨日对比\r\n      keyEnergyIndicators: [],\r\n      // [\r\n      //   {\r\n      //     name: '氧气',\r\n      //     today: 46.80, // 超出目标范围上限\r\n      //     yesterday: 44.64,\r\n      //     unit: 'm³/t',\r\n      //     targetMin: 42.00,\r\n      //     targetMax: 45.00,\r\n      //     change: 4.84,\r\n      //     status: 'danger'\r\n      //   },\r\n      //   {\r\n      //     name: '氮气',\r\n      //     today: 14.82,\r\n      //     yesterday: 15.21,\r\n      //     unit: 'm³/t',\r\n      //     targetMin: 14.00,\r\n      //     targetMax: 16.00,\r\n      //     change: -2.56,\r\n      //     status: 'good'\r\n      //   },\r\n      //   {\r\n      //     name: '氩气',\r\n      //     today: 0.85,\r\n      //     yesterday: 0.88,\r\n      //     unit: 'm³/t',\r\n      //     targetMin: 0.80,\r\n      //     targetMax: 0.90,\r\n      //     change: -3.41,\r\n      //     status: 'good'\r\n      //   },\r\n      //   {\r\n      //     name: '空气',\r\n      //     today: 450, // 低于目标范围下限\r\n      //     yesterday: 481,\r\n      //     unit: 'm³/h',\r\n      //     targetMin: 460,\r\n      //     targetMax: 500,\r\n      //     change: -6.44,\r\n      //     status: 'danger'\r\n      //   },\r\n      //   {\r\n      //     name: '高炉煤气',\r\n      //     today: 985.3,\r\n      //     yesterday: 962.7,\r\n      //     unit: 'm³/t',\r\n      //     targetMin: 950.0,\r\n      //     targetMax: 1000.0,\r\n      //     change: 2.35,\r\n      //     status: 'good'\r\n      //   },\r\n      //   {\r\n      //     name: '转炉煤气',\r\n      //     today: 85.2,\r\n      //     yesterday: 88.4,\r\n      //     unit: 'm³/t',\r\n      //     targetMin: 80.0,\r\n      //     targetMax: 90.0,\r\n      //     change: -3.62,\r\n      //     status: 'good'\r\n      //   },\r\n      //   {\r\n      //     name: '焦炉煤气',\r\n      //     today: 41.3,\r\n      //     yesterday: 42.8,\r\n      //     unit: 'm³/t',\r\n      //     targetMin: 40.0,\r\n      //     targetMax: 45.0,\r\n      //     change: -3.50,\r\n      //     status: 'good'\r\n      //   },\r\n      //   {\r\n      //     name: '饱和蒸汽',\r\n      //     today: 0.52,\r\n      //     yesterday: 0.54,\r\n      //     unit: 't/t',\r\n      //     targetMin: 0.50,\r\n      //     targetMax: 0.58,\r\n      //     change: -3.70,\r\n      //     status: 'good'\r\n      //   },\r\n      //   {\r\n      //     name: '过热蒸汽',\r\n      //     today: 0.33,\r\n      //     yesterday: 0.31,\r\n      //     unit: 't/t',\r\n      //     targetMin: 0.30,\r\n      //     targetMax: 0.35,\r\n      //     change: 6.45,\r\n      //     status: 'warning'\r\n      //   },\r\n      //   {\r\n      //     name: '低压蒸汽',\r\n      //     today: 0.21,\r\n      //     yesterday: 0.23,\r\n      //     unit: 't/t',\r\n      //     targetMin: 0.20,\r\n      //     targetMax: 0.25,\r\n      //     change: -8.70,\r\n      //     status: 'good'\r\n      //   },\r\n      //   {\r\n      //     name: '天然气',\r\n      //     today: 24.3,\r\n      //     yesterday: 25.1,\r\n      //     unit: 'm³/t',\r\n      //     targetMin: 22.0,\r\n      //     targetMax: 26.0,\r\n      //     change: -3.19,\r\n      //     status: 'good'\r\n      //   },\r\n      //   {\r\n      //     name: '压缩天然气',\r\n      //     today: 2.85,\r\n      //     yesterday: 2.91,\r\n      //     unit: 'm³/t',\r\n      //     targetMin: 2.70,\r\n      //     targetMax: 3.00,\r\n      //     change: -2.06,\r\n      //     status: 'good'\r\n      //   }\r\n      // ],\r\n      \r\n      // 能源指标是否超出目标范围标记\r\n      isEnergyChartWarning: {\r\n        electricity: false,\r\n        water: false,\r\n        gas: false,\r\n        steam: false\r\n      },\r\n      \r\n      // 能源目标范围\r\n      energyTargetRanges: {\r\n        electricity: { min: 800, max: 900, unit: '千瓦时' },\r\n        water: { min: 50, max: 60, unit: '吨' },\r\n        gas: { min: 200, max: 240, unit: '立方米' },\r\n        steam: { min: 25, max: 35, unit: '吨' }\r\n      },\r\n    }\r\n  },\r\n  computed: {\r\n    // 计算所有部门的完成率数据（只显示分厂层级）\r\n    departmentCompletionRates() {\r\n      // 按部门分组计算完成率，只处理有\"-\"的分厂层级\r\n      const departmentRates = this.completionData\r\n        .filter(dept => dept.department.includes('-')) // 只选择分厂层级\r\n        .map(dept => {\r\n          const indicators = dept.indicators\r\n          let completedCount = 0\r\n          let totalIndicators = indicators.length\r\n          \r\n          // 计算已完成指标数量，使用4月份数据\r\n          indicators.forEach(indicator => {\r\n            // 使用4月份数据（第3个索引）\r\n            const actual = indicator.values[3] // 第四周/4月份的实际值\r\n            const target = indicator.target\r\n            \r\n            const isCompleted = indicator.isHigherBetter \r\n              ? actual >= target \r\n              : actual <= target\r\n            \r\n            if (isCompleted) completedCount++\r\n          })\r\n          \r\n          // 计算完成率\r\n          const completionRate = (completedCount / totalIndicators) * 100\r\n          \r\n          return {\r\n            department: dept.department.split('-')[1], // 只显示分厂名称\r\n            fullDepartment: dept.department, // 保存完整部门名称用于数据查询\r\n            completionRate: parseFloat(completionRate.toFixed(1)),\r\n            totalIndicators: totalIndicators,\r\n            completedIndicators: completedCount\r\n          }\r\n        })\r\n      \r\n      // 按完成率从高到低排序\r\n      return departmentRates.sort((a, b) => b.completionRate - a.completionRate)\r\n    },\r\n    \r\n    // 计算所有指标的完成率（4月份数据）\r\n    allIndicatorCompletionRates() {\r\n      const allIndicators = []\r\n      \r\n      this.completionData.forEach(dept => {\r\n        if (dept.department.includes('-')) { // 只选择分厂层级\r\n          const departmentName = dept.department.split('-')[1]\r\n          \r\n          dept.indicators.forEach(indicator => {\r\n            // 直接使用04月列的完成率数据\r\n            // 检查是否有最后一列（04月）的完成率数据\r\n            const completionRateStr = indicator.completionRates && indicator.completionRates[3]; // 04月完成率\r\n            let completionRate = 0;\r\n            \r\n            // 如果有直接的完成率数据，则使用它\r\n            if (completionRateStr) {\r\n              // 将百分比字符串转换为数值，例如 \"-1.31%\" -> -1.31\r\n              completionRate = parseFloat(completionRateStr.replace('%', ''));\r\n            } else {\r\n              // 如果没有直接数据，则用原来的方法计算\r\n              const actual = indicator.values[3]; // 4月份实际值\r\n              const target = indicator.target;\r\n              \r\n              if (indicator.isHigherBetter) {\r\n                // 如果指标越高越好，完成率 = 实际值/目标值 * 100% - 100%\r\n                completionRate = ((actual / target) * 100) - 100;\r\n              } else {\r\n                // 如果指标越低越好，完成率 = 目标值/实际值 * 100% - 100%\r\n                completionRate = ((target / actual) * 100) - 100;\r\n              }\r\n            }\r\n            \r\n            // 对完成率进行处理\r\n            // 非数字完成率调整为0\r\n            if (isNaN(completionRate) || !isFinite(completionRate)) {\r\n              completionRate = 0;\r\n            }\r\n            \r\n            // 限制最大完成率绝对值为200%\r\n            if (Math.abs(completionRate) > 200) {\r\n              completionRate = completionRate > 0 ? 200 : -200;\r\n            }\r\n            \r\n            allIndicators.push({\r\n              department: departmentName,\r\n              indicator: indicator.name,\r\n              completionRate: parseFloat(completionRate.toFixed(1)),\r\n              target: indicator.target,\r\n              actual: indicator.values[3], // 4月份实际值\r\n              unit: indicator.unit,\r\n              isHigherBetter: indicator.isHigherBetter\r\n            });\r\n          });\r\n        }\r\n      });\r\n      \r\n      // 按完成率从高到低排序\r\n      return allIndicators.sort((a, b) => b.completionRate - a.completionRate);\r\n    },\r\n    \r\n    // 直接使用所有指标完成率数据\r\n    paginatedIndicatorRates() {\r\n      return this.allIndicatorCompletionRates\r\n    },\r\n    \r\n    // 获取事业部列表（用于筛选）\r\n    businessUnits() {\r\n      const units = new Set()\r\n      this.completionData.forEach(dept => {\r\n        if (dept.department.includes('-')) {\r\n          units.add(dept.department.split('-')[0])\r\n        } else {\r\n          units.add(dept.department)\r\n        }\r\n      })\r\n      return Array.from(units)\r\n    },\r\n    \r\n    // 获取当前选择的事业部下的分厂列表\r\n    currentBusinessUnitDepartments() {\r\n      if (!this.currentBusinessUnit) return []\r\n      \r\n      return this.completionData\r\n        .filter(dept => dept.department.startsWith(this.currentBusinessUnit + '-'))\r\n        .map(dept => ({\r\n          value: dept.department,\r\n          label: dept.department.split('-')[1]\r\n        }))\r\n    },\r\n    \r\n    // 获取当前部门的指标列表\r\n    currentDepartmentIndicators() {\r\n      const deptData = this.completionData.find(dept => dept.department === this.currentDepartment)\r\n      return deptData ? deptData.indicators : []\r\n    },\r\n    \r\n    // 获取所有分厂列表（用于能源消耗详情）\r\n    allFactories() {\r\n      return this.completionData1.map(dept => dept.department)\r\n    }\r\n  },\r\n  watch: {\r\n    // // 监听事业部变化\r\n    // currentBusinessUnit(newVal) {\r\n    //   if (this.currentBusinessUnitDepartments.length > 0) {\r\n    //     this.currentDepartment = this.currentBusinessUnitDepartments[0].value\r\n    //   }\r\n      \r\n    //   // 启动自动切换\r\n    //   this.startAutoSwitchWithinBusinessUnit()\r\n    // },\r\n    \r\n    // // 监听部门变化，自动选择第一个指标\r\n    // currentDepartment(newVal) {\r\n    //   if (this.currentDepartmentIndicators.length > 0) {\r\n    //     this.currentIndicator = this.currentDepartmentIndicators[0].name\r\n    //   }\r\n    //   this.initMonthlyTrendChart()\r\n    // },\r\n    \r\n    // // 监听指标变化，更新图表\r\n    // currentIndicator() {\r\n    //   this.initMonthlyTrendChart()\r\n    // }\r\n  },\r\n  mounted() {\r\n    // 初始化图表\r\n    this.initCharts()\r\n    \r\n    // 计算未完成指标数据\r\n    this.calculateIncompleteData()\r\n    \r\n    // 确保currentEnergyDept有一个有效的初始值\r\n    this.$nextTick(() => {\r\n      this.initEnergyDetailCharts()\r\n      if (this.allFactories && this.allFactories.length > 0) {\r\n        this.currentEnergyDept = this.allFactories[0]\r\n        // 重新初始化能源详情图表\r\n        this.initEnergyDetailCharts()\r\n      }\r\n    })\r\n    this.loadExcelFromRemote();\r\n    // 启动滚动表格\r\n    this.$nextTick(() => {\r\n      // 复制表格内容以实现无缝滚动\r\n      const table = this.$refs.incompleteTable\r\n      if (table && this.incompleteData.length > 0) {\r\n        // 获取表格内容部分（不包含表头）\r\n        const tableBody = table.querySelector('.el-table__body-wrapper')\r\n        if (tableBody) {\r\n          const originalContent = tableBody.innerHTML\r\n          // 在表格内容后面添加一份相同的内容，而不是整个表格\r\n          tableBody.innerHTML += originalContent\r\n        }\r\n      }\r\n      this.startTableScroll()\r\n    })\r\n    \r\n    // 启动事业部内部自动切换\r\n    this.startAutoSwitchWithinBusinessUnit()\r\n    \r\n    // 启动事业部切换（每30秒切换一次）\r\n    this.startBusinessUnitSwitch()\r\n    \r\n    // 启动指标卡片自动滚动\r\n    this.$nextTick(() => {\r\n      this.initIndicatorCardsScroll()\r\n      \r\n      // 为指标卡片容器添加鼠标悬停事件处理\r\n      const cardsContainer = this.$refs.indicatorCardsContainer\r\n      if (cardsContainer) {\r\n        cardsContainer.addEventListener('mouseover', () => {\r\n          this.indicatorCardsScrollPaused = true\r\n        })\r\n        \r\n        cardsContainer.addEventListener('mouseout', () => {\r\n          this.indicatorCardsScrollPaused = false\r\n        })\r\n      }\r\n    })\r\n    \r\n    // 监听窗口大小变化，重新渲染图表\r\n    window.addEventListener('resize', this.resizeCharts)\r\n  },\r\n  beforeDestroy() {\r\n    // 清除定时器\r\n    this.stopAutoSwitch()\r\n    \r\n    if (this.scrollTimer) {\r\n      clearInterval(this.scrollTimer)\r\n      \r\n      // 移除表格滚动的事件监听器\r\n      const table = this.$refs.incompleteTable\r\n      if (table) {\r\n        const tableBody = table.querySelector('.el-table__body-wrapper')\r\n        if (tableBody && this.tableMouseEnterHandler && this.tableMouseLeaveHandler) {\r\n          tableBody.removeEventListener('mouseenter', this.tableMouseEnterHandler);\r\n          tableBody.removeEventListener('mouseleave', this.tableMouseLeaveHandler);\r\n        }\r\n      }\r\n    }\r\n    \r\n    if (this.businessUnitTimer) {\r\n      clearInterval(this.businessUnitTimer)\r\n    }\r\n    \r\n    if (this.energyDeptTimer) {\r\n      clearInterval(this.energyDeptTimer)\r\n    }\r\n    \r\n    if (this.completionChartTimer) {\r\n      clearInterval(this.completionChartTimer)\r\n    }\r\n    \r\n    if (this.indicatorCardsScrollTimer) {\r\n      clearInterval(this.indicatorCardsScrollTimer)\r\n    }\r\n    \r\n    // 移除窗口大小变化监听\r\n    window.removeEventListener('resize', this.resizeCharts)\r\n    \r\n    // 移除指标卡片容器的事件监听器\r\n    const cardsContainer = this.$refs.indicatorCardsContainer\r\n    if (cardsContainer) {\r\n      cardsContainer.removeEventListener('mouseover', () => {\r\n        this.indicatorCardsScrollPaused = true\r\n      })\r\n      \r\n      cardsContainer.removeEventListener('mouseout', () => {\r\n        this.indicatorCardsScrollPaused = false\r\n      })\r\n    }\r\n    \r\n    // 销毁图表实例\r\n    this.disposeCharts()\r\n  },\r\n  created() {\r\n      this.getDateUpdateList()\r\n  },\r\n  methods: {\r\n    // 初始化所有图表\r\n\r\n    loadExcelFromRemote() {\r\n      this.excelLoading = true;\r\n      const url =\r\n        \"https://ydxt.citicsteel.com:8099/minio/xctg/temp/jm关键技径指标.xlsx\";\r\n      axios({\r\n        method: \"get\",\r\n        url,\r\n        responseType: \"arraybuffer\",\r\n      })\r\n        .then((response) => {\r\n          const data = new Uint8Array(response.data);\r\n          const workbook = XLSX.read(data, { type: \"array\" });\r\n          const firstSheet = workbook.Sheets[workbook.SheetNames[0]];\r\n          const jsonData = XLSX.utils.sheet_to_json(firstSheet, {\r\n            header: 1,\r\n            range: 1,\r\n          });\r\n          console.log(jsonData);\r\n          this.processExcelData(jsonData);\r\n          this.excelLoading = false;\r\n        })\r\n        .catch((error) => {\r\n          console.error(\"加载Excel文件失败:\", error);\r\n          this.$message.error(\"加载Excel文件失败，请稍后重试\");\r\n          this.excelLoading = false;\r\n        });\r\n    },\r\n    getDetail(){\r\n      dimensionalitylistPermissionList().then(res => {\r\n        this.adminShow = res.msg;\r\n      });\r\n    },\r\n    getDateUpdateList(){\r\n      dateUpdateList().then(res => {\r\n        this.keyEnergyIndicators = res.data;\r\n      });\r\n    },\r\n\r\n    \r\n    processExcelData(jsonData) {\r\n      if (!jsonData || jsonData.length < 2) return;\r\n      const headers = jsonData[0];\r\n      const colIndexes = {\r\n        factory: headers.indexOf(\"分厂\"),\r\n        name: headers.indexOf(\"指标名称\"),\r\n        target: headers.indexOf(\"目标\"),\r\n        unit: headers.indexOf(\"单位\"),\r\n        jan: headers.indexOf(\"01月实绩\"),\r\n        janStatus: headers.indexOf(\"01月实绩\") + 1,\r\n        feb: headers.indexOf(\"02月实绩\"),\r\n        febStatus: headers.indexOf(\"02月实绩\") + 1,\r\n        mar: headers.indexOf(\"03月实绩\"),\r\n        marStatus: headers.indexOf(\"03月实绩\") + 1,\r\n        apr: headers.indexOf(\"04月实绩\"),\r\n        aprStatus: headers.indexOf(\"04月实绩\") + 1,\r\n        may: headers.indexOf(\"05月实绩\"),\r\n        mayStatus: headers.indexOf(\"05月实绩\") + 1,\r\n      };\r\n\r\n      const dataRows = jsonData.slice(1);\r\n      const techIndicators = dataRows\r\n        .map((row) => {\r\n          if (!row || row.length === 0) return null;\r\n          if (\r\n            row[colIndexes.factory] === undefined ||\r\n            row[colIndexes.name] === undefined\r\n          )\r\n            return null;\r\n\r\n          const indicator = {\r\n            factory: row[colIndexes.factory] || \"\",\r\n            name: row[colIndexes.name] || \"\",\r\n            target:\r\n              row[colIndexes.target] !== undefined\r\n                ? String(row[colIndexes.target])\r\n                : \"\",\r\n            unit: row[colIndexes.unit] || \"\",\r\n            jan:\r\n              row[colIndexes.jan] !== undefined\r\n                ? String(row[colIndexes.jan])\r\n                : \"\",\r\n            janStatus: row[colIndexes.janStatus] === 1.0 ? 1 : 0,\r\n            feb:\r\n              row[colIndexes.feb] !== undefined\r\n                ? String(row[colIndexes.feb])\r\n                : \"\",\r\n            febStatus: row[colIndexes.febStatus] === 1.0 ? 1 : 0,\r\n            mar:\r\n              row[colIndexes.mar] !== undefined\r\n                ? String(row[colIndexes.mar])\r\n                : \"\",\r\n            marStatus: row[colIndexes.marStatus] === 1.0 ? 1 : 0,\r\n            apr:\r\n              row[colIndexes.apr] !== undefined\r\n                ? String(row[colIndexes.apr])\r\n                : \"\",\r\n            aprStatus: row[colIndexes.aprStatus] === 1.0 ? 1 : 0,\r\n            may:\r\n              row[colIndexes.may] !== undefined\r\n                ? String(row[colIndexes.may])\r\n                : \"\",\r\n            mayStatus: row[colIndexes.mayStatus] === 1.0 ? 1 : 0,\r\n          };\r\n\r\n          indicator.monthlyData = [\r\n            parseFloat(indicator.jan) || 0,\r\n            parseFloat(indicator.feb) || 0,\r\n            parseFloat(indicator.mar) || 0,\r\n            parseFloat(indicator.apr) || 0,\r\n            parseFloat(indicator.may) || 0,\r\n          ];\r\n\r\n          return indicator;\r\n        })\r\n        .filter(Boolean);\r\n\r\n      // 处理相同分厂和指标名称的数据，确保数据一致性\r\n      const uniqueKeys = new Map();\r\n      const uniqueIndicators = [];\r\n\r\n      // 先处理相同指标的合并\r\n      techIndicators.forEach((indicator) => {\r\n        const key = `${indicator.factory}_${indicator.name}_${indicator.unit}`;\r\n        if (!uniqueKeys.has(key)) {\r\n          uniqueKeys.set(key, uniqueIndicators.length);\r\n          uniqueIndicators.push(indicator);\r\n        }\r\n      });\r\n\r\n      this.techIndicators = uniqueIndicators;\r\n    },\r\n    \r\n    initCharts() {\r\n      this.$nextTick(() => {\r\n        // 初始化部门完成率柱状图\r\n        this.initDepartmentCompletionChart()\r\n        \r\n        // 设置默认部门和指标\r\n        if (this.currentBusinessUnitDepartments.length > 0 && !this.currentDepartment) {\r\n          this.currentDepartment = this.currentBusinessUnitDepartments[0].value\r\n        }\r\n        \r\n        if (this.currentDepartmentIndicators.length > 0 && !this.currentIndicator) {\r\n          this.currentIndicator = this.currentDepartmentIndicators[0].name\r\n        }\r\n        \r\n        // 初始化月度趋势折线图\r\n        this.initMonthlyTrendChart()\r\n        \r\n        // 设置能源部门默认值并初始化能源子图表\r\n        if (this.allFactories && this.allFactories.length > 0) {\r\n          if (!this.currentEnergyDept) {\r\n            this.currentEnergyDept = this.allFactories[0]\r\n          }\r\n          // 初始化能源子图表\r\n          this.initEnergyDetailCharts()\r\n        }\r\n        \r\n        // 启动事业部内部自动切换\r\n        this.startAutoSwitchWithinBusinessUnit()\r\n        \r\n        // 启动事业部切换（每30秒切换一次）\r\n        this.startBusinessUnitSwitch()\r\n        \r\n        // 启动能源部门自动切换\r\n        this.startEnergyDeptSwitch()\r\n      })\r\n    },\r\n    \r\n    // 启动事业部内部自动切换\r\n    startAutoSwitchWithinBusinessUnit() {\r\n      // 停止之前的自动切换\r\n      this.stopAutoSwitch()\r\n      \r\n      // 启动新的自动切换\r\n      this.trendChartTimer = setInterval(() => {\r\n        this.switchDepartmentWithinBusinessUnit()\r\n      }, 3000) // 分厂切换频率为3秒\r\n    },\r\n    \r\n    // 在当前事业部内切换部门\r\n    switchDepartmentWithinBusinessUnit() {\r\n      const departments = this.currentBusinessUnitDepartments\r\n      if (departments.length <= 1) return\r\n      \r\n      const currentIndex = departments.findIndex(dept => dept.value === this.currentDepartment)\r\n      const nextIndex = (currentIndex + 1) % departments.length\r\n      this.currentDepartment = departments[nextIndex].value\r\n    },\r\n    \r\n    // 处理自动切换开关变化\r\n    handleAutoSwitchChange(value) {\r\n      if (value) {\r\n        // 启动自动切换\r\n        this.startAutoSwitchWithinBusinessUnit()\r\n      } else {\r\n        // 停止自动切换\r\n        this.stopAutoSwitch()\r\n      }\r\n    },\r\n    \r\n    // 启动自动切换\r\n    startAutoSwitch() {\r\n      if (this.trendChartTimer) {\r\n        clearInterval(this.trendChartTimer)\r\n      }\r\n      \r\n      this.trendChartTimer = setInterval(() => {\r\n        this.switchDepartment()\r\n      }, 3000)\r\n    },\r\n    \r\n    // 停止自动切换\r\n    stopAutoSwitch() {\r\n      if (this.trendChartTimer) {\r\n        clearInterval(this.trendChartTimer)\r\n        this.trendChartTimer = null\r\n      }\r\n    },\r\n    \r\n    // 切换完成率图表页码\r\n    changePage(page) {\r\n      this.currentPage = page\r\n      this.initDepartmentCompletionChart()\r\n    },\r\n    \r\n    // 计算未完成指标数据，筛选负数完成率\r\n    calculateIncompleteData() {\r\n      const incomplete = [];\r\n      \r\n      this.completionData.forEach(dept => {\r\n        dept.indicators.forEach(indicator => {\r\n          // 直接使用04月列的完成率数据\r\n          // 检查是否有最后一列（04月）的完成率数据\r\n          const completionRateStr = indicator.completionRates && indicator.completionRates[3]; // 04月完成率\r\n          let completionRate = 0;\r\n          \r\n          // 如果有直接的完成率数据，则使用它\r\n          if (completionRateStr) {\r\n            // 将百分比字符串转换为数值，例如 \"-1.31%\" -> -1.31\r\n            completionRate = parseFloat(completionRateStr.replace('%', ''));\r\n          } else {\r\n            // 如果没有直接数据，则用原来的方法计算\r\n            const actual = indicator.values[3]; // 4月份实际值\r\n            const target = indicator.target;\r\n            \r\n            if (indicator.isHigherBetter) {\r\n              // 如果指标越高越好，完成率 = 实际值/目标值 * 100% - 100%\r\n              completionRate = ((actual / target) * 100) - 100;\r\n            } else {\r\n              // 如果指标越低越好，完成率 = 目标值/实际值 * 100% - 100%\r\n              completionRate = ((target / actual) * 100) - 100;\r\n            }\r\n          }\r\n          \r\n          // 只添加完成率为负数的记录\r\n          if (completionRate < 0) {\r\n            incomplete.push({\r\n              department: dept.department.includes('-') ? dept.department.split('-')[1] : dept.department,\r\n              indicator: indicator.name,\r\n              target: indicator.target,\r\n              actual: indicator.values[3], // 4月份实际值\r\n              unit: indicator.unit,\r\n              completionRate: parseFloat(completionRate.toFixed(1))\r\n            });\r\n          }\r\n        });\r\n      });\r\n      \r\n      // 按完成率从低到高排序\r\n      this.incompleteData = incomplete.sort((a, b) => a.completionRate - b.completionRate);\r\n    },\r\n    \r\n    // 启动表格滚动\r\n    startTableScroll() {\r\n      if (this.incompleteData.length > 0) {\r\n        const table = this.$refs.incompleteTable\r\n        \r\n        // 复制表格内容以实现无缝滚动\r\n        if (table) {\r\n          // 清除之前的滚动定时器\r\n          if (this.scrollTimer) {\r\n            clearInterval(this.scrollTimer)\r\n          }\r\n          \r\n          // 获取表格内容部分（不包含表头）\r\n          const tableBody = table.querySelector('.el-table__body-wrapper')\r\n          if (tableBody) {\r\n            // 创建新的滚动定时器\r\n            let scrollTop = 0\r\n            this.scrollTimer = setInterval(() => {\r\n              // 如果暂停滚动，则不执行滚动操作\r\n              if (this.tableScrollPaused) return;\r\n              \r\n              scrollTop++\r\n              if (scrollTop >= tableBody.scrollHeight / 2) {\r\n                scrollTop = 0\r\n              }\r\n              tableBody.scrollTop = scrollTop\r\n            }, this.scrollSpeed)\r\n            \r\n            // 创建鼠标事件处理函数\r\n            this.tableMouseEnterHandler = () => {\r\n              this.tableScrollPaused = true;\r\n            };\r\n            \r\n            this.tableMouseLeaveHandler = () => {\r\n              this.tableScrollPaused = false;\r\n            };\r\n            \r\n            // 添加鼠标悬停事件处理\r\n            tableBody.addEventListener('mouseenter', this.tableMouseEnterHandler);\r\n            tableBody.addEventListener('mouseleave', this.tableMouseLeaveHandler);\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \r\n    // 切换部门趋势图\r\n    switchDepartment() {\r\n      const currentIndex = this.departments.indexOf(this.currentDepartment)\r\n      const nextIndex = (currentIndex + 1) % this.departments.length\r\n      this.currentDepartment = this.departments[nextIndex]\r\n    },\r\n    \r\n    // 初始化部门完成率柱状图 - 修改为横向柱状图\r\n    initDepartmentCompletionChart() {\r\n      const chartDom = document.getElementById('departmentCompletionChart')\r\n      const chart = echarts.init(chartDom)\r\n      \r\n      // 计算默认显示的百分比\r\n      const defaultDisplayPercent = Math.min(6 / this.allIndicatorCompletionRates.length * 100, 100)\r\n      \r\n      // 使用指标级别的完成率数据\r\n      const labels = this.paginatedIndicatorRates.map(item => {\r\n        return {\r\n          deptName: item.department,\r\n          indicatorName: item.indicator,\r\n          fullText: `${item.department}\\n${item.indicator}`\r\n        }\r\n      })\r\n      const rates = this.paginatedIndicatorRates.map(item => item.completionRate)\r\n      \r\n      const option = {\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: {\r\n            type: 'shadow'\r\n          },\r\n          formatter: function(params) {\r\n            const data = params[0]\r\n            const item = this.paginatedIndicatorRates[data.dataIndex]\r\n            const actualText = item.isHigherBetter \r\n              ? `实际值: ${item.actual}${item.unit}`\r\n              : `实际值: ${item.actual}${item.unit} `\r\n            return `${item.department}-${item.indicator}<br/>\r\n                    目标值: ${item.target}${item.unit}<br/>\r\n                    ${actualText}<br/>\r\n                    完成率: ${item.completionRate}%`\r\n          }.bind(this)\r\n        },\r\n        grid: {\r\n          left: '0', // 左移30px\r\n          right: '0',\r\n          bottom: '3%',\r\n          top: '3%',\r\n          containLabel: true\r\n        },\r\n        dataZoom: [\r\n          {\r\n            type: 'slider',\r\n            show: true,\r\n            yAxisIndex: [0],\r\n            start: 0,\r\n            end: defaultDisplayPercent,\r\n            width: 10,\r\n            handleSize: 20,\r\n            showDetail: false,\r\n            zoomLock: false,\r\n            moveOnMouseWheel: true,\r\n            preventDefaultMouseMove: true\r\n          },\r\n          {\r\n            type: 'inside',\r\n            yAxisIndex: [0],\r\n            start: 0,\r\n            end: defaultDisplayPercent,\r\n            zoomLock: false\r\n          }\r\n        ],\r\n        toolbox: {\r\n          feature: {\r\n            dataZoom: {\r\n              yAxisIndex: 'none'\r\n            },\r\n            restore: {},\r\n            saveAsImage: {}\r\n          },\r\n          right: 10,\r\n          top: 0\r\n        },\r\n        xAxis: {\r\n          type: 'value',\r\n          name: '完成率(%)',\r\n          min: -30, // 调整最小值为-30%，足够显示负数完成率\r\n          max: 30, // 调整最大值为30%，使图表更聚焦\r\n          axisLine: {\r\n            lineStyle: {\r\n              color: '#5470c6'\r\n            }\r\n          },\r\n          splitLine: {\r\n            lineStyle: {\r\n              type: 'dashed',\r\n              color: '#E0E6F1'\r\n            }\r\n          }\r\n        },\r\n        yAxis: {\r\n          type: 'category',\r\n          data: labels.map(item => item.fullText),\r\n          axisLabel: {\r\n            formatter: function(value) {\r\n              return value;\r\n            },\r\n            color: '#333',\r\n            lineHeight: 16,\r\n            margin: 12,\r\n            rich: {\r\n              dept: {\r\n                fontWeight: 'bold',\r\n                lineHeight: 20\r\n              },\r\n              indicator: {\r\n                lineHeight: 20\r\n              }\r\n            }\r\n          },\r\n          axisLine: {\r\n            lineStyle: {\r\n              color: '#5470c6'\r\n            }\r\n          }\r\n        },\r\n        series: [\r\n          {\r\n            name: '完成率',\r\n            type: 'bar',\r\n            data: rates,\r\n            itemStyle: {\r\n              color: function(params) {\r\n                // 根据完成率设置不同颜色\r\n                if (params.data >= 100) {\r\n                  return '#5470c6' // 蓝色，超过100%\r\n                } else if (params.data >= 90) {\r\n                  return '#91cc75'  // 绿色，完成率高\r\n                } else if (params.data >= 70) {\r\n                  return '#fac858'  // 黄色，完成率中等\r\n                } else if (params.data >= 0) {\r\n                  return '#ee6666'  // 红色，完成率低\r\n                } else {\r\n                  return '#ff5252'  // 更深的红色，负数完成率\r\n                }\r\n              }\r\n            },\r\n            label: {\r\n              show: true,\r\n              position: 'right',\r\n              formatter: '{c}%',\r\n              color: function(params) {\r\n                // 负数完成率用白色文字，更加明显\r\n                return params.data < 0 ? '#ffffff' : '#333333';\r\n              },\r\n              fontWeight: 'bold',\r\n              distance: 15\r\n            },\r\n            barWidth: '50%', // 柱子更细\r\n            barCategoryGap: '40%', // 增加柱子间距\r\n            animationDelay: function(idx) {\r\n              return idx * 100 + 100\r\n            }\r\n          }\r\n        ],\r\n        animationEasing: 'elasticOut',\r\n        animationDelayUpdate: function(idx) {\r\n          return idx * 5\r\n        }\r\n      }\r\n      \r\n      chart.setOption(option)\r\n      \r\n      // 保存dataZoom状态，防止滚动条回弹\r\n      chart.on('datazoom', function(params) {\r\n        const { start, end } = params\r\n        option.dataZoom[0].start = start\r\n        option.dataZoom[0].end = end\r\n        option.dataZoom[1].start = start\r\n        option.dataZoom[1].end = end\r\n        chart.setOption(option)\r\n      })\r\n      \r\n      // 创建事件处理器\r\n      this.chartMouseOverHandler = () => {\r\n        this.completionChartScrollPaused = true\r\n      }\r\n      \r\n      this.chartMouseOutHandler = () => {\r\n        this.completionChartScrollPaused = false\r\n      }\r\n      \r\n      // 添加鼠标悬停事件，暂停自动滚动\r\n      chartDom.addEventListener('mouseover', this.chartMouseOverHandler)\r\n      \r\n      // 添加鼠标离开事件，恢复自动滚动\r\n      chartDom.addEventListener('mouseout', this.chartMouseOutHandler)\r\n      \r\n      // 窗口大小变化时自动调整图表大小\r\n      window.addEventListener('resize', function() {\r\n        chart.resize()\r\n      })\r\n      \r\n      // 保存图表实例以便后续使用\r\n      this.completionChart = chart\r\n    },\r\n    \r\n    // 启动完成率图表自动滚动\r\n    startCompletionChartScroll() {\r\n      if (this.completionChartTimer) {\r\n        clearInterval(this.completionChartTimer)\r\n      }\r\n      \r\n      this.completionChartTimer = setInterval(() => {\r\n        if (this.completionChartScrollPaused) return\r\n        \r\n        if (!this.completionChart) return\r\n        \r\n        const option = this.completionChart.getOption()\r\n        let start = option.dataZoom[0].start\r\n        let end = option.dataZoom[0].end\r\n        const step = 0.1 // 每次滚动的百分比，改为0.1，使滚动非常缓慢\r\n        const range = end - start // 当前显示的范围\r\n        \r\n        // 根据滚动方向调整滚动位置\r\n        if (this.completionChartScrollDirection === 'down') {\r\n          // 向下滚动\r\n          if (end < 100) {\r\n            start += step\r\n            end += step\r\n          } else {\r\n            // 已到底部，改变方向\r\n            this.completionChartScrollDirection = 'up'\r\n          }\r\n        } else {\r\n          // 向上滚动\r\n          if (start > 0) {\r\n            start -= step\r\n            end -= step\r\n          } else {\r\n            // 已到顶部，改变方向\r\n            this.completionChartScrollDirection = 'down'\r\n          }\r\n        }\r\n        \r\n        // 更新滚动位置\r\n        option.dataZoom[0].start = start\r\n        option.dataZoom[0].end = end\r\n        option.dataZoom[1].start = start\r\n        option.dataZoom[1].end = end\r\n        this.completionChart.setOption(option)\r\n      }, 300) // 滚动间隔改为300毫秒，进一步减慢滚动速度\r\n    },\r\n    \r\n    // 初始化月度趋势折线图 - 显示单条折线\r\n    initMonthlyTrendChart() {\r\n      const chartDom = document.getElementById('monthlyTrendChart')\r\n      const chart = echarts.init(chartDom)\r\n      \r\n      // 查找当前部门的数据\r\n      const deptData = this.completionData.find(dept => dept.department === this.currentDepartment || dept.department.startsWith(this.currentDepartment))\r\n      \r\n      if (!deptData || !this.currentIndicator) {\r\n        return\r\n      }\r\n      \r\n      // 查找当前指标\r\n      const indicator = deptData.indicators.find(ind => ind.name === this.currentIndicator)\r\n      \r\n      if (!indicator) {\r\n        return\r\n      }\r\n      \r\n      // 准备月份数据\r\n      const months = ['1月', '2月', '3月', '4月']\r\n      \r\n      // 计算数据范围，以便设置y轴范围使折线居中\r\n      const values = indicator.values\r\n      const min = Math.min(...values)\r\n      const max = Math.max(...values)\r\n      const range = max - min\r\n      \r\n      // 设置y轴范围，扩大20%的范围使波动看起来更明显\r\n      const yMin = min - range * 0.4\r\n      const yMax = max + range * 0.4\r\n      \r\n      const option = {\r\n        title: {\r\n          text: `${this.currentDepartment} - ${this.currentIndicator}`,\r\n          left: 'center',\r\n          textStyle: {\r\n            fontSize: 16\r\n          }\r\n        },\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          formatter: function(params) {\r\n            return `${params[0].name}<br/>${indicator.name}: ${params[0].value} ${indicator.unit}<br/>目标值: ${indicator.target} ${indicator.unit}`\r\n          },\r\n          axisPointer: {\r\n            type: 'cross',\r\n            label: {\r\n              backgroundColor: '#6a7985'\r\n            }\r\n          }\r\n        },\r\n        grid: {\r\n          left: '5%',\r\n          right: '5%',\r\n          bottom: '10%',\r\n          top: '60px',\r\n          containLabel: true\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          boundaryGap: false,\r\n          data: months,\r\n          axisLine: {\r\n            lineStyle: {\r\n              width: 2\r\n            }\r\n          }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          name: indicator.unit,\r\n          min: yMin,\r\n          max: yMax,\r\n          axisLabel: {\r\n            formatter: function(value) {\r\n              return value.toFixed(2);\r\n            }\r\n          },\r\n          splitLine: {\r\n            lineStyle: {\r\n              type: 'dashed'\r\n            }\r\n          },\r\n          axisLine: {\r\n            lineStyle: {\r\n              width: 2\r\n            }\r\n          }\r\n        },\r\n        series: [\r\n          {\r\n            name: indicator.name,\r\n            type: 'line',\r\n            data: indicator.values,\r\n            smooth: true, // 平滑曲线\r\n            symbol: 'emptyCircle',\r\n            symbolSize: 10,\r\n            emphasis: {\r\n              itemStyle: {\r\n                shadowBlur: 10,\r\n                shadowColor: 'rgba(64, 158, 255, 0.5)'\r\n              },\r\n              scale: true\r\n            },\r\n            itemStyle: {\r\n              color: '#409EFF',\r\n              borderWidth: 2\r\n            },\r\n            lineStyle: {\r\n              width: 4,\r\n              shadowColor: 'rgba(0, 0, 0, 0.3)',\r\n              shadowBlur: 10,\r\n              shadowOffsetY: 5\r\n            },\r\n            areaStyle: {\r\n              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [\r\n                {\r\n                  offset: 0,\r\n                  color: 'rgba(64, 158, 255, 0.7)'\r\n                },\r\n                {\r\n                  offset: 0.5,\r\n                  color: 'rgba(64, 158, 255, 0.3)'\r\n                },\r\n                {\r\n                  offset: 1,\r\n                  color: 'rgba(64, 158, 255, 0.1)'\r\n                }\r\n              ])\r\n            },\r\n            markLine: {\r\n              silent: true,\r\n              lineStyle: {\r\n                color: '#F56C6C',\r\n                type: 'dashed',\r\n                width: 2\r\n              },\r\n              data: [\r\n                {\r\n                  yAxis: indicator.target,\r\n                  label: {\r\n                    formatter: `目标值: ${indicator.target}`,\r\n                    position: 'insideEndTop',\r\n                    fontSize: 12,\r\n                    backgroundColor: 'rgba(245, 108, 108, 0.2)',\r\n                    padding: [2, 4],\r\n                    borderRadius: 2\r\n                  }\r\n                }\r\n              ]\r\n            },\r\n            animationDuration: 2000,\r\n            animationEasing: 'elasticOut',\r\n            animationDelay: function (idx) {\r\n              return idx * 200;\r\n            }\r\n          },\r\n          {\r\n            name: '目标值',\r\n            type: 'line',\r\n            data: Array(months.length).fill(indicator.target),\r\n            lineStyle: {\r\n              color: '#F56C6C',\r\n              type: 'dashed',\r\n              width: 2\r\n            },\r\n            symbol: 'none'\r\n          }\r\n        ],\r\n        legend: {\r\n          data: [indicator.name, '目标值'],\r\n          bottom: '0%'\r\n        }\r\n      }\r\n      \r\n      chart.setOption(option)\r\n    },\r\n    \r\n    // 初始化能源消耗子图表\r\n    initEnergyDetailCharts() {\r\n      // 检查当前选择的部门数据是否有效\r\n      if (!this.currentEnergyDept || !this.factoryEnergyData) return;\r\n      \r\n      // 重置警告状态\r\n      for (let key in this.isEnergyChartWarning) {\r\n        this.isEnergyChartWarning[key] = false;\r\n      }\r\n      \r\n      // 为每种能源类型创建单独的图表\r\n      this.energyTypes.forEach(type => {\r\n        const chartDom = document.getElementById('energySubchart_' + type.value);\r\n        if (!chartDom) return;\r\n        \r\n        // 清除之前的实例\r\n        const existingChart = echarts.getInstanceByDom(chartDom);\r\n        if (existingChart) {\r\n          existingChart.dispose();\r\n        }\r\n        \r\n        const chart = echarts.init(chartDom);\r\n        \r\n        const data = this.factoryEnergyData[type.value][this.currentEnergyDept] || [];\r\n        const months = this.factoryEnergyData.months;\r\n        const targetRange = this.energyTargetRanges[type.value];\r\n        \r\n        // 计算数据的最小值和最大值，以便设置y轴的范围\r\n        const minValue = Math.min(...data);\r\n        const maxValue = Math.max(...data);\r\n        const valueRange = maxValue - minValue;\r\n        \r\n        // 设置y轴的最小值和最大值，以使折线居中并增加波动感\r\n        // 通过缩小y轴范围使折线显得更加曲折\r\n        const yMin = minValue - valueRange * 0.3; // 下方预留30%的空间\r\n        const yMax = maxValue + valueRange * 0.3; // 上方预留30%的空间\r\n        \r\n        const option = {\r\n          tooltip: {\r\n            trigger: 'axis',\r\n            formatter: function(params) {\r\n              const value = params[0].value;\r\n              return `${params[0].name}<br/>${type.label}: ${value} ${targetRange.unit}`;\r\n            }\r\n          },\r\n          grid: {\r\n            left: '10%',\r\n            right: '5%',\r\n            bottom: '15%',\r\n            top: '15%',\r\n            containLabel: true\r\n          },\r\n          xAxis: {\r\n            type: 'category',\r\n            data: months,\r\n            axisLine: {\r\n              lineStyle: {\r\n                color: '#333333' // 黑色坐标轴\r\n              }\r\n            },\r\n            axisLabel: {\r\n              color: '#333333' // 黑色坐标轴文字\r\n            },\r\n            boundaryGap: false // 让曲线从坐标轴开始\r\n          },\r\n          yAxis: {\r\n            type: 'value',\r\n            name: targetRange.unit,\r\n            min: yMin, // 设置最小值使折线居中\r\n            max: yMax, // 设置最大值使折线居中\r\n            axisLine: {\r\n              lineStyle: {\r\n                color: '#333333' // 黑色坐标轴\r\n              }\r\n            },\r\n            axisLabel: {\r\n              color: '#333333', // 黑色坐标轴文字\r\n              formatter: function(value) {\r\n                // 保留适当的小数位数以避免过度拥挤\r\n                if (value >= 100) {\r\n                  return value.toFixed(0);\r\n                } else {\r\n                  return value.toFixed(1);\r\n                }\r\n              }\r\n            },\r\n            splitLine: {\r\n              lineStyle: {\r\n                type: 'dashed',\r\n                color: '#E0E6F1'\r\n              }\r\n            }\r\n          },\r\n          series: [\r\n            {\r\n              name: type.label,\r\n              type: 'line',\r\n              data: data,\r\n              smooth: true, // 使用平滑曲线\r\n              smoothMonotone: 'none', // 不保持单调性，允许更多波动\r\n              symbol: 'circle',\r\n              symbolSize: 8,\r\n              sampling: 'average', // 使用平均采样\r\n              lineStyle: {\r\n                width: 4, // 加粗线条\r\n                color: type.color // 使用更深的颜色\r\n              },\r\n              itemStyle: {\r\n                color: type.color,\r\n                borderWidth: 2,\r\n                borderColor: '#fff',\r\n                shadowColor: 'rgba(0, 0, 0, 0.3)',\r\n                shadowBlur: 5\r\n              },\r\n              emphasis: {\r\n                itemStyle: {\r\n                  borderWidth: 3,\r\n                  shadowBlur: 10\r\n                },\r\n                lineStyle: {\r\n                  width: 6 // 鼠标悬停时线条更粗\r\n                }\r\n              },\r\n              areaStyle: {\r\n                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [\r\n                  {\r\n                    offset: 0,\r\n                    color: this.hexToRgba(type.color, 0.6) // 更高的透明度使颜色更深\r\n                  },\r\n                  {\r\n                    offset: 1,\r\n                    color: this.hexToRgba(type.color, 0.1)\r\n                  }\r\n                ])\r\n              }\r\n            }\r\n          ]\r\n        };\r\n        \r\n        chart.setOption(option);\r\n      });\r\n    },\r\n    \r\n    // 颜色转换辅助函数\r\n    hexToRgba(hex, alpha) {\r\n      const r = parseInt(hex.slice(1, 3), 16);\r\n      const g = parseInt(hex.slice(3, 5), 16);\r\n      const b = parseInt(hex.slice(5, 7), 16);\r\n      return `rgba(${r}, ${g}, ${b}, ${alpha})`;\r\n    },\r\n    \r\n    // 启动能源部门自动切换\r\n    startEnergyDeptSwitch() {\r\n      // 停止之前的能源部门切换\r\n      if (this.energyDeptTimer) {\r\n        clearInterval(this.energyDeptTimer)\r\n      }\r\n      \r\n      // 启动新的能源部门切换\r\n      this.energyDeptTimer = setInterval(() => {\r\n        this.switchEnergyDept()\r\n      }, 10000) // 每10秒切换一次部门，原来是5000\r\n    },\r\n    \r\n    // 切换能源部门\r\n    switchEnergyDept() {\r\n      const currentIndex = this.allFactories.indexOf(this.currentEnergyDept)\r\n      const nextIndex = (currentIndex + 1) % this.allFactories.length\r\n      this.currentEnergyDept = this.allFactories[nextIndex]\r\n      this.initEnergyDetailCharts()\r\n    },\r\n    \r\n    // 窗口大小变化时重新渲染图表\r\n    resizeCharts() {\r\n      const chartIds = [\r\n        'departmentCompletionChart',\r\n        'monthlyTrendChart'\r\n      ]\r\n      \r\n      chartIds.forEach(id => {\r\n        const chartDom = document.getElementById(id)\r\n        if (chartDom) {\r\n          const chart = echarts.getInstanceByDom(chartDom)\r\n          if (chart) {\r\n            chart.resize()\r\n          }\r\n        }\r\n      })\r\n      \r\n      // 调整能源子图表大小\r\n      this.energyTypes.forEach(type => {\r\n        const chartDom = document.getElementById('energySubchart_' + type.value)\r\n        if (chartDom) {\r\n          const chart = echarts.getInstanceByDom(chartDom)\r\n          if (chart) {\r\n            chart.resize()\r\n          }\r\n        }\r\n      })\r\n    },\r\n    \r\n    // 销毁图表实例\r\n    disposeCharts() {\r\n      const chartIds = [\r\n        'departmentCompletionChart',\r\n        'monthlyTrendChart'\r\n      ]\r\n      \r\n      chartIds.forEach(id => {\r\n        const chartDom = document.getElementById(id)\r\n        if (chartDom) {\r\n          // 移除事件监听器\r\n          if (id === 'departmentCompletionChart' && this.chartMouseOverHandler && this.chartMouseOutHandler) {\r\n            chartDom.removeEventListener('mouseover', this.chartMouseOverHandler)\r\n            chartDom.removeEventListener('mouseout', this.chartMouseOutHandler)\r\n          }\r\n          \r\n          const chart = echarts.getInstanceByDom(chartDom)\r\n          if (chart) {\r\n            chart.dispose()\r\n          }\r\n        }\r\n      })\r\n      \r\n      // 清除完成率图表引用\r\n      this.completionChart = null\r\n      \r\n      // 销毁能源子图表\r\n      this.energyTypes.forEach(type => {\r\n        const chartDom = document.getElementById('energySubchart_' + type.value)\r\n        if (chartDom) {\r\n          const chart = echarts.getInstanceByDom(chartDom)\r\n          if (chart) {\r\n            chart.dispose()\r\n          }\r\n        }\r\n      })\r\n    },\r\n    \r\n\r\n    // 启动事业部切换\r\n    startBusinessUnitSwitch() {\r\n      // 停止之前的事业部切换\r\n      if (this.businessUnitTimer) {\r\n        clearInterval(this.businessUnitTimer)\r\n      }\r\n      \r\n      // 启动新的事业部切换\r\n      this.businessUnitTimer = setInterval(() => {\r\n        this.switchBusinessUnit()\r\n      }, 30000) // 事业部切换频率为30秒\r\n    },\r\n    \r\n    // 切换事业部\r\n    switchBusinessUnit() {\r\n      const currentIndex = this.departments.indexOf(this.currentBusinessUnit)\r\n      const nextIndex = (currentIndex + 1) % this.departments.length\r\n      this.currentBusinessUnit = this.departments[nextIndex]\r\n    },\r\n    \r\n    // 初始化关键能源指标对比图\r\n    initKeyEnergyIndicatorsChart() {\r\n      const chartDom = document.getElementById('keyEnergyIndicatorsChart')\r\n      const chart = echarts.init(chartDom)\r\n      \r\n      const option = {\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: {\r\n            type: 'shadow'\r\n          },\r\n          formatter: function(params) {\r\n            const dataIndex = params[0].dataIndex\r\n            const indicator = this.keyEnergyIndicators[dataIndex]\r\n            const changeText = indicator.change > 0 ? `+${indicator.change}%` : `${indicator.change}%`\r\n            const changeColor = indicator.status === 'good' ? '#67C23A' : indicator.status === 'warning' ? '#E6A23C' : '#F56C6C'\r\n            \r\n            return `\r\n              <div style=\"font-weight:bold\">${indicator.name}</div>\r\n              <div>今日: ${indicator.today} ${indicator.unit}</div>\r\n              <div>昨日: ${indicator.yesterday} ${indicator.unit}</div>\r\n              <div>变化: <span style=\"color:${changeColor}\">${changeText}</span></div>\r\n            `\r\n          }.bind(this)\r\n        },\r\n        grid: {\r\n          left: '3%',\r\n          right: '4%',\r\n          bottom: '3%',\r\n          containLabel: true\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: this.keyEnergyIndicators.map(item => item.name)\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          axisLabel: {\r\n            formatter: function(value) {\r\n              if (value >= 100) {\r\n                return value.toFixed(0)\r\n              } else {\r\n                return value.toFixed(1)\r\n              }\r\n            }\r\n          }\r\n        },\r\n        series: [\r\n          {\r\n            name: '今日值',\r\n            type: 'bar',\r\n            data: this.keyEnergyIndicators.map((item, index) => {\r\n              return {\r\n                value: item.today,\r\n                itemStyle: {\r\n                  color: item.status === 'good' ? '#67C23A' : item.status === 'warning' ? '#E6A23C' : '#F56C6C'\r\n                }\r\n              }\r\n            }),\r\n            label: {\r\n              show: true,\r\n              position: 'top',\r\n              formatter: function(params) {\r\n                const index = params.dataIndex\r\n                return this.keyEnergyIndicators[index].today\r\n              }.bind(this)\r\n            },\r\n            barWidth: '30%'\r\n          },\r\n          {\r\n            name: '昨日值',\r\n            type: 'bar',\r\n            data: this.keyEnergyIndicators.map(item => item.yesterday),\r\n            barWidth: '30%',\r\n            itemStyle: {\r\n              color: '#909399',\r\n              opacity: 0.5\r\n            }\r\n          }\r\n        ],\r\n        legend: {\r\n          data: ['今日值', '昨日值'],\r\n          bottom: 0\r\n        }\r\n      }\r\n      \r\n      chart.setOption(option)\r\n    },\r\n    \r\n    // 根据索引位置计算渐变颜色样式\r\n    getGradientStyle(index, total) {\r\n      // 计算该指标在整体中的相对位置（0-1之间）\r\n      const position = index / (total - 1);\r\n      \r\n      // 定义绿色到红色的渐变颜色数组\r\n      const colorStops = [\r\n        '#52c41a', // 亮绿色\r\n        '#85ce61', // 绿色\r\n        '#b3e19d', // 浅绿色\r\n        '#d4f8be', // 非常浅的绿色\r\n        '#faff72', // 黄色\r\n        '#fadb14', // 金黄色\r\n        '#ffa940', // 橙色\r\n        '#fa8c16', // 深橙色\r\n        '#ff7875', // 浅红色\r\n        '#ff4d4f', // 红色\r\n        '#f5222d', // 亮红色\r\n        '#cf1322'  // 深红色\r\n      ];\r\n      \r\n      // 根据位置在渐变色之间插值获取颜色\r\n      // 找到对应的颜色区间\r\n      const segmentCount = colorStops.length - 1;\r\n      const segment = Math.min(Math.floor(position * segmentCount), segmentCount - 1);\r\n      const localPosition = (position * segmentCount) - segment; // 在当前区间内的相对位置 (0-1)\r\n      \r\n      // 获取区间的起止颜色\r\n      const startColor = colorStops[segment];\r\n      const endColor = colorStops[segment + 1];\r\n      \r\n      // 在两个颜色之间插值\r\n      const bgColor = this.interpolateColors(startColor, endColor, localPosition);\r\n      \r\n      // 计算文字颜色\r\n      // 提取RGB并计算亮度\r\n      let r = parseInt(bgColor.slice(1, 3), 16);\r\n      let g = parseInt(bgColor.slice(3, 5), 16);\r\n      let b = parseInt(bgColor.slice(5, 7), 16);\r\n      const brightness = (r * 299 + g * 587 + b * 114) / 1000;\r\n      \r\n      // 亮度大于140使用深色文字，否则使用浅色文字\r\n      let textColor = brightness > 140 ? '#333333' : '#ffffff';\r\n      \r\n      // 设置边框颜色（比背景色稍深）\r\n      const borderColor = this.adjustColor(bgColor, -20);\r\n      \r\n      return {\r\n        backgroundColor: bgColor,\r\n        color: textColor,\r\n        borderTopColor: borderColor\r\n      };\r\n    },\r\n    \r\n    // 颜色插值函数\r\n    interpolateColors(color1, color2, factor) {\r\n      // 解析颜色\r\n      let r1 = parseInt(color1.slice(1, 3), 16);\r\n      let g1 = parseInt(color1.slice(3, 5), 16);\r\n      let b1 = parseInt(color1.slice(5, 7), 16);\r\n      \r\n      let r2 = parseInt(color2.slice(1, 3), 16);\r\n      let g2 = parseInt(color2.slice(3, 5), 16);\r\n      let b2 = parseInt(color2.slice(5, 7), 16);\r\n      \r\n      // 线性插值\r\n      let r = Math.round(r1 + factor * (r2 - r1));\r\n      let g = Math.round(g1 + factor * (g2 - g1));\r\n      let b = Math.round(b1 + factor * (b2 - b1));\r\n      \r\n      // 转回十六进制\r\n      return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;\r\n    },\r\n    \r\n    // 获取徽章样式\r\n    getBadgeStyle(index, total) {\r\n      // 获取当前卡片的背景色\r\n      const position = index / (total - 1);\r\n      const colorStops = [\r\n        '#52c41a', // 亮绿色\r\n        '#85ce61', // 绿色\r\n        '#b3e19d', // 浅绿色\r\n        '#d4f8be', // 非常浅的绿色\r\n        '#faff72', // 黄色\r\n        '#fadb14', // 金黄色\r\n        '#ffa940', // 橙色\r\n        '#fa8c16', // 深橙色\r\n        '#ff7875', // 浅红色\r\n        '#ff4d4f', // 红色\r\n        '#f5222d', // 亮红色\r\n        '#cf1322'  // 深红色\r\n      ];\r\n      \r\n      const segmentCount = colorStops.length - 1;\r\n      const segment = Math.min(Math.floor(position * segmentCount), segmentCount - 1);\r\n      const localPosition = (position * segmentCount) - segment;\r\n      \r\n      const startColor = colorStops[segment];\r\n      const endColor = colorStops[segment + 1];\r\n      \r\n      const bgColor = this.interpolateColors(startColor, endColor, localPosition);\r\n      \r\n      // 计算文字颜色\r\n      let r = parseInt(bgColor.slice(1, 3), 16);\r\n      let g = parseInt(bgColor.slice(3, 5), 16);\r\n      let b = parseInt(bgColor.slice(5, 7), 16);\r\n      const brightness = (r * 299 + g * 587 + b * 114) / 1000;\r\n      \r\n      // 根据背景亮度设置徽章样式\r\n      if (brightness > 140) {\r\n        // 浅色背景使用深色边框的徽章\r\n        const badgeBgColor = this.adjustColor(bgColor, -40);\r\n        return {\r\n          backgroundColor: badgeBgColor,\r\n          color: '#ffffff',\r\n          boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)'\r\n        };\r\n      } else {\r\n        // 深色背景使用浅色徽章\r\n        return {\r\n          backgroundColor: 'rgba(255, 255, 255, 0.25)',\r\n          color: '#ffffff',\r\n          boxShadow: '0 2px 4px rgba(0, 0, 0, 0.2)'\r\n        };\r\n      }\r\n    },\r\n    \r\n    // 颜色调整辅助函数\r\n    adjustColor(color, amount) {\r\n      // 如果是白色特殊处理\r\n      if (color === '#ffffff') {\r\n        return '#e0e0e0';\r\n      }\r\n      \r\n      // 将颜色转换为RGB\r\n      let r = parseInt(color.slice(1, 3), 16);\r\n      let g = parseInt(color.slice(3, 5), 16);\r\n      let b = parseInt(color.slice(5, 7), 16);\r\n      \r\n      // 调整亮度\r\n      r = Math.max(0, Math.min(255, r + amount));\r\n      g = Math.max(0, Math.min(255, g + amount));\r\n      b = Math.max(0, Math.min(255, b + amount));\r\n      \r\n      // 转回十六进制\r\n      return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;\r\n    },\r\n    \r\n    // 初始化指标卡片滚动\r\n    initIndicatorCardsScroll() {\r\n      // 清除之前的滚动定时器\r\n      if (this.indicatorCardsScrollTimer) {\r\n        clearInterval(this.indicatorCardsScrollTimer);\r\n      }\r\n      \r\n      const container = this.$refs.indicatorCardsContainer;\r\n      if (!container) return;\r\n      \r\n      // 计算滚动所需参数\r\n      let scrollTop = 0;\r\n      const scrollHeight = container.scrollHeight;\r\n      const clientHeight = container.clientHeight;\r\n      const maxScroll = scrollHeight - clientHeight;\r\n      \r\n      // 如果内容不足以滚动，直接返回\r\n      if (maxScroll <= 0) return;\r\n      \r\n      // 滚动步长和速度\r\n      const step = 0.5; // 滚动步长更小，使滚动更平滑\r\n      const scrollInterval = 20; // 滚动更新频率更高，更流畅\r\n      \r\n      this.indicatorCardsScrollTimer = setInterval(() => {\r\n        // 鼠标悬停时暂停滚动\r\n        if (this.indicatorCardsScrollPaused) return;\r\n        \r\n        scrollTop += step;\r\n        \r\n        // 当滚动到底部时，快速回到顶部并继续滚动\r\n        if (scrollTop >= maxScroll) {\r\n          // 重置滚动位置到顶部\r\n          scrollTop = 0;\r\n          container.scrollTop = 0;\r\n          \r\n          // 短暂暂停一下，让用户能看到回到顶部的过程\r\n          this.indicatorCardsScrollPaused = true;\r\n          setTimeout(() => {\r\n            this.indicatorCardsScrollPaused = false;\r\n          }, 1000);\r\n        } else {\r\n          container.scrollTop = scrollTop;\r\n        }\r\n      }, scrollInterval);\r\n    },\r\n\r\n    // 表头样式\r\n    headerCellStyle() {\r\n      return {\r\n        backgroundColor: \"#f5f7fa\",\r\n        color: \"#606266\",\r\n        fontWeight: \"bold\",\r\n      };\r\n    },\r\n\r\n        // 显示指标详情\r\n        showDetails(row) {\r\n      this.currentIndicator = row;\r\n      this.detailDialogVisible = true;\r\n      this.$nextTick(() => {\r\n        this.initDetailChart();\r\n      });\r\n    },\r\n\r\n    // 初始化详情图表\r\n    initDetailChart() {\r\n      if (!this.currentIndicator) return;\r\n\r\n      const chartDom = document.getElementById(\"indicatorChart\");\r\n      const myChart = echarts.init(chartDom);\r\n\r\n      const months = [\"1月\", \"2月\", \"3月\", \"4月\", \"5月\"];\r\n      \r\n      // 生成波动较大的数据，确保围绕目标值有明显起伏\r\n      const targetValue = parseFloat(this.currentIndicator.target) || 100;\r\n      \r\n      // 使用实际数据，如果没有则生成波动数据\r\n      let actualData = [];\r\n      if (this.currentIndicator.monthlyData && this.currentIndicator.monthlyData.length === 5) {\r\n        actualData = this.currentIndicator.monthlyData;\r\n      } else {\r\n        // 生成波动数据，确保有起伏\r\n        const fluctuationRange = targetValue * 0.3; // 波动范围为目标值的30%\r\n        actualData = months.map(() => {\r\n          const fluctuation = (Math.random() - 0.5) * 2 * fluctuationRange;\r\n          return Math.max(0, targetValue + fluctuation);\r\n        });\r\n      }\r\n      \r\n      // 目标值线\r\n      const targetData = Array(5).fill(targetValue);\r\n\r\n      const option = {\r\n        title: {\r\n          text: `${this.currentIndicator.name}月度趋势`,\r\n          left: \"center\",\r\n        },\r\n        tooltip: {\r\n          trigger: \"axis\",\r\n          axisPointer: {\r\n            type: \"cross\",\r\n          },\r\n          formatter: function(params) {\r\n            const actualValue = params[0].value.toFixed(2);\r\n            const targetValue = params[1].value.toFixed(2);\r\n            const diff = (actualValue - targetValue).toFixed(2);\r\n            // 使用统一的白色显示差值\r\n            const diffColor = 'color:#ffffff';\r\n            \r\n            return `${params[0].name}<br/>\r\n                   ${params[0].marker} ${params[0].seriesName}: ${actualValue}<br/>\r\n                   ${params[1].marker} ${params[1].seriesName}: ${targetValue}<br/>\r\n                   <span style=\"${diffColor}\">差值: ${diff}</span>`;\r\n          }\r\n        },\r\n        legend: {\r\n          data: [\"实际值\", \"目标值\"],\r\n          bottom: 10,\r\n        },\r\n        grid: {\r\n          left: \"3%\",\r\n          right: \"4%\",\r\n          bottom: \"15%\",\r\n          containLabel: true,\r\n        },\r\n        xAxis: {\r\n          type: \"category\",\r\n          boundaryGap: false,\r\n          data: months,\r\n        },\r\n        yAxis: {\r\n          type: \"value\",\r\n          name: this.currentIndicator.unit,\r\n          axisLabel: {\r\n            formatter: \"{value}\",\r\n          },\r\n          scale: true, // 缩放Y轴以突出显示数据波动\r\n        },\r\n        series: [\r\n          {\r\n            name: \"实际值\",\r\n            type: \"line\",\r\n            data: actualData,\r\n            itemStyle: {\r\n              color: \"#409EFF\",\r\n            },\r\n            lineStyle: {\r\n              width: 3,\r\n            },\r\n            symbol: \"circle\",\r\n            symbolSize: 8,\r\n            markPoint: {\r\n              data: [\r\n                { type: \"max\", name: \"最大值\" },\r\n                { type: \"min\", name: \"最小值\" }\r\n              ]\r\n            }\r\n          },\r\n          {\r\n            name: \"目标值\",\r\n            type: \"line\",\r\n            data: targetData,\r\n            itemStyle: {\r\n              color: \"#F56C6C\",\r\n            },\r\n            lineStyle: {\r\n              width: 2,\r\n              type: \"dashed\",\r\n            },\r\n            markLine: {\r\n              data: [{ type: \"average\", name: \"目标值\" }],\r\n              label: {\r\n                formatter: \"目标值: {c}\"\r\n              }\r\n            }\r\n          },\r\n        ],\r\n      };\r\n\r\n      myChart.setOption(option);\r\n\r\n      // 响应式处理\r\n      window.addEventListener(\"resize\", () => {\r\n        myChart.resize();\r\n      });\r\n    },\r\n\r\n    // 关闭对话框\r\n    handleDialogClose() {\r\n      this.detailDialogVisible = false;\r\n      this.currentIndicator = null;\r\n      // 清除图表实例\r\n      const chartDom = document.getElementById(\"indicatorChart\");\r\n      if (chartDom) {\r\n        const chart = echarts.getInstanceByDom(chartDom);\r\n        if (chart) {\r\n          chart.dispose();\r\n        }\r\n      }\r\n    },\r\n        // 处理表格单元格合并\r\n        objectSpanMethod({ row, column, rowIndex, columnIndex }) {\r\n      // 仅对分厂和指标名称列进行合并\r\n      if (columnIndex === 0 || columnIndex === 1) {\r\n        // 获取当前行数据\r\n        const currentRow = this.techIndicators[rowIndex];\r\n        if (!currentRow) return { rowspan: 1, colspan: 1 };\r\n\r\n        // 分厂列合并处理\r\n        if (columnIndex === 0) {\r\n          // 如果是第一行或者与前一行分厂不同，则计算合并行数\r\n          if (\r\n            rowIndex === 0 ||\r\n            currentRow.factory !== this.techIndicators[rowIndex - 1].factory\r\n          ) {\r\n            // 计算连续相同分厂的行数\r\n            let rowspan = 1;\r\n            for (let i = rowIndex + 1; i < this.techIndicators.length; i++) {\r\n              if (this.techIndicators[i].factory === currentRow.factory) {\r\n                rowspan++;\r\n              } else {\r\n                break;\r\n              }\r\n            }\r\n            return { rowspan, colspan: 1 };\r\n          } else {\r\n            // 如果当前行与前一行分厂相同，则隐藏当前单元格\r\n            return { rowspan: 0, colspan: 0 };\r\n          }\r\n        }\r\n\r\n        // 指标名称列合并处理\r\n        if (columnIndex === 1) {\r\n          // 如果是第一行，或者与前一行分厂不同，或者分厂相同但指标名称不同\r\n          if (\r\n            rowIndex === 0 ||\r\n            currentRow.factory !== this.techIndicators[rowIndex - 1].factory ||\r\n            currentRow.name !== this.techIndicators[rowIndex - 1].name\r\n          ) {\r\n            // 计算连续相同分厂和指标名称的行数\r\n            let rowspan = 1;\r\n            for (let i = rowIndex + 1; i < this.techIndicators.length; i++) {\r\n              if (\r\n                this.techIndicators[i].factory === currentRow.factory &&\r\n                this.techIndicators[i].name === currentRow.name\r\n              ) {\r\n                rowspan++;\r\n              } else {\r\n                break;\r\n              }\r\n            }\r\n            return { rowspan, colspan: 1 };\r\n          } else {\r\n            // 如果当前行与前一行分厂和指标名称都相同，则隐藏当前单元格\r\n            return { rowspan: 0, colspan: 0 };\r\n          }\r\n        }\r\n      }\r\n      return { rowspan: 1, colspan: 1 };\r\n    },\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.app-container {\r\n  padding: 20px;\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n.page-title {\r\n  text-align: center;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.page-title h2 {\r\n  font-size: 24px;\r\n  color: #303133;\r\n  margin: 0;\r\n}\r\n\r\n.box-card {\r\n  margin-bottom: 20px;\r\n  border-radius: 8px;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.box-card:hover {\r\n  transform: translateY(-5px);\r\n  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.section-header {\r\n  font-weight: bold;\r\n  font-size: 18px;\r\n  color: #333;\r\n  padding: 5px 0;\r\n  border-bottom: 2px solid rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.tech-economic-section .section-header {\r\n  color: #4a6ee0;\r\n  border-bottom-color: #4a6ee0;\r\n}\r\n\r\n.energy-section .section-header {\r\n  color: #47b475;\r\n  border-bottom-color: #47b475;\r\n}\r\n\r\n.chart-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  margin: 0 -10px;\r\n}\r\n\r\n.top-charts-row,\r\n.energy-charts-row {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  flex-wrap: nowrap;\r\n  width: 100%;\r\n  height: 600px!important;\r\n}\r\n\r\n.chart-item {\r\n  flex: 1;\r\n  min-width: 32%;\r\n  margin: 10px;\r\n  background-color: #fff;\r\n  border-radius: 6px;\r\n  padding: 15px;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.chart-title {\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  margin-bottom: 15px;\r\n  color: #303133;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.trend-controls {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.chart {\r\n  height: 340px;\r\n  width: 100%;\r\n}\r\n\r\n.scroll-table-container {\r\n  height: 340px;\r\n  overflow: hidden;\r\n  position: relative;\r\n}\r\n\r\n.scroll-table {\r\n  height: 100%;\r\n  overflow-y: hidden; /* 修改为hidden，使用JS控制滚动 */\r\n  scrollbar-width: thin;\r\n}\r\n\r\n.completion-rate {\r\n  font-weight: bold;\r\n}\r\n\r\n.completion-rate.red {\r\n  color: #F56C6C;\r\n}\r\n\r\n.completion-rate.deep-red {\r\n  color: #ff0000;\r\n  font-weight: bolder;\r\n}\r\n\r\n.completion-rate.green {\r\n  color: #67C23A;\r\n}\r\n\r\n.energy-stats {\r\n  padding: 10px;\r\n}\r\n\r\n.stat-card {\r\n  background-color: #fff;\r\n  padding: 15px;\r\n  border-radius: 6px;\r\n  text-align: center;\r\n  margin-bottom: 15px;\r\n  border-left: 4px solid #409EFF;\r\n  transition: all 0.3s;\r\n  height: 100px;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: space-between;\r\n}\r\n\r\n.stat-card:hover {\r\n  transform: translateY(-3px);\r\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.stat-card.good {\r\n  border-left-color: #67C23A;\r\n}\r\n\r\n.stat-card.warning {\r\n  border-left-color: #E6A23C;\r\n}\r\n\r\n.stat-card.danger {\r\n  border-left-color: #F56C6C;\r\n}\r\n\r\n.stat-title {\r\n  font-size: 14px;\r\n  color: #606266;\r\n}\r\n\r\n.stat-value {\r\n  font-size: 20px;\r\n  font-weight: bold;\r\n  color: #303133;\r\n  margin: 5px 0;\r\n}\r\n\r\n.stat-change {\r\n  font-size: 12px;\r\n  color: #909399;\r\n}\r\n\r\n.pagination-wrapper {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n/* 动画效果 */\r\n.box-card {\r\n  animation: fadeIn 0.6s ease-in-out;\r\n}\r\n\r\n@keyframes fadeIn {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(20px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n.tech-economic-section {\r\n  animation-delay: 0.1s;\r\n  background-color: #e6f0ff; /* 修改技经指标部分的背景色，更深的蓝色背景 */\r\n}\r\n\r\n.energy-section {\r\n  animation-delay: 0.3s;\r\n  background-color: #e6fff0; /* 修改能源指标部分的背景色，更深的绿色背景 */\r\n}\r\n\r\n/* 修改卡片背景色 */\r\n.chart-item {\r\n  background-color: #ffffff; /* 白色背景 */\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); /* 增强阴影 */\r\n  border: 1px solid rgba(0, 0, 0, 0.05); /* 添加细边框 */\r\n}\r\n\r\n/* 技经指标部分的卡片特殊样式 */\r\n.tech-economic-section .chart-item {\r\n  background-color: #f8faff; /* 浅蓝色调背景 */\r\n  border-top: 3px solid #4a6ee0; /* 蓝色上边框 */\r\n}\r\n\r\n/* 能源指标部分的卡片特殊样式 */\r\n.energy-section .chart-item {\r\n  background-color: #f8fff9; /* 浅绿色调背景 */\r\n  border-top: 3px solid #47b475; /* 绿色上边框 */\r\n}\r\n\r\n/* 媒体查询，适应不同屏幕尺寸 */\r\n@media screen and (max-width: 1600px) {\r\n  .top-charts-row,\r\n  .energy-charts-row {\r\n    flex-wrap: wrap;\r\n  }\r\n  \r\n  .chart-item {\r\n    min-width: 45%;\r\n    flex: 0 0 45%;\r\n  }\r\n}\r\n\r\n@media screen and (max-width: 1200px) {\r\n  .chart-item {\r\n    min-width: 100%;\r\n    flex: 0 0 100%;\r\n  }\r\n}\r\n\r\n/* 未完成指标表格样式 */\r\n.el-table {\r\n  border: none;\r\n}\r\n\r\n.el-table::before, .el-table::after {\r\n  content: none;\r\n}\r\n\r\n.el-table td.el-table__cell,\r\n.el-table th.el-table__cell {\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.el-table th.el-table__cell {\r\n  background-color: #f8f8f8;\r\n  color: #606266;\r\n  font-weight: bold;\r\n  font-size: 15px;\r\n}\r\n\r\n.el-table td.el-table__cell {\r\n  color: #606266;\r\n  font-size: 14px;\r\n  padding: 8px 0;\r\n}\r\n\r\n.el-table__row:hover > td {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n.energy-type-selector {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.energy-detail-container {\r\n  height: 340px;\r\n  overflow: auto;\r\n}\r\n\r\n.status-indicator {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 4px 8px;\r\n  border-radius: 4px;\r\n  font-weight: bold;\r\n}\r\n\r\n.status-indicator.normal {\r\n  background-color: #f0f9eb;\r\n  color: #67C23A;\r\n}\r\n\r\n.status-indicator.warning {\r\n  background-color: #fdf6ec;\r\n  color: #E6A23C;\r\n}\r\n\r\n.status-indicator.danger {\r\n  background-color: #fef0f0;\r\n  color: #F56C6C;\r\n}\r\n\r\n.status-indicator i {\r\n  margin-left: 5px;\r\n  font-size: 16px;\r\n}\r\n\r\n.trend-value {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-weight: bold;\r\n}\r\n\r\n.trend-value.up {\r\n  color: #F56C6C;\r\n}\r\n\r\n.trend-value.down {\r\n  color: #67C23A;\r\n}\r\n\r\n.trend-value i {\r\n  margin-right: 3px;\r\n}\r\n\r\n.key-indicators-container {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  justify-content: space-between;\r\n  margin-top: 20px;\r\n  height: 500px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.indicator-card {\r\n  flex: 0 0 23%;\r\n  margin-bottom: 12px;\r\n  padding: 10px;\r\n  background-color: #f0f7ff;\r\n  border-radius: 6px;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);\r\n  text-align: center;\r\n  position: relative;\r\n  border-left: 4px solid #409EFF;\r\n  transition: all 0.3s;\r\n  height: 125px;\r\n}\r\n\r\n.indicator-card.danger {\r\n  background-color: #dc143c; /* 猩红色 */\r\n  color: white;\r\n}\r\n\r\n.indicator-card.danger .indicator-title,\r\n.indicator-card.danger .indicator-value,\r\n.indicator-card.danger .indicator-target,\r\n.indicator-card.danger .indicator-unit,\r\n.indicator-card.danger .indicator-compare {\r\n  color: white;\r\n}\r\n\r\n.indicator-card:hover {\r\n  transform: translateY(-3px);\r\n  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.12);\r\n}\r\n\r\n.indicator-card.good {\r\n  border-left-color: #67C23A;\r\n  background-color: #f0fff5;\r\n}\r\n\r\n.indicator-card.warning {\r\n  border-left-color: #E6A23C;\r\n  background-color: #fffbf0;\r\n}\r\n\r\n.indicator-card.danger {\r\n  border-left-color: #F56C6C;\r\n}\r\n\r\n.indicator-title {\r\n  font-size: 14px;\r\n  color: #606266;\r\n  margin-bottom: 3px;\r\n  font-weight: bold;\r\n}\r\n\r\n.indicator-value {\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n  color: #303133;\r\n  margin: 3px 0;\r\n}\r\n\r\n.indicator-target {\r\n  font-size: 12px;\r\n  color: #909399;\r\n  margin-bottom: 3px;\r\n}\r\n\r\n.indicator-unit {\r\n  font-size: 12px;\r\n  color: #909399;\r\n}\r\n\r\n.indicator-compare {\r\n  font-size: 12px;\r\n  color: #606266;\r\n}\r\n\r\n.indicator-change.up {\r\n  color: #67C23A;\r\n}\r\n\r\n.indicator-change.down {\r\n  color: #F56C6C;\r\n}\r\n\r\n.energy-chart-half {\r\n  flex: 0 0 48%;\r\n  min-width: 48%;\r\n}\r\n\r\n.energy-subchart-container {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  justify-content: space-between;\r\n  height: 500px;\r\n}\r\n\r\n.energy-subchart {\r\n  flex: 0 0 48%;\r\n  height: 48%;\r\n  margin-bottom: 10px;\r\n  border-radius: 4px;\r\n  padding: 5px;\r\n  background-color: #f0f8ff;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  transition: all 0.3s;\r\n  border: 1px solid #d1e6ff;\r\n}\r\n\r\n.energy-chart-warning {\r\n  background-color: rgba(245, 108, 108, 0.1);\r\n  border: 1px solid rgba(245, 108, 108, 0.3);\r\n}\r\n\r\n.subchart-title {\r\n  font-size: 14px;\r\n  font-weight: bold;\r\n  color: #303133;\r\n  text-align: center;\r\n  margin-bottom: 5px;\r\n  background-color: #e6f0ff;\r\n  padding: 3px 0;\r\n  border-radius: 3px;\r\n}\r\n\r\n.subchart {\r\n  height: calc(100% - 0px);\r\n  width: 100%;\r\n}\r\n\r\n.chart-wrapper {\r\n  position: relative;\r\n  width: 100%;\r\n  height: 400px;\r\n  overflow: hidden;\r\n}\r\n\r\n.chart {\r\n  height: 100%;\r\n  width: 100%;\r\n}\r\n\r\n/* 指标卡片样式 */\r\n.indicator-cards-container {\r\n  height: 320px;\r\n  overflow-y: auto;\r\n  overflow-x: hidden;\r\n  position: relative;\r\n  scrollbar-width: thin;\r\n  scrollbar-color: #e0e0e0 #f8f8f8;\r\n  padding: 6px 3px;\r\n}\r\n\r\n.indicator-cards-container::-webkit-scrollbar {\r\n  width: 6px;\r\n}\r\n\r\n.indicator-cards-container::-webkit-scrollbar-track {\r\n  background: #f8f8f8;\r\n}\r\n\r\n.indicator-cards-container::-webkit-scrollbar-thumb {\r\n  background-color: #e0e0e0;\r\n  border-radius: 3px;\r\n}\r\n\r\n.indicator-cards-wrapper {\r\n  display: grid;\r\n  grid-template-columns: repeat(3, 1fr); /* 一行显示3个 */\r\n  gap: 10px; /* 更小的间距 */\r\n  max-width: 100%;\r\n}\r\n\r\n.indicator-card-item {\r\n  background-color: #ffffff;\r\n  border-radius: 6px;\r\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);\r\n  padding: 8px;\r\n  transition: all 0.3s ease;\r\n  border-top: 3px solid #909399;\r\n  display: flex;\r\n  flex-direction: column;\r\n  height: 110px; /* 更小的高度 */\r\n  width: 100%;\r\n  max-width: 100%;\r\n  margin: 0 auto; /* 居中显示 */\r\n}\r\n\r\n.indicator-card-item:hover {\r\n  transform: translateY(-3px);\r\n  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.12);\r\n}\r\n\r\n.indicator-card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.indicator-name {\r\n  font-size: 13px;\r\n  font-weight: bold;\r\n  flex: 1;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.completion-badge {\r\n  background-color: #f0f0f0;\r\n  padding: 1px 5px;\r\n  border-radius: 6px;\r\n  font-size: 10px;\r\n  font-weight: bold;\r\n  min-width: 40px;\r\n  text-align: center;\r\n  transition: all 0.3s ease;\r\n  margin-left: 4px;\r\n}\r\n\r\n.indicator-card-body {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: space-between;\r\n}\r\n\r\n.indicator-department {\r\n  font-size: 11px;\r\n  margin-bottom: 4px;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  font-weight: 500;\r\n}\r\n\r\n.indicator-values {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 3px;\r\n}\r\n\r\n.value-item {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 2px;\r\n  background-color: rgba(255, 255, 255, 0.15);\r\n  padding: 1px 4px;\r\n  border-radius: 2px;\r\n}\r\n\r\n.value-label {\r\n  font-size: 10px;\r\n  font-weight: 500;\r\n}\r\n\r\n.value-number {\r\n  font-size: 11px;\r\n  font-weight: 600;\r\n}\r\n\r\n/* 不同完成率的卡片样式 */\r\n.indicator-card-item.excellent {\r\n  background-color: #67c23a;\r\n  border-top-color: #4d9e29;\r\n}\r\n\r\n.indicator-card-item.excellent .indicator-name,\r\n.indicator-card-item.excellent .indicator-department,\r\n.indicator-card-item.excellent .value-number,\r\n.indicator-card-item.excellent .value-label {\r\n  color: #ffffff;\r\n}\r\n\r\n.indicator-card-item.excellent .completion-badge {\r\n  background-color: #4d9e29;\r\n  color: #ffffff;\r\n}\r\n\r\n.indicator-card-item.very-good {\r\n  background-color: #85ce61;\r\n  border-top-color: #67c23a;\r\n}\r\n\r\n.indicator-card-item.very-good .indicator-name,\r\n.indicator-card-item.very-good .indicator-department,\r\n.indicator-card-item.very-good .value-number,\r\n.indicator-card-item.very-good .value-label {\r\n  color: #ffffff;\r\n}\r\n\r\n.indicator-card-item.very-good .completion-badge {\r\n  background-color: #67c23a;\r\n  color: #ffffff;\r\n}\r\n\r\n.indicator-card-item.good {\r\n  background-color: #b3e19d;\r\n  border-top-color: #85ce61;\r\n}\r\n\r\n.indicator-card-item.good .indicator-name,\r\n.indicator-card-item.good .indicator-department,\r\n.indicator-card-item.good .value-number {\r\n  color: #2e2e2e;\r\n}\r\n\r\n.indicator-card-item.good .completion-badge {\r\n  background-color: #85ce61;\r\n  color: #ffffff;\r\n}\r\n\r\n.indicator-card-item.normal {\r\n  background-color: #f0f9eb;\r\n  border-top-color: #b3e19d;\r\n}\r\n\r\n.indicator-card-item.warning {\r\n  background-color: #fdf6ec;\r\n  border-top-color: #e6a23c;\r\n}\r\n\r\n.indicator-card-item.warning .completion-badge {\r\n  background-color: #e6a23c;\r\n  color: #ffffff;\r\n}\r\n\r\n.indicator-card-item.bad {\r\n  background-color: #fef0f0;\r\n  border-top-color: #f56c6c;\r\n}\r\n\r\n.indicator-card-item.bad .completion-badge {\r\n  background-color: #f56c6c;\r\n  color: #ffffff;\r\n}\r\n\r\n.indicator-card-item.very-bad {\r\n  background-color: #f56c6c;\r\n  border-top-color: #d63b3b;\r\n}\r\n\r\n.indicator-card-item.very-bad .indicator-name,\r\n.indicator-card-item.very-bad .indicator-department,\r\n.indicator-card-item.very-bad .value-number,\r\n.indicator-card-item.very-bad .value-label {\r\n  color: #ffffff;\r\n}\r\n\r\n.indicator-card-item.very-bad .completion-badge {\r\n  background-color: #d63b3b;\r\n  color: #ffffff;\r\n}\r\n\r\n.indicator-card-item.terrible {\r\n  background-color: #dc143c; /* 猩红色 */\r\n  border-top-color: #a00f2d;\r\n}\r\n\r\n.indicator-card-item.terrible .indicator-name,\r\n.indicator-card-item.terrible .indicator-department,\r\n.indicator-card-item.terrible .value-number,\r\n.indicator-card-item.terrible .value-label {\r\n  color: #ffffff;\r\n}\r\n\r\n.indicator-card-item.terrible .completion-badge {\r\n  background-color: #a00f2d;\r\n  color: #ffffff;\r\n}\r\n</style>\r\n\r\n"]}]}