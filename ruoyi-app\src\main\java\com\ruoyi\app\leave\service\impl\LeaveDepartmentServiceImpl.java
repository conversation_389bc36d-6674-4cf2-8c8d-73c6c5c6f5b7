package com.ruoyi.app.leave.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.app.leave.mapper.LeaveDepartmentMapper;
import com.ruoyi.app.leave.domain.LeaveDepartment;
import com.ruoyi.app.leave.service.ILeaveDepartmentService;

/**
 * 出门证部门（厂内单位）Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-03-13
 */
@Service
public class LeaveDepartmentServiceImpl implements ILeaveDepartmentService 
{
    @Autowired
    private LeaveDepartmentMapper leaveDepartmentMapper;

    /**
     * 查询出门证部门（厂内单位）
     * 
     * @param id 出门证部门（厂内单位）ID
     * @return 出门证部门（厂内单位）
     */
    @Override
    public LeaveDepartment selectLeaveDepartmentById(Long id)
    {
        return leaveDepartmentMapper.selectLeaveDepartmentById(id);
    }

    /**
     * 查询出门证部门（厂内单位）列表
     * 
     * @param leaveDepartment 出门证部门（厂内单位）
     * @return 出门证部门（厂内单位）
     */
    @Override
    public List<LeaveDepartment> selectLeaveDepartmentList(LeaveDepartment leaveDepartment)
    {
        return leaveDepartmentMapper.selectLeaveDepartmentList(leaveDepartment);
    }

    /**
     * 新增出门证部门（厂内单位）
     * 
     * @param leaveDepartment 出门证部门（厂内单位）
     * @return 结果
     */
    @Override
    public int insertLeaveDepartment(LeaveDepartment leaveDepartment)
    {
        leaveDepartment.setCreateTime(DateUtils.getNowDate());
        return leaveDepartmentMapper.insertLeaveDepartment(leaveDepartment);
    }

    /**
     * 修改出门证部门（厂内单位）
     * 
     * @param leaveDepartment 出门证部门（厂内单位）
     * @return 结果
     */
    @Override
    public int updateLeaveDepartment(LeaveDepartment leaveDepartment)
    {
        leaveDepartment.setUpdateTime(DateUtils.getNowDate());
        return leaveDepartmentMapper.updateLeaveDepartment(leaveDepartment);
    }

    /**
     * 批量删除出门证部门（厂内单位）
     * 
     * @param ids 需要删除的出门证部门（厂内单位）ID
     * @return 结果
     */
    @Override
    public int deleteLeaveDepartmentByIds(Long[] ids)
    {
        return leaveDepartmentMapper.deleteLeaveDepartmentByIds(ids);
    }

    /**
     * 删除出门证部门（厂内单位）信息
     * 
     * @param id 出门证部门（厂内单位）ID
     * @return 结果
     */
    @Override
    public int deleteLeaveDepartmentById(Long id)
    {
        return leaveDepartmentMapper.deleteLeaveDepartmentById(id);
    }
}
