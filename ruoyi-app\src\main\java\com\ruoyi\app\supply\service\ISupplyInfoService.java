package com.ruoyi.app.supply.service;

import com.ruoyi.app.supply.domain.SupplyInfo;
import com.ruoyi.common.core.page.TableDataInfo;

import java.util.List;

/**
 * 供应商信息Service接口
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface ISupplyInfoService {
    
    /**
     * 查询供应商信息
     *
     * @param supplyCode 供应商代码
     * @return 供应商信息
     */
    public SupplyInfo selectSupplyInfoByCode(Integer supplyCode);

    /**
     * 查询供应商信息列表
     *
     * @param supplyInfo 供应商信息
     * @return 供应商信息集合
     */
    public List<SupplyInfo> selectSupplyInfoList(SupplyInfo supplyInfo);

    /**
     * 查询供应商信息分页列表
     *
     * @param supplyInfo 供应商信息
     * @return 供应商信息分页数据
     */
    public TableDataInfo selectSupplyInfoTable(SupplyInfo supplyInfo);

    /**
     * 新增供应商信息
     *
     * @param supplyInfo 供应商信息
     * @return 结果
     */
    public int insertSupplyInfo(SupplyInfo supplyInfo);

    /**
     * 修改供应商信息
     *
     * @param supplyInfo 供应商信息
     * @return 结果
     */
    public int updateSupplyInfo(SupplyInfo supplyInfo);

    /**
     * 批量删除供应商信息
     *
     * @param supplyCodes 需要删除的供应商代码主键集合
     * @return 结果
     */
    public int deleteSupplyInfoByCodes(Integer[] supplyCodes);

    /**
     * 删除供应商信息信息
     *
     * @param supplyCode 供应商代码主键
     * @return 结果
     */
    public int deleteSupplyInfoByCode(Integer supplyCode);
}
