{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\leave\\plan\\task.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\leave\\plan\\task.vue", "mtime": 1756084866774}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBnZXRUYXNrLCBnZXRUYXNrQnlUYXNrTm8sIGdldFRhc2ttYXRlcmlhbHMsIGdldFByb2Nlc3NMaXN0LCBnZXREaXJlY3RTdXBwbHlQbGFucywgZ2V0RGlyZWN0U3VwcGx5UGxhbkFuZFRhc2tEZXRhaWwsIGhhbmRsZVVubG9hZCwgaGFuZGxlU3RvY2tPdXQsIGlzQWxsb3dEaXNwYXRjaCwgZ2V0UGxhbk1hdGVyaWFscywgZWRpdFRhc2ttYXRlcmlhbHMsIGdldFRhc2tMb2dzLCBhZGRMZWF2ZUxvZywgdXBkYXRlVGFzaywgYWRkTGVhdmVMb2dBbmRFZGl0VGFza01hdGVyaWFsc0FuZFVwZGF0ZVRhc2sgfSBmcm9tICJAL2FwaS9sZWF2ZS90YXNrIjsNCmltcG9ydCB7IGRldGFpbFBsYW4gfSBmcm9tICJAL2FwaS9sZWF2ZS9wbGFuIjsNCmltcG9ydCB7IGxpc3RYY3RnRHJpdmVyQ2FyLCBnZXRYY3RnRHJpdmVyQ2FyLCBkZWxYY3RnRHJpdmVyQ2FyLCBhZGRYY3RnRHJpdmVyQ2FyLCB1cGRhdGVYY3RnRHJpdmVyQ2FyLCBleHBvcnRYY3RnRHJpdmVyQ2FyIH0gZnJvbSAiQC9hcGkvdHJ1Y2svY29tbW9uL3hjdGdEcml2ZXJDYXIiOw0KaW1wb3J0IHsgTWVzc2FnZSB9IGZyb20gImVsZW1lbnQtdWkiOw0KaW1wb3J0IFFSQ29kZSBmcm9tICJxcmNvZGVqczIiOw0KDQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogIkRpc3BhdGNoVGFza0RldGFpbCIsDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIGZhY3RvcnlDb25maXJtRGlhbG9nVmlzaWJsZTogZmFsc2UsDQogICAgICBmYWN0b3J5Q29uZmlybUZvcm06IHsNCiAgICAgICAgY29tcGFueU5hbWU6ICcnLA0KICAgICAgICB0YXNrTm86ICcnLA0KICAgICAgICBhcHBseU5vOiAnJywNCiAgICAgICAgcGxhbk5vOiAnJywNCiAgICAgICAgdGFza1R5cGU6IG51bGwsDQogICAgICAgIHVubG9hZGluZ1dvcmtObzogJycsDQogICAgICAgIHVubG9hZGluZ1RpbWU6IG51bGwsDQogICAgICAgIHNwZWMxTGVuZ3RoOiBudWxsLA0KICAgICAgICBzcGVjMldpZHRoOiBudWxsLA0KICAgICAgICB0b3RhbHM6ICcnLA0KICAgICAgICB0b3RhbDogJycsDQogICAgICAgIHRvdGFsVW5pdDogJycsDQogICAgICAgIHByb2Nlc3NUeXBlOiAnJywNCiAgICAgICAgaGVhdE5vOiAnJywNCiAgICAgICAgc3RlZWxHcmFkZTogJycsDQogICAgICAgIGF4bGVzOiAnJywNCiAgICAgICAgcmVtYXJrOiAnJywNCiAgICAgICAgdGFza1N0YXR1czogOSwgLy8g5a6M5oiQ54q25oCBDQogICAgICAgIGNhck51bTogJycsIC8vIOi9pueJjOWPtw0KICAgICAgICAvLyDlh7rlupPkv6Hmga8NCiAgICAgICAgc3RvY2tPdXRTcGVjMUxlbmd0aDogbnVsbCwNCiAgICAgICAgc3RvY2tPdXRTcGVjMldpZHRoOiBudWxsLA0KICAgICAgICBzdG9ja091dFRvdGFsczogJycsDQogICAgICAgIHN0b2NrT3V0VG90YWxVbml0OiAnJywNCiAgICAgICAgc3RvY2tPdXRUb3RhbDogJycsDQogICAgICAgIHN0b2NrT3V0UHJvY2Vzc1R5cGU6ICcnLA0KICAgICAgICBzdG9ja091dEhlYXRObzogJycsDQogICAgICAgIHN0b2NrT3V0U3RlZWxHcmFkZTogJycsDQogICAgICAgIHN0b2NrT3V0QXhsZXM6ICcnLA0KICAgICAgICBzdG9ja091dFJlbWFyazogJycsDQogICAgICAgIGhhbmRsZWRNYXRlcmlhbE5hbWU6ICcnLA0KICAgICAgICBzb3VyY2VDb21wYW55OiAnJywNCiAgICAgICAgcmVjZWl2ZUNvbXBhbnk6ICcnLA0KICAgICAgICBzaG93RHJvcGRvd246IGZhbHNlLA0KICAgICAgICBleHRyYU9wdGlvbjogJycsDQogICAgICAgIGRlZHVjdFdlaWdodDogbnVsbCwgLy8g5re75Yqg5omj6YeN5a2X5q61DQogICAgICB9LA0KICAgICAgb3B0aW9uRGlhbG9nVmlzaWJsZTogZmFsc2UsDQogICAgICBzZWFyY2hGb3JtOiB7DQogICAgICAgIHBsYW5ObzogJycsDQogICAgICAgIGFwcGx5Tm86ICcnLA0KICAgICAgICByZWNlaXZlQ29tcGFueTogJycNCiAgICAgIH0sDQogICAgICBvcHRpb25MaXN0OiBbXSwNCiAgICAgIGVkaXREb29yTWFuU3RhdHVzOiBmYWxzZSwNCiAgICAgIGVkaXRGYWN0b3J5U3RhdHVzOiBmYWxzZSwNCiAgICAgIC8vIOWPuOacuuS/oeaBrw0KICAgICAgZHJpdmVySW5mbzogew0KICAgICAgICBpZDogMSwNCiAgICAgICAgbmFtZTogJ+eOi+Wwj+aYjicsDQogICAgICAgIGlkQ2FyZDogJzExMDEwMTE5OTAwMTAxMDAwMScsDQogICAgICAgIHBob25lOiAnMTM4MDAxMzgwMDAnLA0KICAgICAgICBnZW5kZXI6ICcxJywNCiAgICAgICAgY29tcGFueTogJ+WMl+S6rOi/kOi+k+aciemZkOWFrOWPuCcsDQogICAgICAgIHBob3RvOiAnaHR0cHM6Ly92aWEucGxhY2Vob2xkZXIuY29tLzE1MCcsDQogICAgICAgIGRyaXZlckxpY2Vuc2VJbWdzOiAnaHR0cHM6Ly92aWEucGxhY2Vob2xkZXIuY29tLzMwMHgyMDAnLA0KICAgICAgICB2ZWhpY2xlTGljZW5zZUltZ3M6ICdodHRwczovL3ZpYS5wbGFjZWhvbGRlci5jb20vMzAweDIwMCcNCiAgICAgIH0sDQoNCiAgICAgIC8vIOi9pui+huS/oeaBrw0KICAgICAgY2FySW5mbzoge30sDQoNCiAgICAgIC8vIOS7u+WKoeeJqei1hOWIl+ihqA0KICAgICAgdGFza01hdGVyaWFsczogW10sDQoNCiAgICAgIC8vIOS7u+WKoeaXpeW/l+WIl+ihqA0KICAgICAgdGFza0xvZ3M6IFtdLA0KDQogICAgICAvLyDnlLPor7fnvJblj7cNCiAgICAgIGFwcGx5Tm86IG51bGwsDQoNCiAgICAgIGlzZG9vck1hbjogZmFsc2UsDQoNCiAgICAgIC8vIOa0vui9puS7u+WKoUlEDQogICAgICBkaXNwYXRjaElkOiBudWxsLA0KDQogICAgICB0YXNrSW5mb0Zvcm06IHt9LA0KDQogICAgICBtZWFzdXJlRmxhZzogbnVsbCwNCg0KICAgICAgYmFja3VwVGFza01hdGVyaWFsczogbnVsbCwNCiAgICAgIHRhc2tObzogbnVsbCwNCg0KICAgICAgc2VsZWN0ZWRPcHRpb246IG51bGwsDQoNCiAgICAgIHBsYW5Gb3JtOiB7fSwNCg0KICAgICAgcHJvY2Vzc1R5cGVPcHRpb25zOiBbXSwgLy8g5Yqo5oCB5Yqg6L2955qE5Yqg5bel57G75Z6L6YCJ6aG5DQoNCiAgICAgIGZpbHRlcmVkUHJvY2Vzc1R5cGVPcHRpb25zOiBbXSwgLy8g6L+H5ruk5ZCO55qE5Yqg5bel57G75Z6L6YCJ6aG5DQoNCiAgICAgIHNlYXJjaFByb2Nlc3NUeXBlUXVlcnk6ICcnLC8vIOaQnOe0ouahhueahOWAvA0KDQogICAgICBkaXJlY3RTdXBwbHlQbGFuTGlzdDogW10sIC8vIOebtOS+m+iuoeWIkuWIl+ihqA0KDQogICAgICBlZGl0aW5nUm93OiBudWxsLA0KDQogICAgICBzZWxlY3RlZFJvd3M6IFtdLCAvLyDmt7vliqDpgInkuK3ooYzmlbDmja7mlbDnu4QNCg0KICAgICAgZGlyZWN0U3VwcGx5UGFyYW1zOiB7fQ0KICAgIH07DQogIH0sDQoNCiAgY29tcHV0ZWQ6IHsNCiAgICBkaXNwbGF5UHJvY2Vzc1R5cGVPcHRpb25zKCkgew0KICAgICAgcmV0dXJuIHRoaXMuc2VhcmNoUHJvY2Vzc1R5cGVRdWVyeSA/IHRoaXMuZmlsdGVyZWRQcm9jZXNzVHlwZU9wdGlvbnMgOiB0aGlzLnByb2Nlc3NUeXBlT3B0aW9uczsNCiAgICB9LA0KDQogICAgLy8g5piv5ZCm5pyJ6YCJ5Lit55qE6aG5DQogICAgaGFzU2VsZWN0ZWRJdGVtcygpIHsNCiAgICAgIHJldHVybiB0aGlzLnNlbGVjdGVkUm93cy5sZW5ndGggPiAwOw0KICAgIH0sDQoNCiAgICAvLyDmt7vliqDorqHnrpflsZ7mgKcNCiAgICBtYXRlcmlhbE5hbWVzKCkgew0KICAgICAgcmV0dXJuIHRoaXMudGFza01hdGVyaWFscy5tYXAoaXRlbSA9PiBpdGVtLm1hdGVyaWFsTmFtZSkuam9pbignICcpOw0KICAgIH0sDQoNCiAgICBtYXRlcmlhbFNwZWNzKCkgew0KICAgICAgcmV0dXJuIHRoaXMudGFza01hdGVyaWFscy5tYXAoaXRlbSA9PiBpdGVtLm1hdGVyaWFsU3BlYykuam9pbignICcpOw0KICAgIH0NCiAgfSwNCg0KICBhY3RpdmF0ZWQoKSB7DQogICAgY29uc29sZS5sb2coImFjdGl2YXRlZOaJp+ihjCIpOw0KICAgIHRoaXMucmVzZXRUYXNrSW5mb0Zvcm0oKTsNCg0KICAgIC8vIOiOt+WPlui3r+eUseWPguaVsCAtIOaUr+aMgeS4pOenjeaWueW8j++8mnF1ZXJ55Y+C5pWw5ZKM6Lev5b6E5Y+C5pWwDQogICAgbGV0IHRhc2tObyA9IHRoaXMuJHJvdXRlLnBhcmFtcy50YXNrTm8gfHwgdGhpcy4kcm91dGUucXVlcnkudGFza05vOw0KDQogICAgaWYgKHRhc2tObykgew0KICAgICAgLy8g5paw55qE5pa55byP77ya6YCa6L+HdGFza05v6I635Y+W5omA5pyJ5Y+C5pWwDQogICAgICB0aGlzLnRhc2tObyA9IHRhc2tObzsNCiAgICAgIGNvbnNvbGUubG9nKCJ0YXNrTm8iLCB0aGlzLnRhc2tObyk7DQogICAgICB0aGlzLnZhbGlkRG9vck1hbigpOw0KDQogICAgICAvLyDkvb/nlKggYXN5bmMvYXdhaXQg56Gu5L+d5oyJ6aG65bqP5omn6KGMDQogICAgICB0aGlzLmluaXRpYWxpemVEYXRhQnlUYXNrTm8oKTsNCiAgICB9IGVsc2Ugew0KICAgICAgLy8g5YW85a655pen55qE5pa55byP77ya5LuOcXVlcnnlj4LmlbDojrflj5YNCiAgICAgIGNvbnN0IHsgZGlzcGF0Y2hJZCwgYXBwbHlObywgbWVhc3VyZUZsYWcsIHBsYW5UeXBlLCB0YXNrTm86IHF1ZXJ5VGFza05vIH0gPSB0aGlzLiRyb3V0ZS5xdWVyeTsNCiAgICAgIHRoaXMuZGlzcGF0Y2hJZCA9IGRpc3BhdGNoSWQ7DQogICAgICB0aGlzLmFwcGx5Tm8gPSBhcHBseU5vOw0KICAgICAgdGhpcy5tZWFzdXJlRmxhZyA9IG1lYXN1cmVGbGFnOw0KICAgICAgY29uc29sZS5sb2coInRoaXMubWVhc3VyZUZsYWciLCB0aGlzLm1lYXN1cmVGbGFnKQ0KICAgICAgdGhpcy5wbGFuVHlwZSA9IHBsYW5UeXBlOw0KICAgICAgdGhpcy50YXNrTm8gPSBxdWVyeVRhc2tObzsNCiAgICAgIGNvbnNvbGUubG9nKCJ0YXNrTm8iLCB0aGlzLnRhc2tObyk7DQogICAgICB0aGlzLnZhbGlkRG9vck1hbigpOw0KDQogICAgICAvLyDkvb/nlKggYXN5bmMvYXdhaXQg56Gu5L+d5oyJ6aG65bqP5omn6KGMDQogICAgICB0aGlzLmluaXRpYWxpemVEYXRhKCk7DQogICAgfQ0KICB9LA0KDQogIG1ldGhvZHM6IHsNCiAgICBnZXREaXJlY3RTdXBwbHlQbGFuQW5kVGFzaygpIHsNCg0KDQogICAgICBsZXQgbGVhdmVUYXNrMCA9IHsNCiAgICAgICAgdGFza05vOiB0aGlzLnRhc2tJbmZvRm9ybS5kaXJlY3RTdXBwbHlUYXNrTm8NCiAgICAgIH0NCg0KICAgICAgZ2V0RGlyZWN0U3VwcGx5UGxhbkFuZFRhc2tEZXRhaWwobGVhdmVUYXNrMCkudGhlbihyZXMgPT4gew0KICAgICAgICBjb25zb2xlLmxvZygiZ2V0RGlyZWN0U3VwcGx5UGxhbkFuZFRhc2tEZXRhaWwiLCByZXMpDQogICAgICAgIGlmIChyZXMuY29kZSA9PSAyMDApIHsNCiAgICAgICAgICB0aGlzLmRpcmVjdFN1cHBseVBhcmFtcy5kaXNwYXRjaElkID0gcmVzLnJvd3NbMF0uaWQ7DQogICAgICAgICAgdGhpcy5kaXJlY3RTdXBwbHlQYXJhbXMuYXBwbHlObyA9IHJlcy5yb3dzWzBdLmFwcGx5Tm87DQogICAgICAgICAgdGhpcy5kaXJlY3RTdXBwbHlQYXJhbXMudGFza05vID0gcmVzLnJvd3NbMF0udGFza05vOw0KICAgICAgICAgIHRoaXMuZGlyZWN0U3VwcGx5UGFyYW1zLm1lYXN1cmVGbGFnID0gcmVzLnJvd3NbMV0ubWVhc3VyZUZsYWc7DQogICAgICAgICAgdGhpcy5kaXJlY3RTdXBwbHlQYXJhbXMucGxhblR5cGUgPSByZXMucm93c1sxXS5wbGFuVHlwZTsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKHJlcy5tZXNzYWdlIHx8ICfojrflj5borqHliJLliJfooajlpLHotKUnKTsNCiAgICAgICAgfQ0KICAgICAgfSkuY2F0Y2goZXJyID0+IHsNCiAgICAgICAgY29uc29sZS5lcnJvcignZ2V0RGlyZWN0U3VwcGx5UGxhbkFuZFRhc2tEZXRhaWwgZXJyb3I6JywgZXJyKTsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign572R57uc5byC5bi477yM56iN5ZCO6YeN6K+VJyk7DQogICAgICAgIHRocm93IGVycjsNCiAgICAgIH0pOw0KDQogICAgfSwNCg0KICAgIHZhbGlkRG9vck1hbigpIHsNCiAgICAgIHRoaXMuJHN0b3JlLmdldHRlcnMucm9sZXMuZm9yRWFjaChpdGVtID0+IHsNCiAgICAgICAgaWYgKGl0ZW0gPT0gJ2xlYXZlLnF1YXJkJykgew0KICAgICAgICAgIHRoaXMuaXNkb29yTWFuID0gdHJ1ZTsNCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgICBjb25zb2xlLmxvZygiaXNkb29yTWFuIiwgdGhpcy5pc2Rvb3JNYW4pDQogICAgfSwNCiAgICBhc3luYyBpbml0aWFsaXplRGF0YSgpIHsNCiAgICAgIHRyeSB7DQogICAgICAgIC8vIOetieW+heaJgOacieW8guatpeaTjeS9nOWujOaIkA0KICAgICAgICBhd2FpdCB0aGlzLmdldFRhc2tJbmZvKCk7DQogICAgICAgIGF3YWl0IHRoaXMuZ2V0VGFza21hdGVyaWFsTGlzdCh0aGlzLnRhc2tObyk7DQogICAgICAgIGF3YWl0IHRoaXMuZ2V0UGxhbkluZm8odGhpcy5hcHBseU5vKTsNCg0KICAgICAgICAvLyDlnKjmiYDmnInmlbDmja7liqDovb3lrozmiJDlkI7miafooYwNCiAgICAgICAgdGhpcy51cGxvYWRGYWN0b3J5Q29uZmlybUZvcm0oKTsNCg0KICAgICAgICAvLyDlhbbku5bliJ3lp4vljJbmk43kvZwNCiAgICAgICAgdGhpcy5nZXRUYXNrTG9nTGlzdCh0aGlzLnRhc2tObyk7DQogICAgICAgIHRoaXMuZ2V0UHJvY2Vzc1R5cGUoKTsNCg0KICAgICAgICAvL+afpeivouebtOS+m+WvueW6lOiuoeWIkuOAgeS7u+WKoeivpuaDhQ0KICAgICAgICB0aGlzLmdldERpcmVjdFN1cHBseVBsYW5BbmRUYXNrKCk7DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBpbml0aWFsaXppbmcgZGF0YTonLCBlcnJvcik7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+aVsOaNruWKoOi9veWksei0pe+8jOivt+WIt+aWsOmhtemdoumHjeivlScpOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICBhc3luYyBpbml0aWFsaXplRGF0YUJ5VGFza05vKCkgew0KICAgICAgdHJ5IHsNCiAgICAgICAgLy8g6YCa6L+HdGFza05v6I635Y+W5Lu75Yqh5L+h5oGvDQogICAgICAgIGF3YWl0IHRoaXMuZ2V0VGFza0luZm9CeVRhc2tObygpOw0KDQogICAgICAgIC8vIOmAmui/h2FwcGx5Tm/ojrflj5borqHliJLkv6Hmga8NCiAgICAgICAgYXdhaXQgdGhpcy5nZXRQbGFuSW5mbyh0aGlzLmFwcGx5Tm8pOw0KDQogICAgICAgIC8vIOiOt+WPluS7u+WKoeeJqei1hOWIl+ihqA0KICAgICAgICBhd2FpdCB0aGlzLmdldFRhc2ttYXRlcmlhbExpc3QodGhpcy50YXNrTm8pOw0KDQogICAgICAgIC8vIOWcqOaJgOacieaVsOaNruWKoOi9veWujOaIkOWQjuaJp+ihjA0KICAgICAgICB0aGlzLnVwbG9hZEZhY3RvcnlDb25maXJtRm9ybSgpOw0KDQogICAgICAgIC8vIOWFtuS7luWIneWni+WMluaTjeS9nA0KICAgICAgICB0aGlzLmdldFRhc2tMb2dMaXN0KHRoaXMudGFza05vKTsNCiAgICAgICAgdGhpcy5nZXRQcm9jZXNzVHlwZSgpOw0KDQogICAgICAgIC8v5p+l6K+i55u05L6b5a+55bqU6K6h5YiS44CB5Lu75Yqh6K+m5oOFDQogICAgICAgIHRoaXMuZ2V0RGlyZWN0U3VwcGx5UGxhbkFuZFRhc2soKTsNCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGluaXRpYWxpemluZyBkYXRhIGJ5IHRhc2tObzonLCBlcnJvcik7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+aVsOaNruWKoOi9veWksei0pe+8jOivt+WIt+aWsOmhtemdoumHjeivlScpOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICB1cGxvYWRGYWN0b3J5Q29uZmlybUZvcm0oKSB7DQogICAgICAvLyDotYvlgLzlkI7vvIzliJ3lp4vljJbmr4/kuKrlhYPntKDnmoQgZG9vcm1hblJlY2VpdmVOdW0g5ZKMIGRvb3JtYW5SZWNlaXZlTnVtSW4NCiAgICAgIHRoaXMudGFza01hdGVyaWFscy5mb3JFYWNoKGl0ZW0gPT4gew0KICAgICAgICBpdGVtLmRvb3JtYW5SZWNlaXZlTnVtID0gaXRlbS5wbGFuTnVtOw0KICAgICAgICBjb25zb2xlLmxvZygiaXRlbS5wbGFuVHlwZSIsIHRoaXMucGxhbkZvcm0ucGxhblR5cGUpOw0KICAgICAgICBpZiAodGhpcy5wbGFuRm9ybS5wbGFuVHlwZSA9PSAyIHx8IHRoaXMucGxhbkZvcm0ucGxhblR5cGUgPT0gMykgew0KICAgICAgICAgIGl0ZW0uZG9vcm1hblJlY2VpdmVOdW1JbiA9IGl0ZW0ucGxhbk51bTsNCiAgICAgICAgfQ0KICAgICAgfSk7DQoNCiAgICAgIGxldCBoYW5kbGVkTWF0ZXJpYWxOYW1lID0gdGhpcy50YXNrTWF0ZXJpYWxzLm1hcChpdGVtID0+IGl0ZW0ubWF0ZXJpYWxOYW1lKS5qb2luKCcgJyk7DQogICAgICBsZXQgbWF0ZXJpYWxTcGVjcyA9IHRoaXMudGFza01hdGVyaWFscy5tYXAoaXRlbSA9PiBpdGVtLm1hdGVyaWFsU3BlYykuam9pbignICcpOw0KICAgICAgLy8g5Yid5aeL5YyW6KGo5Y2V5pWw5o2uDQogICAgICB0aGlzLmZhY3RvcnlDb25maXJtRm9ybSA9IHsNCiAgICAgICAgY29tcGFueU5hbWU6IHRoaXMudGFza0luZm9Gb3JtLmNvbXBhbnlOYW1lLA0KICAgICAgICBncm9zczogdGhpcy50YXNrSW5mb0Zvcm0uZ3Jvc3MsDQogICAgICAgIHNlY0dyb3NzOiB0aGlzLnRhc2tJbmZvRm9ybS5zZWNHcm9zcywNCiAgICAgICAgZHJpdmVyTmFtZTogdGhpcy50YXNrSW5mb0Zvcm0uZHJpdmVyTmFtZSwNCiAgICAgICAgdGFyZTogdGhpcy50YXNrSW5mb0Zvcm0udGFyZSwNCiAgICAgICAgdGFza05vOiB0aGlzLnRhc2tObywNCiAgICAgICAgYXBwbHlObzogdGhpcy5hcHBseU5vLA0KICAgICAgICBwbGFuTm86IHRoaXMudGFza0luZm9Gb3JtLnBsYW5ObywNCiAgICAgICAgdW5sb2FkaW5nV29ya05vOiAnJywNCiAgICAgICAgdW5sb2FkaW5nVGltZTogbmV3IERhdGUoKSwNCiAgICAgICAgc3BlYzFMZW5ndGg6IG51bGwsDQogICAgICAgIHNwZWMyV2lkdGg6IG51bGwsDQogICAgICAgIHRvdGFsczogJycsDQogICAgICAgIHRvdGFsOiAnJywNCiAgICAgICAgdG90YWxVbml0OiAnJywNCiAgICAgICAgcHJvY2Vzc1R5cGU6ICcnLA0KICAgICAgICBoZWF0Tm86ICcnLA0KICAgICAgICBzdGVlbEdyYWRlOiAnJywNCiAgICAgICAgYXhsZXM6ICcnLA0KICAgICAgICByZW1hcms6ICcnLA0KICAgICAgICB0YXNrU3RhdHVzOiA5LA0KICAgICAgICBjYXJOdW06IHRoaXMudGFza0luZm9Gb3JtLmNhck51bSwgLy8g5Yid5aeL5YyW6L2m54mM5Y+3DQogICAgICAgIGhhbmRsZWRNYXRlcmlhbE5hbWU6IGhhbmRsZWRNYXRlcmlhbE5hbWUsDQogICAgICAgIG1hdGVyaWFsU3BlY3M6IG1hdGVyaWFsU3BlY3MsDQogICAgICAgIHNvdXJjZUNvbXBhbnk6IHRoaXMucGxhbkZvcm0uc291cmNlQ29tcGFueSwNCiAgICAgICAgcmVjZWl2ZUNvbXBhbnk6IHRoaXMucGxhbkZvcm0ucmVjZWl2ZUNvbXBhbnksDQogICAgICAgIHNob3dEcm9wZG93bjogZmFsc2UsIC8vIOaYr+WQpuWQr+eUqOmineWklumAiemhuQ0KICAgICAgICBleHRyYU9wdGlvbjogJycsIC8vIOmineWklumAiemhueeahOWAvA0KICAgICAgICAvLyDlh7rlupPkv6Hmga8NCiAgICAgICAgc3RvY2tPdXRTcGVjMUxlbmd0aDogbnVsbCwNCiAgICAgICAgc3RvY2tPdXRTcGVjMldpZHRoOiBudWxsLA0KICAgICAgICBzdG9ja091dFRvdGFsczogJycsDQogICAgICAgIHN0b2NrT3V0VG90YWxVbml0OiAnJywNCiAgICAgICAgc3RvY2tPdXRUb3RhbDogJycsDQogICAgICAgIHN0b2NrT3V0UHJvY2Vzc1R5cGU6ICcnLA0KICAgICAgICBzdG9ja091dEhlYXRObzogJycsDQogICAgICAgIHN0b2NrT3V0U3RlZWxHcmFkZTogJycsDQogICAgICAgIHN0b2NrT3V0QXhsZXM6ICcnLA0KICAgICAgICBzdG9ja091dFJlbWFyazogJycsDQogICAgICAgIGRlZHVjdFdlaWdodDogbnVsbCwgLy8g5re75Yqg5omj6YeN5a2X5q615Yid5aeL5YyWDQogICAgICB9Ow0KICAgIH0sDQoNCiAgICBvcGVuTmV3V2luZG93KCkgew0KICAgICAgY29uc3QgbmV3V2luZG93VXJsID0gJ2h0dHA6Ly9sb2NhbGhvc3QvbGVhdmUvbGVhdmVQbGFuTGlzdCc7IC8vIOabv+aNouS4uuWunumZheimgei3s+i9rOeahOmhtemdoiBVUkwNCiAgICAgIHdpbmRvdy5vcGVuKG5ld1dpbmRvd1VybCwgJ19ibGFuaycpOyAvLyDmiZPlvIDmlrDnqpflj6Plubbot7Povazoh7PmjIflrpogVVJMDQogICAgfSwNCiAgICAvL+iOt+WPluWPr+S7peebtOS+m+eahOiuoeWIkg0KICAgIGFzeW5jIGdldERpcmVjdFN1cHBseUxpc3QoKSB7DQogICAgICB0cnkgew0KICAgICAgICBsZXQgbGVhdmVQbGFuID0gew0KICAgICAgICAgIHNvdXJjZUNvbXBhbnk6IHRoaXMucGxhbkZvcm0uc291cmNlQ29tcGFueSwNCiAgICAgICAgICBwbGFuVHlwZTogMywNCiAgICAgICAgfQ0KICAgICAgICBjb25zb2xlLmxvZygi6I635Y+W5Y+v5Lul55u05L6b55qE6K6h5YiSIiwgbGVhdmVQbGFuKQ0KDQogICAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IGdldERpcmVjdFN1cHBseVBsYW5zKGxlYXZlUGxhbik7DQogICAgICAgIGNvbnNvbGUubG9nKCJnZXREaXJlY3RTdXBwbHlQbGFucyIsIHJlcykNCiAgICAgICAgaWYgKHJlcy5jb2RlID09IDIwMCkgew0KICAgICAgICAgIHRoaXMuZGlyZWN0U3VwcGx5UGxhbkxpc3QgPSByZXMucm93czsNCiAgICAgICAgICAvLyAvL+afpeivouavj+S4quiuoeWIkueahOeJqei1hA0KICAgICAgICAgIC8vIGZvciAoY29uc3QgaXRlbSBvZiB0aGlzLmRpcmVjdFN1cHBseVBsYW5MaXN0KSB7DQogICAgICAgICAgLy8gICBjb25zb2xlLmxvZygiaXRlbSIsIGl0ZW0pDQogICAgICAgICAgLy8gICBsZXQgbGVhdmVQbGFuTWF0ZXJpYWwgPSB7DQogICAgICAgICAgLy8gICAgIGFwcGx5Tm86IGl0ZW0uYXBwbHlObw0KICAgICAgICAgIC8vICAgfTsNCiAgICAgICAgICAvLyAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZ2V0UGxhbk1hdGVyaWFscyhsZWF2ZVBsYW5NYXRlcmlhbCk7DQogICAgICAgICAgLy8gICBpZiAocmVzcG9uc2UuY29kZSA9PSAyMDApIHsNCiAgICAgICAgICAvLyAgICAgY29uc29sZS5sb2coImdldFBsYW5NYXRlcmlhbHMiLCByZXNwb25zZSkNCiAgICAgICAgICAvLyAgICAgaXRlbS5tYXRlcmlhbE5hbWUgPSByZXNwb25zZS5yb3dzWzBdLm1hdGVyaWFsTmFtZTsNCiAgICAgICAgICAvLyAgICAgaXRlbS5tYXRlcmlhbFNwZWMgPSByZXNwb25zZS5yb3dzWzBdLm1hdGVyaWFsU3BlYzsNCiAgICAgICAgICAvLyAgIH0gZWxzZSB7DQogICAgICAgICAgLy8gICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IocmVzcG9uc2UubWVzc2FnZSB8fCAn6I635Y+W6K6h5YiS54mp6LWE5aSx6LSlJyk7DQogICAgICAgICAgLy8gICB9DQogICAgICAgICAgLy8gfQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IocmVzLm1lc3NhZ2UgfHwgJ+iOt+WPluiuoeWIkuWIl+ihqOWksei0pScpOw0KICAgICAgICB9DQogICAgICB9IGNhdGNoIChlcnIpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcignZ2V0RGlyZWN0U3VwcGx5UGxhbnMgZXJyb3I6JywgZXJyKTsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign572R57uc5byC5bi477yM56iN5ZCO6YeN6K+VJyk7DQogICAgICAgIHRocm93IGVycjsNCiAgICAgIH0NCiAgICB9LA0KICAgIGZpbHRlclByb2Nlc3NUeXBlKHF1ZXJ5KSB7DQogICAgICB0aGlzLnNlYXJjaFByb2Nlc3NUeXBlUXVlcnkgPSBxdWVyeTsNCg0KICAgICAgaWYgKHRoaXMuc2VhcmNoUHJvY2Vzc1R5cGVRdWVyeSkgew0KICAgICAgICBjb25zb2xlLmxvZygicHJvY2Vzc1R5cGVPcHRpb25zIiwgdGhpcy5wcm9jZXNzVHlwZU9wdGlvbnMpDQoNCiAgICAgICAgdGhpcy5maWx0ZXJlZFByb2Nlc3NUeXBlT3B0aW9ucyA9IHRoaXMucHJvY2Vzc1R5cGVPcHRpb25zLmZpbHRlcihpdGVtID0+DQogICAgICAgICAgaXRlbS52YWx1ZS5pbmNsdWRlcyhxdWVyeSkNCiAgICAgICAgKTsNCiAgICAgIH0gZWxzZSB7DQoNCiAgICAgICAgdGhpcy5maWx0ZXJlZFByb2Nlc3NUeXBlT3B0aW9ucyA9IHRoaXMucHJvY2Vzc1R5cGVPcHRpb25zOw0KICAgICAgfQ0KICAgIH0sDQogICAgZ2V0UHJvY2Vzc1R5cGUoKSB7DQogICAgICBnZXRQcm9jZXNzTGlzdCgpLnRoZW4ocmVzID0+IHsNCiAgICAgICAgY29uc29sZS5sb2coImdldFByb2Nlc3NMaXN0IiwgcmVzKQ0KICAgICAgICBpZiAocmVzLmNvZGUgPT0gMjAwKSB7DQogICAgICAgICAgdGhpcy5wcm9jZXNzVHlwZU9wdGlvbnMgPSByZXMucm93cy5tYXAoaXRlbSA9PiAoew0KICAgICAgICAgICAgdmFsdWU6IGl0ZW0ucHJvY2Vzc25hbWUsDQogICAgICAgICAgICBsYWJlbDogaXRlbS5wcm9jZXNzbmFtZQ0KICAgICAgICAgIH0pKTsNCiAgICAgICAgICB0aGlzLmZpbHRlcmVkUHJvY2Vzc1R5cGVPcHRpb25zID0gdGhpcy5wcm9jZXNzVHlwZU9wdGlvbnM7IC8vIOWIneWni+WMlui/h+a7pOWQjueahOmAiemhuQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IocmVzLm1lc3NhZ2UgfHwgJ+iOt+WPluWKoOW3peexu+Wei+Wksei0pScpOw0KICAgICAgICB9DQogICAgICB9KS5jYXRjaChlcnIgPT4gew0KICAgICAgICBjb25zb2xlLmVycm9yKCdnZXRQcm9jZXNzTGlzdCBlcnJvcjonLCBlcnIpOw0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfnvZHnu5zlvILluLjvvIznqI3lkI7ph43or5UnKTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgYXN5bmMgZ2V0UGxhbkluZm8oYXBwbHlObykgew0KICAgICAgdHJ5IHsNCiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBkZXRhaWxQbGFuKGFwcGx5Tm8pOw0KICAgICAgICBjb25zb2xlLmxvZygiZGV0YWlsUGxhbiIsIHJlc3BvbnNlKTsNCiAgICAgICAgdGhpcy5wbGFuRm9ybSA9IHJlc3BvbnNlLmRhdGE7DQoNCiAgICAgICAgLy8g5LuO6K6h5YiS5L+h5oGv5Lit6I635Y+WcGxhblR5cGXlkoxtZWFzdXJlRmxhZw0KICAgICAgICB0aGlzLnBsYW5UeXBlID0gdGhpcy5wbGFuRm9ybS5wbGFuVHlwZTsNCiAgICAgICAgdGhpcy5tZWFzdXJlRmxhZyA9IHRoaXMucGxhbkZvcm0ubWVhc3VyZUZsYWc7DQogICAgICAgIGNvbnNvbGUubG9nKCJ0aGlzLnBsYW5UeXBlIiwgdGhpcy5wbGFuVHlwZSk7DQogICAgICAgIGNvbnNvbGUubG9nKCJ0aGlzLm1lYXN1cmVGbGFnIiwgdGhpcy5tZWFzdXJlRmxhZyk7DQoNCiAgICAgICAgYXdhaXQgdGhpcy5nZXREaXJlY3RTdXBwbHlMaXN0KCk7DQogICAgICAgIHJldHVybiByZXNwb25zZTsNCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ2dldFBsYW5JbmZvIGVycm9yOicsIGVycm9yKTsNCiAgICAgICAgdGhyb3cgZXJyb3I7DQogICAgICB9DQogICAgfSwNCiAgICBvcGVuRmFjdG9yeUNvbmZpcm1EaWFsb2coKSB7DQogICAgICBsZXQgaGFuZGxlZE1hdGVyaWFsTmFtZSA9IHRoaXMudGFza01hdGVyaWFscy5tYXAoaXRlbSA9PiBpdGVtLm1hdGVyaWFsTmFtZSkuam9pbignICcpOw0KICAgICAgLy8g5Yid5aeL5YyW6KGo5Y2V5pWw5o2uDQogICAgICB0aGlzLmZhY3RvcnlDb25maXJtRm9ybSA9IHsNCiAgICAgICAgY29tcGFueU5hbWU6IHRoaXMudGFza0luZm9Gb3JtLmNvbXBhbnlOYW1lLA0KICAgICAgICBncm9zczogdGhpcy50YXNrSW5mb0Zvcm0uZ3Jvc3MsDQogICAgICAgIHNlY0dyb3NzOiB0aGlzLnRhc2tJbmZvRm9ybS5zZWNHcm9zcywNCiAgICAgICAgdGFyZTogdGhpcy50YXNrSW5mb0Zvcm0udGFyZSwNCiAgICAgICAgdGFza05vOiB0aGlzLnRhc2tObywNCiAgICAgICAgYXBwbHlObzogdGhpcy5hcHBseU5vLA0KICAgICAgICBwbGFuTm86IHRoaXMudGFza0luZm9Gb3JtLnBsYW5ObywNCiAgICAgICAgdW5sb2FkaW5nV29ya05vOiAnJywNCiAgICAgICAgdW5sb2FkaW5nVGltZTogbmV3IERhdGUoKSwNCiAgICAgICAgc3BlYzFMZW5ndGg6IG51bGwsDQogICAgICAgIHNwZWMyV2lkdGg6IG51bGwsDQogICAgICAgIHRvdGFsczogJycsDQogICAgICAgIHRvdGFsOiAnJywNCiAgICAgICAgdG90YWxVbml0OiAnJywNCiAgICAgICAgcHJvY2Vzc1R5cGU6ICcnLA0KICAgICAgICBoZWF0Tm86ICcnLA0KICAgICAgICBzdGVlbEdyYWRlOiAnJywNCiAgICAgICAgYXhsZXM6ICcnLA0KICAgICAgICByZW1hcms6ICcnLA0KICAgICAgICB0YXNrU3RhdHVzOiA5LA0KICAgICAgICBjYXJOdW06IHRoaXMudGFza0luZm9Gb3JtLmNhck51bSwgLy8g5Yid5aeL5YyW6L2m54mM5Y+3DQogICAgICAgIGhhbmRsZWRNYXRlcmlhbE5hbWU6IGhhbmRsZWRNYXRlcmlhbE5hbWUsDQogICAgICAgIHNvdXJjZUNvbXBhbnk6IHRoaXMucGxhbkZvcm0uc291cmNlQ29tcGFueSwNCiAgICAgICAgcmVjZWl2ZUNvbXBhbnk6IHRoaXMucGxhbkZvcm0ucmVjZWl2ZUNvbXBhbnksDQogICAgICAgIHNob3dEcm9wZG93bjogZmFsc2UsIC8vIOaYr+WQpuWQr+eUqOmineWklumAiemhuQ0KICAgICAgICBleHRyYU9wdGlvbjogJycsIC8vIOmineWklumAiemhueeahOWAvA0KICAgICAgICAvLyDlh7rlupPkv6Hmga8NCiAgICAgICAgc3RvY2tPdXRTcGVjMUxlbmd0aDogbnVsbCwNCiAgICAgICAgc3RvY2tPdXRTcGVjMldpZHRoOiBudWxsLA0KICAgICAgICBzdG9ja091dFRvdGFsczogJycsDQogICAgICAgIHN0b2NrT3V0VG90YWxVbml0OiAnJywNCiAgICAgICAgc3RvY2tPdXRUb3RhbDogJycsDQogICAgICAgIHN0b2NrT3V0UHJvY2Vzc1R5cGU6ICcnLA0KICAgICAgICBzdG9ja091dEhlYXRObzogJycsDQogICAgICAgIHN0b2NrT3V0U3RlZWxHcmFkZTogJycsDQogICAgICAgIHN0b2NrT3V0QXhsZXM6ICcnLA0KICAgICAgICBzdG9ja091dFJlbWFyazogJycsDQogICAgICAgIGRlZHVjdFdlaWdodDogbnVsbCwgLy8g5re75Yqg5omj6YeN5a2X5q615Yid5aeL5YyWDQogICAgICB9Ow0KICAgICAgdGhpcy5mYWN0b3J5Q29uZmlybURpYWxvZ1Zpc2libGUgPSB0cnVlOw0KICAgIH0sDQogICAgc3VibWl0RmFjdG9yeUNvbmZpcm0oKSB7DQogICAgICBpZiAodGhpcy5mYWN0b3J5Q29uZmlybUZvcm0uc2hvd0Ryb3Bkb3duID09IHRydWUpIHsNCiAgICAgICAgaWYgKHRoaXMuZmFjdG9yeUNvbmZpcm1Gb3JtLmV4dHJhT3B0aW9uID09IG51bGwgfHwgdGhpcy5mYWN0b3J5Q29uZmlybUZvcm0uZXh0cmFPcHRpb24gPT0gJycpIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfor7fpgInmi6npop3lpJbpgInpobknKTsNCiAgICAgICAgICByZXR1cm47DQogICAgICAgIH0NCiAgICAgIH0NCg0KICAgICAgbGV0IHN1Ym1pdERhdGEgPSB7fTsNCiAgICAgIGlmICh0aGlzLnRhc2tJbmZvRm9ybS5pc0RpcmVjdFN1cHBseSA9PSAzKSB7DQogICAgICAgIC8vIOaehOW7uuaPkOS6pOaVsOaNrg0KICAgICAgICBzdWJtaXREYXRhID0gew0KICAgICAgICAgIGxlYXZlVGFzazogew0KICAgICAgICAgICAgaWQ6IHRoaXMuZGlzcGF0Y2hJZCwNCiAgICAgICAgICAgIHRhc2tObzogdGhpcy50YXNrTm8sDQogICAgICAgICAgICBhcHBseU5vOiB0aGlzLmFwcGx5Tm8sDQogICAgICAgICAgICAvL+WFpeW6k+S/oeaBrw0KICAgICAgICAgICAgc3BlYzFMZW5ndGg6IHRoaXMuZmFjdG9yeUNvbmZpcm1Gb3JtLnNwZWMxTGVuZ3RoLA0KICAgICAgICAgICAgc3BlYzJXaWR0aDogdGhpcy5mYWN0b3J5Q29uZmlybUZvcm0uc3BlYzJXaWR0aCwNCiAgICAgICAgICAgIHRvdGFsczogdGhpcy5mYWN0b3J5Q29uZmlybUZvcm0udG90YWwgKyB0aGlzLmZhY3RvcnlDb25maXJtRm9ybS50b3RhbFVuaXQsDQogICAgICAgICAgICBwcm9jZXNzVHlwZTogdGhpcy5mYWN0b3J5Q29uZmlybUZvcm0ucHJvY2Vzc1R5cGUsDQogICAgICAgICAgICBoZWF0Tm86IHRoaXMuZmFjdG9yeUNvbmZpcm1Gb3JtLmhlYXRObywNCiAgICAgICAgICAgIHN0ZWVsR3JhZGU6IHRoaXMuZmFjdG9yeUNvbmZpcm1Gb3JtLnN0ZWVsR3JhZGUsDQogICAgICAgICAgICBheGxlczogdGhpcy5mYWN0b3J5Q29uZmlybUZvcm0uYXhsZXMsDQogICAgICAgICAgICByZW1hcms6IHRoaXMuZmFjdG9yeUNvbmZpcm1Gb3JtLnJlbWFyaywNCiAgICAgICAgICAgIGNhck51bTogdGhpcy50YXNrSW5mb0Zvcm0uY2FyTnVtLA0KICAgICAgICAgICAgZHJpdmVyTmFtZTogdGhpcy50YXNrSW5mb0Zvcm0uZHJpdmVyTmFtZSwNCiAgICAgICAgICAgIGlzRGlyZWN0U3VwcGx5OiAzLA0KICAgICAgICAgICAgcGxhbk5vOiB0aGlzLnRhc2tJbmZvRm9ybS5wbGFuTm8sDQogICAgICAgICAgICBkZWR1Y3RXZWlnaHQ6IHRoaXMuZmFjdG9yeUNvbmZpcm1Gb3JtLmRlZHVjdFdlaWdodCwgLy8g5re75Yqg5omj6YeN5a2X5q61DQoNCiAgICAgICAgICAgIC8vIOWHuuW6k+S/oeaBrw0KICAgICAgICAgICAgc3RvY2tPdXRTcGVjMUxlbmd0aDogdGhpcy5mYWN0b3J5Q29uZmlybUZvcm0uc3RvY2tPdXRTcGVjMUxlbmd0aCwNCiAgICAgICAgICAgIHN0b2NrT3V0U3BlYzJXaWR0aDogdGhpcy5mYWN0b3J5Q29uZmlybUZvcm0uc3RvY2tPdXRTcGVjMldpZHRoLA0KICAgICAgICAgICAgc3RvY2tPdXRUb3RhbHM6IHRoaXMuZmFjdG9yeUNvbmZpcm1Gb3JtLnN0b2NrT3V0VG90YWwgKyB0aGlzLmZhY3RvcnlDb25maXJtRm9ybS5zdG9ja091dFRvdGFsVW5pdCwNCiAgICAgICAgICAgIHN0b2NrT3V0UHJvY2Vzc1R5cGU6IHRoaXMuZmFjdG9yeUNvbmZpcm1Gb3JtLnN0b2NrT3V0UHJvY2Vzc1R5cGUsDQogICAgICAgICAgICBzdG9ja091dEhlYXRObzogdGhpcy5mYWN0b3J5Q29uZmlybUZvcm0uc3RvY2tPdXRIZWF0Tm8sDQogICAgICAgICAgICBzdG9ja091dFN0ZWVsR3JhZGU6IHRoaXMuZmFjdG9yeUNvbmZpcm1Gb3JtLnN0b2NrT3V0U3RlZWxHcmFkZSwNCiAgICAgICAgICAgIHN0b2NrT3V0QXhsZXM6IHRoaXMuZmFjdG9yeUNvbmZpcm1Gb3JtLnN0b2NrT3V0QXhsZXMsDQogICAgICAgICAgICBzdG9ja091dFJlbWFyazogdGhpcy5mYWN0b3J5Q29uZmlybUZvcm0uc3RvY2tPdXRSZW1hcmssDQogICAgICAgICAgICAvLyDmm7TmlLnku7vliqHnirbmgIE6IDkNCiAgICAgICAgICAgIC8vIHRvZG8g5Lu75Yqh54q25oCB5aaC5L2V5Y+Y5YyWDQogICAgICAgICAgICB0YXNrU3RhdHVzOiA4LA0KICAgICAgICAgICAgdGFza1R5cGU6IHRoaXMudGFza0luZm9Gb3JtLnRhc2tUeXBlLA0KICAgICAgICAgIH0sDQogICAgICAgICAgbGVhdmVQbGFuOiB0aGlzLnBsYW5Gb3JtLA0KICAgICAgICAgIGxlYXZlVGFza01hdGVyaWFsOiB0aGlzLnRhc2tNYXRlcmlhbHNbMF0sDQogICAgICAgIH07DQogICAgICB9IGVsc2Ugew0KICAgICAgICAvLyDmnoTlu7rmj5DkuqTmlbDmja4NCiAgICAgICAgc3VibWl0RGF0YSA9IHsNCiAgICAgICAgICBsZWF2ZVRhc2s6IHsNCiAgICAgICAgICAgIGlkOiB0aGlzLmRpc3BhdGNoSWQsDQogICAgICAgICAgICB0YXNrTm86IHRoaXMudGFza05vLA0KICAgICAgICAgICAgYXBwbHlObzogdGhpcy5hcHBseU5vLA0KICAgICAgICAgICAgcGxhbk5vOiB0aGlzLnRhc2tJbmZvRm9ybS5wbGFuTm8sDQogICAgICAgICAgICAvL+WFpeW6k+S/oeaBrw0KICAgICAgICAgICAgc3BlYzFMZW5ndGg6IHRoaXMuZmFjdG9yeUNvbmZpcm1Gb3JtLnNwZWMxTGVuZ3RoLA0KICAgICAgICAgICAgc3BlYzJXaWR0aDogdGhpcy5mYWN0b3J5Q29uZmlybUZvcm0uc3BlYzJXaWR0aCwNCiAgICAgICAgICAgIHRvdGFsczogdGhpcy5mYWN0b3J5Q29uZmlybUZvcm0udG90YWwgKyB0aGlzLmZhY3RvcnlDb25maXJtRm9ybS50b3RhbFVuaXQsDQogICAgICAgICAgICBwcm9jZXNzVHlwZTogdGhpcy5mYWN0b3J5Q29uZmlybUZvcm0ucHJvY2Vzc1R5cGUsDQogICAgICAgICAgICBoZWF0Tm86IHRoaXMuZmFjdG9yeUNvbmZpcm1Gb3JtLmhlYXRObywNCiAgICAgICAgICAgIHN0ZWVsR3JhZGU6IHRoaXMuZmFjdG9yeUNvbmZpcm1Gb3JtLnN0ZWVsR3JhZGUsDQogICAgICAgICAgICBheGxlczogdGhpcy5mYWN0b3J5Q29uZmlybUZvcm0uYXhsZXMsDQogICAgICAgICAgICByZW1hcms6IHRoaXMuZmFjdG9yeUNvbmZpcm1Gb3JtLnJlbWFyaywNCiAgICAgICAgICAgIGNhck51bTogdGhpcy50YXNrSW5mb0Zvcm0uY2FyTnVtLA0KICAgICAgICAgICAgZHJpdmVyTmFtZTogdGhpcy50YXNrSW5mb0Zvcm0uZHJpdmVyTmFtZSwNCiAgICAgICAgICAgIGlzRGlyZWN0U3VwcGx5OiAwLCAvLyDpu5jorqTkuI3mmK/nm7TkvpsNCiAgICAgICAgICAgIGRlZHVjdFdlaWdodDogdGhpcy5mYWN0b3J5Q29uZmlybUZvcm0uZGVkdWN0V2VpZ2h0LCAvLyDmt7vliqDmiaPph43lrZfmrrUNCiAgICAgICAgICAgIGRpcmVjdFN1cHBseVRhc2tObzogdGhpcy5mYWN0b3J5Q29uZmlybUZvcm0uZXh0cmFPcHRpb24sDQogICAgICAgICAgICAvLyDlh7rlupPkv6Hmga8NCiAgICAgICAgICAgIHN0b2NrT3V0U3BlYzFMZW5ndGg6IHRoaXMuZmFjdG9yeUNvbmZpcm1Gb3JtLnN0b2NrT3V0U3BlYzFMZW5ndGgsDQogICAgICAgICAgICBzdG9ja091dFNwZWMyV2lkdGg6IHRoaXMuZmFjdG9yeUNvbmZpcm1Gb3JtLnN0b2NrT3V0U3BlYzJXaWR0aCwNCiAgICAgICAgICAgIHN0b2NrT3V0VG90YWxzOiB0aGlzLmZhY3RvcnlDb25maXJtRm9ybS5zdG9ja091dFRvdGFsICsgdGhpcy5mYWN0b3J5Q29uZmlybUZvcm0uc3RvY2tPdXRUb3RhbFVuaXQsDQogICAgICAgICAgICBzdG9ja091dFByb2Nlc3NUeXBlOiB0aGlzLmZhY3RvcnlDb25maXJtRm9ybS5zdG9ja091dFByb2Nlc3NUeXBlLA0KICAgICAgICAgICAgc3RvY2tPdXRIZWF0Tm86IHRoaXMuZmFjdG9yeUNvbmZpcm1Gb3JtLnN0b2NrT3V0SGVhdE5vLA0KICAgICAgICAgICAgc3RvY2tPdXRTdGVlbEdyYWRlOiB0aGlzLmZhY3RvcnlDb25maXJtRm9ybS5zdG9ja091dFN0ZWVsR3JhZGUsDQogICAgICAgICAgICBzdG9ja091dEF4bGVzOiB0aGlzLmZhY3RvcnlDb25maXJtRm9ybS5zdG9ja091dEF4bGVzLA0KICAgICAgICAgICAgc3RvY2tPdXRSZW1hcms6IHRoaXMuZmFjdG9yeUNvbmZpcm1Gb3JtLnN0b2NrT3V0UmVtYXJrLA0KICAgICAgICAgICAgLy8g5pu05pS55Lu75Yqh54q25oCBOiA5DQogICAgICAgICAgICAvLyB0b2RvIOS7u+WKoeeKtuaAgeWmguS9leWPmOWMlg0KICAgICAgICAgICAgdGFza1N0YXR1czogOCwNCiAgICAgICAgICAgIHRhc2tUeXBlOiB0aGlzLnRhc2tJbmZvRm9ybS50YXNrVHlwZSwNCiAgICAgICAgICB9LA0KICAgICAgICAgIGxlYXZlUGxhbjogdGhpcy5wbGFuRm9ybSwNCiAgICAgICAgICBsZWF2ZVRhc2tNYXRlcmlhbDogdGhpcy50YXNrTWF0ZXJpYWxzWzBdLA0KICAgICAgICB9Ow0KICAgICAgfQ0KDQoNCg0KICAgICAgbGV0IGRpcmVjdFN1cHBseVRhc2sgPSB7DQogICAgICAgIC8vdGFza05v5ZCO5Y+w6Zuq6Iqx55Sf5oiQDQogICAgICAgIGFwcGx5Tm86IHRoaXMuZmFjdG9yeUNvbmZpcm1Gb3JtLmV4dHJhT3B0aW9uLA0KICAgICAgICB0YXNrVHlwZTogMywNCiAgICAgICAgdGFza1N0YXR1czogNywNCiAgICAgICAgc2VjR3Jvc3M6IHRoaXMudGFza0luZm9Gb3JtLnNlY0dyb3NzLA0KICAgICAgICBzZWNHcm9zc1RpbWU6IHRoaXMudGFza0luZm9Gb3JtLnNlY0dyb3NzVGltZSwNCiAgICAgICAgcGxhbk5vOiB0aGlzLnRhc2tJbmZvRm9ybS5wbGFuTm8sDQogICAgICAgIGRyaXZlck5hbWU6IHRoaXMudGFza0luZm9Gb3JtLmRyaXZlck5hbWUsDQogICAgICAgIHNleDogdGhpcy50YXNrSW5mb0Zvcm0uc2V4LA0KICAgICAgICBtb2JpbGVQaG9uZTogdGhpcy50YXNrSW5mb0Zvcm0ubW9iaWxlUGhvbmUsDQogICAgICAgIGlkQ2FyZE5vOiB0aGlzLnRhc2tJbmZvRm9ybS5pZENhcmRObywNCiAgICAgICAgY2FyTnVtOiB0aGlzLnRhc2tJbmZvRm9ybS5jYXJOdW0sDQogICAgICAgIHZlaGljbGVFbWlzc2lvblN0YW5kYXJkczogdGhpcy50YXNrSW5mb0Zvcm0udmVoaWNsZUVtaXNzaW9uU3RhbmRhcmRzLA0KICAgICAgICBmYWNlSW1nOiB0aGlzLnRhc2tJbmZvRm9ybS5mYWNlSW1nLA0KICAgICAgICBkcml2aW5nTGljZW5zZUltZzogdGhpcy50YXNrSW5mb0Zvcm0uZHJpdmluZ0xpY2Vuc2VJbWcsDQogICAgICAgIGRyaXZlckxpY2Vuc2VJbWc6IHRoaXMudGFza0luZm9Gb3JtLmRyaXZlckxpY2Vuc2VJbWcsDQogICAgICAgIGNvbXBhbnlOYW1lOiB0aGlzLnRhc2tJbmZvRm9ybS5jb21wYW55TmFtZSwNCiAgICAgICAgaXNEaXJlY3RTdXBwbHk6IDMNCiAgICAgIH07DQoNCiAgICAgIGxldCBkaXJlY3RTdXBwbHlUYXNrTWF0ZXJpYWxMaXN0ID0gdGhpcy50YXNrTWF0ZXJpYWxzOw0KDQogICAgICBpZiAodGhpcy5mYWN0b3J5Q29uZmlybUZvcm0uc2hvd0Ryb3Bkb3duID09IHRydWUgJiYgdGhpcy5mYWN0b3J5Q29uZmlybUZvcm0uZXh0cmFPcHRpb24gIT0gbnVsbCAmJiB0aGlzLmZhY3RvcnlDb25maXJtRm9ybS5leHRyYU9wdGlvbiAhPSAnJykgew0KICAgICAgICBzdWJtaXREYXRhLmxlYXZlVGFzay5pc0RpcmVjdFN1cHBseSA9IDE7IC8vIOiuvue9ruS4uuebtOS+mw0KICAgICAgICBzdWJtaXREYXRhLmRpcmVjdFN1cHBseVRhc2sgPSBkaXJlY3RTdXBwbHlUYXNrOw0KICAgICAgICBzdWJtaXREYXRhLmRpcmVjdFN1cHBseVRhc2tNYXRlcmlhbExpc3QgPSBkaXJlY3RTdXBwbHlUYXNrTWF0ZXJpYWxMaXN0Ow0KICAgICAgfQ0KDQogICAgICBoYW5kbGVVbmxvYWQoc3VibWl0RGF0YSkudGhlbihyZXMgPT4gew0KICAgICAgICBjb25zb2xlLmxvZygiaGFuZGxlVW5sb2FkIiwgcmVzKQ0KICAgICAgICBpZiAocmVzLmNvZGUgPT0gMjAwKSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfnoa7orqTlhaXlupPmiJDlip8nKTsNCiAgICAgICAgICB0aGlzLmZhY3RvcnlDb25maXJtRGlhbG9nVmlzaWJsZSA9IGZhbHNlOw0KICAgICAgICAgIHRoaXMuZ2V0VGFza0xvZ0xpc3QodGhpcy50YXNrTm8pOw0KICAgICAgICAgIHRoaXMuZ2V0VGFza0luZm8oKTsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAvLyDlhbbku5blpLHotKXljp/lm6ANCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKHJlcy5tZXNzYWdlIHx8ICfnoa7orqTlhaXlupPlpLHotKUnKTsNCiAgICAgICAgfQ0KICAgICAgfSkuY2F0Y2goZXJyID0+IHsNCiAgICAgICAgY29uc29sZS5lcnJvcignaGFuZGxlRGlyZWN0U3VwcGx5IGVycm9yOicsIGVycik7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+e9kee7nOW8guW4uO+8jOeojeWQjumHjeivlScpOw0KICAgICAgfSk7DQogICAgfSwNCg0KICAgIHN1Ym1pdFN0b2NrT3V0Q29uZmlybSgpIHsNCg0KICAgICAgLy8g5Yik5pat55So5oi36KeS6Imy5p2D6ZmQDQogICAgICBjb25zdCByb2xlcyA9IHRoaXMuJHN0b3JlLmdldHRlcnMucm9sZXM7DQogICAgICBpZiAoIXJvbGVzLmluY2x1ZGVzKCdsZWF2ZS51bmxvYWRpbmcnKSkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfmgqjmsqHmnInnoa7orqTlh7rlupPmnYPpmZAnKTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KICAgICAgLy8g5p6E5bu65o+Q5Lqk5pWw5o2uDQogICAgICBsZXQgc3VibWl0RGF0YSA9IHsNCiAgICAgICAgbGVhdmVUYXNrOiB7DQogICAgICAgICAgLy90b2RvIOiuoemHj+ezu+e7n+ihpeWFheS/oeaBr+W+heWujOWWhA0KICAgICAgICAgIGlkOiB0aGlzLmRpc3BhdGNoSWQsDQogICAgICAgICAgdGFza05vOiB0aGlzLnRhc2tObywNCiAgICAgICAgICBhcHBseU5vOiB0aGlzLmFwcGx5Tm8sDQogICAgICAgICAgcGxhbk5vOiB0aGlzLnRhc2tJbmZvRm9ybS5wbGFuTm8sDQogICAgICAgICAgLy8g5Ye65bqT5L+h5oGvDQogICAgICAgICAgc3RvY2tPdXRTcGVjMUxlbmd0aDogdGhpcy5mYWN0b3J5Q29uZmlybUZvcm0uc3RvY2tPdXRTcGVjMUxlbmd0aCwNCiAgICAgICAgICBzdG9ja091dFNwZWMyV2lkdGg6IHRoaXMuZmFjdG9yeUNvbmZpcm1Gb3JtLnN0b2NrT3V0U3BlYzJXaWR0aCwNCiAgICAgICAgICBzdG9ja091dFRvdGFsczogdGhpcy5mYWN0b3J5Q29uZmlybUZvcm0uc3RvY2tPdXRUb3RhbCArIHRoaXMuZmFjdG9yeUNvbmZpcm1Gb3JtLnN0b2NrT3V0VG90YWxVbml0LA0KICAgICAgICAgIHN0b2NrT3V0UHJvY2Vzc1R5cGU6IHRoaXMuZmFjdG9yeUNvbmZpcm1Gb3JtLnN0b2NrT3V0UHJvY2Vzc1R5cGUsDQogICAgICAgICAgc3RvY2tPdXRIZWF0Tm86IHRoaXMuZmFjdG9yeUNvbmZpcm1Gb3JtLnN0b2NrT3V0SGVhdE5vLA0KICAgICAgICAgIHN0b2NrT3V0U3RlZWxHcmFkZTogdGhpcy5mYWN0b3J5Q29uZmlybUZvcm0uc3RvY2tPdXRTdGVlbEdyYWRlLA0KICAgICAgICAgIHN0b2NrT3V0QXhsZXM6IHRoaXMuZmFjdG9yeUNvbmZpcm1Gb3JtLnN0b2NrT3V0QXhsZXMsDQogICAgICAgICAgc3RvY2tPdXRSZW1hcms6IHRoaXMuZmFjdG9yeUNvbmZpcm1Gb3JtLnN0b2NrT3V0UmVtYXJrLA0KDQogICAgICAgICAgLy8g5pu05pS55Lu75Yqh54q25oCBOiA5DQogICAgICAgICAgdGFza1N0YXR1czogMywNCiAgICAgICAgICBjYXJOdW06IHRoaXMudGFza0luZm9Gb3JtLmNhck51bSwNCiAgICAgICAgfSwNCiAgICAgICAgbGVhdmVQbGFuOiB0aGlzLnBsYW5Gb3JtLA0KICAgICAgICBsZWF2ZVRhc2tNYXRlcmlhbDogdGhpcy50YXNrTWF0ZXJpYWxzWzBdLA0KICAgICAgfTsNCg0KICAgICAgaGFuZGxlU3RvY2tPdXQoc3VibWl0RGF0YSkudGhlbihyZXMgPT4gew0KICAgICAgICBjb25zb2xlLmxvZygiaGFuZGxlU3RvY2tPdXQiLCByZXMpDQogICAgICAgIGlmIChyZXMuY29kZSA9PSAyMDApIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+ehruiupOWHuuW6k+aIkOWKnycpOw0KICAgICAgICAgIHRoaXMuZmFjdG9yeUNvbmZpcm1EaWFsb2dWaXNpYmxlID0gZmFsc2U7DQogICAgICAgICAgdGhpcy5nZXRUYXNrTG9nTGlzdCh0aGlzLnRhc2tObyk7DQogICAgICAgICAgdGhpcy5nZXRUYXNrSW5mbygpOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIC8vIOWFtuS7luWksei0peWOn+WboA0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IocmVzLm1lc3NhZ2UgfHwgJ+ehruiupOWHuuW6k+Wksei0pScpOw0KICAgICAgICB9DQogICAgICB9KS5jYXRjaChlcnIgPT4gew0KICAgICAgICBjb25zb2xlLmVycm9yKCdoYW5kbGVEaXJlY3RTdXBwbHkgZXJyb3I6JywgZXJyKTsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign572R57uc5byC5bi477yM56iN5ZCO6YeN6K+VJyk7DQogICAgICB9KTsNCiAgICB9LA0KDQogICAgaGFuZGxlRmFjdG9yeUNvbmZpcm0oKSB7DQogICAgICBpZiAodGhpcy5lZGl0RmFjdG9yeVN0YXR1cykgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+ivt+WFiOS/neWtmCcpOw0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCg0KDQogICAgICAvL3RvZG8NCiAgICAgIC8v55Sf5oiQ5rS+6L2m5pel5b+XDQogICAgICBsZXQgbGVhdmVUYXNrTG9nID0ge307DQogICAgICBsZWF2ZVRhc2tMb2cubG9nVHlwZSA9IDI7DQogICAgICBsZWF2ZVRhc2tMb2cudGFza05vID0gdGhpcy50YXNrTm87DQogICAgICBsZWF2ZVRhc2tMb2cuYXBwbHlObyA9IHRoaXMuYXBwbHlObzsNCiAgICAgIGxlYXZlVGFza0xvZy5pbmZvID0gJ+WIhuWOguehruiupOaVsOmHjyc7DQoNCg0KICAgICAgbGV0IGZhY3RvcnlUYXNrSW5mbyA9IHt9DQogICAgICAvL3RvZG8g5Ye65YWl5Zy6DQogICAgICBmYWN0b3J5VGFza0luZm8uaWQgPSB0aGlzLnRhc2tJbmZvRm9ybS5pZA0KICAgICAgZmFjdG9yeVRhc2tJbmZvLnVubG9hZGluZ1dvcmtObyA9ICfljbjotKfkurrljaDkvY3nrKYnDQogICAgICBmYWN0b3J5VGFza0luZm8udW5sb2FkaW5nVGltZSA9IG5ldyBEYXRlKCkNCiAgICAgIGZhY3RvcnlUYXNrSW5mby50YXNrU3RhdHVzID0gOQ0KDQogICAgICBsZXQgcGFyYW0gPSB7fTsNCiAgICAgIHBhcmFtLnRhc2tNYXRlcmlhbExpc3QgPSB0aGlzLnRhc2tNYXRlcmlhbHM7DQogICAgICBwYXJhbS5sZWF2ZUxvZyA9IGxlYXZlVGFza0xvZzsNCiAgICAgIHBhcmFtLmxlYXZlVGFzayA9IGZhY3RvcnlUYXNrSW5mbzsNCiAgICAgIHBhcmFtLm1lYXN1cmVGbGFnID0gdGhpcy5tZWFzdXJlRmxhZzsNCg0KICAgICAgYWRkTGVhdmVMb2dBbmRFZGl0VGFza01hdGVyaWFsc0FuZFVwZGF0ZVRhc2socGFyYW0pLnRoZW4ocmVzID0+IHsNCiAgICAgICAgY29uc29sZS5sb2coImFkZExlYXZlTG9nQW5kRWRpdFRhc2tNYXRlcmlhbHNBbmRVcGRhdGVUYXNrIiwgcmVzKQ0KICAgICAgICBpZiAocmVzLmNvZGUgPT0gMjAwKSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfliIbljoLnoa7orqTmiJDlip8nKTsNCiAgICAgICAgICB0aGlzLmdldFRhc2tMb2dMaXN0KHRoaXMudGFza05vKTsNCiAgICAgICAgICB0aGlzLmdldFRhc2tJbmZvKCk7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgLy8g5YW25LuW5aSx6LSl5Y6f5ZugDQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihyZXMubWVzc2FnZSB8fCAn5YiG5Y6C56Gu6K6k5oiQ5YqfJyk7DQogICAgICAgIH0NCiAgICAgIH0pLmNhdGNoKGVyciA9PiB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ2hhbmRsZUZhY3RvcnlDb25maXJtIGVycm9yOicsIGVycik7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+e9kee7nOW8guW4uO+8jOeojeWQjumHjeivlScpOw0KICAgICAgfSk7DQogICAgfSwNCg0KDQogICAgaGFuZGxlRG9vck1hbkNvbmZpcm0oKSB7DQogICAgICBpZiAodGhpcy5lZGl0RG9vck1hblN0YXR1cykgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+ivt+WFiOS/neWtmCcpOw0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCg0KICAgICAgbGV0IGxlYXZlVGFza0xvZyA9IHt9Ow0KICAgICAgbGVhdmVUYXNrTG9nLmxvZ1R5cGUgPSAyOw0KICAgICAgbGVhdmVUYXNrTG9nLnRhc2tObyA9IHRoaXMudGFza05vOw0KICAgICAgbGVhdmVUYXNrTG9nLmFwcGx5Tm8gPSB0aGlzLmFwcGx5Tm87DQogICAgICBsZWF2ZVRhc2tMb2cuaW5mbyA9ICfpl6jljavnoa7orqTmlbDph48nOw0KDQoNCg0KICAgICAgbGV0IGRvb3JNYW5UYXNrSW5mbyA9IHt9DQogICAgICBkb29yTWFuVGFza0luZm8uaWQgPSB0aGlzLnRhc2tJbmZvRm9ybS5pZA0KICAgICAgaWYgKHRoaXMudGFza0luZm9Gb3JtLnRhc2tUeXBlID09IDEpIHsNCiAgICAgICAgZG9vck1hblRhc2tJbmZvLnRhc2tTdGF0dXMgPSA5DQogICAgICAgIGRvb3JNYW5UYXNrSW5mby5sZWF2ZVRpbWUgPSBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCkuc2xpY2UoMCwgMTkpLnJlcGxhY2UoJ1QnLCAnICcpDQogICAgICAgIC8v56a75Y6C5aSn6ZeoDQogICAgICB9IGVsc2UgaWYgKHRoaXMudGFza0luZm9Gb3JtLnRhc2tUeXBlID09IDIgJiYgdGhpcy5tZWFzdXJlRmxhZyA9PSAwKSB7DQogICAgICAgIGRvb3JNYW5UYXNrSW5mby50YXNrU3RhdHVzID0gNw0KICAgICAgICBkb29yTWFuVGFza0luZm8uZW50ZXJUaW1lID0gbmV3IERhdGUoKS50b0lTT1N0cmluZygpLnNsaWNlKDAsIDE5KS5yZXBsYWNlKCdUJywgJyAnKQ0KICAgICAgICAvL+WHuuWOguWkp+mXqA0KICAgICAgfSBlbHNlIGlmICh0aGlzLnRhc2tJbmZvRm9ybS50YXNrVHlwZSA9PSAyICYmIHRoaXMubWVhc3VyZUZsYWcgPT0gMSkgew0KICAgICAgICBkb29yTWFuVGFza0luZm8udGFza1N0YXR1cyA9IDYNCiAgICAgICAgZG9vck1hblRhc2tJbmZvLmVudGVyVGltZSA9IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKS5zbGljZSgwLCAxOSkucmVwbGFjZSgnVCcsICcgJykNCiAgICAgICAgLy/lh7rljoLlpKfpl6gNCiAgICAgIH0gZWxzZSBpZiAodGhpcy50YXNrSW5mb0Zvcm0udGFza1R5cGUgPT0gMyAmJiB0aGlzLnRhc2tJbmZvRm9ybS50YXNrU3RhdHVzID09IDQpIHsNCiAgICAgICAgZG9vck1hblRhc2tJbmZvLnRhc2tTdGF0dXMgPSA1DQogICAgICAgIGRvb3JNYW5UYXNrSW5mby5sZWF2ZVRpbWUgPSBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCkuc2xpY2UoMCwgMTkpLnJlcGxhY2UoJ1QnLCAnICcpDQogICAgICAgIC8v56a75Y6C5aSn6ZeoDQogICAgICB9IGVsc2UgaWYgKHRoaXMudGFza0luZm9Gb3JtLnRhc2tUeXBlID09IDMgJiYgdGhpcy5tZWFzdXJlRmxhZyA9PSAwICYmIHRoaXMudGFza0luZm9Gb3JtLnRhc2tTdGF0dXMgPT0gNSkgew0KICAgICAgICBkb29yTWFuVGFza0luZm8udGFza1N0YXR1cyA9IDcNCiAgICAgICAgZG9vck1hblRhc2tJbmZvLmVudGVyVGltZSA9IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKS5zbGljZSgwLCAxOSkucmVwbGFjZSgnVCcsICcgJykNCiAgICAgICAgLy/lh7rljoLlpKfpl6gNCiAgICAgIH0gZWxzZSBpZiAodGhpcy50YXNrSW5mb0Zvcm0udGFza1R5cGUgPT0gMyAmJiB0aGlzLm1lYXN1cmVGbGFnID09IDEgJiYgdGhpcy50YXNrSW5mb0Zvcm0udGFza1N0YXR1cyA9PSA1KSB7DQogICAgICAgIGRvb3JNYW5UYXNrSW5mby50YXNrU3RhdHVzID0gNg0KICAgICAgICBkb29yTWFuVGFza0luZm8uZW50ZXJUaW1lID0gbmV3IERhdGUoKS50b0lTT1N0cmluZygpLnNsaWNlKDAsIDE5KS5yZXBsYWNlKCdUJywgJyAnKQ0KICAgICAgICAvL+WHuuWOguWkp+mXqA0KICAgICAgfQ0KDQogICAgICBsZXQgcGFyYW0gPSB7fTsNCiAgICAgIHBhcmFtLnRhc2tNYXRlcmlhbExpc3QgPSB0aGlzLnRhc2tNYXRlcmlhbHM7DQogICAgICBwYXJhbS5sZWF2ZUxvZyA9IGxlYXZlVGFza0xvZzsNCiAgICAgIHBhcmFtLmxlYXZlVGFzayA9IGRvb3JNYW5UYXNrSW5mbzsNCiAgICAgIHBhcmFtLm1lYXN1cmVGbGFnID0gdGhpcy5tZWFzdXJlRmxhZzsNCg0KICAgICAgYWRkTGVhdmVMb2dBbmRFZGl0VGFza01hdGVyaWFsc0FuZFVwZGF0ZVRhc2socGFyYW0pLnRoZW4ocmVzID0+IHsNCiAgICAgICAgY29uc29sZS5sb2coImFkZExlYXZlTG9nQW5kRWRpdFRhc2tNYXRlcmlhbHNBbmRVcGRhdGVUYXNrIiwgcmVzKQ0KICAgICAgICBpZiAocmVzLmNvZGUgPT0gMjAwKSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfpl6jljavnoa7orqTmiJDlip8nKTsNCiAgICAgICAgICB0aGlzLmdldFRhc2tMb2dMaXN0KHRoaXMudGFza05vKTsNCiAgICAgICAgICB0aGlzLmdldFRhc2tJbmZvKCk7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgLy8g5YW25LuW5aSx6LSl5Y6f5ZugDQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihyZXMubWVzc2FnZSB8fCAn6Zeo5Y2r56Gu6K6k5oiQ5YqfJyk7DQogICAgICAgIH0NCiAgICAgIH0pLmNhdGNoKGVyciA9PiB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ2hhbmRsZURvb3JNYW5Db25maXJtIGVycm9yOicsIGVycik7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+e9kee7nOW8guW4uO+8jOeojeWQjumHjeivlScpOw0KICAgICAgfSk7DQoNCiAgICAgIC8vIHRoaXMudGFza01hdGVyaWFscy5tYXAoaXRlbSA9PiB7DQogICAgICAvLyAgIGVkaXRUYXNrbWF0ZXJpYWxzKGl0ZW0pOw0KICAgICAgLy8gfSkNCiAgICAgIC8vdG9kbw0KICAgICAgLy8gbGV0IGxlYXZlVGFza0xvZyA9IHt9Ow0KICAgICAgLy8gbGVhdmVUYXNrTG9nLmxvZ1R5cGUgPSAyOw0KICAgICAgbGVhdmVUYXNrTG9nLnRhc2tObyA9IHRoaXMudGFza05vOw0KICAgICAgbGVhdmVUYXNrTG9nLmFwcGx5Tm8gPSB0aGlzLmFwcGx5Tm87DQogICAgICBsZWF2ZVRhc2tMb2cuaW5mbyA9ICfpl6jljavnoa7orqTmlbDph48nOw0KICAgICAgLy8gYWRkTGVhdmVMb2cobGVhdmVUYXNrTG9nKTsNCiAgICAgIC8vIHRoaXMuZ2V0VGFza0xvZ0xpc3QodGhpcy50YXNrTm8pOw0KDQogICAgICAvLyBsZXQgZG9vck1hblRhc2tJbmZvID0ge30NCiAgICAgIC8vIGRvb3JNYW5UYXNrSW5mby5pZCA9IHRoaXMudGFza0luZm9Gb3JtLmlkDQogICAgICAvLyBpZiAodGhpcy50YXNrSW5mb0Zvcm0udGFza1R5cGUgPT0gMSkgew0KICAgICAgLy8gICBkb29yTWFuVGFza0luZm8udGFza1N0YXR1cyA9IDkNCiAgICAgIC8vICAgZG9vck1hblRhc2tJbmZvLmxlYXZlVGltZSA9IG5ldyBEYXRlKCkNCiAgICAgIC8vICAgLy/nprvljoLlpKfpl6gNCiAgICAgIC8vIH0gZWxzZSBpZiAodGhpcy50YXNrSW5mb0Zvcm0udGFza1R5cGUgPT0gMiAmJiB0aGlzLm1lYXN1cmVGbGFnID09IDApIHsNCiAgICAgIC8vICAgZG9vck1hblRhc2tJbmZvLnRhc2tTdGF0dXMgPSA3DQogICAgICAvLyAgIGRvb3JNYW5UYXNrSW5mby5lbnRlclRpbWUgPSBuZXcgRGF0ZSgpDQogICAgICAvLyAgIC8v5Ye65Y6C5aSn6ZeoDQogICAgICAvLyB9IGVsc2UgaWYgKHRoaXMudGFza0luZm9Gb3JtLnRhc2tUeXBlID09IDIgJiYgdGhpcy5tZWFzdXJlRmxhZyA9PSAxKSB7DQogICAgICAvLyAgIGRvb3JNYW5UYXNrSW5mby50YXNrU3RhdHVzID0gNg0KICAgICAgLy8gICBkb29yTWFuVGFza0luZm8uZW50ZXJUaW1lID0gbmV3IERhdGUoKQ0KICAgICAgLy8gICAvL+WHuuWOguWkp+mXqA0KICAgICAgLy8gfSBlbHNlIGlmICh0aGlzLnRhc2tJbmZvRm9ybS50YXNrVHlwZSA9PSAzICYmIHRoaXMudGFza0luZm9Gb3JtLnRhc2tTdGF0dXMgPT0gNCkgew0KICAgICAgLy8gICBkb29yTWFuVGFza0luZm8udGFza1N0YXR1cyA9IDUNCiAgICAgIC8vICAgZG9vck1hblRhc2tJbmZvLmxlYXZlVGltZSA9IG5ldyBEYXRlKCkNCiAgICAgIC8vICAgLy/nprvljoLlpKfpl6gNCiAgICAgIC8vIH0gZWxzZSBpZiAodGhpcy50YXNrSW5mb0Zvcm0udGFza1R5cGUgPT0gMyAmJiB0aGlzLm1lYXN1cmVGbGFnID09IDAgJiYgdGhpcy50YXNrSW5mb0Zvcm0udGFza1N0YXR1cyA9PSA1KSB7DQogICAgICAvLyAgIGRvb3JNYW5UYXNrSW5mby50YXNrU3RhdHVzID0gNw0KICAgICAgLy8gICBkb29yTWFuVGFza0luZm8uZW50ZXJUaW1lID0gbmV3IERhdGUoKQ0KICAgICAgLy8gICAvL+WHuuWOguWkp+mXqA0KICAgICAgLy8gfSBlbHNlIGlmICh0aGlzLnRhc2tJbmZvRm9ybS50YXNrVHlwZSA9PSAzICYmIHRoaXMubWVhc3VyZUZsYWcgPT0gMSAmJiB0aGlzLnRhc2tJbmZvRm9ybS50YXNrU3RhdHVzID09IDUpIHsNCiAgICAgIC8vICAgZG9vck1hblRhc2tJbmZvLnRhc2tTdGF0dXMgPSA2DQogICAgICAvLyAgIGRvb3JNYW5UYXNrSW5mby5lbnRlclRpbWUgPSBuZXcgRGF0ZSgpDQogICAgICAvLyAgIC8v5Ye65Y6C5aSn6ZeoDQogICAgICAvLyB9DQogICAgICAvLyB1cGRhdGVUYXNrKGRvb3JNYW5UYXNrSW5mbyk7DQogICAgICAvLyB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+mXqOWNq+ehruiupOaIkOWKnycpOw0KDQogICAgICAvLyBzZXRUaW1lb3V0KCgpID0+IHsNCiAgICAgIC8vICAgdGhpcy5nZXRUYXNrSW5mbygpOw0KICAgICAgLy8gfSwgNTAwKQ0KDQogICAgfSwNCg0KICAgIGhhbmRsZURvb3JNYW5NZWFzdXJlQ29uZmlybSgpIHsNCiAgICAgIC8vIOWIpOaWreeUqOaIt+inkuiJsuadg+mZkA0KICAgICAgY29uc3Qgcm9sZXMgPSB0aGlzLiRzdG9yZS5nZXR0ZXJzLnJvbGVzOw0KICAgICAgaWYgKCFyb2xlcy5pbmNsdWRlcygnbGVhdmUuZ3VhcmQnKSkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfmgqjmsqHmnInpl6jljavlh7rljoLnoa7orqTmnYPpmZAnKTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KDQogICAgICBsZXQgbGVhdmVUYXNrTG9nID0ge307DQogICAgICBsZWF2ZVRhc2tMb2cubG9nVHlwZSA9IDI7DQogICAgICBsZWF2ZVRhc2tMb2cudGFza05vID0gdGhpcy50YXNrTm87DQogICAgICBsZWF2ZVRhc2tMb2cuYXBwbHlObyA9IHRoaXMuYXBwbHlObzsNCiAgICAgIGlmICh0aGlzLnRhc2tJbmZvRm9ybS50YXNrU3RhdHVzID09IDQpIHsNCiAgICAgICAgbGVhdmVUYXNrTG9nLmluZm8gPSAn6Zeo5Y2r5Ye65Y6C56Gu6K6k77yM56Gu6K6k54mp6LWE77yaJyArIHRoaXMudGFza01hdGVyaWFscy5tYXAoaXRlbSA9PiBpdGVtLm1hdGVyaWFsTmFtZSkuam9pbign44CBICcpOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgbGVhdmVUYXNrTG9nLmluZm8gPSAn6Zeo5Y2r5YWl5Y6C56Gu6K6k77yM56Gu6K6k54mp6LWE77yaJyArIHRoaXMudGFza01hdGVyaWFscy5tYXAoaXRlbSA9PiBpdGVtLm1hdGVyaWFsTmFtZSkuam9pbign44CBICcpOw0KICAgICAgfQ0KDQogICAgICBsZXQgZG9vck1hblRhc2tJbmZvID0ge30NCiAgICAgIGRvb3JNYW5UYXNrSW5mby5pZCA9IHRoaXMudGFza0luZm9Gb3JtLmlkDQogICAgICBpZiAodGhpcy50YXNrSW5mb0Zvcm0udGFza1R5cGUgPT0gMSkgew0KICAgICAgICBkb29yTWFuVGFza0luZm8udGFza1N0YXR1cyA9IDkNCiAgICAgICAgZG9vck1hblRhc2tJbmZvLmxlYXZlVGltZSA9IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKS5zbGljZSgwLCAxOSkucmVwbGFjZSgnVCcsICcgJykNCiAgICAgICAgLy/nprvljoLlpKfpl6gNCiAgICAgIH0gZWxzZSBpZiAodGhpcy50YXNrSW5mb0Zvcm0udGFza1R5cGUgPT0gMiAmJiB0aGlzLm1lYXN1cmVGbGFnID09IDApIHsNCiAgICAgICAgZG9vck1hblRhc2tJbmZvLnRhc2tTdGF0dXMgPSA3DQogICAgICAgIGRvb3JNYW5UYXNrSW5mby5lbnRlclRpbWUgPSBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCkuc2xpY2UoMCwgMTkpLnJlcGxhY2UoJ1QnLCAnICcpDQogICAgICAgIC8v5Ye65Y6C5aSn6ZeoDQogICAgICB9IGVsc2UgaWYgKHRoaXMudGFza0luZm9Gb3JtLnRhc2tUeXBlID09IDIgJiYgdGhpcy5tZWFzdXJlRmxhZyA9PSAxKSB7DQogICAgICAgIGRvb3JNYW5UYXNrSW5mby50YXNrU3RhdHVzID0gNg0KICAgICAgICBkb29yTWFuVGFza0luZm8uZW50ZXJUaW1lID0gbmV3IERhdGUoKS50b0lTT1N0cmluZygpLnNsaWNlKDAsIDE5KS5yZXBsYWNlKCdUJywgJyAnKQ0KICAgICAgICAvL+WHuuWOguWkp+mXqA0KICAgICAgfSBlbHNlIGlmICh0aGlzLnRhc2tJbmZvRm9ybS50YXNrVHlwZSA9PSAzICYmIHRoaXMudGFza0luZm9Gb3JtLnRhc2tTdGF0dXMgPT0gNCkgew0KICAgICAgICBkb29yTWFuVGFza0luZm8udGFza1N0YXR1cyA9IDUNCiAgICAgICAgZG9vck1hblRhc2tJbmZvLmxlYXZlVGltZSA9IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKS5zbGljZSgwLCAxOSkucmVwbGFjZSgnVCcsICcgJykNCiAgICAgICAgLy/nprvljoLlpKfpl6gNCiAgICAgIH0gZWxzZSBpZiAodGhpcy50YXNrSW5mb0Zvcm0udGFza1R5cGUgPT0gMyAmJiB0aGlzLm1lYXN1cmVGbGFnID09IDAgJiYgdGhpcy50YXNrSW5mb0Zvcm0udGFza1N0YXR1cyA9PSA1KSB7DQogICAgICAgIGRvb3JNYW5UYXNrSW5mby50YXNrU3RhdHVzID0gNw0KICAgICAgICBkb29yTWFuVGFza0luZm8uZW50ZXJUaW1lID0gbmV3IERhdGUoKS50b0lTT1N0cmluZygpLnNsaWNlKDAsIDE5KS5yZXBsYWNlKCdUJywgJyAnKQ0KICAgICAgICAvL+WHuuWOguWkp+mXqA0KICAgICAgfSBlbHNlIGlmICh0aGlzLnRhc2tJbmZvRm9ybS50YXNrVHlwZSA9PSAzICYmIHRoaXMubWVhc3VyZUZsYWcgPT0gMSAmJiB0aGlzLnRhc2tJbmZvRm9ybS50YXNrU3RhdHVzID09IDUpIHsNCiAgICAgICAgZG9vck1hblRhc2tJbmZvLnRhc2tTdGF0dXMgPSA2DQogICAgICAgIGRvb3JNYW5UYXNrSW5mby5lbnRlclRpbWUgPSBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCkuc2xpY2UoMCwgMTkpLnJlcGxhY2UoJ1QnLCAnICcpDQogICAgICAgIC8v5Ye65Y6C5aSn6ZeoDQogICAgICB9DQoNCiAgICAgIGxldCBwYXJhbSA9IHt9Ow0KICAgICAgcGFyYW0udGFza01hdGVyaWFsTGlzdCA9IHRoaXMudGFza01hdGVyaWFsczsNCiAgICAgIHBhcmFtLmxlYXZlTG9nID0gbGVhdmVUYXNrTG9nOw0KICAgICAgcGFyYW0ubGVhdmVUYXNrID0gZG9vck1hblRhc2tJbmZvOw0KICAgICAgcGFyYW0ubWVhc3VyZUZsYWcgPSB0aGlzLm1lYXN1cmVGbGFnOw0KDQogICAgICBhZGRMZWF2ZUxvZ0FuZEVkaXRUYXNrTWF0ZXJpYWxzQW5kVXBkYXRlVGFzayhwYXJhbSkudGhlbihyZXMgPT4gew0KICAgICAgICBjb25zb2xlLmxvZygiYWRkTGVhdmVMb2dBbmRFZGl0VGFza01hdGVyaWFsc0FuZFVwZGF0ZVRhc2siLCByZXMpDQogICAgICAgIGlmIChyZXMuY29kZSA9PSAyMDApIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+mXqOWNq+ehruiupOaIkOWKnycpOw0KICAgICAgICAgIHRoaXMuZ2V0VGFza0xvZ0xpc3QodGhpcy50YXNrTm8pOw0KICAgICAgICAgIHRoaXMuZ2V0VGFza0luZm8oKTsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAvLyDlhbbku5blpLHotKXljp/lm6ANCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKHJlcy5tZXNzYWdlIHx8ICfpl6jljavnoa7orqTmiJDlip8nKTsNCiAgICAgICAgfQ0KICAgICAgfSkuY2F0Y2goZXJyID0+IHsNCiAgICAgICAgY29uc29sZS5lcnJvcignaGFuZGxlRG9vck1hbkNvbmZpcm0gZXJyb3I6JywgZXJyKTsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign572R57uc5byC5bi477yM56iN5ZCO6YeN6K+VJyk7DQogICAgICB9KTsNCiAgICAgIC8vdG9kbw0KDQogICAgfSwNCiAgICAvLyDnlJ/miJDkuoznu7TnoIENCiAgICBjcmVhdFFyQ29kZSgpIHsNCiAgICAgIGlmICh0aGlzLnRhc2tJbmZvRm9ybS5xckNvZGVDb250ZW50KSB7DQogICAgICAgIHRoaXMuJHJlZnMucXJDb2RlLmlubmVySFRNTCA9ICIiOw0KICAgICAgICB2YXIgWVNxckNvZGUgPSBuZXcgUVJDb2RlKHRoaXMuJHJlZnMucXJDb2RlLCB7DQogICAgICAgICAgdGV4dDogdGhpcy50YXNrSW5mb0Zvcm0ucXJDb2RlQ29udGVudCwgLy8g6ZyA6KaB6L2s5o2i5Li65LqM57u056CB55qE5YaF5a65DQogICAgICAgICAgd2lkdGg6IDEyMCwNCiAgICAgICAgICBoZWlnaHQ6IDEyMCwNCiAgICAgICAgICBjb2xvckRhcms6ICIjMDAwMDAwIiwNCiAgICAgICAgICBjb2xvckxpZ2h0OiAiI2ZmZmZmZiIsDQogICAgICAgICAgY29ycmVjdExldmVsOiBRUkNvZGUuQ29ycmVjdExldmVsLkgsDQogICAgICAgIH0pOw0KICAgICAgfQ0KICAgIH0sDQogICAgZ2V0VGFza0xvZ0xpc3QodGFza05vKSB7DQogICAgICBsZXQgdGFza0xvZyA9IHt9Ow0KICAgICAgdGFza0xvZy50YXNrTm8gPSB0YXNrTm8NCiAgICAgIGdldFRhc2tMb2dzKHRhc2tMb2cpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICBjb25zb2xlLmxvZygiZ2V0VGFza0xvZ3MiLCByZXNwb25zZSk7DQogICAgICAgIC8vIHRoaXMudGFza0xvZ3MgPSByZXNwb25zZS5yb3dzOw0KICAgICAgICBsZXQgbG9ncyA9IHJlc3BvbnNlLnJvd3MgfHwgW107DQogICAgICAgIC8vIOaJvuWHuuWMheWQqyLku7vliqHlrozmiJAi55qE5pel5b+XDQogICAgICAgIGNvbnN0IGZpbmlzaGVkTG9ncyA9IGxvZ3MuZmlsdGVyKGxvZyA9PiBsb2cuaW5mbyAmJiBsb2cuaW5mby5pbmNsdWRlcygn5Lu75Yqh5a6M5oiQJykpOw0KICAgICAgICBjb25zdCBvdGhlckxvZ3MgPSBsb2dzLmZpbHRlcihsb2cgPT4gIShsb2cuaW5mbyAmJiBsb2cuaW5mby5pbmNsdWRlcygn5Lu75Yqh5a6M5oiQJykpKTsNCiAgICAgICAgLy8g5YWI5pS+IuS7u+WKoeWujOaIkCLvvIzlho3mlL7lhbbku5YNCiAgICAgICAgdGhpcy50YXNrTG9ncyA9IFsuLi5maW5pc2hlZExvZ3MsIC4uLm90aGVyTG9nc107DQogICAgICB9KQ0KDQogICAgfSwNCiAgICBhc3luYyBnZXRUYXNrbWF0ZXJpYWxMaXN0KHRhc2tObykgew0KICAgICAgdHJ5IHsNCiAgICAgICAgY29uc29sZS5sb2coImdldFRhc2ttYXRlcmlhbExpc3QiKTsNCiAgICAgICAgbGV0IGxlYXZlTWF0ZXJpYWwgPSB7fTsNCiAgICAgICAgbGVhdmVNYXRlcmlhbC50YXNrTm8gPSB0YXNrTm87DQogICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZ2V0VGFza21hdGVyaWFscyhsZWF2ZU1hdGVyaWFsKTsNCiAgICAgICAgdGhpcy50YXNrTWF0ZXJpYWxzID0gcmVzcG9uc2Uucm93czsNCiAgICAgICAgLy8g6LWL5YC85ZCO77yM5Yid5aeL5YyW5q+P5Liq5YWD57Sg55qEIGRvb3JtYW5SZWNlaXZlTnVtIOWSjCBkb29ybWFuUmVjZWl2ZU51bUluDQogICAgICAgIHRoaXMudGFza01hdGVyaWFscy5mb3JFYWNoKGl0ZW0gPT4gew0KICAgICAgICAgIGl0ZW0uZG9vcm1hblJlY2VpdmVOdW0gPSBpdGVtLnBsYW5OdW07DQogICAgICAgICAgY29uc29sZS5sb2coIml0ZW0ucGxhblR5cGUiLCB0aGlzLnBsYW5Gb3JtLnBsYW5UeXBlKTsNCiAgICAgICAgICBpZiAodGhpcy5wbGFuRm9ybS5wbGFuVHlwZSA9PSAyIHx8IHRoaXMucGxhbkZvcm0ucGxhblR5cGUgPT0gMykgew0KICAgICAgICAgICAgaXRlbS5kb29ybWFuUmVjZWl2ZU51bUluID0gaXRlbS5wbGFuTnVtOw0KICAgICAgICAgIH0NCiAgICAgICAgfSk7DQogICAgICAgIGNvbnNvbGUubG9nKCJ0YXNrTWF0ZXJpYWxzIiwgdGhpcy50YXNrTWF0ZXJpYWxzKTsNCiAgICAgICAgcmV0dXJuIHJlc3BvbnNlOw0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcignZ2V0VGFza21hdGVyaWFsTGlzdCBlcnJvcjonLCBlcnJvcik7DQogICAgICAgIHRocm93IGVycm9yOw0KICAgICAgfQ0KICAgIH0sDQogICAgZWRpdERvb3JNYW5Sb3cocm93KSB7DQogICAgICByb3cuX2JhY2t1cCA9IEpTT04ucGFyc2UoSlNPTi5zdHJpbmdpZnkocm93KSk7Ly/mt7Hmi7fotJ0NCiAgICAgIHRoaXMuZWRpdGluZ1JvdyA9IHJvdzsNCiAgICAgIHRoaXMuZWRpdERvb3JNYW5TdGF0dXMgPSB0cnVlOw0KICAgICAgY29uc29sZS5sb2coInRoaXMuZWRpdERvb3JNYW5Sb3ciLCByb3cpOw0KICAgIH0sDQogICAgZWRpdEZhY3RvcnlSb3coKSB7DQogICAgICB0aGlzLmJhY2t1cE1hdGVyaWFscyA9IEpTT04ucGFyc2UoSlNPTi5zdHJpbmdpZnkodGhpcy50YXNrTWF0ZXJpYWxzKSk7Ly/mt7Hmi7fotJ0NCiAgICAgIHRoaXMuZWRpdEZhY3RvcnlTdGF0dXMgPSB0cnVlOw0KICAgIH0sDQogICAgY2FuY2VsRG9vck1hbkVkaXQocm93KSB7DQogICAgICAvL+a3seaLt+i0nQ0KICAgICAgaWYgKHJvdy5fYmFja3VwKSB7DQogICAgICAgIC8vIOaBouWkjeWkh+S7veaVsOaNrg0KICAgICAgICBPYmplY3QuYXNzaWduKHJvdywgcm93Ll9iYWNrdXApOw0KICAgICAgICBkZWxldGUgcm93Ll9iYWNrdXA7IC8vIOWIoOmZpOWkh+S7veaVsOaNrg0KICAgICAgfTsNCiAgICAgIHRoaXMuZWRpdGluZ1JvdyA9IG51bGw7IC8vIOa4heepuuW9k+WJjee8lui+keihjA0KICAgICAgdGhpcy5lZGl0RG9vck1hblN0YXR1cyA9IGZhbHNlOw0KICAgIH0sDQogICAgY2FuY2VsRmFjdG9yeUVkaXQoKSB7DQogICAgICB0aGlzLnRhc2tNYXRlcmlhbHMgPSBKU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KHRoaXMuYmFja3VwTWF0ZXJpYWxzKSk7Ly/mt7Hmi7fotJ0NCiAgICAgIGNvbnNvbGUubG9nKCJ0aGlzLnRhc2tNYXRlcmlhbHMiLCB0aGlzLnRhc2tNYXRlcmlhbHMpOw0KICAgICAgdGhpcy5lZGl0RmFjdG9yeVN0YXR1cyA9IGZhbHNlOw0KICAgIH0sDQoNCiAgICBzYXZlRG9vck1hblJvd0luKCkgew0KICAgICAgLy8g5Yik5pat55So5oi36KeS6Imy5p2D6ZmQDQogICAgICBjb25zdCByb2xlcyA9IHRoaXMuJHN0b3JlLmdldHRlcnMucm9sZXM7DQogICAgICBpZiAoIXJvbGVzLmluY2x1ZGVzKCdsZWF2ZS5ndWFyZCcpKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+aCqOayoeaciemXqOWNq+WHuuWOguehruiupOadg+mZkCcpOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQoNCiAgICAgIGlmICh0aGlzLnRhc2tNYXRlcmlhbHMubGVuZ3RoID09IDApIHsNCiAgICAgICAgY29uc29sZS5sb2coInRhc2tNYXRlcmlhbHMiLCB0aGlzLnRhc2tNYXRlcmlhbHMpOw0KICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+eJqei1hOW8guW4uCcpOw0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCg0KICAgICAgLy8g5qCh6aqMZG9vcm1hblJlY2VpdmVOdW1JbuaYr+WQpuetieS6jnBsYW5OdW0NCiAgICAgIGZvciAoY29uc3QgaXRlbSBvZiB0aGlzLnRhc2tNYXRlcmlhbHMpIHsNCiAgICAgICAgaWYgKGl0ZW0uZG9vcm1hblJlY2VpdmVOdW1JbiAhPT0gaXRlbS5wbGFuTnVtKSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKGDnianotYQiJHtpdGVtLm1hdGVyaWFsTmFtZX0i55qE6Zeo5Y2r5YWl5Y6C56Gu6K6k5pWw6YePKCR7aXRlbS5kb29ybWFuUmVjZWl2ZU51bUlufSnkuI7orqHliJLmlbDph48oJHtpdGVtLnBsYW5OdW19KeS4jeS4gOiHtO+8jOivt+ajgOafpWApOw0KICAgICAgICAgIHJldHVybjsNCiAgICAgICAgfQ0KICAgICAgfQ0KDQogICAgICBsZXQgbGVhdmVUYXNrTG9nID0ge307DQogICAgICBsZWF2ZVRhc2tMb2cubG9nVHlwZSA9IDI7DQogICAgICBsZWF2ZVRhc2tMb2cudGFza05vID0gdGhpcy50YXNrTm87DQogICAgICBsZWF2ZVRhc2tMb2cuYXBwbHlObyA9IHRoaXMuYXBwbHlObzsNCiAgICAgIGxlYXZlVGFza0xvZy5pbmZvID0gJ+mXqOWNq+WFpeWOguehruiupO+8jOehruiupOeJqei1hO+8micgKyB0aGlzLnRhc2tNYXRlcmlhbHMubWFwKGl0ZW0gPT4gaXRlbS5tYXRlcmlhbE5hbWUpLmpvaW4oJ+OAgSAnKTsNCg0KICAgICAgbGV0IGRvb3JNYW5UYXNrSW5mbyA9IHt9DQogICAgICBkb29yTWFuVGFza0luZm8uaWQgPSB0aGlzLnRhc2tJbmZvRm9ybS5pZDsNCiAgICAgIGlmICh0aGlzLnRhc2tJbmZvRm9ybS50YXNrVHlwZSA9PSAxKSB7DQogICAgICAgIGRvb3JNYW5UYXNrSW5mby50YXNrU3RhdHVzID0gOQ0KICAgICAgICBkb29yTWFuVGFza0luZm8ubGVhdmVUaW1lID0gbmV3IERhdGUoKS50b0lTT1N0cmluZygpLnNsaWNlKDAsIDE5KS5yZXBsYWNlKCdUJywgJyAnKQ0KICAgICAgICAvL+emu+WOguWkp+mXqA0KICAgICAgfSBlbHNlIGlmICh0aGlzLnRhc2tJbmZvRm9ybS50YXNrVHlwZSA9PSAyICYmIHRoaXMubWVhc3VyZUZsYWcgPT0gMCkgew0KICAgICAgICBkb29yTWFuVGFza0luZm8udGFza1N0YXR1cyA9IDcNCiAgICAgICAgZG9vck1hblRhc2tJbmZvLmVudGVyVGltZSA9IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKS5zbGljZSgwLCAxOSkucmVwbGFjZSgnVCcsICcgJykNCiAgICAgICAgLy/lh7rljoLlpKfpl6gNCiAgICAgIH0gZWxzZSBpZiAodGhpcy50YXNrSW5mb0Zvcm0udGFza1R5cGUgPT0gMiAmJiB0aGlzLm1lYXN1cmVGbGFnID09IDEpIHsNCiAgICAgICAgZG9vck1hblRhc2tJbmZvLnRhc2tTdGF0dXMgPSA2DQogICAgICAgIGRvb3JNYW5UYXNrSW5mby5lbnRlclRpbWUgPSBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCkuc2xpY2UoMCwgMTkpLnJlcGxhY2UoJ1QnLCAnICcpDQogICAgICAgIC8v5Ye65Y6C5aSn6ZeoDQogICAgICB9IGVsc2UgaWYgKHRoaXMudGFza0luZm9Gb3JtLnRhc2tUeXBlID09IDMgJiYgdGhpcy50YXNrSW5mb0Zvcm0udGFza1N0YXR1cyA9PSA0KSB7DQogICAgICAgIGRvb3JNYW5UYXNrSW5mby50YXNrU3RhdHVzID0gNQ0KICAgICAgICBkb29yTWFuVGFza0luZm8ubGVhdmVUaW1lID0gbmV3IERhdGUoKS50b0lTT1N0cmluZygpLnNsaWNlKDAsIDE5KS5yZXBsYWNlKCdUJywgJyAnKQ0KICAgICAgICAvL+emu+WOguWkp+mXqA0KICAgICAgfSBlbHNlIGlmICh0aGlzLnRhc2tJbmZvRm9ybS50YXNrVHlwZSA9PSAzICYmIHRoaXMubWVhc3VyZUZsYWcgPT0gMCAmJiB0aGlzLnRhc2tJbmZvRm9ybS50YXNrU3RhdHVzID09IDUpIHsNCiAgICAgICAgZG9vck1hblRhc2tJbmZvLnRhc2tTdGF0dXMgPSA3DQogICAgICAgIGRvb3JNYW5UYXNrSW5mby5lbnRlclRpbWUgPSBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCkuc2xpY2UoMCwgMTkpLnJlcGxhY2UoJ1QnLCAnICcpDQogICAgICAgIC8v5Ye65Y6C5aSn6ZeoDQogICAgICB9IGVsc2UgaWYgKHRoaXMudGFza0luZm9Gb3JtLnRhc2tUeXBlID09IDMgJiYgdGhpcy5tZWFzdXJlRmxhZyA9PSAxICYmIHRoaXMudGFza0luZm9Gb3JtLnRhc2tTdGF0dXMgPT0gNSkgew0KICAgICAgICBkb29yTWFuVGFza0luZm8udGFza1N0YXR1cyA9IDYNCiAgICAgICAgZG9vck1hblRhc2tJbmZvLmVudGVyVGltZSA9IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKS5zbGljZSgwLCAxOSkucmVwbGFjZSgnVCcsICcgJykNCiAgICAgICAgLy/lh7rljoLlpKfpl6gNCiAgICAgIH0NCg0KICAgICAgbGV0IHBhcmFtID0gew0KICAgICAgICB0YXNrTWF0ZXJpYWxMaXN0OiB0aGlzLnRhc2tNYXRlcmlhbHMsDQogICAgICAgIGxlYXZlTG9nOiBsZWF2ZVRhc2tMb2csDQogICAgICAgIGxlYXZlVGFzazogZG9vck1hblRhc2tJbmZvLA0KICAgICAgICBtZWFzdXJlRmxhZzogdGhpcy5tZWFzdXJlRmxhZw0KICAgICAgfTsNCg0KICAgICAgY29uc29sZS5sb2coImFkZExlYXZlTG9nQW5kRWRpdFRhc2tNYXRlcmlhbHNBbmRVcGRhdGVUYXNrIiwgcGFyYW0sIHRoaXMudGFza0luZm9Gb3JtLnRhc2tUeXBlKTsNCg0KDQogICAgICBhZGRMZWF2ZUxvZ0FuZEVkaXRUYXNrTWF0ZXJpYWxzQW5kVXBkYXRlVGFzayhwYXJhbSkudGhlbihyZXMgPT4gew0KICAgICAgICBjb25zb2xlLmxvZygiYWRkTGVhdmVMb2dBbmRFZGl0VGFza01hdGVyaWFsc0FuZFVwZGF0ZVRhc2siLCByZXMpDQogICAgICAgIGlmIChyZXMuY29kZSA9PSAyMDApIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+mXqOWNq+ehruiupOaIkOWKnycpOw0KICAgICAgICAgIHRoaXMuZ2V0VGFza0xvZ0xpc3QodGhpcy50YXNrTm8pOw0KICAgICAgICAgIHRoaXMuZ2V0VGFza0luZm8oKTsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAvLyDlhbbku5blpLHotKXljp/lm6ANCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKHJlcy5tZXNzYWdlIHx8ICfpl6jljavnoa7orqTmiJDlip8nKTsNCiAgICAgICAgfQ0KICAgICAgfSkuY2F0Y2goZXJyID0+IHsNCiAgICAgICAgY29uc29sZS5lcnJvcignaGFuZGxlRG9vck1hbkNvbmZpcm0gZXJyb3I6JywgZXJyKTsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign572R57uc5byC5bi477yM56iN5ZCO6YeN6K+VJyk7DQogICAgICB9KTsNCg0KICAgICAgdGhpcy5lZGl0RG9vck1hblN0YXR1cyA9IGZhbHNlOw0KICAgIH0sDQoNCiAgICBzYXZlRG9vck1hblJvdygpIHsNCiAgICAgIC8vIOWIpOaWreeUqOaIt+inkuiJsuadg+mZkA0KICAgICAgY29uc3Qgcm9sZXMgPSB0aGlzLiRzdG9yZS5nZXR0ZXJzLnJvbGVzOw0KICAgICAgY29uc29sZS5sb2coInJvbGVzIiwgcm9sZXMpOw0KICAgICAgaWYgKCFyb2xlcy5pbmNsdWRlcygnbGVhdmUuZ3VhcmQnKSkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfmgqjmsqHmnInpl6jljavlh7rljoLnoa7orqTmnYPpmZAnKTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KDQogICAgICBpZiAodGhpcy50YXNrTWF0ZXJpYWxzLmxlbmd0aCA9PSAwKSB7DQogICAgICAgIGNvbnNvbGUubG9nKCJ0YXNrTWF0ZXJpYWxzIiwgdGhpcy50YXNrTWF0ZXJpYWxzKTsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfnianotYTlvILluLgnKTsNCiAgICAgICAgcmV0dXJuDQogICAgICB9DQoNCiAgICAgIC8vIOagoemqjGRvb3JtYW5SZWNlaXZlTnVt5piv5ZCm562J5LqOcGxhbk51bQ0KICAgICAgZm9yIChjb25zdCBpdGVtIG9mIHRoaXMudGFza01hdGVyaWFscykgew0KICAgICAgICBpZiAoaXRlbS5kb29ybWFuUmVjZWl2ZU51bSAhPT0gaXRlbS5wbGFuTnVtKSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKGDnianotYQiJHtpdGVtLm1hdGVyaWFsTmFtZX0i55qE6Zeo5Y2r56Gu6K6k5pWw6YePKCR7aXRlbS5kb29ybWFuUmVjZWl2ZU51bX0p5LiO6K6h5YiS5pWw6YePKCR7aXRlbS5wbGFuTnVtfSnkuI3kuIDoh7TvvIzor7fmo4Dmn6VgKTsNCiAgICAgICAgICByZXR1cm47DQogICAgICAgIH0NCiAgICAgIH0NCg0KICAgICAgbGV0IGxlYXZlVGFza0xvZyA9IHt9Ow0KICAgICAgbGVhdmVUYXNrTG9nLmxvZ1R5cGUgPSAyOw0KICAgICAgbGVhdmVUYXNrTG9nLnRhc2tObyA9IHRoaXMudGFza05vOw0KICAgICAgbGVhdmVUYXNrTG9nLmFwcGx5Tm8gPSB0aGlzLmFwcGx5Tm87DQogICAgICBsZWF2ZVRhc2tMb2cuaW5mbyA9ICfpl6jljavlh7rljoLnoa7orqTvvIznoa7orqTnianotYTvvJonICsgdGhpcy50YXNrTWF0ZXJpYWxzLm1hcChpdGVtID0+IGl0ZW0ubWF0ZXJpYWxOYW1lKS5qb2luKCfjgIEgJyk7DQoNCiAgICAgIGxldCBkb29yTWFuVGFza0luZm8gPSB7fQ0KICAgICAgZG9vck1hblRhc2tJbmZvLmlkID0gdGhpcy50YXNrSW5mb0Zvcm0uaWQNCiAgICAgIGlmICh0aGlzLnRhc2tJbmZvRm9ybS50YXNrVHlwZSA9PSAxKSB7DQogICAgICAgIGRvb3JNYW5UYXNrSW5mby50YXNrU3RhdHVzID0gOQ0KICAgICAgICBkb29yTWFuVGFza0luZm8ubGVhdmVUaW1lID0gbmV3IERhdGUoKS50b0lTT1N0cmluZygpLnNsaWNlKDAsIDE5KS5yZXBsYWNlKCdUJywgJyAnKQ0KICAgICAgICAvL+emu+WOguWkp+mXqA0KICAgICAgfSBlbHNlIGlmICh0aGlzLnRhc2tJbmZvRm9ybS50YXNrVHlwZSA9PSAyICYmIHRoaXMubWVhc3VyZUZsYWcgPT0gMCkgew0KICAgICAgICBkb29yTWFuVGFza0luZm8udGFza1N0YXR1cyA9IDcNCiAgICAgICAgZG9vck1hblRhc2tJbmZvLmVudGVyVGltZSA9IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKS5zbGljZSgwLCAxOSkucmVwbGFjZSgnVCcsICcgJykNCiAgICAgICAgLy/lh7rljoLlpKfpl6gNCiAgICAgIH0gZWxzZSBpZiAodGhpcy50YXNrSW5mb0Zvcm0udGFza1R5cGUgPT0gMiAmJiB0aGlzLm1lYXN1cmVGbGFnID09IDEpIHsNCiAgICAgICAgZG9vck1hblRhc2tJbmZvLnRhc2tTdGF0dXMgPSA2DQogICAgICAgIGRvb3JNYW5UYXNrSW5mby5lbnRlclRpbWUgPSBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCkuc2xpY2UoMCwgMTkpLnJlcGxhY2UoJ1QnLCAnICcpDQogICAgICAgIC8v5Ye65Y6C5aSn6ZeoDQogICAgICB9IGVsc2UgaWYgKHRoaXMudGFza0luZm9Gb3JtLnRhc2tUeXBlID09IDMgJiYgdGhpcy50YXNrSW5mb0Zvcm0udGFza1N0YXR1cyA9PSA0KSB7DQogICAgICAgIGRvb3JNYW5UYXNrSW5mby50YXNrU3RhdHVzID0gNQ0KICAgICAgICBkb29yTWFuVGFza0luZm8ubGVhdmVUaW1lID0gbmV3IERhdGUoKS50b0lTT1N0cmluZygpLnNsaWNlKDAsIDE5KS5yZXBsYWNlKCdUJywgJyAnKQ0KICAgICAgICAvL+emu+WOguWkp+mXqA0KICAgICAgfSBlbHNlIGlmICh0aGlzLnRhc2tJbmZvRm9ybS50YXNrVHlwZSA9PSAzICYmIHRoaXMubWVhc3VyZUZsYWcgPT0gMCAmJiB0aGlzLnRhc2tJbmZvRm9ybS50YXNrU3RhdHVzID09IDUpIHsNCiAgICAgICAgZG9vck1hblRhc2tJbmZvLnRhc2tTdGF0dXMgPSA3DQogICAgICAgIGRvb3JNYW5UYXNrSW5mby5lbnRlclRpbWUgPSBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCkuc2xpY2UoMCwgMTkpLnJlcGxhY2UoJ1QnLCAnICcpDQogICAgICAgIC8v5Ye65Y6C5aSn6ZeoDQogICAgICB9IGVsc2UgaWYgKHRoaXMudGFza0luZm9Gb3JtLnRhc2tUeXBlID09IDMgJiYgdGhpcy5tZWFzdXJlRmxhZyA9PSAxICYmIHRoaXMudGFza0luZm9Gb3JtLnRhc2tTdGF0dXMgPT0gNSkgew0KICAgICAgICBkb29yTWFuVGFza0luZm8udGFza1N0YXR1cyA9IDYNCiAgICAgICAgZG9vck1hblRhc2tJbmZvLmVudGVyVGltZSA9IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKS5zbGljZSgwLCAxOSkucmVwbGFjZSgnVCcsICcgJykNCiAgICAgICAgLy/lh7rljoLlpKfpl6gNCiAgICAgIH0NCg0KICAgICAgbGV0IHBhcmFtID0gew0KICAgICAgICB0YXNrTWF0ZXJpYWxMaXN0OiB0aGlzLnRhc2tNYXRlcmlhbHMsDQogICAgICAgIGxlYXZlTG9nOiBsZWF2ZVRhc2tMb2csDQogICAgICAgIGxlYXZlVGFzazogZG9vck1hblRhc2tJbmZvLA0KICAgICAgICBtZWFzdXJlRmxhZzogdGhpcy5tZWFzdXJlRmxhZw0KICAgICAgfTsNCg0KICAgICAgY29uc29sZS5sb2coImFkZExlYXZlTG9nQW5kRWRpdFRhc2tNYXRlcmlhbHNBbmRVcGRhdGVUYXNrIiwgcGFyYW0sIHRoaXMudGFza0luZm9Gb3JtLnRhc2tUeXBlKTsNCg0KDQogICAgICBhZGRMZWF2ZUxvZ0FuZEVkaXRUYXNrTWF0ZXJpYWxzQW5kVXBkYXRlVGFzayhwYXJhbSkudGhlbihyZXMgPT4gew0KICAgICAgICBjb25zb2xlLmxvZygiYWRkTGVhdmVMb2dBbmRFZGl0VGFza01hdGVyaWFsc0FuZFVwZGF0ZVRhc2siLCByZXMpDQogICAgICAgIGlmIChyZXMuY29kZSA9PSAyMDApIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+mXqOWNq+ehruiupOaIkOWKnycpOw0KICAgICAgICAgIHRoaXMuZ2V0VGFza0xvZ0xpc3QodGhpcy50YXNrTm8pOw0KICAgICAgICAgIHRoaXMuZ2V0VGFza0luZm8oKTsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAvLyDlhbbku5blpLHotKXljp/lm6ANCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKHJlcy5tZXNzYWdlIHx8ICfpl6jljavnoa7orqTmiJDlip8nKTsNCiAgICAgICAgfQ0KICAgICAgfSkuY2F0Y2goZXJyID0+IHsNCiAgICAgICAgY29uc29sZS5lcnJvcignaGFuZGxlRG9vck1hbkNvbmZpcm0gZXJyb3I6JywgZXJyKTsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign572R57uc5byC5bi477yM56iN5ZCO6YeN6K+VJyk7DQogICAgICB9KTsNCg0KICAgICAgdGhpcy5lZGl0RG9vck1hblN0YXR1cyA9IGZhbHNlOw0KICAgIH0sDQoNCg0KICAgIHNhdmVGYWN0b3J5Um93KCkgew0KDQogICAgICB0aGlzLmVkaXRGYWN0b3J5U3RhdHVzID0gZmFsc2U7DQogICAgfSwNCg0KICAgIHJlc2V0VGFza0luZm9Gb3JtKCkgew0KICAgICAgdGhpcy50YXNrSW5mb0Zvcm0gPSB7fTsNCiAgICB9LA0KDQogICAgYXN5bmMgZ2V0VGFza0luZm8oKSB7DQogICAgICB0cnkgew0KICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGdldFRhc2sodGhpcy5kaXNwYXRjaElkKTsNCiAgICAgICAgdGhpcy50YXNrSW5mb0Zvcm0gPSByZXNwb25zZS5kYXRhOw0KICAgICAgICBjb25zb2xlLmxvZygidGhpcy50YXNrSW5mb0Zvcm0iLCB0aGlzLnRhc2tJbmZvRm9ybSk7DQogICAgICAgIGlmICh0aGlzLnRhc2tJbmZvRm9ybS5saWNlbnNlUGxhdGVDb2xvciA9PSAxKSB7DQogICAgICAgICAgdGhpcy50YXNrSW5mb0Zvcm0ubGljZW5zZVBsYXRlQ29sb3IgPSAn6JOd6ImyJw0KICAgICAgICB9IGVsc2UgaWYgKHRoaXMudGFza0luZm9Gb3JtLmxpY2Vuc2VQbGF0ZUNvbG9yID09IDIpIHsNCiAgICAgICAgICB0aGlzLnRhc2tJbmZvRm9ybS5saWNlbnNlUGxhdGVDb2xvciA9ICfnu7/oibInDQogICAgICAgIH0gZWxzZSBpZiAodGhpcy50YXNrSW5mb0Zvcm0ubGljZW5zZVBsYXRlQ29sb3IgPT0gMykgew0KICAgICAgICAgIHRoaXMudGFza0luZm9Gb3JtLmxpY2Vuc2VQbGF0ZUNvbG9yID0gJ+m7hCcNCiAgICAgICAgfSBlbHNlIGlmICh0aGlzLnRhc2tJbmZvRm9ybS5saWNlbnNlUGxhdGVDb2xvciA9PSA0KSB7DQogICAgICAgICAgdGhpcy50YXNrSW5mb0Zvcm0ubGljZW5zZVBsYXRlQ29sb3IgPSAn6buE57u/6ImyJw0KICAgICAgICB9DQogICAgICAgIGNvbnNvbGUubG9nKCJ0aGlzLnRhc2tJbmZvRm9ybSIsIHRoaXMudGFza0luZm9Gb3JtKTsNCiAgICAgICAgLy8g55Sf5oiQ5LqM57u056CBDQogICAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgICB0aGlzLmNyZWF0UXJDb2RlKCk7DQogICAgICAgIH0pOw0KICAgICAgICByZXR1cm4gcmVzcG9uc2U7DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCdnZXRUYXNrSW5mbyBlcnJvcjonLCBlcnJvcik7DQogICAgICAgIHRocm93IGVycm9yOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICBhc3luYyBnZXRUYXNrSW5mb0J5VGFza05vKCkgew0KICAgICAgdHJ5IHsNCiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBnZXRUYXNrQnlUYXNrTm8odGhpcy50YXNrTm8pOw0KICAgICAgICB0aGlzLnRhc2tJbmZvRm9ybSA9IHJlc3BvbnNlLmRhdGE7DQogICAgICAgIGNvbnNvbGUubG9nKCJ0aGlzLnRhc2tJbmZvRm9ybSIsIHRoaXMudGFza0luZm9Gb3JtKTsNCg0KICAgICAgICAvLyDku47ov5Tlm57nmoTmlbDmja7kuK3ojrflj5bmiYDpnIDnmoTlj4LmlbANCiAgICAgICAgdGhpcy5kaXNwYXRjaElkID0gdGhpcy50YXNrSW5mb0Zvcm0uaWQ7DQogICAgICAgIHRoaXMuYXBwbHlObyA9IHRoaXMudGFza0luZm9Gb3JtLmFwcGx5Tm87DQoNCiAgICAgICAgaWYgKHRoaXMudGFza0luZm9Gb3JtLmxpY2Vuc2VQbGF0ZUNvbG9yID09IDEpIHsNCiAgICAgICAgICB0aGlzLnRhc2tJbmZvRm9ybS5saWNlbnNlUGxhdGVDb2xvciA9ICfok53oibInDQogICAgICAgIH0gZWxzZSBpZiAodGhpcy50YXNrSW5mb0Zvcm0ubGljZW5zZVBsYXRlQ29sb3IgPT0gMikgew0KICAgICAgICAgIHRoaXMudGFza0luZm9Gb3JtLmxpY2Vuc2VQbGF0ZUNvbG9yID0gJ+e7v+iJsicNCiAgICAgICAgfSBlbHNlIGlmICh0aGlzLnRhc2tJbmZvRm9ybS5saWNlbnNlUGxhdGVDb2xvciA9PSAzKSB7DQogICAgICAgICAgdGhpcy50YXNrSW5mb0Zvcm0ubGljZW5zZVBsYXRlQ29sb3IgPSAn6buEJw0KICAgICAgICB9IGVsc2UgaWYgKHRoaXMudGFza0luZm9Gb3JtLmxpY2Vuc2VQbGF0ZUNvbG9yID09IDQpIHsNCiAgICAgICAgICB0aGlzLnRhc2tJbmZvRm9ybS5saWNlbnNlUGxhdGVDb2xvciA9ICfpu4Tnu7/oibInDQogICAgICAgIH0NCiAgICAgICAgY29uc29sZS5sb2coInRoaXMudGFza0luZm9Gb3JtIiwgdGhpcy50YXNrSW5mb0Zvcm0pOw0KICAgICAgICAvLyDnlJ/miJDkuoznu7TnoIENCiAgICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICAgIHRoaXMuY3JlYXRRckNvZGUoKTsNCiAgICAgICAgfSk7DQogICAgICAgIHJldHVybiByZXNwb25zZTsNCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ2dldFRhc2tJbmZvQnlUYXNrTm8gZXJyb3I6JywgZXJyb3IpOw0KICAgICAgICB0aHJvdyBlcnJvcjsNCiAgICAgIH0NCiAgICB9LA0KDQoNCiAgICBnZXRTdGF0dXNUZXh0KHN0YW5kYXJkKSB7DQogICAgICBjb25zdCBzdGFuZGFyZE1hcCA9IHsNCiAgICAgICAgMTogJ+W+hei/h+earumHjScsDQogICAgICAgIDI6ICflvoXoo4XotKcnLA0KICAgICAgICAzOiAn5b6F6L+H5q+b6YeNJywNCiAgICAgICAgNDogJ+W+heWHuuWOgicsDQogICAgICAgIDU6ICflvoXov5TljoInLA0KICAgICAgICA2OiAn5b6F6L+H5q+b6YeNKOWkjeejhSknLA0KICAgICAgICA3OiAn5b6F5Y246LSnJywNCiAgICAgICAgODogJ+W+hei/h+earumHjSjlpI3no4UpJywNCiAgICAgICAgOTogJ+WujOaIkCcNCiAgICAgIH07DQogICAgICByZXR1cm4gc3RhbmRhcmRNYXBbc3RhbmRhcmRdIHx8ICfmnKrnn6UnOw0KICAgIH0sDQoNCiAgICAvL+iuoeWIkueKtuaAgQ0KICAgIGdldFBsYW5TdGF0dXNUZXh0KHN0YW5kYXJkKSB7DQogICAgICBjb25zdCBzdGFuZGFyZE1hcCA9IHsNCiAgICAgICAgMTogJ+W+heWIhuWOguWuoeaJuScsDQogICAgICAgIDI6ICflvoXliIbljoLlpI3lrqEnLA0KICAgICAgICAzOiAn5b6F55Sf5Lqn5oyH5oyl5Lit5b+D5a6h5om5JywNCiAgICAgICAgNDogJ+WuoeaJueWujOaIkCcsDQogICAgICAgIDU6ICflt7Llh7rljoInLA0KICAgICAgICA2OiAn6YOo5YiG5pS26LSnJywNCiAgICAgICAgNzogJ+W3suWujOaIkCcsDQogICAgICAgIDExOiAn6amz5ZueJywNCiAgICAgICAgMTI6ICflup/lvIMnLA0KICAgICAgICAxMzogJ+i/h+acnycsDQogICAgICAgICflvoXliIbljoLlrqHmibknOiAn5b6F5YiG5Y6C5a6h5om5JywNCiAgICAgICAgJ+W+heWIhuWOguWkjeWuoSc6ICflvoXliIbljoLlpI3lrqEnLA0KICAgICAgICAn5b6F55Sf5Lqn5oyH5oyl5Lit5b+D5a6h5om5JzogJ+W+heeUn+S6p+aMh+aMpeS4reW/g+WuoeaJuScsDQogICAgICAgICflrqHmibnlrozmiJAnOiAn5a6h5om55a6M5oiQJywNCiAgICAgICAgJ+W3suWHuuWOgic6ICflt7Llh7rljoInLA0KICAgICAgICAn6YOo5YiG5pS26LSnJzogJ+mDqOWIhuaUtui0pycsDQogICAgICAgICflt7LlrozmiJAnOiAn5bey5a6M5oiQJywNCiAgICAgICAgJ+mps+Wbnic6ICfpqbPlm54nLA0KICAgICAgICAn5bqf5byDJzogJ+W6n+W8gycsDQogICAgICAgICfov4fmnJ8nOiAn6L+H5pyfJywNCiAgICAgIH07DQogICAgICByZXR1cm4gc3RhbmRhcmRNYXBbc3RhbmRhcmRdIHx8ICfmnKrnn6UnOw0KICAgIH0sDQogICAgLy8g6I635Y+W5o6S5pS+5qCH5YeG5paH5pysDQogICAgZ2V0RW1pc3Npb25TdGFuZGFyZHNUZXh0KHN0YW5kYXJkKSB7DQogICAgICBjb25zdCBzdGFuZGFyZE1hcCA9IHsNCiAgICAgICAgMTogJ+WbveS6lCcsDQogICAgICAgIDI6ICflm73lha0nLA0KICAgICAgICAzOiAn5paw6IO95rqQJw0KICAgICAgfTsNCiAgICAgIHJldHVybiBzdGFuZGFyZE1hcFtzdGFuZGFyZF0gfHwgJ+acquefpSc7DQogICAgfSwNCg0KICAgIC8vIOiOt+WPluaOkuaUvuagh+WHhuagh+etvuexu+Weiw0KICAgIGdldEVtaXNzaW9uU3RhbmRhcmRzVGFnVHlwZShzdGFuZGFyZCkgew0KICAgICAgY29uc3QgdHlwZU1hcCA9IHsNCiAgICAgICAgMTogJ3dhcm5pbmcnLCAgLy8g5Zu95LqUDQogICAgICAgIDI6ICdzdWNjZXNzJywgIC8vIOWbveWFrQ0KICAgICAgICAzOiAncHJpbWFyeScgICAvLyDmlrDog73mupANCiAgICAgIH07DQogICAgICByZXR1cm4gdHlwZU1hcFtzdGFuZGFyZF0gfHwgJ2luZm8nOw0KICAgIH0sDQoNCiAgICAvLyDojrflj5bnianotYTnirbmgIHmlofmnKwNCiAgICBnZXRNYXRlcmlhbFN0YXR1c1RleHQoc3RhdHVzKSB7DQogICAgICBjb25zdCBzdGF0dXNNYXAgPSB7DQogICAgICAgIDE6ICflvoXoo4Xovb0nLA0KICAgICAgICAyOiAn5bey6KOF6L29JywNCiAgICAgICAgMzogJ+W3suetvuaUticsDQogICAgICAgIDQ6ICflvILluLgnDQogICAgICB9Ow0KICAgICAgcmV0dXJuIHN0YXR1c01hcFtzdGF0dXNdIHx8ICfmnKrnn6XnirbmgIEnOw0KICAgIH0sDQoNCiAgICAvLyDojrflj5bnianotYTnirbmgIHmoIfnrb7nsbvlnosNCiAgICBnZXRNYXRlcmlhbFN0YXR1c1R5cGUoc3RhdHVzKSB7DQogICAgICBjb25zdCB0eXBlTWFwID0gew0KICAgICAgICAxOiAnaW5mbycsICAgICAvLyDlvoXoo4Xovb0NCiAgICAgICAgMjogJ3dhcm5pbmcnLCAgLy8g5bey6KOF6L29DQogICAgICAgIDM6ICdzdWNjZXNzJywgIC8vIOW3suetvuaUtg0KICAgICAgICA0OiAnZGFuZ2VyJyAgICAvLyDlvILluLgNCiAgICAgIH07DQogICAgICByZXR1cm4gdHlwZU1hcFtzdGF0dXNdIHx8ICdpbmZvJzsNCiAgICB9LA0KDQogICAgLy8g6I635Y+W5pel5b+X6aKc6ImyDQogICAgZ2V0TG9nQ29sb3IobG9nKSB7DQogICAgICBjb25zdCBsb2dUeXBlQ29sb3JNYXAgPSB7DQogICAgICAgIDE6ICcjNDA5RUZGJywgLy8g5Yib5bu6DQogICAgICAgIDI6ICcjRTZBMjNDJywgLy8g5pu05pawDQogICAgICAgIDM6ICcjNjdDMjNBJywgLy8g5a6M5oiQDQogICAgICAgIDQ6ICcjRjU2QzZDJywgLy8g5byC5bi4DQogICAgICAgIDU6ICcjOTA5Mzk5JyAgLy8g5YW25LuWDQogICAgICB9Ow0KICAgICAgcmV0dXJuIGxvZ1R5cGVDb2xvck1hcFtsb2cudHlwZV0gfHwgJyM0MDlFRkYnOw0KICAgIH0sDQoNCiAgICAvLyDov5Tlm57mjInpkq4NCiAgICBjYW5jZWwoKSB7DQogICAgICB0aGlzLiRyb3V0ZXIuZ28oLTEpOw0KICAgIH0sDQoNCiAgICAvLyDojrflj5bku7vliqHor6bmg4XmlbDmja4NCiAgICBnZXRUYXNrRGV0YWlsKGRpc3BhdGNoSWQpIHsNCiAgICAgIC8vIOWunumZhemhueebruS4rei/memHjOmcgOimgeiwg+eUqEFQSeiOt+WPluaVsOaNrg0KICAgICAgLy8gZ2V0RGlzcGF0Y2hUYXNrRGV0YWlsKGRpc3BhdGNoSWQpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgLy8gICBjb25zdCB7IGRyaXZlckluZm8sIGNhckluZm8sIHRhc2tNYXRlcmlhbHMsIHRhc2tMb2dzIH0gPSByZXNwb25zZS5kYXRhOw0KICAgICAgLy8gICB0aGlzLmRyaXZlckluZm8gPSBkcml2ZXJJbmZvOw0KICAgICAgLy8gICB0aGlzLmNhckluZm8gPSBjYXJJbmZvOw0KICAgICAgLy8gICB0aGlzLnRhc2tNYXRlcmlhbHMgPSB0YXNrTWF0ZXJpYWxzOw0KICAgICAgLy8gICB0aGlzLnRhc2tMb2dzID0gdGFza0xvZ3M7DQogICAgICAvLyB9KTsNCiAgICB9LA0KICAgIGhhbmRsZVNob3dEcm9wZG93bkNoYW5nZSh2YWwpIHsNCiAgICAgIGlmICghdmFsKSB7DQogICAgICAgIHRoaXMuZmFjdG9yeUNvbmZpcm1Gb3JtLmV4dHJhT3B0aW9uID0gJyc7DQogICAgICB9DQogICAgfSwNCiAgICBvcGVuT3B0aW9uRGlhbG9nKCkgew0KICAgICAgdGhpcy5vcHRpb25EaWFsb2dWaXNpYmxlID0gdHJ1ZTsNCiAgICAgIHRoaXMubG9hZE9wdGlvbnMoKTsNCiAgICAgIC8vIOmHjee9rumAieS4reeKtuaAgQ0KICAgICAgdGhpcy5zZWxlY3RlZE9wdGlvbiA9IG51bGw7DQogICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgIGlmICh0aGlzLiRyZWZzLm9wdGlvblRhYmxlKSB7DQogICAgICAgICAgdGhpcy4kcmVmcy5vcHRpb25UYWJsZS5jbGVhclNlbGVjdGlvbigpOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIGhhbmRsZU9wdGlvblNlbGVjdGlvbihzZWxlY3Rpb24pIHsNCiAgICAgIC8vIOWPquS/neeVmeacgOWQjumAieS4reeahOS4gOmhuQ0KICAgICAgaWYgKHNlbGVjdGlvbi5sZW5ndGggPiAxKSB7DQogICAgICAgIGNvbnN0IGxhc3RTZWxlY3RlZCA9IHNlbGVjdGlvbltzZWxlY3Rpb24ubGVuZ3RoIC0gMV07DQogICAgICAgIHRoaXMuJHJlZnMub3B0aW9uVGFibGUuY2xlYXJTZWxlY3Rpb24oKTsNCiAgICAgICAgdGhpcy4kcmVmcy5vcHRpb25UYWJsZS50b2dnbGVSb3dTZWxlY3Rpb24obGFzdFNlbGVjdGVkLCB0cnVlKTsNCiAgICAgICAgdGhpcy5zZWxlY3RlZE9wdGlvbiA9IGxhc3RTZWxlY3RlZDsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuc2VsZWN0ZWRPcHRpb24gPSBzZWxlY3Rpb25bMF07DQogICAgICB9DQogICAgfSwNCiAgICBjb25maXJtT3B0aW9uU2VsZWN0aW9uKCkgew0KICAgICAgaWYgKCF0aGlzLnNlbGVjdGVkT3B0aW9uKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn6K+36YCJ5oup5LiA5Liq6YCJ6aG5Jyk7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCg0KICAgICAgdGhpcy5mYWN0b3J5Q29uZmlybUZvcm0uZXh0cmFPcHRpb24gPSB0aGlzLnNlbGVjdGVkT3B0aW9uLmFwcGx5Tm87DQoNCiAgICAgIC8vIGxldCBkaXNwYXRjaEluZm8gPSB7fTsNCiAgICAgIC8vIGRpc3BhdGNoSW5mby5jYXJOdW0gPSB0aGlzLnRhc2tJbmZvRm9ybS5jYXJOdW07DQogICAgICAvLyBkaXNwYXRjaEluZm8uaXNEaXJlY3RTdXBwbHkgPSAxOw0KDQogICAgICAvLyBpc0FsbG93RGlzcGF0Y2goZGlzcGF0Y2hJbmZvKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgIC8vICAgbGV0IHJvdyA9IHJlc3BvbnNlLmRhdGE7DQogICAgICAvLyAgIGlmIChyb3cgPiAwKSB7DQogICAgICAvLyAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi5b2T5YmN6L2m5pyJ5q2j5Zyo5omn6KGM55qE5Lu75YqhIikNCiAgICAgIC8vICAgICByZXR1cm47DQogICAgICAvLyAgIH0gZWxzZSB7DQogICAgICAvLyAgICAgdGhpcy5vcHRpb25EaWFsb2dWaXNpYmxlID0gZmFsc2U7DQogICAgICAvLyAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfpgInpobnlt7Lnoa7orqQnKTsNCiAgICAgIC8vICAgfQ0KICAgICAgLy8gICBjb25zb2xlLmxvZygidGhpcy5pc0FsbG93RGlzcGF0Y2giLCByZXNwb25zZSk7DQogICAgICAvLyB9KS5jYXRjaChlcnIgPT4gew0KICAgICAgLy8gICBjb25zb2xlLmVycm9yKCdkaXNwYXRjaCBlcnJvcjonLCBlcnIpOw0KICAgICAgLy8gICB0aGlzLiRtZXNzYWdlLmVycm9yKCfnvZHnu5zlvILluLjvvIznqI3lkI7ph43or5UnKTsNCiAgICAgIC8vIH0pOw0KDQogICAgICB0aGlzLm9wdGlvbkRpYWxvZ1Zpc2libGUgPSBmYWxzZTsNCiAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn6YCJ6aG55bey56Gu6K6kJyk7DQoNCg0KDQogICAgfSwNCiAgICBsb2FkT3B0aW9ucygpIHsNCiAgICAgIC8vIOi/memHjOW6lOivpeiwg+eUqEFQSeiOt+WPlmxlYXZlX3BsYW7ooajnmoTmlbDmja4NCiAgICAgIHRoaXMub3B0aW9uTGlzdCA9IHRoaXMuZGlyZWN0U3VwcGx5UGxhbkxpc3Q7IC8vIOS9v+eUqOebtOS+m+iuoeWIkuWIl+ihqOS9nOS4uumAiemhueaVsOaNrlwNCiAgICAgIHRoaXMub3B0aW9uTGlzdC5mb3JFYWNoKGl0ZW0gPT4gew0KICAgICAgICBpdGVtLnBsYW5TdGF0dXMgPSB0aGlzLmdldFBsYW5TdGF0dXNUZXh0KGl0ZW0ucGxhblN0YXR1cyk7DQogICAgICB9KTsNCiAgICAgIGNvbnNvbGUubG9nKCJvcHRpb25MaXN0IiwgdGhpcy5vcHRpb25MaXN0KQ0KICAgIH0sDQogICAgZ2V0QnVzaW5lc3NDYXRlZ29yeVRleHQoY2F0ZWdvcnkpIHsNCiAgICAgIGNvbnN0IGNhdGVnb3J5TWFwID0gew0KICAgICAgICAxOiAn6YCa55So77yI5Ye65Y6C5LiN6L+U5Zue77yJJywNCiAgICAgICAgMTE6ICfpgJrnlKjvvIjlh7rljoLov5Tlm57vvIknLA0KICAgICAgICAxMjogJ+WnlOWkluWKoOW3pe+8iOWHuuWOgui/lOWbnu+8iScsDQogICAgICAgIDIxOiAn5pyJ6K6h5YiS6YeP6K6h6YeP77yI6Leo5Yy66LCD5ouo77yJJywNCiAgICAgICAgMjI6ICfnn63mnJ/vvIjot6jljLrosIPmi6jvvIknLA0KICAgICAgICAyMzogJ+mSouadv++8iOWchumSou+8ie+8iOi3qOWMuuiwg+aLqO+8iScsDQogICAgICAgIDMxOiAn6YCa55So77yI6YCA6LSn55Sz6K+377yJJw0KICAgICAgfTsNCiAgICAgIHJldHVybiBjYXRlZ29yeU1hcFtjYXRlZ29yeV0gfHwgJ+acquefpeexu+Weiyc7DQogICAgfSwNCiAgICBzZWFyY2hPcHRpb25zKCkgew0KICAgICAgLy8g5Y+W5Ye65bm26L2s5bCP5YaZDQogICAgICBjb25zdCBzZWFyY2hQbGFuTm8gPSAodGhpcy5zZWFyY2hGb3JtLnBsYW5ObyB8fCAnJykudG9Mb3dlckNhc2UoKTsNCiAgICAgIGNvbnN0IHNlYXJjaEFwcGx5Tm8gPSAodGhpcy5zZWFyY2hGb3JtLmFwcGx5Tm8gfHwgJycpLnRvTG93ZXJDYXNlKCk7DQogICAgICBjb25zdCBzZWFyY2hSZWNlaXZlQ29tcGFueSA9ICh0aGlzLnNlYXJjaEZvcm0ucmVjZWl2ZUNvbXBhbnkgfHwgJycpLnRvTG93ZXJDYXNlKCk7DQoNCiAgICAgIC8vIOi/h+a7pA0KICAgICAgdGhpcy5vcHRpb25MaXN0ID0gdGhpcy5kaXJlY3RTdXBwbHlQbGFuTGlzdC5maWx0ZXIoaXRlbSA9PiB7DQogICAgICAgIGNvbnN0IHBsYW5ObyA9IChpdGVtLnBsYW5ObyB8fCAnJykudG9TdHJpbmcoKS50b0xvd2VyQ2FzZSgpOw0KICAgICAgICBjb25zdCBhcHBseU5vID0gKGl0ZW0uYXBwbHlObyB8fCAnJykudG9TdHJpbmcoKS50b0xvd2VyQ2FzZSgpOw0KICAgICAgICBjb25zdCByZWNlaXZlQ29tcGFueSA9IChpdGVtLnJlY2VpdmVDb21wYW55IHx8ICcnKS50b1N0cmluZygpLnRvTG93ZXJDYXNlKCk7DQoNCiAgICAgICAgLy8g5Li656m65LiN5L2c5Li65p2h5Lu2DQogICAgICAgIGNvbnN0IG1hdGNoUGxhbk5vID0gIXNlYXJjaFBsYW5ObyB8fCBwbGFuTm8uaW5jbHVkZXMoc2VhcmNoUGxhbk5vKTsNCiAgICAgICAgY29uc3QgbWF0Y2hBcHBseU5vID0gIXNlYXJjaEFwcGx5Tm8gfHwgYXBwbHlOby5pbmNsdWRlcyhzZWFyY2hBcHBseU5vKTsNCiAgICAgICAgY29uc3QgbWF0Y2hSZWNlaXZlQ29tcGFueSA9ICFzZWFyY2hSZWNlaXZlQ29tcGFueSB8fCByZWNlaXZlQ29tcGFueS5pbmNsdWRlcyhzZWFyY2hSZWNlaXZlQ29tcGFueSk7DQoNCiAgICAgICAgcmV0dXJuIG1hdGNoUGxhbk5vICYmIG1hdGNoQXBwbHlObyAmJiBtYXRjaFJlY2VpdmVDb21wYW55Ow0KICAgICAgfSk7DQoNCiAgICAgIC8vIOabtOaWsOeKtuaAgeaYvuekug0KICAgICAgdGhpcy5vcHRpb25MaXN0LmZvckVhY2goaXRlbSA9PiB7DQogICAgICAgIGl0ZW0ucGxhblN0YXR1cyA9IHRoaXMuZ2V0UGxhblN0YXR1c1RleHQoaXRlbS5wbGFuU3RhdHVzKTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgcmVzZXRTZWFyY2goKSB7DQogICAgICB0aGlzLnNlYXJjaEZvcm0gPSB7DQogICAgICAgIHBsYW5ObzogJycsDQogICAgICAgIGFwcGx5Tm86ICcnLA0KICAgICAgICByZWNlaXZlQ29tcGFueTogJycNCiAgICAgIH07DQogICAgICB0aGlzLmxvYWRPcHRpb25zKCk7IC8vIOmHjeaWsOWKoOi9veaJgOacieaVsOaNrg0KICAgIH0sDQogICAgZ2V0VGFza1R5cGVUZXh0KHR5cGUpIHsNCiAgICAgIGNvbnN0IHR5cGVNYXAgPSB7DQogICAgICAgIDE6ICflh7rljoInLA0KICAgICAgICAyOiAn6L+U5Y6CJywNCiAgICAgICAgMzogJ+i3qOWMuuiwg+aLqCcNCiAgICAgIH07DQogICAgICByZXR1cm4gdHlwZU1hcFt0eXBlXSB8fCAn5pyq55+lJzsNCiAgICB9LA0KICAgIC8vIC8vIOWIpOaWreihjOaYr+WQpuWPr+mAiQ0KICAgIC8vIGlzU2VsZWN0YWJsZShyb3cpIHsNCiAgICAvLyAgIC8vIOW9k+mXqOWNq+ehruiupOaVsOmHj+S4jeS4ujDml7bvvIzor6XooYzlj6/pgIkNCiAgICAvLyAgIHJldHVybiByb3cuZG9vcm1hblJlY2VpdmVOdW0gPiAwICYmIHRoaXMudGFza0luZm9Gb3JtLnRhc2tTdGF0dXMgIT09IDk7DQogICAgLy8gfSwNCg0KICAgIC8vIOihqOagvOmAieaLqeWPmOWMluaXtueahOWkhOeQhuWHveaVsA0KICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsNCiAgICAgIHRoaXMuc2VsZWN0ZWRSb3dzID0gc2VsZWN0aW9uOw0KICAgIH0sDQoNCiAgICAvLyDlpITnkIbpnZ7orqHph4/liIbljoLnoa7orqQNCiAgICBoYW5kbGVOb25NZWFzdXJlRmFjdG9yeUNvbmZpcm0oKSB7DQogICAgICBjb25zdCByb2xlcyA9IHRoaXMuJHN0b3JlLmdldHRlcnMucm9sZXM7DQogICAgICBpZiAoIXJvbGVzLmluY2x1ZGVzKCdsZWF2ZS51bmxvYWRpbmcnKSkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfmgqjmsqHmnInpl6jljavlh7rljoLnoa7orqTmnYPpmZAnKTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KICAgICAgbGV0IGlzSGFuZGxlZCA9IGZhbHNlOw0KICAgICAgdGhpcy5zZWxlY3RlZFJvd3MuZm9yRWFjaChpdGVtID0+IHsNCiAgICAgICAgaWYgKGl0ZW0uZG9vcm1hblJlY2VpdmVOdW0gIT09IGl0ZW0ucGxhbk51bSkgew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn6Zeo5Y2r56Gu6K6k5pWw6YeP5ZKM6K6h5YiS5pWw6YeP5LiN5LiA6Ie077yM6K+35qOA5p+lJyk7DQogICAgICAgICAgaXNIYW5kbGVkID0gdHJ1ZTsNCiAgICAgICAgfQ0KICAgICAgfSk7DQoNCiAgICAgIGlmIChpc0hhbmRsZWQpIHsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KDQogICAgICAvLyBpZiAodGhpcy5zZWxlY3RlZFJvd3MubGVuZ3RoID09PSAwKSB7DQogICAgICAvLyAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn6K+36YCJ5oup6ZyA6KaB56Gu6K6k55qE54mp6LWEJyk7DQogICAgICAvLyAgIHJldHVybjsNCiAgICAgIC8vIH0NCg0KICAgICAgLy8g55Sf5oiQ5rS+6L2m5pel5b+XDQogICAgICBsZXQgbGVhdmVUYXNrTG9nID0gew0KICAgICAgICBsb2dUeXBlOiAyLA0KICAgICAgICB0YXNrTm86IHRoaXMudGFza05vLA0KICAgICAgICBhcHBseU5vOiB0aGlzLmFwcGx5Tm8sDQogICAgICAgIGluZm86ICfliIbljoLmjqXmlLbnoa7orqTvvIznoa7orqTnianotYTvvJonICsgdGhpcy50YXNrTWF0ZXJpYWxzLm1hcChpdGVtID0+IGl0ZW0ubWF0ZXJpYWxOYW1lKS5qb2luKCfjgIEgJykNCiAgICAgIH07DQoNCiAgICAgIC8vIOaehOW7uuS7u+WKoeS/oeaBrw0KICAgICAgbGV0IGZhY3RvcnlUYXNrSW5mbyA9IHsNCiAgICAgICAgaWQ6IHRoaXMudGFza0luZm9Gb3JtLmlkLA0KICAgICAgICB1bmxvYWRpbmdXb3JrTm86ICfljbjotKfkurrljaDkvY3nrKYnLC8v5ZCO56uvdXBkYXRlTGVhdmVUYXNr5pa55rOVDQogICAgICAgIHVubG9hZGluZ1RpbWU6IG5ldyBEYXRlKCksDQogICAgICAgIHRhc2tTdGF0dXM6IDkNCiAgICAgIH07DQoNCiAgICAgIHRoaXMuc2VsZWN0ZWRSb3dzLmZvckVhY2goaXRlbSA9PiB7DQogICAgICAgIC8vIOiuvue9rumdnuiuoemHj+WIhuWOguehruiupOaVsOmHjw0KICAgICAgICBpdGVtLmZhY3RvcnlSZWNlaXZlTnVtID0gaXRlbS5kb29ybWFuUmVjZWl2ZU51bTsNCiAgICAgIH0pOw0KDQogICAgICAvLyDmnoTlu7ror7fmsYLlj4LmlbANCiAgICAgIGxldCBwYXJhbSA9IHsNCiAgICAgICAgdGFza01hdGVyaWFsTGlzdDogdGhpcy5zZWxlY3RlZFJvd3MsIC8vIOS9v+eUqOmAieS4reeahOihjOaVsOaNrg0KICAgICAgICBsZWF2ZUxvZzogbGVhdmVUYXNrTG9nLA0KICAgICAgICBsZWF2ZVRhc2s6IGZhY3RvcnlUYXNrSW5mbywNCiAgICAgICAgbWVhc3VyZUZsYWc6IHRoaXMubWVhc3VyZUZsYWcNCiAgICAgIH07DQoNCiAgICAgIC8vIOWPkemAgeivt+axgg0KICAgICAgYWRkTGVhdmVMb2dBbmRFZGl0VGFza01hdGVyaWFsc0FuZFVwZGF0ZVRhc2socGFyYW0pLnRoZW4ocmVzID0+IHsNCiAgICAgICAgaWYgKHJlcy5jb2RlID09IDIwMCkgew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn6Z2e6K6h6YeP5YiG5Y6C56Gu6K6k5oiQ5YqfJyk7DQogICAgICAgICAgdGhpcy5nZXRUYXNrTG9nTGlzdCh0aGlzLnRhc2tObyk7DQogICAgICAgICAgdGhpcy5nZXRUYXNrSW5mbygpOw0KICAgICAgICAgIC8vIOa4heepuumAieS4reeKtuaAgQ0KICAgICAgICAgIHRoaXMuc2VsZWN0ZWRSb3dzID0gW107DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihyZXMubWVzc2FnZSB8fCAn6Z2e6K6h6YeP5YiG5Y6C56Gu6K6k5aSx6LSlJyk7DQogICAgICAgIH0NCiAgICAgIH0pLmNhdGNoKGVyciA9PiB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ2hhbmRsZU5vbk1lYXN1cmVGYWN0b3J5Q29uZmlybSBlcnJvcjonLCBlcnIpOw0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfnvZHnu5zlvILluLjvvIznqI3lkI7ph43or5UnKTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgb3Blbk5ld1Rhc2tXaW5kb3coKSB7DQogICAgICBjb25zb2xlLmxvZygib3Blbk5ld1Rhc2tXaW5kb3ciLCB0aGlzLmRpcmVjdFN1cHBseVBhcmFtcyk7DQogICAgICBsZXQgZGlzcGF0Y2hJZCA9IHRoaXMuZGlyZWN0U3VwcGx5UGFyYW1zLmRpc3BhdGNoSWQ7DQogICAgICBsZXQgYXBwbHlObyA9IEJpZ0ludCh0aGlzLmRpcmVjdFN1cHBseVBhcmFtcy5hcHBseU5vKTsNCiAgICAgIGxldCBtZWFzdXJlRmxhZyA9IHRoaXMuZGlyZWN0U3VwcGx5UGFyYW1zLm1lYXN1cmVGbGFnOw0KICAgICAgbGV0IHBsYW5UeXBlID0gdGhpcy5kaXJlY3RTdXBwbHlQYXJhbXMucGxhblR5cGU7DQogICAgICBsZXQgdGFza05vID0gQmlnSW50KHRoaXMuZGlyZWN0U3VwcGx5UGFyYW1zLnRhc2tObyk7DQogICAgICBjb25zdCB1cmwgPSBgaHR0cDovL2xvY2FsaG9zdC9sZWF2ZS9wbGFuL3Rhc2s/ZGlzcGF0Y2hJZD0ke2Rpc3BhdGNoSWR9JmFwcGx5Tm89JHthcHBseU5vfSZtZWFzdXJlRmxhZz0ke21lYXN1cmVGbGFnfSZwbGFuVHlwZT0ke3BsYW5UeXBlfSZ0YXNrTm89JHt0YXNrTm99YDsNCiAgICAgIHdpbmRvdy5vcGVuKHVybCwgJ19ibGFuaycpOw0KICAgIH0sDQogIH0NCn07DQo="}, {"version": 3, "sources": ["task.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAy1BA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;;AAEA;;AAEA;;AAEA;AACA;;AAEA;;AAEA;;AAEA;;AAEA;;AAEA;;AAEA;;AAEA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;;AAGA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "task.vue", "sourceRoot": "src/views/leave/plan", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-card class=\"box-card\">\r\n      <div slot=\"header\" class=\"card-header\" style=\"display: flex; align-items: center; justify-content: flex-start;\">\r\n        <h2>派车任务详情</h2>\r\n        <el-tag size=\"medium\" style=\"margin-left: 20px; margin-top: 10px;\">\r\n          任务状态： {{ getStatusText(taskInfoForm.taskStatus) }}\r\n        </el-tag>\r\n      </div>\r\n\r\n      <!-- 任务流程图部分 -->\r\n      <div class=\"section-container\">\r\n        <div class=\"section-title\">任务流程</div>\r\n        <div class=\"process-flow-container\">\r\n          <!-- <img style=\"width: 100%; max-height: 400px; object-fit: contain;\" :src=\"require('@/assets/images/task-flow-chart.png')\" /> -->\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 通行证二维码部分 -->\r\n      <div class=\"section-container\">\r\n        <div class=\"section-title\">通行证二维码</div>\r\n        <div class=\"qrcode-container\">\r\n          <div ref=\"qrCode\" class=\"qrcode\"></div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 司机信息部分 -->\r\n      <div class=\"section-container\">\r\n        <div class=\"section-title\">司机信息</div>\r\n        <el-descriptions :column=\"2\" border>\r\n          <el-descriptions-item label=\"姓名\">\r\n            <template slot=\"label\"><i class=\"el-icon-user\"></i> 姓名</template>\r\n            {{ taskInfoForm.driverName }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"手机号\">\r\n            <template slot=\"label\"><i class=\"el-icon-mobile-phone\"></i> 手机号</template>\r\n            {{ taskInfoForm.mobilePhone }}\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"身份证号\">\r\n            <template slot=\"label\"><i class=\"el-icon-document\"></i> 身份证号</template>\r\n            {{ taskInfoForm.idCardNo }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"性别\">\r\n            <template slot=\"label\"><i class=\"el-icon-user\"></i> 性别</template>\r\n            {{ taskInfoForm.sex === 1 ? '男' : '女' }}\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"所属单位\">\r\n            <template slot=\"label\"><i class=\"el-icon-office-building\"></i> 所属单位</template>\r\n            {{ taskInfoForm.companyName }}\r\n          </el-descriptions-item>\r\n        </el-descriptions>\r\n\r\n        <!-- 司机照片和证件照片 -->\r\n        <div class=\"driver-photos\"\r\n          v-if=\"driverInfo.photo || driverInfo.driverLicenseImgs || driverInfo.vehicleLicenseImgs\">\r\n          <div class=\"photo-item\" v-if=\"driverInfo.photo\">\r\n            <h4><i class=\"el-icon-picture-outline\"></i> 司机照片</h4>\r\n            <div class=\"photo-container\">\r\n              <!-- <img :src=\"taskInfoForm.faceImg\" alt=\"司机照片\"> -->\r\n\r\n              <el-image style=\"width: 200px; height: 200px\" :src=\"taskInfoForm.faceImg\" fit=\"contain\" fallback=\"\"\r\n                :preview-src-list=\"[taskInfoForm.faceImg]\">\r\n                <template #error>\r\n                  <div style=\"width: 100%; height: 100%;\"></div> <!-- 空白区域 -->\r\n                </template>\r\n              </el-image>\r\n            </div>\r\n          </div>\r\n          <div class=\"photo-item\" v-if=\"driverInfo.driverLicenseImgs\">\r\n            <h4><i class=\"el-icon-picture-outline\"></i> 驾驶证照片</h4>\r\n            <div class=\"photo-container\">\r\n              <!-- <img :src=\"taskInfoForm.driverLicenseImg\" alt=\"驾驶证照片\"> -->\r\n\r\n              <el-image style=\"width: 200px; height: 200px\" :src=\"taskInfoForm.driverLicenseImg\" fit=\"contain\"\r\n                fallback=\"\" :preview-src-list=\"[taskInfoForm.driverLicenseImg]\">\r\n                <template #error>\r\n                  <div style=\"width: 100%; height: 100%;\"></div> <!-- 空白区域 -->\r\n                </template>\r\n              </el-image>\r\n            </div>\r\n          </div>\r\n          <div class=\"photo-item\" v-if=\"driverInfo.vehicleLicenseImgs\">\r\n            <h4><i class=\"el-icon-picture-outline\"></i> 行驶证照片</h4>\r\n            <div class=\"photo-container\">\r\n              <!-- <img :src=\"taskInfoForm.drivingLicenseImg\" alt=\"行驶证照片\"> -->\r\n\r\n              <el-image style=\"width: 200px; height: 200px\" :src=\"taskInfoForm.drivingLicenseImg\" fit=\"contain\"\r\n                fallback=\"\" :preview-src-list=\"[taskInfoForm.drivingLicenseImg]\">\r\n                <template #error>\r\n                  <div style=\"width: 100%; height: 100%;\"></div> <!-- 空白区域 -->\r\n                </template>\r\n              </el-image>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 车辆信息部分 -->\r\n      <div class=\"section-container\">\r\n        <div class=\"section-title\">车辆信息</div>\r\n        <el-descriptions :column=\"2\" border>\r\n          <el-descriptions-item label=\"车牌号\" v-if=\"taskInfoForm.carNum != null\">\r\n            <template slot=\"label\"><i class=\"el-icon-truck\"></i> 车牌号</template>\r\n            <el-tag type=\"primary\">{{ taskInfoForm.carNum }}</el-tag>\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"车牌颜色\" v-if=\"taskInfoForm.licensePlateColor != null\">\r\n            <template slot=\"label\"><i class=\"el-icon-takeaway-box\"></i> 车牌颜色</template>\r\n            {{ taskInfoForm.licensePlateColor }}\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"车辆道路运输证号\" v-if=\"taskInfoForm.trailerId != null\">\r\n            <template slot=\"label\"><i class=\"el-icon-document\"></i> 运输证号</template>\r\n            {{ taskInfoForm.trailerId }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"挂车号牌\" v-if=\"taskInfoForm.trailerNumber\">\r\n            <template slot=\"label\"><i class=\"el-icon-truck\"></i> 挂车号牌</template>\r\n            <el-tag type=\"info\">{{ taskInfoForm.trailerNumber }}</el-tag>\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"挂车道路运输证号\" v-if=\"taskInfoForm.trailerId\">\r\n            <template slot=\"label\"><i class=\"el-icon-document\"></i> 挂车运输证号</template>\r\n            {{ taskInfoForm.trailerId }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"轴型\" v-if=\"taskInfoForm.axisType != null\">\r\n            <template slot=\"label\"><i class=\"el-icon-data-line\"></i> 轴型</template>\r\n            {{ taskInfoForm.axisType }}\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"货车自重\" v-if=\"taskInfoForm.driverWeight != null\">\r\n            <template slot=\"label\"><i class=\"el-icon-heavy-rain\"></i> 货车自重</template>\r\n            {{ taskInfoForm.driverWeight }} kg\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"车货总质量限值\" v-if=\"taskInfoForm.maxWeight != null\">\r\n            <template slot=\"label\"><i class=\"el-icon-opportunity\"></i> 总质量限值</template>\r\n            {{ taskInfoForm.maxWeight }} kg\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"车辆排放标准\" v-if=\"taskInfoForm.vehicleEmissionStandards != null\">\r\n            <template slot=\"label\"><i class=\"el-icon-magic-stick\"></i> 排放标准</template>\r\n            <el-tag :type=\"getEmissionStandardsTagType(taskInfoForm.vehicleEmissionStandards)\">\r\n              {{ getEmissionStandardsText(taskInfoForm.vehicleEmissionStandards) }}\r\n            </el-tag>\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"发动机号\" v-if=\"taskInfoForm.engineNumber != null\">\r\n            <template slot=\"label\"><i class=\"el-icon-set-up\"></i> 发动机号</template>\r\n            {{ taskInfoForm.engineNumber }}\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"车辆识别代码\" v-if=\"taskInfoForm.vinNumber != null\">\r\n            <template slot=\"label\"><i class=\"el-icon-document-checked\"></i> 车辆识别代码</template>\r\n            {{ taskInfoForm.vinNumber }}\r\n          </el-descriptions-item>\r\n        </el-descriptions>\r\n      </div>\r\n\r\n      <!-- 任务物资列表部分 -->\r\n      <div class=\"section-container\">\r\n        <div class=\"section-title\">物资列表</div>\r\n        <el-table :data=\"taskMaterials\" style=\"width: 100%\" border @selection-change=\"handleSelectionChange\">\r\n          <!-- <el-table-column type=\"selection\" width=\"55\" v-if=\"measureFlag == 0\">\r\n          </el-table-column> -->\r\n          <el-table-column type=\"index\" width=\"50\" label=\"序号\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"materialName\" label=\"物资名称\" width=\"150\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"materialSpec\" label=\"物资型号规格\" width=\"150\">\r\n          </el-table-column>\r\n          <!-- v-if=\"measureFlag == 0\" -->\r\n          <el-table-column prop=\"planNum\" label=\"计划数量\" width=\"120\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"measureUnit\" label=\"单位\" width=\"120\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"doormanReceiveNum\" label=\"门卫出厂确认数量\" width=\"230\"\r\n            v-if=\"taskInfoForm.taskStatus >= 4 && measureFlag == 0 && taskInfoForm.taskType != 2\">\r\n            <template slot-scope=\"scope\">\r\n              <el-input-number v-model=\"scope.row.doormanReceiveNum\" :min=\"0\" controls-position=\"right\"\r\n                :disabled=\"!isdoorMan && taskInfoForm.taskStatus !== 4\" />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"doormanReceiveNumIn\" label=\"门卫入厂确认数量\" width=\"230\"\r\n            v-if=\"measureFlag == 0 && taskInfoForm.taskType !== 1 && taskInfoForm.taskStatus >= 5\">\r\n            <template slot-scope=\"scope\">\r\n              <el-input-number v-model=\"scope.row.doormanReceiveNumIn\" :min=\"0\" controls-position=\"right\"\r\n                :disabled=\"!isdoorMan && taskInfoForm.taskStatus !== 5\" />\r\n            </template>\r\n          </el-table-column>\r\n          <!-- v-if=\"measureFlag == 0 && taskInfoForm.taskType == 2\" -->\r\n          <el-table-column prop=\"doormanReceiveNum\" label=\"分厂确认数量\" width=\"230\"\r\n            v-if=\"measureFlag == 0 && taskInfoForm.taskStatus > 7 && (taskInfoForm.taskType == 3 || taskInfoForm.taskType == 2)\">\r\n            <!-- <template slot-scope=\"scope\">\r\n              <el-input v-model=\"scope.row.doormanReceiveNum\" :min=\"0\" controls-position=\"right\" disabled />\r\n            </template> -->\r\n          </el-table-column>\r\n          <el-table-column prop=\"remark\" label=\"备注\">\r\n          </el-table-column>\r\n\r\n          <!-- v-if=\"taskInfoForm.taskStatus == 4 || taskInfoForm.taskStatus == 5 && (measureFlag == 0 && taskInfoForm.taskType == 2 && taskInfoForm.taskStatus == 7)\" -->\r\n          <!-- <el-table-column v-if=\"measureFlag == 0 && (taskInfoForm.taskStatus == 4 || taskInfoForm.taskStatus == 5)\"\r\n            label=\"操作\" width=\"200\" fixed=\"right\">\r\n            <template slot-scope=\"scope\">\r\n              <div style=\"display: flex; flex-wrap: wrap; gap: 4px;\">\r\n\r\n                <div v-if=\"editingRow === scope.row\">\r\n                  <el-button size=\"mini\" type=\"success\" @click=\"saveDoorManRow(scope.row)\">保存</el-button>\r\n                  <el-button size=\"mini\" @click=\"cancelDoorManEdit(scope.row)\">取消</el-button>\r\n                </div>\r\n\r\n                <div v-else>\r\n                  <el-button v-hasPermi=\"['leave:task:doorManConfirm']\" size=\"mini\" type=\"primary\"\r\n                    @click=\"editDoorManRow(scope.row)\">门卫编辑</el-button>\r\n                </div>\r\n              </div>\r\n            </template>\r\n          </el-table-column> -->\r\n        </el-table>\r\n\r\n        <div class=\"btn-wrapper\" v-if=\"measureFlag == 0 && taskInfoForm.taskStatus == 4\">\r\n          <el-button type=\"primary\" size=\"medium\" @click=\"saveDoorManRow\" class=\"dispatch-btn\">\r\n            <!-- :disabled=\"!hasSelectedItems\" -->\r\n            门卫出厂确认\r\n          </el-button>\r\n        </div>\r\n        <div class=\"btn-wrapper\" v-if=\"measureFlag == 0 && taskInfoForm.taskStatus == 5\">\r\n          <el-button type=\"primary\" size=\"medium\" @click=\"saveDoorManRowIn\" class=\"dispatch-btn\">\r\n            <!-- :disabled=\"!hasSelectedItems\" -->\r\n            门卫入厂确认\r\n          </el-button>\r\n        </div>\r\n        <div class=\"button-container\" v-if=\"measureFlag == 0 && taskInfoForm.taskStatus == 7\">\r\n          <el-button type=\"primary\" @click=\"handleNonMeasureFactoryConfirm\">\r\n            分厂确认\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"section-container\" v-if=\"measureFlag == 1\">\r\n        <div class=\"section-title\">计量信息</div>\r\n        <div class=\"info-footer\" style=\"margin-top: 20px;\" v-if=\"measureFlag == 1\">\r\n          <el-descriptions :column=\"3\" border>\r\n            <el-descriptions-item label=\"皮重\" :label-style=\"{ width: '200px' }\" v-if=\"taskInfoForm.tare != null\">\r\n              {{ taskInfoForm.tare + ' 吨' }}\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"毛重\" :label-style=\"{ width: '200px' }\" v-if=\"taskInfoForm.gross != null\">\r\n              {{ taskInfoForm.gross + ' 吨' }}\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"净重\" :label-style=\"{ width: '200px' }\" v-if=\"taskInfoForm.netWeight != null\">\r\n              {{ taskInfoForm.netWeight + ' 吨' }}\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"皮重时间\" :label-style=\"{ width: '200px' }\" v-if=\"taskInfoForm.tareTime != null\">\r\n              {{ taskInfoForm.tareTime }}\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"毛重时间\" :label-style=\"{ width: '200px' }\" v-if=\"taskInfoForm.grossTime != null\">\r\n              {{ taskInfoForm.grossTime }}\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"净重时间\" :label-style=\"{ width: '200px' }\" v-if=\"taskInfoForm.netWeight != null\">\r\n              {{ taskInfoForm.grossTime }}\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"皮重（复磅）\" :label-style=\"{ width: '200px' }\" v-if=\"taskInfoForm.secTare != null\">\r\n              {{ taskInfoForm.secTare + ' 吨' }}\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"毛重（复磅）\" :label-style=\"{ width: '200px' }\" v-if=\"taskInfoForm.secGross != null\">\r\n              {{ taskInfoForm.secGross + ' 吨' }}\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"净重（复磅）\" :label-style=\"{ width: '200px' }\"\r\n              v-if=\"taskInfoForm.secNetWeight != null\">\r\n              {{ taskInfoForm.secNetWeight + ' 吨' }}\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"皮重时间（复磅）\" :label-style=\"{ width: '200px' }\"\r\n              v-if=\"taskInfoForm.secTareTime != null\">\r\n              {{ taskInfoForm.secTareTime }}\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"毛重时间（复磅）\" :label-style=\"{ width: '200px' }\"\r\n              v-if=\"taskInfoForm.secGrossTime != null\">\r\n              {{ taskInfoForm.secGrossTime }}\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"净重时间（复磅）\" :label-style=\"{ width: '200px' }\"\r\n              v-if=\"taskInfoForm.secNetWeightTime != null\">\r\n              {{ taskInfoForm.secNetWeightTime }}\r\n            </el-descriptions-item>\r\n          </el-descriptions>\r\n          <!-- v-if=\"taskInfoForm.taskStatus == 4 || taskInfoForm.taskStatus == 5\" -->\r\n          <div class=\"btn-wrapper\" v-if=\"measureFlag == 1 && taskInfoForm.taskStatus == 4\">\r\n            <el-button type=\"primary\" size=\"medium\" @click=\"handleDoorManMeasureConfirm\" class=\"dispatch-btn\">\r\n              门卫出厂确认\r\n            </el-button>\r\n          </div>\r\n          <div class=\"btn-wrapper\" v-if=\"measureFlag == 1 && taskInfoForm.taskStatus == 5\">\r\n            <el-button type=\"primary\" size=\"medium\" @click=\"handleDoorManMeasureConfirm\" class=\"dispatch-btn\">\r\n              门卫入厂确认\r\n            </el-button>\r\n          </div>\r\n          <!-- 新增分厂确认按钮 -->\r\n          <!-- <div class=\"btn-wrapper\">\r\n            <el-button type=\"primary\" size=\"medium\" @click=\"openFactoryConfirmDialog\" class=\"dispatch-btn\">\r\n              分厂确认\r\n            </el-button>\r\n          </div> -->\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 可编辑的出库信息表单 -->\r\n      <div class=\"section-container\" v-if=\"measureFlag == 1 && taskInfoForm.taskStatus == 2\">\r\n        <div class=\"section-title\">出库信息</div>\r\n\r\n        <el-form :model=\"factoryConfirmForm\" label-width=\"120px\">\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"计划号\">\r\n                <el-input v-model=\"factoryConfirmForm.planNo\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"车牌号\">\r\n                <el-input v-model=\"factoryConfirmForm.carNum\" placeholder=\"请输入车牌号\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"物资名称\">\r\n                <el-input :value=\"taskMaterials.map(item => item.materialName).join(' ')\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"皮重(t)\">\r\n                <el-input :value=\"taskInfoForm.tare\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\" v-if=\"planForm.planType == 3\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"物料规格\">\r\n                <el-input :value=\"taskMaterials.map(item => item.materialSpec).join(' ')\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"供货单位\">\r\n                <el-input v-model=\"factoryConfirmForm.sourceCompany\" placeholder=\"请输入车牌号\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"收货单位\">\r\n                <el-input v-model=\"factoryConfirmForm.receiveCompany\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"规格\" v-if=\"taskInfoForm.taskType\">\r\n                <el-input v-model=\"factoryConfirmForm.stockOutSpec1Length\" placeholder=\"请输入规格\"></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"总数\">\r\n                <el-input v-model=\"factoryConfirmForm.stockOutTotal\" placeholder=\"请输入总数\"></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"总数单位\">\r\n                <el-select v-model=\"factoryConfirmForm.stockOutTotalUnit\" placeholder=\"请选择总数单位\">\r\n                  <el-option label=\"件\" value=\"件\"></el-option>\r\n                  <el-option label=\"支\" value=\"支\"></el-option>\r\n                  <el-option label=\"张\" value=\"张\"></el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"加工类型\">\r\n                <el-select v-model=\"factoryConfirmForm.stockOutProcessType\" placeholder=\"请选择加工类型\" filterable\r\n                  :filter-method=\"filterProcessType\">\r\n                  <el-option v-for=\"item in filteredProcessTypeOptions\" :key=\"item.value\" :label=\"item.label\"\r\n                    :value=\"item.value\"></el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"炉号/批号\">\r\n                <el-input v-model=\"factoryConfirmForm.stockOutHeatNo\" placeholder=\"请输入炉号\"></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"钢种\">\r\n                <el-input v-model=\"factoryConfirmForm.stockOutSteelGrade\" placeholder=\"请输入钢种\"></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"轴数\">\r\n                <el-select v-model=\"factoryConfirmForm.stockOutAxles\" placeholder=\"请选择轴数\">\r\n                  <el-option label=\"2\" value=\"2\"></el-option>\r\n                  <el-option label=\"3\" value=\"3\"></el-option>\r\n                  <el-option label=\"4\" value=\"4\"></el-option>\r\n                  <el-option label=\"5\" value=\"5\"></el-option>\r\n                  <el-option label=\"6轴标准\" value=\"6轴标准\"></el-option>\r\n                  <el-option label=\"6轴非标准\" value=\"6轴非标准\"></el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-form-item label=\"出库备注\">\r\n            <el-input type=\"textarea\" v-model=\"factoryConfirmForm.stockOutRemark\" placeholder=\"请输入出库备注\"></el-input>\r\n          </el-form-item>\r\n        </el-form>\r\n\r\n        <div class=\"btn-wrapper\">\r\n          <el-button type=\"primary\" @click=\"submitStockOutConfirm\" size=\"medium\" class=\"dispatch-btn\">确认出库</el-button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 只读的出库信息表单 -->\r\n      <div class=\"section-container\"\r\n        v-if=\"measureFlag == 1 && taskInfoForm.taskStatus > 2 && taskInfoForm.taskType !== 2 && taskInfoForm.isDirectSupply != 3\">\r\n        <div class=\"section-title\">出库信息</div>\r\n\r\n        <el-form :model=\"taskInfoForm\" label-width=\"120px\">\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"计划号\">\r\n                <el-input :value=\"taskInfoForm.planNo\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"车牌号\">\r\n                <el-input :value=\"taskInfoForm.carNum\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"物资名称\">\r\n                <el-input :value=\"materialNames\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"皮重(t)\">\r\n                <el-input :value=\"taskInfoForm.tare\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\" v-if=\"planForm.planType == 3\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"物料规格\">\r\n                <el-input :value=\"materialSpecs\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"供货单位\">\r\n                <el-input :value=\"planForm.sourceCompany\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"收货单位\">\r\n                <el-input :value=\"planForm.receiveCompany\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"规格\">\r\n                <el-input :value=\"taskInfoForm.stockOutSpec1Length\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"总数\">\r\n                <el-input :value=\"taskInfoForm.stockOutTotals\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\" v-if=\"taskInfoForm.taskType == 2\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"加工类型\">\r\n                <el-input :value=\"taskInfoForm.stockOutProcessType\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"炉号/批号\">\r\n                <el-input :value=\"taskInfoForm.stockOutHeatNo\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"钢种\">\r\n                <el-input :value=\"taskInfoForm.stockOutSteelGrade\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"轴数\">\r\n                <el-input :value=\"taskInfoForm.stockOutAxles\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-form-item label=\"备注\">\r\n            <el-input type=\"textarea\" :value=\"taskInfoForm.stockOutRemark\" disabled></el-input>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n\r\n      <!-- 可编辑的入库信息表单 -->\r\n      <div class=\"section-container\" v-if=\"measureFlag == 1 && taskInfoForm.taskStatus == 7\">\r\n        <div class=\"section-title\">入库信息</div>\r\n\r\n        <el-form :model=\"factoryConfirmForm\" label-width=\"120px\">\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"计划号\">\r\n                <el-input v-model=\"factoryConfirmForm.planNo\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"车牌号\">\r\n                <el-input v-model=\"factoryConfirmForm.carNum\" placeholder=\"请输入车牌号\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"物资名称\">\r\n                <el-input :value=\"materialNames\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <!-- <div\r\n                v-if=\"taskInfoForm.isDirectSupply == 0 || taskInfoForm.isDirectSupply == null || taskInfoForm.isDirectSupply == ''\">\r\n                <el-form-item label=\"毛重(t)\">\r\n                  <el-input v-model=\"factoryConfirmForm.secGross\" placeholder=\"\" disabled></el-input>\r\n                </el-form-item>\r\n              </div>\r\n\r\n              <div v-if=\"taskInfoForm.isDirectSupply == 1\">\r\n                <el-form-item label=\"毛重(t)\">\r\n                  <el-input v-model=\"factoryConfirmForm.gross\" placeholder=\"\" disabled></el-input>\r\n                </el-form-item>\r\n              </div> -->\r\n\r\n              <el-form-item label=\"毛重(t)\">\r\n                <el-input :value=\"taskInfoForm.secGross\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"供货单位\">\r\n                <el-input v-model=\"factoryConfirmForm.sourceCompany\" placeholder=\"请输入车牌号\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"收货单位\">\r\n                <el-input v-model=\"factoryConfirmForm.receiveCompany\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"加工类型\">\r\n                <el-select v-model=\"factoryConfirmForm.processType\" placeholder=\"请选择加工类型\" filterable\r\n                  :filter-method=\"filterProcessType\">\r\n                  <el-option v-for=\"item in filteredProcessTypeOptions\" :key=\"item.value\" :label=\"item.label\"\r\n                    :value=\"item.value\"></el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"钢种\">\r\n                <el-input v-model=\"factoryConfirmForm.steelGrade\" placeholder=\"请输入钢种\"></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"规格\">\r\n                <el-input v-model=\"factoryConfirmForm.spec1Length\" placeholder=\"请输入规格\"></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"总数\">\r\n                <el-input v-model=\"factoryConfirmForm.total\" placeholder=\"请输入总数\"></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"总数单位\">\r\n                <el-select v-model=\"factoryConfirmForm.totalUnit\" placeholder=\"请选择总数单位\">\r\n                  <el-option label=\"件\" value=\"件\"></el-option>\r\n                  <el-option label=\"支\" value=\"支\"></el-option>\r\n                  <el-option label=\"张\" value=\"张\"></el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"炉号/批号\">\r\n                <el-input v-model=\"factoryConfirmForm.heatNo\" placeholder=\"请输入炉号/批号\"></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"扣重(t)\">\r\n                <el-input v-model=\"factoryConfirmForm.deductWeight\" placeholder=\"请输入扣重\"></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-form-item label=\"出库备注\">\r\n            <el-input type=\"textarea\" v-model=\"factoryConfirmForm.remark\" placeholder=\"请输入出库备注\"></el-input>\r\n          </el-form-item>\r\n\r\n          <el-form-item label=\"是否直供\" v-if=\"taskInfoForm.taskType == 2\">\r\n            <el-checkbox v-model=\"factoryConfirmForm.showDropdown\" @change=\"handleShowDropdownChange\">是否直供</el-checkbox>\r\n          </el-form-item>\r\n\r\n          <el-form-item v-if=\"factoryConfirmForm.showDropdown\" label=\"直供申请单号\">\r\n            <el-input v-model=\"factoryConfirmForm.extraOption\" placeholder=\"请选择直供申请单号\" readonly style=\"width: 300px;\">\r\n              <el-button slot=\"append\" icon=\"el-icon-search\" @click=\"openOptionDialog\"></el-button>\r\n            </el-input>\r\n          </el-form-item>\r\n        </el-form>\r\n\r\n        <div class=\"btn-wrapper\">\r\n          <el-button type=\"primary\" @click=\"submitFactoryConfirm\" size=\"medium\" class=\"dispatch-btn\">确认入库</el-button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 只读的入库信息表单 -->\r\n      <div class=\"section-container\"\r\n        v-if=\"measureFlag == 1 && taskInfoForm.taskStatus > 7 && taskInfoForm.taskType !== 1\">\r\n        <div class=\"section-title\">入库信息</div>\r\n\r\n        <el-form :model=\"taskInfoForm\" label-width=\"120px\">\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"计划号\">\r\n                <el-input :value=\"taskInfoForm.planNo\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"车牌号\">\r\n                <el-input :value=\"taskInfoForm.carNum\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"物资名称\">\r\n                <el-input :value=\"materialNames\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <!-- <div\r\n                v-if=\"taskInfoForm.isDirectSupply == 0 || taskInfoForm.isDirectSupply == null || taskInfoForm.isDirectSupply == ''\">\r\n                <el-form-item label=\"毛重(t)\">\r\n                  <el-input :value=\"taskInfoForm.secGross\" disabled></el-input>\r\n                </el-form-item>\r\n              </div>\r\n\r\n              <div v-if=\"taskInfoForm.isDirectSupply == 1\">\r\n                <el-form-item label=\"毛重(t)\">\r\n                  <el-input :value=\"taskInfoForm.gross\" disabled></el-input>\r\n                </el-form-item>\r\n              </div> -->\r\n\r\n              <el-form-item label=\"毛重(t)\">\r\n                <el-input :value=\"taskInfoForm.secGross\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"供货单位\">\r\n                <el-input :value=\"planForm.sourceCompany\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"收货单位\">\r\n                <el-input :value=\"planForm.receiveCompany\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"加工类型\">\r\n                <el-input :value=\"taskInfoForm.processType\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"钢种\">\r\n                <el-input :value=\"taskInfoForm.steelGrade\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"规格\">\r\n                <el-input :value=\"taskInfoForm.spec1Length\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"总数\">\r\n                <el-input :value=\"taskInfoForm.totals\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"炉号/批号\">\r\n                <el-input :value=\"taskInfoForm.heatNo\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"扣重\">\r\n                <el-input :value=\"taskInfoForm.deductWeight\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-form-item label=\"备注\">\r\n            <el-input type=\"textarea\" :value=\"taskInfoForm.remark\" disabled></el-input>\r\n          </el-form-item>\r\n\r\n\r\n          <el-form-item v-if=\"taskInfoForm.directSupplyTaskNo\" label=\"直供对应任务单号\">\r\n            <el-input :value=\"taskInfoForm.directSupplyTaskNo\" disabled style=\"width: 300px;\"></el-input>\r\n            <el-button style=\"margin-left: 10px; font-size: 14px; padding: 5px 10px;\" type=\"primary\"\r\n              @click=\"openNewTaskWindow\">前往任务单号</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n\r\n      <!-- 日志列表部分 -->\r\n      <div class=\"section-container\">\r\n        <div class=\"section-title\">任务日志</div>\r\n        <el-timeline>\r\n          <el-timeline-item v-for=\"(log, index) in taskLogs\" :key=\"index\" :timestamp=\"log.createTime\"\r\n            :color=\"getLogColor(log)\">\r\n            {{ log.info }}\r\n          </el-timeline-item>\r\n        </el-timeline>\r\n      </div>\r\n\r\n      <div class=\"form-footer\">\r\n        <el-button @click=\"cancel\">返 回</el-button>\r\n      </div>\r\n    </el-card>\r\n\r\n    <!-- 选项弹窗 -->\r\n    <el-dialog title=\"选择直供申请单号\" :visible.sync=\"optionDialogVisible\" width=\"1600px\">\r\n      <el-form :inline=\"true\" :model=\"searchForm\" class=\"demo-form-inline\">\r\n        <el-form-item label=\"计划号\">\r\n          <el-input v-model=\"searchForm.planNo\" placeholder=\"请输入计划号\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"申请编号\">\r\n          <el-input v-model=\"searchForm.applyNo\" placeholder=\"请输入申请编号\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"收货单位\">\r\n          <el-input v-model=\"searchForm.receiveCompany\" placeholder=\"请输入收货单位\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-button type=\"primary\" @click=\"searchOptions\">查询</el-button>\r\n          <el-button @click=\"resetSearch\">重置</el-button>\r\n          <el-button style=\"margin-left: 10px; font-size: 14px; padding: 5px 10px;\" type=\"primary\"\r\n            @click=\"openNewWindow\">直供对应任务号\r\n          </el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n      <el-table :data=\"optionList\" style=\"width: 100%\" @selection-change=\"handleOptionSelection\" ref=\"optionTable\">\r\n        <el-table-column type=\"selection\" width=\"55\" />\r\n        <el-table-column prop=\"planNo\" label=\"计划号\" width=\"150\" />\r\n        <el-table-column prop=\"applyNo\" label=\"申请编号\" width=\"150\" />\r\n        <el-table-column prop=\"materialName\" label=\"物资名称\" width=\"150\" />\r\n        <el-table-column prop=\"materialSpec\" label=\"物料规格\" width=\"120\" />\r\n        <el-table-column prop=\"sourceCompany\" label=\"申请单位\" width=\"150\" />\r\n        <el-table-column prop=\"receiveCompany\" label=\"收货单位\" width=\"150\" />\r\n        <el-table-column prop=\"plannedAmount\" label=\"计划量/t\" width=\"150\" />\r\n        <el-table-column prop=\"startTime\" label=\"开始时间\" width=\"160\">\r\n          <template slot-scope=\"scope\">\r\n            {{ parseTime(scope.row.create_time) }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"endTime\" label=\"结束时间\" width=\"160\">\r\n          <template slot-scope=\"scope\">\r\n            {{ parseTime(scope.row.create_time) }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"planStatus\" label=\"状态\" width=\"150\" />\r\n      </el-table>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"optionDialogVisible = false\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"confirmOptionSelection\">确认</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getTask, getTaskByTaskNo, getTaskmaterials, getProcessList, getDirectSupplyPlans, getDirectSupplyPlanAndTaskDetail, handleUnload, handleStockOut, isAllowDispatch, getPlanMaterials, editTaskmaterials, getTaskLogs, addLeaveLog, updateTask, addLeaveLogAndEditTaskMaterialsAndUpdateTask } from \"@/api/leave/task\";\r\nimport { detailPlan } from \"@/api/leave/plan\";\r\nimport { listXctgDriverCar, getXctgDriverCar, delXctgDriverCar, addXctgDriverCar, updateXctgDriverCar, exportXctgDriverCar } from \"@/api/truck/common/xctgDriverCar\";\r\nimport { Message } from \"element-ui\";\r\nimport QRCode from \"qrcodejs2\";\r\n\r\n\r\nexport default {\r\n  name: \"DispatchTaskDetail\",\r\n  data() {\r\n    return {\r\n      factoryConfirmDialogVisible: false,\r\n      factoryConfirmForm: {\r\n        companyName: '',\r\n        taskNo: '',\r\n        applyNo: '',\r\n        planNo: '',\r\n        taskType: null,\r\n        unloadingWorkNo: '',\r\n        unloadingTime: null,\r\n        spec1Length: null,\r\n        spec2Width: null,\r\n        totals: '',\r\n        total: '',\r\n        totalUnit: '',\r\n        processType: '',\r\n        heatNo: '',\r\n        steelGrade: '',\r\n        axles: '',\r\n        remark: '',\r\n        taskStatus: 9, // 完成状态\r\n        carNum: '', // 车牌号\r\n        // 出库信息\r\n        stockOutSpec1Length: null,\r\n        stockOutSpec2Width: null,\r\n        stockOutTotals: '',\r\n        stockOutTotalUnit: '',\r\n        stockOutTotal: '',\r\n        stockOutProcessType: '',\r\n        stockOutHeatNo: '',\r\n        stockOutSteelGrade: '',\r\n        stockOutAxles: '',\r\n        stockOutRemark: '',\r\n        handledMaterialName: '',\r\n        sourceCompany: '',\r\n        receiveCompany: '',\r\n        showDropdown: false,\r\n        extraOption: '',\r\n        deductWeight: null, // 添加扣重字段\r\n      },\r\n      optionDialogVisible: false,\r\n      searchForm: {\r\n        planNo: '',\r\n        applyNo: '',\r\n        receiveCompany: ''\r\n      },\r\n      optionList: [],\r\n      editDoorManStatus: false,\r\n      editFactoryStatus: false,\r\n      // 司机信息\r\n      driverInfo: {\r\n        id: 1,\r\n        name: '王小明',\r\n        idCard: '110101199001010001',\r\n        phone: '13800138000',\r\n        gender: '1',\r\n        company: '北京运输有限公司',\r\n        photo: 'https://via.placeholder.com/150',\r\n        driverLicenseImgs: 'https://via.placeholder.com/300x200',\r\n        vehicleLicenseImgs: 'https://via.placeholder.com/300x200'\r\n      },\r\n\r\n      // 车辆信息\r\n      carInfo: {},\r\n\r\n      // 任务物资列表\r\n      taskMaterials: [],\r\n\r\n      // 任务日志列表\r\n      taskLogs: [],\r\n\r\n      // 申请编号\r\n      applyNo: null,\r\n\r\n      isdoorMan: false,\r\n\r\n      // 派车任务ID\r\n      dispatchId: null,\r\n\r\n      taskInfoForm: {},\r\n\r\n      measureFlag: null,\r\n\r\n      backupTaskMaterials: null,\r\n      taskNo: null,\r\n\r\n      selectedOption: null,\r\n\r\n      planForm: {},\r\n\r\n      processTypeOptions: [], // 动态加载的加工类型选项\r\n\r\n      filteredProcessTypeOptions: [], // 过滤后的加工类型选项\r\n\r\n      searchProcessTypeQuery: '',// 搜索框的值\r\n\r\n      directSupplyPlanList: [], // 直供计划列表\r\n\r\n      editingRow: null,\r\n\r\n      selectedRows: [], // 添加选中行数据数组\r\n\r\n      directSupplyParams: {}\r\n    };\r\n  },\r\n\r\n  computed: {\r\n    displayProcessTypeOptions() {\r\n      return this.searchProcessTypeQuery ? this.filteredProcessTypeOptions : this.processTypeOptions;\r\n    },\r\n\r\n    // 是否有选中的项\r\n    hasSelectedItems() {\r\n      return this.selectedRows.length > 0;\r\n    },\r\n\r\n    // 添加计算属性\r\n    materialNames() {\r\n      return this.taskMaterials.map(item => item.materialName).join(' ');\r\n    },\r\n\r\n    materialSpecs() {\r\n      return this.taskMaterials.map(item => item.materialSpec).join(' ');\r\n    }\r\n  },\r\n\r\n  activated() {\r\n    console.log(\"activated执行\");\r\n    this.resetTaskInfoForm();\r\n\r\n    // 获取路由参数 - 支持两种方式：query参数和路径参数\r\n    let taskNo = this.$route.params.taskNo || this.$route.query.taskNo;\r\n\r\n    if (taskNo) {\r\n      // 新的方式：通过taskNo获取所有参数\r\n      this.taskNo = taskNo;\r\n      console.log(\"taskNo\", this.taskNo);\r\n      this.validDoorMan();\r\n\r\n      // 使用 async/await 确保按顺序执行\r\n      this.initializeDataByTaskNo();\r\n    } else {\r\n      // 兼容旧的方式：从query参数获取\r\n      const { dispatchId, applyNo, measureFlag, planType, taskNo: queryTaskNo } = this.$route.query;\r\n      this.dispatchId = dispatchId;\r\n      this.applyNo = applyNo;\r\n      this.measureFlag = measureFlag;\r\n      console.log(\"this.measureFlag\", this.measureFlag)\r\n      this.planType = planType;\r\n      this.taskNo = queryTaskNo;\r\n      console.log(\"taskNo\", this.taskNo);\r\n      this.validDoorMan();\r\n\r\n      // 使用 async/await 确保按顺序执行\r\n      this.initializeData();\r\n    }\r\n  },\r\n\r\n  methods: {\r\n    getDirectSupplyPlanAndTask() {\r\n\r\n\r\n      let leaveTask0 = {\r\n        taskNo: this.taskInfoForm.directSupplyTaskNo\r\n      }\r\n\r\n      getDirectSupplyPlanAndTaskDetail(leaveTask0).then(res => {\r\n        console.log(\"getDirectSupplyPlanAndTaskDetail\", res)\r\n        if (res.code == 200) {\r\n          this.directSupplyParams.dispatchId = res.rows[0].id;\r\n          this.directSupplyParams.applyNo = res.rows[0].applyNo;\r\n          this.directSupplyParams.taskNo = res.rows[0].taskNo;\r\n          this.directSupplyParams.measureFlag = res.rows[1].measureFlag;\r\n          this.directSupplyParams.planType = res.rows[1].planType;\r\n        } else {\r\n          this.$message.error(res.message || '获取计划列表失败');\r\n        }\r\n      }).catch(err => {\r\n        console.error('getDirectSupplyPlanAndTaskDetail error:', err);\r\n        this.$message.error('网络异常，稍后重试');\r\n        throw err;\r\n      });\r\n\r\n    },\r\n\r\n    validDoorMan() {\r\n      this.$store.getters.roles.forEach(item => {\r\n        if (item == 'leave.quard') {\r\n          this.isdoorMan = true;\r\n        }\r\n      });\r\n      console.log(\"isdoorMan\", this.isdoorMan)\r\n    },\r\n    async initializeData() {\r\n      try {\r\n        // 等待所有异步操作完成\r\n        await this.getTaskInfo();\r\n        await this.getTaskmaterialList(this.taskNo);\r\n        await this.getPlanInfo(this.applyNo);\r\n\r\n        // 在所有数据加载完成后执行\r\n        this.uploadFactoryConfirmForm();\r\n\r\n        // 其他初始化操作\r\n        this.getTaskLogList(this.taskNo);\r\n        this.getProcessType();\r\n\r\n        //查询直供对应计划、任务详情\r\n        this.getDirectSupplyPlanAndTask();\r\n      } catch (error) {\r\n        console.error('Error initializing data:', error);\r\n        this.$message.error('数据加载失败，请刷新页面重试');\r\n      }\r\n    },\r\n\r\n    async initializeDataByTaskNo() {\r\n      try {\r\n        // 通过taskNo获取任务信息\r\n        await this.getTaskInfoByTaskNo();\r\n\r\n        // 通过applyNo获取计划信息\r\n        await this.getPlanInfo(this.applyNo);\r\n\r\n        // 获取任务物资列表\r\n        await this.getTaskmaterialList(this.taskNo);\r\n\r\n        // 在所有数据加载完成后执行\r\n        this.uploadFactoryConfirmForm();\r\n\r\n        // 其他初始化操作\r\n        this.getTaskLogList(this.taskNo);\r\n        this.getProcessType();\r\n\r\n        //查询直供对应计划、任务详情\r\n        this.getDirectSupplyPlanAndTask();\r\n      } catch (error) {\r\n        console.error('Error initializing data by taskNo:', error);\r\n        this.$message.error('数据加载失败，请刷新页面重试');\r\n      }\r\n    },\r\n\r\n    uploadFactoryConfirmForm() {\r\n      // 赋值后，初始化每个元素的 doormanReceiveNum 和 doormanReceiveNumIn\r\n      this.taskMaterials.forEach(item => {\r\n        item.doormanReceiveNum = item.planNum;\r\n        console.log(\"item.planType\", this.planForm.planType);\r\n        if (this.planForm.planType == 2 || this.planForm.planType == 3) {\r\n          item.doormanReceiveNumIn = item.planNum;\r\n        }\r\n      });\r\n\r\n      let handledMaterialName = this.taskMaterials.map(item => item.materialName).join(' ');\r\n      let materialSpecs = this.taskMaterials.map(item => item.materialSpec).join(' ');\r\n      // 初始化表单数据\r\n      this.factoryConfirmForm = {\r\n        companyName: this.taskInfoForm.companyName,\r\n        gross: this.taskInfoForm.gross,\r\n        secGross: this.taskInfoForm.secGross,\r\n        driverName: this.taskInfoForm.driverName,\r\n        tare: this.taskInfoForm.tare,\r\n        taskNo: this.taskNo,\r\n        applyNo: this.applyNo,\r\n        planNo: this.taskInfoForm.planNo,\r\n        unloadingWorkNo: '',\r\n        unloadingTime: new Date(),\r\n        spec1Length: null,\r\n        spec2Width: null,\r\n        totals: '',\r\n        total: '',\r\n        totalUnit: '',\r\n        processType: '',\r\n        heatNo: '',\r\n        steelGrade: '',\r\n        axles: '',\r\n        remark: '',\r\n        taskStatus: 9,\r\n        carNum: this.taskInfoForm.carNum, // 初始化车牌号\r\n        handledMaterialName: handledMaterialName,\r\n        materialSpecs: materialSpecs,\r\n        sourceCompany: this.planForm.sourceCompany,\r\n        receiveCompany: this.planForm.receiveCompany,\r\n        showDropdown: false, // 是否启用额外选项\r\n        extraOption: '', // 额外选项的值\r\n        // 出库信息\r\n        stockOutSpec1Length: null,\r\n        stockOutSpec2Width: null,\r\n        stockOutTotals: '',\r\n        stockOutTotalUnit: '',\r\n        stockOutTotal: '',\r\n        stockOutProcessType: '',\r\n        stockOutHeatNo: '',\r\n        stockOutSteelGrade: '',\r\n        stockOutAxles: '',\r\n        stockOutRemark: '',\r\n        deductWeight: null, // 添加扣重字段初始化\r\n      };\r\n    },\r\n\r\n    openNewWindow() {\r\n      const newWindowUrl = 'http://localhost/leave/leavePlanList'; // 替换为实际要跳转的页面 URL\r\n      window.open(newWindowUrl, '_blank'); // 打开新窗口并跳转至指定 URL\r\n    },\r\n    //获取可以直供的计划\r\n    async getDirectSupplyList() {\r\n      try {\r\n        let leavePlan = {\r\n          sourceCompany: this.planForm.sourceCompany,\r\n          planType: 3,\r\n        }\r\n        console.log(\"获取可以直供的计划\", leavePlan)\r\n\r\n        const res = await getDirectSupplyPlans(leavePlan);\r\n        console.log(\"getDirectSupplyPlans\", res)\r\n        if (res.code == 200) {\r\n          this.directSupplyPlanList = res.rows;\r\n          // //查询每个计划的物资\r\n          // for (const item of this.directSupplyPlanList) {\r\n          //   console.log(\"item\", item)\r\n          //   let leavePlanMaterial = {\r\n          //     applyNo: item.applyNo\r\n          //   };\r\n          //   const response = await getPlanMaterials(leavePlanMaterial);\r\n          //   if (response.code == 200) {\r\n          //     console.log(\"getPlanMaterials\", response)\r\n          //     item.materialName = response.rows[0].materialName;\r\n          //     item.materialSpec = response.rows[0].materialSpec;\r\n          //   } else {\r\n          //     this.$message.error(response.message || '获取计划物资失败');\r\n          //   }\r\n          // }\r\n        } else {\r\n          this.$message.error(res.message || '获取计划列表失败');\r\n        }\r\n      } catch (err) {\r\n        console.error('getDirectSupplyPlans error:', err);\r\n        this.$message.error('网络异常，稍后重试');\r\n        throw err;\r\n      }\r\n    },\r\n    filterProcessType(query) {\r\n      this.searchProcessTypeQuery = query;\r\n\r\n      if (this.searchProcessTypeQuery) {\r\n        console.log(\"processTypeOptions\", this.processTypeOptions)\r\n\r\n        this.filteredProcessTypeOptions = this.processTypeOptions.filter(item =>\r\n          item.value.includes(query)\r\n        );\r\n      } else {\r\n\r\n        this.filteredProcessTypeOptions = this.processTypeOptions;\r\n      }\r\n    },\r\n    getProcessType() {\r\n      getProcessList().then(res => {\r\n        console.log(\"getProcessList\", res)\r\n        if (res.code == 200) {\r\n          this.processTypeOptions = res.rows.map(item => ({\r\n            value: item.processname,\r\n            label: item.processname\r\n          }));\r\n          this.filteredProcessTypeOptions = this.processTypeOptions; // 初始化过滤后的选项\r\n        } else {\r\n          this.$message.error(res.message || '获取加工类型失败');\r\n        }\r\n      }).catch(err => {\r\n        console.error('getProcessList error:', err);\r\n        this.$message.error('网络异常，稍后重试');\r\n      });\r\n    },\r\n    async getPlanInfo(applyNo) {\r\n      try {\r\n        const response = await detailPlan(applyNo);\r\n        console.log(\"detailPlan\", response);\r\n        this.planForm = response.data;\r\n\r\n        // 从计划信息中获取planType和measureFlag\r\n        this.planType = this.planForm.planType;\r\n        this.measureFlag = this.planForm.measureFlag;\r\n        console.log(\"this.planType\", this.planType);\r\n        console.log(\"this.measureFlag\", this.measureFlag);\r\n\r\n        await this.getDirectSupplyList();\r\n        return response;\r\n      } catch (error) {\r\n        console.error('getPlanInfo error:', error);\r\n        throw error;\r\n      }\r\n    },\r\n    openFactoryConfirmDialog() {\r\n      let handledMaterialName = this.taskMaterials.map(item => item.materialName).join(' ');\r\n      // 初始化表单数据\r\n      this.factoryConfirmForm = {\r\n        companyName: this.taskInfoForm.companyName,\r\n        gross: this.taskInfoForm.gross,\r\n        secGross: this.taskInfoForm.secGross,\r\n        tare: this.taskInfoForm.tare,\r\n        taskNo: this.taskNo,\r\n        applyNo: this.applyNo,\r\n        planNo: this.taskInfoForm.planNo,\r\n        unloadingWorkNo: '',\r\n        unloadingTime: new Date(),\r\n        spec1Length: null,\r\n        spec2Width: null,\r\n        totals: '',\r\n        total: '',\r\n        totalUnit: '',\r\n        processType: '',\r\n        heatNo: '',\r\n        steelGrade: '',\r\n        axles: '',\r\n        remark: '',\r\n        taskStatus: 9,\r\n        carNum: this.taskInfoForm.carNum, // 初始化车牌号\r\n        handledMaterialName: handledMaterialName,\r\n        sourceCompany: this.planForm.sourceCompany,\r\n        receiveCompany: this.planForm.receiveCompany,\r\n        showDropdown: false, // 是否启用额外选项\r\n        extraOption: '', // 额外选项的值\r\n        // 出库信息\r\n        stockOutSpec1Length: null,\r\n        stockOutSpec2Width: null,\r\n        stockOutTotals: '',\r\n        stockOutTotalUnit: '',\r\n        stockOutTotal: '',\r\n        stockOutProcessType: '',\r\n        stockOutHeatNo: '',\r\n        stockOutSteelGrade: '',\r\n        stockOutAxles: '',\r\n        stockOutRemark: '',\r\n        deductWeight: null, // 添加扣重字段初始化\r\n      };\r\n      this.factoryConfirmDialogVisible = true;\r\n    },\r\n    submitFactoryConfirm() {\r\n      if (this.factoryConfirmForm.showDropdown == true) {\r\n        if (this.factoryConfirmForm.extraOption == null || this.factoryConfirmForm.extraOption == '') {\r\n          this.$message.error('请选择额外选项');\r\n          return;\r\n        }\r\n      }\r\n\r\n      let submitData = {};\r\n      if (this.taskInfoForm.isDirectSupply == 3) {\r\n        // 构建提交数据\r\n        submitData = {\r\n          leaveTask: {\r\n            id: this.dispatchId,\r\n            taskNo: this.taskNo,\r\n            applyNo: this.applyNo,\r\n            //入库信息\r\n            spec1Length: this.factoryConfirmForm.spec1Length,\r\n            spec2Width: this.factoryConfirmForm.spec2Width,\r\n            totals: this.factoryConfirmForm.total + this.factoryConfirmForm.totalUnit,\r\n            processType: this.factoryConfirmForm.processType,\r\n            heatNo: this.factoryConfirmForm.heatNo,\r\n            steelGrade: this.factoryConfirmForm.steelGrade,\r\n            axles: this.factoryConfirmForm.axles,\r\n            remark: this.factoryConfirmForm.remark,\r\n            carNum: this.taskInfoForm.carNum,\r\n            driverName: this.taskInfoForm.driverName,\r\n            isDirectSupply: 3,\r\n            planNo: this.taskInfoForm.planNo,\r\n            deductWeight: this.factoryConfirmForm.deductWeight, // 添加扣重字段\r\n\r\n            // 出库信息\r\n            stockOutSpec1Length: this.factoryConfirmForm.stockOutSpec1Length,\r\n            stockOutSpec2Width: this.factoryConfirmForm.stockOutSpec2Width,\r\n            stockOutTotals: this.factoryConfirmForm.stockOutTotal + this.factoryConfirmForm.stockOutTotalUnit,\r\n            stockOutProcessType: this.factoryConfirmForm.stockOutProcessType,\r\n            stockOutHeatNo: this.factoryConfirmForm.stockOutHeatNo,\r\n            stockOutSteelGrade: this.factoryConfirmForm.stockOutSteelGrade,\r\n            stockOutAxles: this.factoryConfirmForm.stockOutAxles,\r\n            stockOutRemark: this.factoryConfirmForm.stockOutRemark,\r\n            // 更改任务状态: 9\r\n            // todo 任务状态如何变化\r\n            taskStatus: 8,\r\n            taskType: this.taskInfoForm.taskType,\r\n          },\r\n          leavePlan: this.planForm,\r\n          leaveTaskMaterial: this.taskMaterials[0],\r\n        };\r\n      } else {\r\n        // 构建提交数据\r\n        submitData = {\r\n          leaveTask: {\r\n            id: this.dispatchId,\r\n            taskNo: this.taskNo,\r\n            applyNo: this.applyNo,\r\n            planNo: this.taskInfoForm.planNo,\r\n            //入库信息\r\n            spec1Length: this.factoryConfirmForm.spec1Length,\r\n            spec2Width: this.factoryConfirmForm.spec2Width,\r\n            totals: this.factoryConfirmForm.total + this.factoryConfirmForm.totalUnit,\r\n            processType: this.factoryConfirmForm.processType,\r\n            heatNo: this.factoryConfirmForm.heatNo,\r\n            steelGrade: this.factoryConfirmForm.steelGrade,\r\n            axles: this.factoryConfirmForm.axles,\r\n            remark: this.factoryConfirmForm.remark,\r\n            carNum: this.taskInfoForm.carNum,\r\n            driverName: this.taskInfoForm.driverName,\r\n            isDirectSupply: 0, // 默认不是直供\r\n            deductWeight: this.factoryConfirmForm.deductWeight, // 添加扣重字段\r\n            directSupplyTaskNo: this.factoryConfirmForm.extraOption,\r\n            // 出库信息\r\n            stockOutSpec1Length: this.factoryConfirmForm.stockOutSpec1Length,\r\n            stockOutSpec2Width: this.factoryConfirmForm.stockOutSpec2Width,\r\n            stockOutTotals: this.factoryConfirmForm.stockOutTotal + this.factoryConfirmForm.stockOutTotalUnit,\r\n            stockOutProcessType: this.factoryConfirmForm.stockOutProcessType,\r\n            stockOutHeatNo: this.factoryConfirmForm.stockOutHeatNo,\r\n            stockOutSteelGrade: this.factoryConfirmForm.stockOutSteelGrade,\r\n            stockOutAxles: this.factoryConfirmForm.stockOutAxles,\r\n            stockOutRemark: this.factoryConfirmForm.stockOutRemark,\r\n            // 更改任务状态: 9\r\n            // todo 任务状态如何变化\r\n            taskStatus: 8,\r\n            taskType: this.taskInfoForm.taskType,\r\n          },\r\n          leavePlan: this.planForm,\r\n          leaveTaskMaterial: this.taskMaterials[0],\r\n        };\r\n      }\r\n\r\n\r\n\r\n      let directSupplyTask = {\r\n        //taskNo后台雪花生成\r\n        applyNo: this.factoryConfirmForm.extraOption,\r\n        taskType: 3,\r\n        taskStatus: 7,\r\n        secGross: this.taskInfoForm.secGross,\r\n        secGrossTime: this.taskInfoForm.secGrossTime,\r\n        planNo: this.taskInfoForm.planNo,\r\n        driverName: this.taskInfoForm.driverName,\r\n        sex: this.taskInfoForm.sex,\r\n        mobilePhone: this.taskInfoForm.mobilePhone,\r\n        idCardNo: this.taskInfoForm.idCardNo,\r\n        carNum: this.taskInfoForm.carNum,\r\n        vehicleEmissionStandards: this.taskInfoForm.vehicleEmissionStandards,\r\n        faceImg: this.taskInfoForm.faceImg,\r\n        drivingLicenseImg: this.taskInfoForm.drivingLicenseImg,\r\n        driverLicenseImg: this.taskInfoForm.driverLicenseImg,\r\n        companyName: this.taskInfoForm.companyName,\r\n        isDirectSupply: 3\r\n      };\r\n\r\n      let directSupplyTaskMaterialList = this.taskMaterials;\r\n\r\n      if (this.factoryConfirmForm.showDropdown == true && this.factoryConfirmForm.extraOption != null && this.factoryConfirmForm.extraOption != '') {\r\n        submitData.leaveTask.isDirectSupply = 1; // 设置为直供\r\n        submitData.directSupplyTask = directSupplyTask;\r\n        submitData.directSupplyTaskMaterialList = directSupplyTaskMaterialList;\r\n      }\r\n\r\n      handleUnload(submitData).then(res => {\r\n        console.log(\"handleUnload\", res)\r\n        if (res.code == 200) {\r\n          this.$message.success('确认入库成功');\r\n          this.factoryConfirmDialogVisible = false;\r\n          this.getTaskLogList(this.taskNo);\r\n          this.getTaskInfo();\r\n        } else {\r\n          // 其他失败原因\r\n          this.$message.error(res.message || '确认入库失败');\r\n        }\r\n      }).catch(err => {\r\n        console.error('handleDirectSupply error:', err);\r\n        this.$message.error('网络异常，稍后重试');\r\n      });\r\n    },\r\n\r\n    submitStockOutConfirm() {\r\n\r\n      // 判断用户角色权限\r\n      const roles = this.$store.getters.roles;\r\n      if (!roles.includes('leave.unloading')) {\r\n        this.$message.error('您没有确认出库权限');\r\n        return;\r\n      }\r\n      // 构建提交数据\r\n      let submitData = {\r\n        leaveTask: {\r\n          //todo 计量系统补充信息待完善\r\n          id: this.dispatchId,\r\n          taskNo: this.taskNo,\r\n          applyNo: this.applyNo,\r\n          planNo: this.taskInfoForm.planNo,\r\n          // 出库信息\r\n          stockOutSpec1Length: this.factoryConfirmForm.stockOutSpec1Length,\r\n          stockOutSpec2Width: this.factoryConfirmForm.stockOutSpec2Width,\r\n          stockOutTotals: this.factoryConfirmForm.stockOutTotal + this.factoryConfirmForm.stockOutTotalUnit,\r\n          stockOutProcessType: this.factoryConfirmForm.stockOutProcessType,\r\n          stockOutHeatNo: this.factoryConfirmForm.stockOutHeatNo,\r\n          stockOutSteelGrade: this.factoryConfirmForm.stockOutSteelGrade,\r\n          stockOutAxles: this.factoryConfirmForm.stockOutAxles,\r\n          stockOutRemark: this.factoryConfirmForm.stockOutRemark,\r\n\r\n          // 更改任务状态: 9\r\n          taskStatus: 3,\r\n          carNum: this.taskInfoForm.carNum,\r\n        },\r\n        leavePlan: this.planForm,\r\n        leaveTaskMaterial: this.taskMaterials[0],\r\n      };\r\n\r\n      handleStockOut(submitData).then(res => {\r\n        console.log(\"handleStockOut\", res)\r\n        if (res.code == 200) {\r\n          this.$message.success('确认出库成功');\r\n          this.factoryConfirmDialogVisible = false;\r\n          this.getTaskLogList(this.taskNo);\r\n          this.getTaskInfo();\r\n        } else {\r\n          // 其他失败原因\r\n          this.$message.error(res.message || '确认出库失败');\r\n        }\r\n      }).catch(err => {\r\n        console.error('handleDirectSupply error:', err);\r\n        this.$message.error('网络异常，稍后重试');\r\n      });\r\n    },\r\n\r\n    handleFactoryConfirm() {\r\n      if (this.editFactoryStatus) {\r\n        this.$message.warning('请先保存');\r\n        return\r\n      }\r\n\r\n\r\n      //todo\r\n      //生成派车日志\r\n      let leaveTaskLog = {};\r\n      leaveTaskLog.logType = 2;\r\n      leaveTaskLog.taskNo = this.taskNo;\r\n      leaveTaskLog.applyNo = this.applyNo;\r\n      leaveTaskLog.info = '分厂确认数量';\r\n\r\n\r\n      let factoryTaskInfo = {}\r\n      //todo 出入场\r\n      factoryTaskInfo.id = this.taskInfoForm.id\r\n      factoryTaskInfo.unloadingWorkNo = '卸货人占位符'\r\n      factoryTaskInfo.unloadingTime = new Date()\r\n      factoryTaskInfo.taskStatus = 9\r\n\r\n      let param = {};\r\n      param.taskMaterialList = this.taskMaterials;\r\n      param.leaveLog = leaveTaskLog;\r\n      param.leaveTask = factoryTaskInfo;\r\n      param.measureFlag = this.measureFlag;\r\n\r\n      addLeaveLogAndEditTaskMaterialsAndUpdateTask(param).then(res => {\r\n        console.log(\"addLeaveLogAndEditTaskMaterialsAndUpdateTask\", res)\r\n        if (res.code == 200) {\r\n          this.$message.success('分厂确认成功');\r\n          this.getTaskLogList(this.taskNo);\r\n          this.getTaskInfo();\r\n        } else {\r\n          // 其他失败原因\r\n          this.$message.error(res.message || '分厂确认成功');\r\n        }\r\n      }).catch(err => {\r\n        console.error('handleFactoryConfirm error:', err);\r\n        this.$message.error('网络异常，稍后重试');\r\n      });\r\n    },\r\n\r\n\r\n    handleDoorManConfirm() {\r\n      if (this.editDoorManStatus) {\r\n        this.$message.warning('请先保存');\r\n        return\r\n      }\r\n\r\n      let leaveTaskLog = {};\r\n      leaveTaskLog.logType = 2;\r\n      leaveTaskLog.taskNo = this.taskNo;\r\n      leaveTaskLog.applyNo = this.applyNo;\r\n      leaveTaskLog.info = '门卫确认数量';\r\n\r\n\r\n\r\n      let doorManTaskInfo = {}\r\n      doorManTaskInfo.id = this.taskInfoForm.id\r\n      if (this.taskInfoForm.taskType == 1) {\r\n        doorManTaskInfo.taskStatus = 9\r\n        doorManTaskInfo.leaveTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //离厂大门\r\n      } else if (this.taskInfoForm.taskType == 2 && this.measureFlag == 0) {\r\n        doorManTaskInfo.taskStatus = 7\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      } else if (this.taskInfoForm.taskType == 2 && this.measureFlag == 1) {\r\n        doorManTaskInfo.taskStatus = 6\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      } else if (this.taskInfoForm.taskType == 3 && this.taskInfoForm.taskStatus == 4) {\r\n        doorManTaskInfo.taskStatus = 5\r\n        doorManTaskInfo.leaveTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //离厂大门\r\n      } else if (this.taskInfoForm.taskType == 3 && this.measureFlag == 0 && this.taskInfoForm.taskStatus == 5) {\r\n        doorManTaskInfo.taskStatus = 7\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      } else if (this.taskInfoForm.taskType == 3 && this.measureFlag == 1 && this.taskInfoForm.taskStatus == 5) {\r\n        doorManTaskInfo.taskStatus = 6\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      }\r\n\r\n      let param = {};\r\n      param.taskMaterialList = this.taskMaterials;\r\n      param.leaveLog = leaveTaskLog;\r\n      param.leaveTask = doorManTaskInfo;\r\n      param.measureFlag = this.measureFlag;\r\n\r\n      addLeaveLogAndEditTaskMaterialsAndUpdateTask(param).then(res => {\r\n        console.log(\"addLeaveLogAndEditTaskMaterialsAndUpdateTask\", res)\r\n        if (res.code == 200) {\r\n          this.$message.success('门卫确认成功');\r\n          this.getTaskLogList(this.taskNo);\r\n          this.getTaskInfo();\r\n        } else {\r\n          // 其他失败原因\r\n          this.$message.error(res.message || '门卫确认成功');\r\n        }\r\n      }).catch(err => {\r\n        console.error('handleDoorManConfirm error:', err);\r\n        this.$message.error('网络异常，稍后重试');\r\n      });\r\n\r\n      // this.taskMaterials.map(item => {\r\n      //   editTaskmaterials(item);\r\n      // })\r\n      //todo\r\n      // let leaveTaskLog = {};\r\n      // leaveTaskLog.logType = 2;\r\n      leaveTaskLog.taskNo = this.taskNo;\r\n      leaveTaskLog.applyNo = this.applyNo;\r\n      leaveTaskLog.info = '门卫确认数量';\r\n      // addLeaveLog(leaveTaskLog);\r\n      // this.getTaskLogList(this.taskNo);\r\n\r\n      // let doorManTaskInfo = {}\r\n      // doorManTaskInfo.id = this.taskInfoForm.id\r\n      // if (this.taskInfoForm.taskType == 1) {\r\n      //   doorManTaskInfo.taskStatus = 9\r\n      //   doorManTaskInfo.leaveTime = new Date()\r\n      //   //离厂大门\r\n      // } else if (this.taskInfoForm.taskType == 2 && this.measureFlag == 0) {\r\n      //   doorManTaskInfo.taskStatus = 7\r\n      //   doorManTaskInfo.enterTime = new Date()\r\n      //   //出厂大门\r\n      // } else if (this.taskInfoForm.taskType == 2 && this.measureFlag == 1) {\r\n      //   doorManTaskInfo.taskStatus = 6\r\n      //   doorManTaskInfo.enterTime = new Date()\r\n      //   //出厂大门\r\n      // } else if (this.taskInfoForm.taskType == 3 && this.taskInfoForm.taskStatus == 4) {\r\n      //   doorManTaskInfo.taskStatus = 5\r\n      //   doorManTaskInfo.leaveTime = new Date()\r\n      //   //离厂大门\r\n      // } else if (this.taskInfoForm.taskType == 3 && this.measureFlag == 0 && this.taskInfoForm.taskStatus == 5) {\r\n      //   doorManTaskInfo.taskStatus = 7\r\n      //   doorManTaskInfo.enterTime = new Date()\r\n      //   //出厂大门\r\n      // } else if (this.taskInfoForm.taskType == 3 && this.measureFlag == 1 && this.taskInfoForm.taskStatus == 5) {\r\n      //   doorManTaskInfo.taskStatus = 6\r\n      //   doorManTaskInfo.enterTime = new Date()\r\n      //   //出厂大门\r\n      // }\r\n      // updateTask(doorManTaskInfo);\r\n      // this.$message.success('门卫确认成功');\r\n\r\n      // setTimeout(() => {\r\n      //   this.getTaskInfo();\r\n      // }, 500)\r\n\r\n    },\r\n\r\n    handleDoorManMeasureConfirm() {\r\n      // 判断用户角色权限\r\n      const roles = this.$store.getters.roles;\r\n      if (!roles.includes('leave.guard')) {\r\n        this.$message.error('您没有门卫出厂确认权限');\r\n        return;\r\n      }\r\n\r\n      let leaveTaskLog = {};\r\n      leaveTaskLog.logType = 2;\r\n      leaveTaskLog.taskNo = this.taskNo;\r\n      leaveTaskLog.applyNo = this.applyNo;\r\n      if (this.taskInfoForm.taskStatus == 4) {\r\n        leaveTaskLog.info = '门卫出厂确认，确认物资：' + this.taskMaterials.map(item => item.materialName).join('、 ');\r\n      } else {\r\n        leaveTaskLog.info = '门卫入厂确认，确认物资：' + this.taskMaterials.map(item => item.materialName).join('、 ');\r\n      }\r\n\r\n      let doorManTaskInfo = {}\r\n      doorManTaskInfo.id = this.taskInfoForm.id\r\n      if (this.taskInfoForm.taskType == 1) {\r\n        doorManTaskInfo.taskStatus = 9\r\n        doorManTaskInfo.leaveTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //离厂大门\r\n      } else if (this.taskInfoForm.taskType == 2 && this.measureFlag == 0) {\r\n        doorManTaskInfo.taskStatus = 7\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      } else if (this.taskInfoForm.taskType == 2 && this.measureFlag == 1) {\r\n        doorManTaskInfo.taskStatus = 6\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      } else if (this.taskInfoForm.taskType == 3 && this.taskInfoForm.taskStatus == 4) {\r\n        doorManTaskInfo.taskStatus = 5\r\n        doorManTaskInfo.leaveTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //离厂大门\r\n      } else if (this.taskInfoForm.taskType == 3 && this.measureFlag == 0 && this.taskInfoForm.taskStatus == 5) {\r\n        doorManTaskInfo.taskStatus = 7\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      } else if (this.taskInfoForm.taskType == 3 && this.measureFlag == 1 && this.taskInfoForm.taskStatus == 5) {\r\n        doorManTaskInfo.taskStatus = 6\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      }\r\n\r\n      let param = {};\r\n      param.taskMaterialList = this.taskMaterials;\r\n      param.leaveLog = leaveTaskLog;\r\n      param.leaveTask = doorManTaskInfo;\r\n      param.measureFlag = this.measureFlag;\r\n\r\n      addLeaveLogAndEditTaskMaterialsAndUpdateTask(param).then(res => {\r\n        console.log(\"addLeaveLogAndEditTaskMaterialsAndUpdateTask\", res)\r\n        if (res.code == 200) {\r\n          this.$message.success('门卫确认成功');\r\n          this.getTaskLogList(this.taskNo);\r\n          this.getTaskInfo();\r\n        } else {\r\n          // 其他失败原因\r\n          this.$message.error(res.message || '门卫确认成功');\r\n        }\r\n      }).catch(err => {\r\n        console.error('handleDoorManConfirm error:', err);\r\n        this.$message.error('网络异常，稍后重试');\r\n      });\r\n      //todo\r\n\r\n    },\r\n    // 生成二维码\r\n    creatQrCode() {\r\n      if (this.taskInfoForm.qrCodeContent) {\r\n        this.$refs.qrCode.innerHTML = \"\";\r\n        var YSqrCode = new QRCode(this.$refs.qrCode, {\r\n          text: this.taskInfoForm.qrCodeContent, // 需要转换为二维码的内容\r\n          width: 120,\r\n          height: 120,\r\n          colorDark: \"#000000\",\r\n          colorLight: \"#ffffff\",\r\n          correctLevel: QRCode.CorrectLevel.H,\r\n        });\r\n      }\r\n    },\r\n    getTaskLogList(taskNo) {\r\n      let taskLog = {};\r\n      taskLog.taskNo = taskNo\r\n      getTaskLogs(taskLog).then(response => {\r\n        console.log(\"getTaskLogs\", response);\r\n        // this.taskLogs = response.rows;\r\n        let logs = response.rows || [];\r\n        // 找出包含\"任务完成\"的日志\r\n        const finishedLogs = logs.filter(log => log.info && log.info.includes('任务完成'));\r\n        const otherLogs = logs.filter(log => !(log.info && log.info.includes('任务完成')));\r\n        // 先放\"任务完成\"，再放其他\r\n        this.taskLogs = [...finishedLogs, ...otherLogs];\r\n      })\r\n\r\n    },\r\n    async getTaskmaterialList(taskNo) {\r\n      try {\r\n        console.log(\"getTaskmaterialList\");\r\n        let leaveMaterial = {};\r\n        leaveMaterial.taskNo = taskNo;\r\n        const response = await getTaskmaterials(leaveMaterial);\r\n        this.taskMaterials = response.rows;\r\n        // 赋值后，初始化每个元素的 doormanReceiveNum 和 doormanReceiveNumIn\r\n        this.taskMaterials.forEach(item => {\r\n          item.doormanReceiveNum = item.planNum;\r\n          console.log(\"item.planType\", this.planForm.planType);\r\n          if (this.planForm.planType == 2 || this.planForm.planType == 3) {\r\n            item.doormanReceiveNumIn = item.planNum;\r\n          }\r\n        });\r\n        console.log(\"taskMaterials\", this.taskMaterials);\r\n        return response;\r\n      } catch (error) {\r\n        console.error('getTaskmaterialList error:', error);\r\n        throw error;\r\n      }\r\n    },\r\n    editDoorManRow(row) {\r\n      row._backup = JSON.parse(JSON.stringify(row));//深拷贝\r\n      this.editingRow = row;\r\n      this.editDoorManStatus = true;\r\n      console.log(\"this.editDoorManRow\", row);\r\n    },\r\n    editFactoryRow() {\r\n      this.backupMaterials = JSON.parse(JSON.stringify(this.taskMaterials));//深拷贝\r\n      this.editFactoryStatus = true;\r\n    },\r\n    cancelDoorManEdit(row) {\r\n      //深拷贝\r\n      if (row._backup) {\r\n        // 恢复备份数据\r\n        Object.assign(row, row._backup);\r\n        delete row._backup; // 删除备份数据\r\n      };\r\n      this.editingRow = null; // 清空当前编辑行\r\n      this.editDoorManStatus = false;\r\n    },\r\n    cancelFactoryEdit() {\r\n      this.taskMaterials = JSON.parse(JSON.stringify(this.backupMaterials));//深拷贝\r\n      console.log(\"this.taskMaterials\", this.taskMaterials);\r\n      this.editFactoryStatus = false;\r\n    },\r\n\r\n    saveDoorManRowIn() {\r\n      // 判断用户角色权限\r\n      const roles = this.$store.getters.roles;\r\n      if (!roles.includes('leave.guard')) {\r\n        this.$message.error('您没有门卫出厂确认权限');\r\n        return;\r\n      }\r\n\r\n      if (this.taskMaterials.length == 0) {\r\n        console.log(\"taskMaterials\", this.taskMaterials);\r\n        this.$message.warning('物资异常');\r\n        return\r\n      }\r\n\r\n      // 校验doormanReceiveNumIn是否等于planNum\r\n      for (const item of this.taskMaterials) {\r\n        if (item.doormanReceiveNumIn !== item.planNum) {\r\n          this.$message.warning(`物资\"${item.materialName}\"的门卫入厂确认数量(${item.doormanReceiveNumIn})与计划数量(${item.planNum})不一致，请检查`);\r\n          return;\r\n        }\r\n      }\r\n\r\n      let leaveTaskLog = {};\r\n      leaveTaskLog.logType = 2;\r\n      leaveTaskLog.taskNo = this.taskNo;\r\n      leaveTaskLog.applyNo = this.applyNo;\r\n      leaveTaskLog.info = '门卫入厂确认，确认物资：' + this.taskMaterials.map(item => item.materialName).join('、 ');\r\n\r\n      let doorManTaskInfo = {}\r\n      doorManTaskInfo.id = this.taskInfoForm.id;\r\n      if (this.taskInfoForm.taskType == 1) {\r\n        doorManTaskInfo.taskStatus = 9\r\n        doorManTaskInfo.leaveTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //离厂大门\r\n      } else if (this.taskInfoForm.taskType == 2 && this.measureFlag == 0) {\r\n        doorManTaskInfo.taskStatus = 7\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      } else if (this.taskInfoForm.taskType == 2 && this.measureFlag == 1) {\r\n        doorManTaskInfo.taskStatus = 6\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      } else if (this.taskInfoForm.taskType == 3 && this.taskInfoForm.taskStatus == 4) {\r\n        doorManTaskInfo.taskStatus = 5\r\n        doorManTaskInfo.leaveTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //离厂大门\r\n      } else if (this.taskInfoForm.taskType == 3 && this.measureFlag == 0 && this.taskInfoForm.taskStatus == 5) {\r\n        doorManTaskInfo.taskStatus = 7\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      } else if (this.taskInfoForm.taskType == 3 && this.measureFlag == 1 && this.taskInfoForm.taskStatus == 5) {\r\n        doorManTaskInfo.taskStatus = 6\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      }\r\n\r\n      let param = {\r\n        taskMaterialList: this.taskMaterials,\r\n        leaveLog: leaveTaskLog,\r\n        leaveTask: doorManTaskInfo,\r\n        measureFlag: this.measureFlag\r\n      };\r\n\r\n      console.log(\"addLeaveLogAndEditTaskMaterialsAndUpdateTask\", param, this.taskInfoForm.taskType);\r\n\r\n\r\n      addLeaveLogAndEditTaskMaterialsAndUpdateTask(param).then(res => {\r\n        console.log(\"addLeaveLogAndEditTaskMaterialsAndUpdateTask\", res)\r\n        if (res.code == 200) {\r\n          this.$message.success('门卫确认成功');\r\n          this.getTaskLogList(this.taskNo);\r\n          this.getTaskInfo();\r\n        } else {\r\n          // 其他失败原因\r\n          this.$message.error(res.message || '门卫确认成功');\r\n        }\r\n      }).catch(err => {\r\n        console.error('handleDoorManConfirm error:', err);\r\n        this.$message.error('网络异常，稍后重试');\r\n      });\r\n\r\n      this.editDoorManStatus = false;\r\n    },\r\n\r\n    saveDoorManRow() {\r\n      // 判断用户角色权限\r\n      const roles = this.$store.getters.roles;\r\n      console.log(\"roles\", roles);\r\n      if (!roles.includes('leave.guard')) {\r\n        this.$message.error('您没有门卫出厂确认权限');\r\n        return;\r\n      }\r\n\r\n      if (this.taskMaterials.length == 0) {\r\n        console.log(\"taskMaterials\", this.taskMaterials);\r\n        this.$message.warning('物资异常');\r\n        return\r\n      }\r\n\r\n      // 校验doormanReceiveNum是否等于planNum\r\n      for (const item of this.taskMaterials) {\r\n        if (item.doormanReceiveNum !== item.planNum) {\r\n          this.$message.warning(`物资\"${item.materialName}\"的门卫确认数量(${item.doormanReceiveNum})与计划数量(${item.planNum})不一致，请检查`);\r\n          return;\r\n        }\r\n      }\r\n\r\n      let leaveTaskLog = {};\r\n      leaveTaskLog.logType = 2;\r\n      leaveTaskLog.taskNo = this.taskNo;\r\n      leaveTaskLog.applyNo = this.applyNo;\r\n      leaveTaskLog.info = '门卫出厂确认，确认物资：' + this.taskMaterials.map(item => item.materialName).join('、 ');\r\n\r\n      let doorManTaskInfo = {}\r\n      doorManTaskInfo.id = this.taskInfoForm.id\r\n      if (this.taskInfoForm.taskType == 1) {\r\n        doorManTaskInfo.taskStatus = 9\r\n        doorManTaskInfo.leaveTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //离厂大门\r\n      } else if (this.taskInfoForm.taskType == 2 && this.measureFlag == 0) {\r\n        doorManTaskInfo.taskStatus = 7\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      } else if (this.taskInfoForm.taskType == 2 && this.measureFlag == 1) {\r\n        doorManTaskInfo.taskStatus = 6\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      } else if (this.taskInfoForm.taskType == 3 && this.taskInfoForm.taskStatus == 4) {\r\n        doorManTaskInfo.taskStatus = 5\r\n        doorManTaskInfo.leaveTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //离厂大门\r\n      } else if (this.taskInfoForm.taskType == 3 && this.measureFlag == 0 && this.taskInfoForm.taskStatus == 5) {\r\n        doorManTaskInfo.taskStatus = 7\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      } else if (this.taskInfoForm.taskType == 3 && this.measureFlag == 1 && this.taskInfoForm.taskStatus == 5) {\r\n        doorManTaskInfo.taskStatus = 6\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      }\r\n\r\n      let param = {\r\n        taskMaterialList: this.taskMaterials,\r\n        leaveLog: leaveTaskLog,\r\n        leaveTask: doorManTaskInfo,\r\n        measureFlag: this.measureFlag\r\n      };\r\n\r\n      console.log(\"addLeaveLogAndEditTaskMaterialsAndUpdateTask\", param, this.taskInfoForm.taskType);\r\n\r\n\r\n      addLeaveLogAndEditTaskMaterialsAndUpdateTask(param).then(res => {\r\n        console.log(\"addLeaveLogAndEditTaskMaterialsAndUpdateTask\", res)\r\n        if (res.code == 200) {\r\n          this.$message.success('门卫确认成功');\r\n          this.getTaskLogList(this.taskNo);\r\n          this.getTaskInfo();\r\n        } else {\r\n          // 其他失败原因\r\n          this.$message.error(res.message || '门卫确认成功');\r\n        }\r\n      }).catch(err => {\r\n        console.error('handleDoorManConfirm error:', err);\r\n        this.$message.error('网络异常，稍后重试');\r\n      });\r\n\r\n      this.editDoorManStatus = false;\r\n    },\r\n\r\n\r\n    saveFactoryRow() {\r\n\r\n      this.editFactoryStatus = false;\r\n    },\r\n\r\n    resetTaskInfoForm() {\r\n      this.taskInfoForm = {};\r\n    },\r\n\r\n    async getTaskInfo() {\r\n      try {\r\n        const response = await getTask(this.dispatchId);\r\n        this.taskInfoForm = response.data;\r\n        console.log(\"this.taskInfoForm\", this.taskInfoForm);\r\n        if (this.taskInfoForm.licensePlateColor == 1) {\r\n          this.taskInfoForm.licensePlateColor = '蓝色'\r\n        } else if (this.taskInfoForm.licensePlateColor == 2) {\r\n          this.taskInfoForm.licensePlateColor = '绿色'\r\n        } else if (this.taskInfoForm.licensePlateColor == 3) {\r\n          this.taskInfoForm.licensePlateColor = '黄'\r\n        } else if (this.taskInfoForm.licensePlateColor == 4) {\r\n          this.taskInfoForm.licensePlateColor = '黄绿色'\r\n        }\r\n        console.log(\"this.taskInfoForm\", this.taskInfoForm);\r\n        // 生成二维码\r\n        this.$nextTick(() => {\r\n          this.creatQrCode();\r\n        });\r\n        return response;\r\n      } catch (error) {\r\n        console.error('getTaskInfo error:', error);\r\n        throw error;\r\n      }\r\n    },\r\n\r\n    async getTaskInfoByTaskNo() {\r\n      try {\r\n        const response = await getTaskByTaskNo(this.taskNo);\r\n        this.taskInfoForm = response.data;\r\n        console.log(\"this.taskInfoForm\", this.taskInfoForm);\r\n\r\n        // 从返回的数据中获取所需的参数\r\n        this.dispatchId = this.taskInfoForm.id;\r\n        this.applyNo = this.taskInfoForm.applyNo;\r\n\r\n        if (this.taskInfoForm.licensePlateColor == 1) {\r\n          this.taskInfoForm.licensePlateColor = '蓝色'\r\n        } else if (this.taskInfoForm.licensePlateColor == 2) {\r\n          this.taskInfoForm.licensePlateColor = '绿色'\r\n        } else if (this.taskInfoForm.licensePlateColor == 3) {\r\n          this.taskInfoForm.licensePlateColor = '黄'\r\n        } else if (this.taskInfoForm.licensePlateColor == 4) {\r\n          this.taskInfoForm.licensePlateColor = '黄绿色'\r\n        }\r\n        console.log(\"this.taskInfoForm\", this.taskInfoForm);\r\n        // 生成二维码\r\n        this.$nextTick(() => {\r\n          this.creatQrCode();\r\n        });\r\n        return response;\r\n      } catch (error) {\r\n        console.error('getTaskInfoByTaskNo error:', error);\r\n        throw error;\r\n      }\r\n    },\r\n\r\n\r\n    getStatusText(standard) {\r\n      const standardMap = {\r\n        1: '待过皮重',\r\n        2: '待装货',\r\n        3: '待过毛重',\r\n        4: '待出厂',\r\n        5: '待返厂',\r\n        6: '待过毛重(复磅)',\r\n        7: '待卸货',\r\n        8: '待过皮重(复磅)',\r\n        9: '完成'\r\n      };\r\n      return standardMap[standard] || '未知';\r\n    },\r\n\r\n    //计划状态\r\n    getPlanStatusText(standard) {\r\n      const standardMap = {\r\n        1: '待分厂审批',\r\n        2: '待分厂复审',\r\n        3: '待生产指挥中心审批',\r\n        4: '审批完成',\r\n        5: '已出厂',\r\n        6: '部分收货',\r\n        7: '已完成',\r\n        11: '驳回',\r\n        12: '废弃',\r\n        13: '过期',\r\n        '待分厂审批': '待分厂审批',\r\n        '待分厂复审': '待分厂复审',\r\n        '待生产指挥中心审批': '待生产指挥中心审批',\r\n        '审批完成': '审批完成',\r\n        '已出厂': '已出厂',\r\n        '部分收货': '部分收货',\r\n        '已完成': '已完成',\r\n        '驳回': '驳回',\r\n        '废弃': '废弃',\r\n        '过期': '过期',\r\n      };\r\n      return standardMap[standard] || '未知';\r\n    },\r\n    // 获取排放标准文本\r\n    getEmissionStandardsText(standard) {\r\n      const standardMap = {\r\n        1: '国五',\r\n        2: '国六',\r\n        3: '新能源'\r\n      };\r\n      return standardMap[standard] || '未知';\r\n    },\r\n\r\n    // 获取排放标准标签类型\r\n    getEmissionStandardsTagType(standard) {\r\n      const typeMap = {\r\n        1: 'warning',  // 国五\r\n        2: 'success',  // 国六\r\n        3: 'primary'   // 新能源\r\n      };\r\n      return typeMap[standard] || 'info';\r\n    },\r\n\r\n    // 获取物资状态文本\r\n    getMaterialStatusText(status) {\r\n      const statusMap = {\r\n        1: '待装载',\r\n        2: '已装载',\r\n        3: '已签收',\r\n        4: '异常'\r\n      };\r\n      return statusMap[status] || '未知状态';\r\n    },\r\n\r\n    // 获取物资状态标签类型\r\n    getMaterialStatusType(status) {\r\n      const typeMap = {\r\n        1: 'info',     // 待装载\r\n        2: 'warning',  // 已装载\r\n        3: 'success',  // 已签收\r\n        4: 'danger'    // 异常\r\n      };\r\n      return typeMap[status] || 'info';\r\n    },\r\n\r\n    // 获取日志颜色\r\n    getLogColor(log) {\r\n      const logTypeColorMap = {\r\n        1: '#409EFF', // 创建\r\n        2: '#E6A23C', // 更新\r\n        3: '#67C23A', // 完成\r\n        4: '#F56C6C', // 异常\r\n        5: '#909399'  // 其他\r\n      };\r\n      return logTypeColorMap[log.type] || '#409EFF';\r\n    },\r\n\r\n    // 返回按钮\r\n    cancel() {\r\n      this.$router.go(-1);\r\n    },\r\n\r\n    // 获取任务详情数据\r\n    getTaskDetail(dispatchId) {\r\n      // 实际项目中这里需要调用API获取数据\r\n      // getDispatchTaskDetail(dispatchId).then(response => {\r\n      //   const { driverInfo, carInfo, taskMaterials, taskLogs } = response.data;\r\n      //   this.driverInfo = driverInfo;\r\n      //   this.carInfo = carInfo;\r\n      //   this.taskMaterials = taskMaterials;\r\n      //   this.taskLogs = taskLogs;\r\n      // });\r\n    },\r\n    handleShowDropdownChange(val) {\r\n      if (!val) {\r\n        this.factoryConfirmForm.extraOption = '';\r\n      }\r\n    },\r\n    openOptionDialog() {\r\n      this.optionDialogVisible = true;\r\n      this.loadOptions();\r\n      // 重置选中状态\r\n      this.selectedOption = null;\r\n      this.$nextTick(() => {\r\n        if (this.$refs.optionTable) {\r\n          this.$refs.optionTable.clearSelection();\r\n        }\r\n      });\r\n    },\r\n    handleOptionSelection(selection) {\r\n      // 只保留最后选中的一项\r\n      if (selection.length > 1) {\r\n        const lastSelected = selection[selection.length - 1];\r\n        this.$refs.optionTable.clearSelection();\r\n        this.$refs.optionTable.toggleRowSelection(lastSelected, true);\r\n        this.selectedOption = lastSelected;\r\n      } else {\r\n        this.selectedOption = selection[0];\r\n      }\r\n    },\r\n    confirmOptionSelection() {\r\n      if (!this.selectedOption) {\r\n        this.$message.warning('请选择一个选项');\r\n        return;\r\n      }\r\n\r\n      this.factoryConfirmForm.extraOption = this.selectedOption.applyNo;\r\n\r\n      // let dispatchInfo = {};\r\n      // dispatchInfo.carNum = this.taskInfoForm.carNum;\r\n      // dispatchInfo.isDirectSupply = 1;\r\n\r\n      // isAllowDispatch(dispatchInfo).then(response => {\r\n      //   let row = response.data;\r\n      //   if (row > 0) {\r\n      //     this.$message.error(\"当前车有正在执行的任务\")\r\n      //     return;\r\n      //   } else {\r\n      //     this.optionDialogVisible = false;\r\n      //     this.$message.success('选项已确认');\r\n      //   }\r\n      //   console.log(\"this.isAllowDispatch\", response);\r\n      // }).catch(err => {\r\n      //   console.error('dispatch error:', err);\r\n      //   this.$message.error('网络异常，稍后重试');\r\n      // });\r\n\r\n      this.optionDialogVisible = false;\r\n      this.$message.success('选项已确认');\r\n\r\n\r\n\r\n    },\r\n    loadOptions() {\r\n      // 这里应该调用API获取leave_plan表的数据\r\n      this.optionList = this.directSupplyPlanList; // 使用直供计划列表作为选项数据\\\r\n      this.optionList.forEach(item => {\r\n        item.planStatus = this.getPlanStatusText(item.planStatus);\r\n      });\r\n      console.log(\"optionList\", this.optionList)\r\n    },\r\n    getBusinessCategoryText(category) {\r\n      const categoryMap = {\r\n        1: '通用（出厂不返回）',\r\n        11: '通用（出厂返回）',\r\n        12: '委外加工（出厂返回）',\r\n        21: '有计划量计量（跨区调拨）',\r\n        22: '短期（跨区调拨）',\r\n        23: '钢板（圆钢）（跨区调拨）',\r\n        31: '通用（退货申请）'\r\n      };\r\n      return categoryMap[category] || '未知类型';\r\n    },\r\n    searchOptions() {\r\n      // 取出并转小写\r\n      const searchPlanNo = (this.searchForm.planNo || '').toLowerCase();\r\n      const searchApplyNo = (this.searchForm.applyNo || '').toLowerCase();\r\n      const searchReceiveCompany = (this.searchForm.receiveCompany || '').toLowerCase();\r\n\r\n      // 过滤\r\n      this.optionList = this.directSupplyPlanList.filter(item => {\r\n        const planNo = (item.planNo || '').toString().toLowerCase();\r\n        const applyNo = (item.applyNo || '').toString().toLowerCase();\r\n        const receiveCompany = (item.receiveCompany || '').toString().toLowerCase();\r\n\r\n        // 为空不作为条件\r\n        const matchPlanNo = !searchPlanNo || planNo.includes(searchPlanNo);\r\n        const matchApplyNo = !searchApplyNo || applyNo.includes(searchApplyNo);\r\n        const matchReceiveCompany = !searchReceiveCompany || receiveCompany.includes(searchReceiveCompany);\r\n\r\n        return matchPlanNo && matchApplyNo && matchReceiveCompany;\r\n      });\r\n\r\n      // 更新状态显示\r\n      this.optionList.forEach(item => {\r\n        item.planStatus = this.getPlanStatusText(item.planStatus);\r\n      });\r\n    },\r\n    resetSearch() {\r\n      this.searchForm = {\r\n        planNo: '',\r\n        applyNo: '',\r\n        receiveCompany: ''\r\n      };\r\n      this.loadOptions(); // 重新加载所有数据\r\n    },\r\n    getTaskTypeText(type) {\r\n      const typeMap = {\r\n        1: '出厂',\r\n        2: '返厂',\r\n        3: '跨区调拨'\r\n      };\r\n      return typeMap[type] || '未知';\r\n    },\r\n    // // 判断行是否可选\r\n    // isSelectable(row) {\r\n    //   // 当门卫确认数量不为0时，该行可选\r\n    //   return row.doormanReceiveNum > 0 && this.taskInfoForm.taskStatus !== 9;\r\n    // },\r\n\r\n    // 表格选择变化时的处理函数\r\n    handleSelectionChange(selection) {\r\n      this.selectedRows = selection;\r\n    },\r\n\r\n    // 处理非计量分厂确认\r\n    handleNonMeasureFactoryConfirm() {\r\n      const roles = this.$store.getters.roles;\r\n      if (!roles.includes('leave.unloading')) {\r\n        this.$message.error('您没有门卫出厂确认权限');\r\n        return;\r\n      }\r\n      let isHandled = false;\r\n      this.selectedRows.forEach(item => {\r\n        if (item.doormanReceiveNum !== item.planNum) {\r\n          this.$message.warning('门卫确认数量和计划数量不一致，请检查');\r\n          isHandled = true;\r\n        }\r\n      });\r\n\r\n      if (isHandled) {\r\n        return;\r\n      }\r\n\r\n      // if (this.selectedRows.length === 0) {\r\n      //   this.$message.warning('请选择需要确认的物资');\r\n      //   return;\r\n      // }\r\n\r\n      // 生成派车日志\r\n      let leaveTaskLog = {\r\n        logType: 2,\r\n        taskNo: this.taskNo,\r\n        applyNo: this.applyNo,\r\n        info: '分厂接收确认，确认物资：' + this.taskMaterials.map(item => item.materialName).join('、 ')\r\n      };\r\n\r\n      // 构建任务信息\r\n      let factoryTaskInfo = {\r\n        id: this.taskInfoForm.id,\r\n        unloadingWorkNo: '卸货人占位符',//后端updateLeaveTask方法\r\n        unloadingTime: new Date(),\r\n        taskStatus: 9\r\n      };\r\n\r\n      this.selectedRows.forEach(item => {\r\n        // 设置非计量分厂确认数量\r\n        item.factoryReceiveNum = item.doormanReceiveNum;\r\n      });\r\n\r\n      // 构建请求参数\r\n      let param = {\r\n        taskMaterialList: this.selectedRows, // 使用选中的行数据\r\n        leaveLog: leaveTaskLog,\r\n        leaveTask: factoryTaskInfo,\r\n        measureFlag: this.measureFlag\r\n      };\r\n\r\n      // 发送请求\r\n      addLeaveLogAndEditTaskMaterialsAndUpdateTask(param).then(res => {\r\n        if (res.code == 200) {\r\n          this.$message.success('非计量分厂确认成功');\r\n          this.getTaskLogList(this.taskNo);\r\n          this.getTaskInfo();\r\n          // 清空选中状态\r\n          this.selectedRows = [];\r\n        } else {\r\n          this.$message.error(res.message || '非计量分厂确认失败');\r\n        }\r\n      }).catch(err => {\r\n        console.error('handleNonMeasureFactoryConfirm error:', err);\r\n        this.$message.error('网络异常，稍后重试');\r\n      });\r\n    },\r\n    openNewTaskWindow() {\r\n      console.log(\"openNewTaskWindow\", this.directSupplyParams);\r\n      let dispatchId = this.directSupplyParams.dispatchId;\r\n      let applyNo = BigInt(this.directSupplyParams.applyNo);\r\n      let measureFlag = this.directSupplyParams.measureFlag;\r\n      let planType = this.directSupplyParams.planType;\r\n      let taskNo = BigInt(this.directSupplyParams.taskNo);\r\n      const url = `http://localhost/leave/plan/task?dispatchId=${dispatchId}&applyNo=${applyNo}&measureFlag=${measureFlag}&planType=${planType}&taskNo=${taskNo}`;\r\n      window.open(url, '_blank');\r\n    },\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.btn-wrapper {\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n\r\n.dispatch-btn {\r\n  margin-left: 15px;\r\n}\r\n\r\n.app-container {\r\n  padding: 20px;\r\n}\r\n\r\n.qrcode-container {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  padding: 20px;\r\n}\r\n\r\n.qrcode {\r\n  margin: 0 auto;\r\n}\r\n\r\n.box-card {\r\n  margin-bottom: 20px;\r\n  border-radius: 5px;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.section-container {\r\n  margin-bottom: 30px;\r\n  border-radius: 8px;\r\n  background: #fff;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);\r\n  overflow: hidden;\r\n  border: 1px solid #ebeef5;\r\n}\r\n\r\n.section-container:nth-child(1) {\r\n  border-top: 4px solid #F56C6C;\r\n  /* 通行证二维码模块 - 红色 */\r\n}\r\n\r\n.section-container:nth-child(2) {\r\n  border-top: 4px solid #409EFF;\r\n  /* 司机信息模块 - 蓝色 */\r\n}\r\n\r\n.section-container:nth-child(3) {\r\n  border-top: 4px solid #67C23A;\r\n  /* 车辆信息模块 - 绿色 */\r\n}\r\n\r\n.section-container:nth-child(4) {\r\n  border-top: 4px solid #E6A23C;\r\n  /* 物资列表模块 - 橙色 */\r\n}\r\n\r\n.section-container:nth-child(5) {\r\n  border-top: 4px solid #909399;\r\n  /* 日志列表模块 - 灰色 */\r\n}\r\n\r\n.section-title {\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  padding: 15px 20px;\r\n  margin-bottom: 15px;\r\n  border-bottom: 1px solid #ebeef5;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  background: #fafafa;\r\n  position: relative;\r\n  padding-left: 30px;\r\n}\r\n\r\n.section-title::before {\r\n  content: '';\r\n  width: 4px;\r\n  height: 16px;\r\n  background: currentColor;\r\n  position: absolute;\r\n  left: 15px;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  border-radius: 2px;\r\n}\r\n\r\n.section-container:nth-child(1) .section-title {\r\n  color: #F56C6C;\r\n}\r\n\r\n.section-container:nth-child(2) .section-title {\r\n  color: #409EFF;\r\n}\r\n\r\n.section-container:nth-child(3) .section-title {\r\n  color: #67C23A;\r\n}\r\n\r\n.section-container:nth-child(4) .section-title {\r\n  color: #E6A23C;\r\n}\r\n\r\n.section-container:nth-child(5) .section-title {\r\n  color: #909399;\r\n}\r\n\r\n.section-container .el-descriptions,\r\n.section-container .el-table,\r\n.section-container .el-timeline {\r\n  padding: 0 20px 20px;\r\n}\r\n\r\n.form-footer {\r\n  margin-top: 30px;\r\n  text-align: center;\r\n}\r\n\r\n.driver-photos {\r\n  padding: 0 20px 20px;\r\n  display: flex;\r\n  gap: 20px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.photo-item {\r\n  width: 300px;\r\n  border: 1px solid #ebeef5;\r\n  border-radius: 4px;\r\n  overflow: hidden;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.photo-item h4 {\r\n  padding: 10px;\r\n  background: #f5f7fa;\r\n  margin: 0;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.photo-container {\r\n  padding: 10px;\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n\r\n.photo-container img {\r\n  max-width: 100%;\r\n  max-height: 200px;\r\n  object-fit: contain;\r\n}\r\n\r\n.button-container {\r\n  margin-top: 20px;\r\n  text-align: center;\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n.el-table {\r\n  border-radius: 4px;\r\n  overflow: hidden;\r\n\r\n  th {\r\n    background-color: #fafafa !important;\r\n    color: #606266;\r\n    font-weight: bold;\r\n  }\r\n\r\n  td {\r\n    padding: 12px 0;\r\n  }\r\n}\r\n\r\n\r\n\r\n.el-timeline {\r\n  padding: 20px !important;\r\n\r\n  .el-timeline-item__node {\r\n    width: 12px;\r\n    height: 12px;\r\n  }\r\n\r\n  .el-timeline-item__content {\r\n    padding: 0 0 0 25px;\r\n  }\r\n}\r\n\r\n.el-descriptions {\r\n  .el-descriptions-item__label {\r\n    background-color: #fafafa;\r\n  }\r\n}\r\n\r\n.el-tag {\r\n  border-radius: 12px;\r\n  padding: 0 10px;\r\n}\r\n</style>\r\n"]}]}