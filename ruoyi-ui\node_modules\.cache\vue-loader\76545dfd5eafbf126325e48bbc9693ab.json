{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\suppPunishment\\punishmentBasis-module.vue?vue&type=style&index=0&id=5e61e69e&scoped=true&lang=css", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\suppPunishment\\punishmentBasis-module.vue", "mtime": 1756099891103}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\@vue\\cli-service\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi8qIOW8ueeql+WGheWuueWuueWZqCAqLw0KLmJhc2lzLWRpYWxvZy1jb250ZW50IHsNCiAgcGFkZGluZzogMTBweCAwOw0KfQ0KDQovKiDnq6DoioLmoIfpopjmoLflvI8gKi8NCi5zZWN0aW9uLXRpdGxlIHsNCiAgZm9udC1zaXplOiAxNnB4Ow0KICBjb2xvcjogIzMwMzEzMzsNCiAgbWFyZ2luOiAwIDAgMTVweCAwOw0KICBmb250LXdlaWdodDogNjAwOw0KfQ0KDQovKiDpobbnuqfmoIfpopjmoLflvI/vvIjpgInmi6nkvp3mja7nsbvlnovvvIkgKi8NCi5iYXNpcy1vcHRpb25zIC5zZWN0aW9uLXRpdGxlIHsNCiAgbWFyZ2luLWJvdHRvbTogMTVweDsNCn0NCg0KLyog5aSE572a5L6d5o2u6YCJ6aG55qC35byPICovDQouYmFzaXMtb3B0aW9ucyB7DQogIG1hcmdpbi1ib3R0b206IDIwcHg7DQp9DQoNCi5iYXNpcy1pdGVtIHsNCiAgbWFyZ2luLWJvdHRvbTogMTVweDsNCiAgcGFkZGluZzogMDsNCn0NCg0KLyog5paw55qE6KGM5biD5bGAICovDQouYmFzaXMtcm93IHsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgd2lkdGg6IDEwMCU7DQogIG1pbi1oZWlnaHQ6IDM2cHg7DQp9DQoNCi5yYWRpby13cmFwcGVyIHsNCiAgd2lkdGg6IDEyMHB4Ow0KICBmbGV4LXNocmluazogMDsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgaGVpZ2h0OiAzNnB4Ow0KfQ0KDQouaW5wdXQtd3JhcHBlciB7DQogIHdpZHRoOiBjYWxjKDEwMCUgLSAxMzVweCk7DQogIG1hcmdpbi1sZWZ0OiAxNHB4Ow0KfQ0KDQouYWxpZ25lZC1pbnB1dCB7DQogIHdpZHRoOiAxMDAlOw0KfQ0KDQouYWxpZ25lZC1pbnB1dCA6OnYtZGVlcCAuZWwtaW5wdXRfX2lubmVyIHsNCiAgaGVpZ2h0OiAzNnB4Ow0KICBsaW5lLWhlaWdodDogMzZweDsNCiAgYm9yZGVyLXJhZGl1czogNHB4Ow0KICBmb250LXNpemU6IDE0cHg7DQp9DQoNCi8qIOWNlemAieahhuWvuem9kOagt+W8jyAqLw0KLnJhZGlvLXdyYXBwZXIgOjp2LWRlZXAgLmVsLXJhZGlvIHsNCiAgaGVpZ2h0OiAzNnB4Ow0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBtYXJnaW46IDA7DQp9DQoNCi5yYWRpby13cmFwcGVyIDo6di1kZWVwIC5lbC1yYWRpb19faW5wdXQgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBtYXJnaW4tcmlnaHQ6IDhweDsNCn0NCg0KLnJhZGlvLXdyYXBwZXIgOjp2LWRlZXAgLmVsLXJhZGlvX19pbm5lciB7DQogIHdpZHRoOiAxNnB4Ow0KICBoZWlnaHQ6IDE2cHg7DQp9DQoNCi5yYWRpby13cmFwcGVyIDo6di1kZWVwIC5lbC1yYWRpb19fbGFiZWwgew0KICBmb250LXNpemU6IDE0cHg7DQogIGxpbmUtaGVpZ2h0OiAzNnB4Ow0KICBwYWRkaW5nLWxlZnQ6IDhweDsNCiAgY29sb3I6ICM2MDYyNjY7DQp9DQoNCi8qIOS+neaNruWGheWuueWMuuWfn+agt+W8jyAqLw0KLmJhc2lzLWNvbnRlbnQgew0KICBtYXJnaW4tYm90dG9tOiAyMHB4Ow0KfQ0KDQouYmFzaXMtY29udGVudCAuc2VjdGlvbi10aXRsZSB7DQogIG1hcmdpbi1ib3R0b206IDE1cHg7DQp9DQoNCi5jb250ZW50LXdyYXBwZXIgew0KICBwYWRkaW5nOiAwOw0KfQ0KDQouY29udGVudC10ZXh0YXJlYSB7DQogIHdpZHRoOiAxMDAlOw0KfQ0KDQouY29udGVudC10ZXh0YXJlYSA6OnYtZGVlcCAuZWwtdGV4dGFyZWFfX2lubmVyIHsNCiAgYm9yZGVyLXJhZGl1czogNHB4Ow0KICBmb250LWZhbWlseTogaW5oZXJpdDsNCiAgZm9udC1zaXplOiAxNHB4Ow0KICBsaW5lLWhlaWdodDogMS41Ow0KICBtaW4taGVpZ2h0OiAxMjBweDsNCn0NCg0KLyog6aKE6KeI5Yy65Z+f5qC35byPICovDQoucHJldmlldy1hcmVhIHsNCiAgbWFyZ2luLWJvdHRvbTogMDsNCn0NCg0KLnByZXZpZXctYXJlYSAuc2VjdGlvbi10aXRsZSB7DQogIG1hcmdpbi1ib3R0b206IDE1cHg7DQp9DQoNCi5wcmV2aWV3LXdyYXBwZXIgew0KICBwYWRkaW5nOiAwOw0KfQ0KDQoucHJldmlldy10ZXh0YXJlYSB7DQogIHdpZHRoOiAxMDAlOw0KfQ0KDQoucHJldmlldy10ZXh0YXJlYSA6OnYtZGVlcCAuZWwtdGV4dGFyZWFfX2lubmVyIHsNCiAgYm9yZGVyLXJhZGl1czogNHB4Ow0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmZmZmOw0KICBmb250LWZhbWlseTogaW5oZXJpdDsNCiAgZm9udC1zaXplOiAxNHB4Ow0KICBsaW5lLWhlaWdodDogMS41Ow0KICBtaW4taGVpZ2h0OiAxMjBweDsNCn0NCg0KLyog5by556qX5qCH6aKY5bGF5LitICovDQo6OnYtZGVlcCAuZWwtZGlhbG9nX19oZWFkZXIgew0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogIGJhY2tncm91bmQ6ICNmOGY5ZmE7DQogIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZTllY2VmOw0KfQ0KDQo6OnYtZGVlcCAuZWwtZGlhbG9nX190aXRsZSB7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgd2lkdGg6IDEwMCU7DQogIGRpc3BsYXk6IGJsb2NrOw0KICBmb250LXdlaWdodDogNjAwOw0KICBjb2xvcjogIzMwMzEzMzsNCn0NCg0KLyog5LiT6Zeo55qE5by556qX5qC35byPICovDQo6OnYtZGVlcCAuYmFzaXMtZGlhbG9nIHsNCiAgbWFyZ2luLXRvcDogNXZoICFpbXBvcnRhbnQ7DQp9DQoNCjo6di1kZWVwIC5iYXNpcy1kaWFsb2cgLmVsLWRpYWxvZ19fYm9keSB7DQogIHBhZGRpbmc6IDI1cHg7DQogIG1heC1oZWlnaHQ6IDc1dmg7DQogIG92ZXJmbG93LXk6IGF1dG87DQogIGJhY2tncm91bmQtY29sb3I6ICNmZmZmZmY7DQp9DQoNCjo6di1kZWVwIC5iYXNpcy1kaWFsb2cgLmVsLWRpYWxvZ19faGVhZGVyIHsNCiAgcGFkZGluZzogMjBweCAyNXB4IDE1cHg7DQp9DQoNCjo6di1kZWVwIC5iYXNpcy1kaWFsb2cgLmVsLWRpYWxvZ19fZm9vdGVyIHsNCiAgcGFkZGluZzogMTVweCAyNXB4IDIwcHg7DQogIHRleHQtYWxpZ246IHJpZ2h0Ow0KfQ0KDQovKiDlupXpg6jmjInpkq7moLflvI8gKi8NCi5mb290ZXItYnV0dG9ucyB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogZmxleC1lbmQ7DQogIGdhcDogMTBweDsNCn0NCg0KLyog5Y2V6YCJ5qGG57uE5qC35byPICovDQo6OnYtZGVlcCAuZWwtcmFkaW8tZ3JvdXAgew0KICB3aWR0aDogMTAwJTsNCn0NCg0KOjp2LWRlZXAgLmVsLXJhZGlvIHsNCiAgbWFyZ2luLXJpZ2h0OiAwOw0KICBtYXJnaW4tYm90dG9tOiAwOw0KfQ0KDQo6OnYtZGVlcCAuZWwtcmFkaW9fX2xhYmVsIHsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgd2lkdGg6IDEwMCU7DQogIGZvbnQtc2l6ZTogMTRweDsNCiAgY29sb3I6ICM2MDYyNjY7DQp9DQoNCjo6di1kZWVwIC5lbC1yYWRpb19faW5wdXQuaXMtY2hlY2tlZCArIC5lbC1yYWRpb19fbGFiZWwgew0KICBjb2xvcjogIzQwOUVGRjsNCn0NCg0KLyog5b+F5aGr5qCH6K+G56ym5qC35byPICovDQoucmVxdWlyZWQtbWFyayB7DQogIGNvbG9yOiAjRjU2QzZDOw0KICBtYXJnaW4tcmlnaHQ6IDRweDsNCiAgZm9udC13ZWlnaHQ6IGJvbGQ7DQp9DQo="}, {"version": 3, "sources": ["punishmentBasis-module.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmYA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "punishmentBasis-module.vue", "sourceRoot": "src/views/suppPunishment", "sourcesContent": ["<template>\r\n  <el-dialog\r\n    title=\"处罚依据选择\"\r\n    :visible.sync=\"visible\"\r\n    width=\"500px\"\r\n    top=\"5vh\"\r\n    append-to-body\r\n    @close=\"handleClose\"\r\n    :close-on-click-modal=\"false\"\r\n    custom-class=\"basis-dialog\"\r\n  >\r\n    <div class=\"basis-dialog-content\">\r\n      <!-- 处罚依据选项 -->\r\n      <div class=\"basis-options\">\r\n        <h4 class=\"section-title\"><span class=\"required-mark\">*</span>选择依据类型：</h4>\r\n        <el-radio-group v-model=\"selectedBasisType\" @change=\"handleBasisTypeChange\">\r\n          <!-- 质量异议单号 -->\r\n          <div class=\"basis-item\">\r\n            <div class=\"basis-row\">\r\n              <div class=\"radio-wrapper\">\r\n                <el-radio label=\"quality\" @change=\"handleBasisTypeChange\">质量异议单号</el-radio>\r\n              </div>\r\n              <div class=\"input-wrapper\">\r\n                <el-input\r\n                  v-model=\"qualityNumber\"\r\n                  placeholder=\"请输入质量异议单号\"\r\n                  class=\"aligned-input\"\r\n                  @input=\"updateBasisText\"\r\n                  @focus=\"checkQuality\"\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 制度名称 -->\r\n          <div class=\"basis-item\">\r\n            <div class=\"basis-row\">\r\n              <div class=\"radio-wrapper\">\r\n                <el-radio label=\"system\" @change=\"handleBasisTypeChange\">制度名称</el-radio>\r\n              </div>\r\n              <div class=\"input-wrapper\">\r\n                <el-input\r\n                  v-model=\"systemName\"\r\n                  placeholder=\"请输入制度名称\"\r\n                  class=\"aligned-input\"\r\n                  @input=\"updateBasisText\"\r\n                  @focus=\"checkSystem\"\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 文件报批单号 -->\r\n          <div class=\"basis-item\">\r\n            <div class=\"basis-row\">\r\n              <div class=\"radio-wrapper\">\r\n                <el-radio label=\"report\" @change=\"handleBasisTypeChange\">文件报批单号</el-radio>\r\n              </div>\r\n              <div class=\"input-wrapper\">\r\n                <el-input\r\n                  v-model=\"reportName\"\r\n                  placeholder=\"请输入文件报批单号\"\r\n                  class=\"aligned-input\"\r\n                  @input=\"updateBasisText\"\r\n                  @focus=\"checkReport\"\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 巡检处罚单号 -->\r\n          <div class=\"basis-item\">\r\n            <div class=\"basis-row\">\r\n              <div class=\"radio-wrapper\">\r\n                <el-radio label=\"inspection\" @change=\"handleBasisTypeChange\">巡检处罚单号</el-radio>\r\n              </div>\r\n              <div class=\"input-wrapper\">\r\n                <el-input\r\n                  v-model=\"inspectionNumber\"\r\n                  placeholder=\"请输入巡检处罚单号\"\r\n                  class=\"aligned-input\"\r\n                  @input=\"updateBasisText\"\r\n                  @focus=\"checkInspection\"\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 安管处罚单号 -->\r\n          <div class=\"basis-item\">\r\n            <div class=\"basis-row\">\r\n              <div class=\"radio-wrapper\">\r\n                <el-radio label=\"safety\" @change=\"handleBasisTypeChange\">安管处罚单号</el-radio>\r\n              </div>\r\n              <div class=\"input-wrapper\">\r\n                <el-input\r\n                  v-model=\"safetyNumber\"\r\n                  placeholder=\"请输入安管处罚单号\"\r\n                  class=\"aligned-input\"\r\n                  @input=\"updateBasisText\"\r\n                  @focus=\"checkSafety\"\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-radio-group>\r\n      </div>\r\n\r\n      <!-- 依据内容 -->\r\n      <div class=\"basis-content\">\r\n        <h4 class=\"section-title\"><span class=\"required-mark\">*</span>依据内容：</h4>\r\n        <div class=\"content-wrapper\">\r\n          <el-input\r\n            v-model=\"basisContent\"\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n            placeholder=\"请输入依据内容\"\r\n            @input=\"updateBasisText\"\r\n            class=\"content-textarea\"\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n    </div>\r\n\r\n    <!-- 底部按钮 -->\r\n    <div slot=\"footer\" class=\"dialog-footer\">\r\n      <div class=\"footer-buttons\">\r\n        <el-button type=\"primary\" @click=\"handleConfirm\">确 定</el-button>\r\n        <el-button @click=\"handleClose\">取 消</el-button>\r\n      </div>\r\n    </div>\r\n  </el-dialog>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: \"PunishmentBasisDialog\",\r\n  data() {\r\n    return {\r\n      // 是否显示弹出层\r\n      visible: false,\r\n      // 选中的依据类型\r\n      selectedBasisType: '',\r\n      // 质量异议单号\r\n      qualityNumber: '',\r\n      // 制度名称\r\n      systemName: '',\r\n      // 报告名称\r\n      reportName: '',\r\n      // 巡检处罚单号\r\n      inspectionNumber: '',\r\n      // 安管处罚单号\r\n      safetyNumber: '',\r\n      // 依据内容\r\n      basisContent: '',\r\n      // 预览文本\r\n      previewText: ''\r\n    };\r\n  },\r\n  methods: {\r\n    /** 显示弹窗 */\r\n    show(currentValue = '') {\r\n      this.visible = true;\r\n      this.parseCurrentValue(currentValue);\r\n      this.updatePreview();\r\n      // 确保弹窗完全打开后再进行其他操作\r\n      this.$nextTick(() => {\r\n        console.log('弹窗已显示，当前数据：', {\r\n          selectedBasisType: this.selectedBasisType,\r\n          qualityNumber: this.qualityNumber,\r\n          systemName: this.systemName,\r\n          reportName: this.reportName,\r\n          inspectionNumber: this.inspectionNumber,\r\n          safetyNumber: this.safetyNumber,\r\n          basisContent: this.basisContent\r\n        });\r\n      });\r\n    },\r\n    \r\n    /** 隐藏弹窗 */\r\n    hide() {\r\n      this.visible = false;\r\n    },\r\n    \r\n    /** 关闭弹窗 */\r\n    handleClose() {\r\n      this.visible = false;\r\n      this.reset();\r\n    },\r\n    \r\n    /** 重置数据 */\r\n    reset() {\r\n      this.selectedBasisType = '';\r\n      this.qualityNumber = '';\r\n      this.systemName = '';\r\n      this.reportName = '';\r\n      this.inspectionNumber = '';\r\n      this.safetyNumber = '';\r\n      this.basisContent = '';\r\n      this.previewText = '';\r\n    },\r\n    \r\n    /** 解析当前值 */\r\n    parseCurrentValue(value) {\r\n      if (!value) {\r\n        this.reset();\r\n        return;\r\n      }\r\n      \r\n      // 尝试解析现有的依据内容\r\n      if (value.includes('质量异议单号：')) {\r\n        this.selectedBasisType = 'quality';\r\n        const match = value.match(/质量异议单号：([^；\\n]*)/);\r\n        if (match) {\r\n          this.qualityNumber = match[1].trim();\r\n        }\r\n      } else if (value.includes('制度名称：')) {\r\n        this.selectedBasisType = 'system';\r\n        const match = value.match(/制度名称：([^；\\n]*)/);\r\n        if (match) {\r\n          this.systemName = match[1].trim();\r\n        }\r\n      } else if (value.includes('报告：')) {\r\n        this.selectedBasisType = 'report';\r\n        const match = value.match(/报告：([^；\\n]*)/);\r\n        if (match) {\r\n          this.reportName = match[1].trim();\r\n        }\r\n      } else if (value.includes('巡检处罚单号：')) {\r\n        this.selectedBasisType = 'inspection';\r\n        const match = value.match(/巡检处罚单号：([^；\\n]*)/);\r\n        if (match) {\r\n          this.inspectionNumber = match[1].trim();\r\n        }\r\n      } else if (value.includes('安管处罚单号：')) {\r\n        this.selectedBasisType = 'safety';\r\n        const match = value.match(/安管处罚单号：([^；\\n]*)/);\r\n        if (match) {\r\n          this.safetyNumber = match[1].trim();\r\n        }\r\n      }\r\n      \r\n      // 解析依据内容\r\n      const contentMatch = value.match(/依据内容：([^]*)/);\r\n      if (contentMatch) {\r\n        this.basisContent = contentMatch[1].trim();\r\n      } else {\r\n        // 如果没有找到依据内容标识，将整个内容作为依据内容\r\n        this.basisContent = value;\r\n      }\r\n    },\r\n    \r\n    /** 依据类型变化 */\r\n    handleBasisTypeChange() {\r\n      this.updatePreview();\r\n    },\r\n    \r\n    /** 更新依据文本 */\r\n    updateBasisText() {\r\n      this.updatePreview();\r\n    },\r\n\r\n    /** 点击质量异议单号输入框时自动选中 */\r\n    checkQuality() {\r\n      if (this.selectedBasisType !== 'quality') {\r\n        this.selectedBasisType = 'quality';\r\n        this.updatePreview();\r\n      }\r\n    },\r\n\r\n    /** 点击制度名称输入框时自动选中 */\r\n    checkSystem() {\r\n      if (this.selectedBasisType !== 'system') {\r\n        this.selectedBasisType = 'system';\r\n        this.updatePreview();\r\n      }\r\n    },\r\n\r\n    /** 点击报告输入框时自动选中 */\r\n    checkReport() {\r\n      if (this.selectedBasisType !== 'report') {\r\n        this.selectedBasisType = 'report';\r\n        this.updatePreview();\r\n      }\r\n    },\r\n\r\n    /** 点击巡检处罚单号输入框时自动选中 */\r\n    checkInspection() {\r\n      if (this.selectedBasisType !== 'inspection') {\r\n        this.selectedBasisType = 'inspection';\r\n        this.updatePreview();\r\n      }\r\n    },\r\n\r\n    /** 点击安管处罚单号输入框时自动选中 */\r\n    checkSafety() {\r\n      if (this.selectedBasisType !== 'safety') {\r\n        this.selectedBasisType = 'safety';\r\n        this.updatePreview();\r\n      }\r\n    },\r\n    \r\n    /** 更新预览 */\r\n    updatePreview() {\r\n      const parts = [];\r\n\r\n      // 添加选中的依据类型信息\r\n      if (this.selectedBasisType === 'quality' && this.qualityNumber) {\r\n        parts.push(`质量异议单号：${this.qualityNumber}`);\r\n      } else if (this.selectedBasisType === 'system' && this.systemName) {\r\n        parts.push(`制度名称：${this.systemName}`);\r\n      } else if (this.selectedBasisType === 'report' && this.reportName) {\r\n        parts.push(`报告：${this.reportName}`);\r\n      } else if (this.selectedBasisType === 'inspection' && this.inspectionNumber) {\r\n        parts.push(`巡检处罚单号：${this.inspectionNumber}`);\r\n      } else if (this.selectedBasisType === 'safety' && this.safetyNumber) {\r\n        parts.push(`安管处罚单号：${this.safetyNumber}`);\r\n      }\r\n\r\n      // 添加依据内容\r\n      if (this.basisContent) {\r\n        parts.push(`依据内容：${this.basisContent}`);\r\n      }\r\n\r\n      this.previewText = parts.join('；');\r\n\r\n      console.log('预览更新：', {\r\n        selectedBasisType: this.selectedBasisType,\r\n        qualityNumber: this.qualityNumber,\r\n        systemName: this.systemName,\r\n        reportName: this.reportName,\r\n        inspectionNumber: this.inspectionNumber,\r\n        safetyNumber: this.safetyNumber,\r\n        basisContent: this.basisContent,\r\n        previewText: this.previewText\r\n      });\r\n    },\r\n    \r\n    /** 确认选择 */\r\n    handleConfirm() {\r\n      this.updatePreview();\r\n\r\n      // 验证是否填写了必要信息\r\n      if (!this.selectedBasisType) {\r\n        this.$message.warning('请选择依据类型');\r\n        return;\r\n      }\r\n\r\n      if (this.selectedBasisType === 'quality' && !this.qualityNumber) {\r\n        this.$message.warning('请输入质量异议单号');\r\n        return;\r\n      }\r\n\r\n      if (this.selectedBasisType === 'system' && !this.systemName) {\r\n        this.$message.warning('请输入制度名称');\r\n        return;\r\n      }\r\n\r\n      if (this.selectedBasisType === 'report' && !this.reportName) {\r\n        this.$message.warning('请输入文件报批单号');\r\n        return;\r\n      }\r\n\r\n      if (this.selectedBasisType === 'inspection' && !this.inspectionNumber) {\r\n        this.$message.warning('请输入巡检处罚单号');\r\n        return;\r\n      }\r\n\r\n      if (this.selectedBasisType === 'safety' && !this.safetyNumber) {\r\n        this.$message.warning('请输入安管处罚单号');\r\n        return;\r\n      }\r\n\r\n      if (!this.basisContent) {\r\n        this.$message.warning('请输入依据内容');\r\n        return;\r\n      }\r\n\r\n      this.$emit('select', this.previewText);\r\n      this.handleClose();\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n/* 弹窗内容容器 */\r\n.basis-dialog-content {\r\n  padding: 10px 0;\r\n}\r\n\r\n/* 章节标题样式 */\r\n.section-title {\r\n  font-size: 16px;\r\n  color: #303133;\r\n  margin: 0 0 15px 0;\r\n  font-weight: 600;\r\n}\r\n\r\n/* 顶级标题样式（选择依据类型） */\r\n.basis-options .section-title {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n/* 处罚依据选项样式 */\r\n.basis-options {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.basis-item {\r\n  margin-bottom: 15px;\r\n  padding: 0;\r\n}\r\n\r\n/* 新的行布局 */\r\n.basis-row {\r\n  display: flex;\r\n  align-items: center;\r\n  width: 100%;\r\n  min-height: 36px;\r\n}\r\n\r\n.radio-wrapper {\r\n  width: 120px;\r\n  flex-shrink: 0;\r\n  display: flex;\r\n  align-items: center;\r\n  height: 36px;\r\n}\r\n\r\n.input-wrapper {\r\n  width: calc(100% - 135px);\r\n  margin-left: 14px;\r\n}\r\n\r\n.aligned-input {\r\n  width: 100%;\r\n}\r\n\r\n.aligned-input ::v-deep .el-input__inner {\r\n  height: 36px;\r\n  line-height: 36px;\r\n  border-radius: 4px;\r\n  font-size: 14px;\r\n}\r\n\r\n/* 单选框对齐样式 */\r\n.radio-wrapper ::v-deep .el-radio {\r\n  height: 36px;\r\n  display: flex;\r\n  align-items: center;\r\n  margin: 0;\r\n}\r\n\r\n.radio-wrapper ::v-deep .el-radio__input {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-right: 8px;\r\n}\r\n\r\n.radio-wrapper ::v-deep .el-radio__inner {\r\n  width: 16px;\r\n  height: 16px;\r\n}\r\n\r\n.radio-wrapper ::v-deep .el-radio__label {\r\n  font-size: 14px;\r\n  line-height: 36px;\r\n  padding-left: 8px;\r\n  color: #606266;\r\n}\r\n\r\n/* 依据内容区域样式 */\r\n.basis-content {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.basis-content .section-title {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.content-wrapper {\r\n  padding: 0;\r\n}\r\n\r\n.content-textarea {\r\n  width: 100%;\r\n}\r\n\r\n.content-textarea ::v-deep .el-textarea__inner {\r\n  border-radius: 4px;\r\n  font-family: inherit;\r\n  font-size: 14px;\r\n  line-height: 1.5;\r\n  min-height: 120px;\r\n}\r\n\r\n/* 预览区域样式 */\r\n.preview-area {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.preview-area .section-title {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.preview-wrapper {\r\n  padding: 0;\r\n}\r\n\r\n.preview-textarea {\r\n  width: 100%;\r\n}\r\n\r\n.preview-textarea ::v-deep .el-textarea__inner {\r\n  border-radius: 4px;\r\n  background-color: #ffffff;\r\n  font-family: inherit;\r\n  font-size: 14px;\r\n  line-height: 1.5;\r\n  min-height: 120px;\r\n}\r\n\r\n/* 弹窗标题居中 */\r\n::v-deep .el-dialog__header {\r\n  text-align: center;\r\n  background: #f8f9fa;\r\n  border-bottom: 1px solid #e9ecef;\r\n}\r\n\r\n::v-deep .el-dialog__title {\r\n  text-align: center;\r\n  width: 100%;\r\n  display: block;\r\n  font-weight: 600;\r\n  color: #303133;\r\n}\r\n\r\n/* 专门的弹窗样式 */\r\n::v-deep .basis-dialog {\r\n  margin-top: 5vh !important;\r\n}\r\n\r\n::v-deep .basis-dialog .el-dialog__body {\r\n  padding: 25px;\r\n  max-height: 75vh;\r\n  overflow-y: auto;\r\n  background-color: #ffffff;\r\n}\r\n\r\n::v-deep .basis-dialog .el-dialog__header {\r\n  padding: 20px 25px 15px;\r\n}\r\n\r\n::v-deep .basis-dialog .el-dialog__footer {\r\n  padding: 15px 25px 20px;\r\n  text-align: right;\r\n}\r\n\r\n/* 底部按钮样式 */\r\n.footer-buttons {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  gap: 10px;\r\n}\r\n\r\n/* 单选框组样式 */\r\n::v-deep .el-radio-group {\r\n  width: 100%;\r\n}\r\n\r\n::v-deep .el-radio {\r\n  margin-right: 0;\r\n  margin-bottom: 0;\r\n}\r\n\r\n::v-deep .el-radio__label {\r\n  display: flex;\r\n  align-items: center;\r\n  width: 100%;\r\n  font-size: 14px;\r\n  color: #606266;\r\n}\r\n\r\n::v-deep .el-radio__input.is-checked + .el-radio__label {\r\n  color: #409EFF;\r\n}\r\n\r\n/* 必填标识符样式 */\r\n.required-mark {\r\n  color: #F56C6C;\r\n  margin-right: 4px;\r\n  font-weight: bold;\r\n}\r\n</style>\r\n"]}]}