<template>
  <div class="app-container">
    <el-alert title="提示：若无法找到适合的物资编码，请联系业务部门人员进行物资新增，并绑定合同！" type="success" effect="dark"></el-alert>
    <br>
    <el-alert title="提示：供货清单仅允许选择“X”结尾代表新件的物资编码，若需配送返修件，请前往返修单页面申请" type="success" effect="dark">
    </el-alert>
    <br>
    <!-- <div class="transition-box">

        <div>
            <strong>提示：若无法找到适合的物资编码，请联系业务部门人员进行物资新增，并绑定合同！<br>
                提示：供货清单仅允许选择“X”结尾代表新件的物资编码，若需配送返修件，请前往返修单页面申请<br>
                提示：尊敬的供应商们，根据管理需要，特此通知：自3月11日0点至3月17日24点期间，请注意对计量类物资不进行过磅操作。请将送货单交至物管处后前往分厂卸货。从3月18日0点开始，将恢复原有送货流程。请您通知相关司机人员遵守上述安排。感谢您的配合与理解！
            </strong>
        </div>

    </div> -->

    <el-form ref="form" :model="form" :rules="rules" label-width="120px">
      <div style="width:100%">
        <!-- <el-form-item label="运输车牌号：" prop="carNum">
            <el-input style="width:220px" v-model="form.carNum" clearable></el-input>
        </el-form-item> -->

        <el-form-item label="货车司机" prop="driverId" :rules="[{ required: true, message: '司机信息不能为空' }]">
          <el-select style="width:300px" v-model="form.driverId" filterable :filter-method="filterDriverData"
            placeholder="请选择（如果显示不出请在输入框搜索）" @change="handleDriverChange">
            <el-option v-for="item in filteredDriverOptions.slice(0, 50)" :key="item.id" :label="item.driverInfo"
              :value="item.id">
            </el-option>
          </el-select>

          <el-button style="margin-left: 10px; font-size: 14px; padding: 5px 10px;" type="primary"
            @click="openNewDriverWindow">前往新增或修改司机信息
          </el-button>
        </el-form-item>
        <el-form-item class="custom-form-label" label="提醒：">
          <span style="color:#0052cc;">应海关高认要求，入厂货车必须提供货车司机具体信息</span>
        </el-form-item>


        <el-form-item label="司机名称" prop="name" v-if="form.name != null">
          <el-input v-model="form.name" placeholder="请输入司机名称" disabled style="width:300px" />
        </el-form-item>
        <el-form-item label="手机号" prop="phone" v-if="form.phone != null">
          <el-input v-model="form.phone" placeholder="请输入手机号" disabled style="width:300px" />
        </el-form-item>
        <el-form-item label="身份证号" prop="idCard" v-if="form.idCard != null">
          <el-input v-model="form.idCard" placeholder="请输入身份证号" disabled style="width:300px" />
        </el-form-item>

        <el-form-item label="人脸照片" prop="faceImg" v-if="form.photo != null && form.photo != ''">
          <div class="image-grid">
            <!-- <el-image v-for="(image, index) in form.faceImgList" :key="index" :src="image" fit="cover"
                      lazy></el-image> -->
            <el-image style="width: 200px; height: 200px" :src="form.photo" fit="cover"></el-image>
          </div>
        </el-form-item>

        <el-form-item label="驾驶证照片" prop="driverLicenseImgs" v-if="form.driverLicenseImgs != null && form.driverLicenseImgs != ''">
          <div class="image-grid">
            <!-- <el-image v-for="(image, index) in form.drivingLicenseImgList" :key="index" :src="image" fit="cover"
              lazy></el-image> -->
            <el-image style="width: 200px; height: 200px" :src="form.driverLicenseImgs" fit="cover"></el-image>
          </div>
        </el-form-item>

        <el-form-item label="行驶证照片" prop="vehicleLicenseImgs" v-if="form.vehicleLicenseImgs != null && form.vehicleLicenseImgs != ''">
          <div class="image-grid">
            <!-- <el-image v-for="(image, index) in form.driverLicenseImgList" :key="index" :src="image" fit="cover"
              lazy></el-image> -->
            <el-image style="width: 200px; height: 200px" :src="form.vehicleLicenseImgs" fit="cover"></el-image>
          </div>
        </el-form-item>

        <el-form-item label="货车" prop="carId" :rules="[{ required: true, message: '货车信息不能为空' }]">
          <el-select style="width:300px" v-model="form.carId" filterable :filter-method="filterCarData"
            placeholder="请选择（如果显示不出请在输入框搜索）" @change="handleCarChange">
            <el-option v-for="item in filteredCarOptions.slice(0, 50)" :key="item.id" :label="item.carNumber"
              :value="item.id">
            </el-option>
          </el-select>

          <el-button style="margin-left: 10px; font-size: 14px; padding: 5px 10px;" type="primary"
            @click="openNewCarWindow">前往新增或修改货车信息
          </el-button>
        </el-form-item>

        <el-form-item label="车牌号" prop="carNum" v-if="form.carNumber != null">
          <el-input v-model="form.carNumber" placeholder="请输入车牌号" disabled style="width:300px" />
        </el-form-item>
        <el-form-item label="车辆排放标准" prop="vehicleEmissionStandards" v-if="form.vehicleEmissionStandards != null">
          <!-- <el-input v-model="form.vehicleEmissionStandards" placeholder="请选择车辆排放标准" disabled>
          </el-input> -->


          <el-select v-model="form.vehicleEmissionStandards" placeholder="请选择车辆排放标准" disabled style="width:300px">
            <el-option v-for="dict in vehicleEmissionStandardsOptions" :key="dict.dictValue" :label="dict.dictLabel"
              :value="dict.dictValue"></el-option>
          </el-select>
        </el-form-item>


        <el-form-item label="供货时间：" prop="supplyTime">
          <el-date-picker value-format="yyyy-MM-dd" v-model="form.supplyTime" type="date" placeholder="选择日期"
            style="width:300px">
          </el-date-picker>
        </el-form-item>

        <el-form-item class="custom-form-label" label="提醒：">
          <span style="color:#0052cc;">供货时间开始三天内，货车准许入厂，请认真填写供货时间</span>
        </el-form-item>

        <el-form-item label="是否计量：" prop="measureFlag">
          <el-radio-group @input="measureChange" v-model="form.measureFlag">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="物资信息：">
          <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
              <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAddContract">选择物资
              </el-button>
            </el-col>
          </el-row>
          <el-card class="box-card contractCard" v-for="(contract, index) in contractList" :key="index">
            <div slot="header" class="clearfix">
              <span>合同编号：{{ contract.contractNo }}</span>
            </div>
            <!-- <div v-for="item in contract.itemList" :key="item.itemNo" class="text item">
                    <el-row>
                        <el-col>
                            <div style="padding: 6px 0;">
                                {{ `${item.itemName}（编号：${item.itemNo}，规格：${item.itemSpec})` }}
                                <el-button icon="el-icon-delete" circle type="danger" size="mini" @click="handleItemDel(contract,item)"></el-button>
                            </div>
                        </el-col>
                        <el-col>
                            <el-form ref="itemform" :model="item">
                                <el-form-item v-if="!form.measureFlag" :rules="[{ required: true, message: '数量不能为空'}]"
                                label="数量：" label-width="100px" prop="amount">
                                    <el-input v-model.number="item.amount" style="width:220px"
                    type="number" clearable></el-input> {{ item.measureUnit }}
                                </el-form-item>
                                <el-form-item v-if="form.measureFlag" :rules="[{ required: true, message: '数量不能为空'}]"
                                label="数量：" label-width="100px" prop="amount">
                                    <el-input v-model.number="item.amount" style="width:220px"
                    type="number" clearable></el-input> 件
                                </el-form-item>
                                <el-form-item v-if="form.measureFlag" style="margin-top: 10px;" :rules="[{ required: true, message: '数量不能为空'}]"
                                label="净重：" label-width="100px" prop="amount">
                                    <el-input v-model.number="item.supplyWeight" style="width:220px"
                    type="number" clearable></el-input> {{ item.measureUnit }}
                                </el-form-item>
                            </el-form>
                        </el-col>
                    </el-row>
                </div> -->
            <el-table :data="contract.itemList" :header-cell-style="itemHeaderStyle" style="width: 100%">
              <el-table-column prop="itemNo" label="物资编码" min-width="150">
              </el-table-column>
              <el-table-column prop="itemName" label="物资名称" min-width="200">
              </el-table-column>
              <el-table-column label="规格" min-width="150">
                <template slot-scope="scopes">
                  {{
                    scopes.row.itemSpec == null ?
                      `无(分厂:${scopes.row.factoryDesc},产线:${scopes.row.productionLineDesc})` :
                      `${scopes.row.itemSpec}(分厂:${scopes.row.factoryDesc}，产线:${scopes.row.productionLineDesc})`
                  }}
                </template>
              </el-table-column>
              <!-- 非计量 -->
              <el-table-column v-if="!form.measureFlag" label="数量" min-width="150">

                <template slot-scope="scope">
                  <div style="display: flex;">
                    <el-form ref="itemform" :model="scope.row" inline-message>
                      <el-form-item :rules="[{ required: true, message: '数量不能为空' }]" prop="amount">
                        <el-input v-model.number="scope.row.amount" placeholder="请输入数量" type="number" />
                      </el-form-item>
                    </el-form>
                    <span style="line-height: 36px;padding-left: 5px;">{{
                      scope.row.measureUnit
                    }}</span>
                  </div>
                </template>
              </el-table-column>
              <!-- 计量 -->
              <el-table-column v-if="form.measureFlag" label="数量" min-width="100">

                <template slot-scope="scope">
                  <div style="display: flex;">
                    <el-form ref="itemform" :model="scope.row" inline-message>
                      <el-form-item :rules="[{ required: true, message: '数量不能为空' }]" prop="amount">
                        <el-input v-model.number="scope.row.amount" placeholder="请输入数量" type="number" />
                      </el-form-item>
                    </el-form>
                    <span style="line-height: 36px;padding-left: 5px;">件</span>
                  </div>
                </template>
              </el-table-column>
              <el-table-column v-if="form.measureFlag" label="净重" min-width="100">

                <template slot-scope="scope">
                  <div style="display: flex;">
                    <el-form ref="itemform" :model="scope.row" inline-message>
                      <el-form-item :rules="[{ required: true, message: '净重不能为空' }]" prop="supplyWeight">
                        <el-input v-model.number="scope.row.supplyWeight" placeholder="请输入重量" type="number" />
                      </el-form-item>
                    </el-form>
                    <span style="line-height: 36px;padding-left: 5px;">{{
                      scope.row.measureUnit
                    }}</span>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="100">

                <template slot-scope="scope">
                  <el-button type="text" size="small" @click="handleItemDel(contract, scope.row.itemNo)">删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </el-form-item>
        <!-- <el-button v-if="contractList.length > 0" type="success" icon="el-icon-check" size="mini" @click="submitSupply">提交清单</el-button> -->
      </div>
    </el-form>

    <!-- 添加或修改对话框 -->
    <el-dialog :title="title" :visible.sync="open" append-to-body fullscreen>
      <div>
        <el-form :model="contractSearchParam" ref="contractSearchForm" :inline="true" label-width="68px">
          <el-form-item label="合同编号" prop="contractNo">
            <el-input v-model="contractSearchParam.contractNo" placeholder="请输入合同编号" clearable size="small"
              @keyup.enter.native="handleContractSearch" />
          </el-form-item>
          <el-form-item>
            <el-button type="cyan" icon="el-icon-search" size="mini" @click="handleContractSearch">搜索
            </el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetContractSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div style="height:634px" class="dialog-box">
        <el-empty v-if="showContractData.length == 0" description="无数据"></el-empty>
        <div v-else class="mybox-card" v-for="(contract, index) in showContractData" :key="index">
          <div class="mybox-header" @click="handleItemOpenClose(contract)">
            <div class="clearfix">
              <span style="font-size:16px;margin-right: 20px;">合同名称：{{ contract.contractName }}（编号：{{
                contract.contractNo
              }}）</span>
              <!-- <el-tag v-if="contract.status == 1" type="success">可用</el-tag>
              <el-tag v-else type="danger">禁用</el-tag> -->
              <i v-if="contract.isOpen" style="float: right; padding: 3px 0" class="el-icon-arrow-up"></i>
              <i v-else class="el-icon-arrow-down" style="float: right; padding: 3px 0"></i>
              <!-- <el-button  v-if="contract.isOpen" style="float: right; padding: 3px 0" type="text"  @click="closeItemSelection(contract)">收起</el-button>
              <el-button v-else style="float: right; padding: 3px 0" type="text" @click="openItemSelection(contract)">展开</el-button> -->
            </div>
          </div>
          <div class="mybox-body" v-if="contract.isOpen">
            <el-form :model="contract.itemSearchParam" :ref="itemRef(contract)" :inline="true" label-width="68px">
              <el-form-item label="物资编号" prop="itemNo">
                <el-input v-model="contract.itemSearchParam.itemNo" placeholder="请输入物资编号" clearable size="small" />
              </el-form-item>
              <el-form-item label="物资名称" prop="itemName">
                <el-input v-model="contract.itemSearchParam.itemName" placeholder="请输入物资名称" clearable size="small" />
              </el-form-item>
              <el-form-item>
                <el-button type="cyan" icon="el-icon-search" size="mini" @click="handleItemSearch(contract)">搜索
                </el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetItemSearch(contract)">重置
                </el-button>
              </el-form-item>
            </el-form>
            <el-table :ref="contract.contractNo" :data="contract.showTableList" :header-cell-style="itemHeaderStyle"
              style="width: 100%" :row-key="getRowKeys" @selection-change="handleSelectionChange($event, contract)">
              <el-table-column type="selection" width="55" :selectable="itemIsSelectable" :reserve-selection="true">
              </el-table-column>
              <el-table-column prop="itemNo" label="物资编码" min-width="150">
              </el-table-column>
              <el-table-column prop="itemName" label="物资名称" min-width="200">
              </el-table-column>
              <el-table-column label="规格" min-width="150">

                <template slot-scope="scopes">
                  {{
                    scopes.row.itemSpec == null ?
                      `无(分厂:${scopes.row.factoryDesc},产线:${scopes.row.productionLineDesc})` :
                      `${scopes.row.itemSpec}(分厂:${scopes.row.factoryDesc},产线:${scopes.row.productionLineDesc})`
                  }}
                </template>
              </el-table-column>
              <!-- <el-table-column
              label="状态">
              <template slot-scope="props">
                  <el-tag v-if="props.row.status == 1" type="success">可用</el-tag>
                  <el-tag v-else type="danger">禁用</el-tag>
              </template>
          </el-table-column> -->
              <!-- 非计量 -->
              <el-table-column v-if="!form.measureFlag" label="数量" min-width="150">

                <template slot-scope="scope">
                  <div style="display: flex;">
                    <el-input v-model="scope.row.amount" placeholder="请输入数量" type="number" />
                    <span style="line-height: 36px;padding-left: 5px;">{{
                      scope.row.measureUnit
                    }}</span>
                  </div>
                </template>
              </el-table-column>
              <!-- 计量 -->
              <el-table-column v-if="form.measureFlag" label="数量" min-width="100">

                <template slot-scope="scope">
                  <div style="display: flex;">
                    <el-input v-model="scope.row.amount" placeholder="请输入数量" type="number" />
                    <span style="line-height: 36px;padding-left: 5px;">件</span>
                  </div>
                </template>
              </el-table-column>
              <el-table-column v-if="form.measureFlag" label="净重" min-width="100">

                <template slot-scope="scope">
                  <div style="display: flex;">
                    <el-input v-model="scope.row.supplyWeight" placeholder="请输入重量" type="number" />
                    <span style="line-height: 36px;padding-left: 5px;">{{
                      scope.row.measureUnit
                    }}</span>
                  </div>
                </template>
              </el-table-column>
            </el-table>

            <el-pagination background small @current-change="handleCurrentChange($event, contract, index)"
              :current-page.sync="contract.currentPage" :page-size="contract.pageSize" layout="total,prev, pager, next"
              :total="contract.total">
            </el-pagination>
            <!-- <el-checkbox-group v-model="contract.checkboxGroup" size="small"> -->
            <!-- <el-checkbox v-for="item in contract.itemList" :key="item.itemNo" :label="item.itemName" @change="checkChange($event,item)" border></el-checkbox> -->
            <!-- </el-checkbox-group> -->
          </div>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitContract">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog title="用户协议" :visible.sync="showDialog" width="60%" :close-on-click-modal="false"
      :close-on-press-escape="false" center :modal="false">
      <div class="dialog-body">
        <p>尊敬的合作伙伴：</p>
        <p>您好！衷心感谢您一直以来对我司的支持，为了强化生产现场管理、保障我司的信息安全，特将有关规定告知如下：</p>
        <ol>
          <li>1、请您在进入我司区域后，包括办公区域、生产区域、研发区域、厂区道路等，<span class="highlight">未经允许，不随意拍照或录像。</span></li>
          <li>2、请您妥善保管工作照或录像，<span class="highlight">不将其随意转发</span>任何无关人员，<span
              class="highlight">不擅自剪辑、传播、上传、发布任何平台。</span>
          </li>
          <li>3、请您在我司许可的指定工作区域内活动，<span class="highlight">不随意走动，全力保护我司的各类信息安全，遵守我司的各项管理规定。</span></li>
          <li>4、请对您公司<span class="highlight">所有派出</span>进入我司区域工作的<span class="highlight">人员进行宣贯，知晓并遵守</span>以上三项规定。</li>
        </ol>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button :disabled="!isAgreeEnabled" @click="onAgreeClick">本人已阅知，并承诺遵守{{ countDown ? '(' + countDown + ')' :
          ''
        }}</el-button>
      </span>
    </el-dialog>
  </div>



</template>

<style scoped>
.mybox-card {
  border-radius: 4px;
  border: 1px solid #ebeef5;
  background-color: #fff;
  overflow: hidden;
  color: #303133;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1);
  margin-bottom: 10px
}

.mybox-header {
  padding: 18px 20px;
  box-sizing: border-box;
}

.mybox-body {
  border-top: 1px solid #ebeef5;
  /* padding: 20px; */
}

.el-dialog__body {
  padding: 10px 30px;
}

.contractCard {
  margin-bottom: 10px
}

.dialog-box {
  overflow: auto;
}

.dialog-box::-webkit-scrollbar {
  display: none;
}

.dialog-footer {
  text-align: center
}

.transition-box {
  grid-auto-columns: 10px;
  width: auto;
  height: auto;
  border-radius: 4px;
  background-color: #CCCCCC;
  text-align: center;
  color: #DC1437;
  padding: 20px 20px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  margin-right: 20px;
  text-align: left;
  margin-bottom: 20px;
}

.image-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 10px;
}

.custom-form-label>>>.el-form-item__label {
  color: #0052cc;
}


.highlight {
  color: red;
  font-weight: bold;
}

.dialog-body p {
  margin-bottom: 10px;
}

.dialog-body ol {
  margin-bottom: 10px;
}

.dialog-body ol li {
  margin-bottom: 5px;
}
</style>

<script>
import { listContract, addBatch } from "@/api/dgcb/supplier/supply";
import { listAllDriver, getXctgDriverUserList, getXctgDriverCarList } from "@/api/dgcb/driver/driver";
import { isNotify, addNotify } from "@/api/truck/notify/notify";
import UploadImage from '@/components/MoreUploadImage';//引用组件

export default {
  components: {
    UploadImage,
  },
  name: "addSupplyInfo",
  props: {
    submitCancel: {
      type: Function,
      default: null
    }
  },
  data() {
    return {
      searchDriverQuery: '',
      filteredDriverOptions: [],

      searchCarQuery: '',
      filteredCarOptions: [],

      itemHeaderStyle: {
        "background-color": "#fff"
      },
      driverList: [],
      carList: [],
      // 物资信息隐藏序列
      openIndex: [],
      // 遮罩层
      loading: true,
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 合同待确认选择项
      contractSelection: [],
      // 已选择合同列表
      contractList: [],
      // 物资多选信息
      selectData: {},
      // 显示物资列表
      // showTableList:[],
      // 源合同数据
      contractData: [],
      // 过滤数据
      showContractData: [],
      // 删除数据
      delItemData: [],
      form: {
        carNum: null,
        supplyTime: null,
        measureFlag: 0,
        status: 1,
        tdgcb05List: []
      },
      // 合同搜索条件
      contractSearchParam: {
        contractNo: ""
      },
      // 表单校验
      rules: {
        /**
         carNum: [
         {
         required: true,
         message: "车牌号不能为空",
         trigger: "blur"
         },
         {
         pattern: /^(([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z](([0-9]{5}[DF])|([DF]([A-HJ-NP-Z0-9])[0-9]{4})))|([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9挂学警港澳使领]))$/,
         message: "车牌号格式不正确"
         }
         ],*/
        supplyTime: [
          {
            required: true,
            message: "供货时间不能为空",
            trigger: "blur"
          }
        ],
      },
      // 测试合同数据
      responseData: [{
        contractNo: '1',
        contractName: '合同名称1',
        validTime: '2016-05-02',
        tdgcb03List: []
      }],
      showDialog: false,
      isAgreeEnabled: false,
      countDown: 5,
      timer: null,
    }
  },

  computed: {
    // 默认显示前50条，若有搜索，则显示搜索后的数据
    displayDriverListOptions() {
      return this.searchDriverQuery ? this.filteredDriverOptions : this.driverList.slice(0, 50);
    },
    displayCarListOptions() {
      return this.searchCarQuery ? this.filteredCarOptions : this.carList.slice(0, 50);
    }
  },

  created() {
    console.log('showDialog:', this.showDialog);
    this.getList();
    this.getDriverList();
    this.getCarList();

    let param = {}
    param.businessType = 1;
    isNotify(param).then(response => {
      if (response.data) { // 假设接口返回的数据为 true 或 false
        this.showDialog = true;
      } else {
        this.showDialog = false;
      }
    }).catch(error => {
      console.error('Failed to call isNotify:', error);
      this.showDialog = false; // 如果接口调用失败，不显示弹框
    });


    this.timer = setInterval(() => {
      if (this.countDown > 0) {
        this.countDown--;
      } else {
        this.isAgreeEnabled = true;
        clearInterval(this.timer);
      }
    }, 1000);
  },
  beforeDestroy() {
    clearInterval(this.timer);
  },
  methods: {
    // 搜索过滤逻辑
    filterDriverData(query) {
      this.searchDriverQuery = query;

      if (this.searchDriverQuery) {

        this.filteredDriverOptions = this.driverList.filter(item =>
          item.driverInfo.includes(query)
        );
      } else {

        this.filteredDriverOptions = this.driverList.slice(0, 50);
      }
    },

    // 搜索过滤逻辑
    filterCarData(query) {
      this.searchCarQuery = query;

      if (this.searchCarQuery) {

        this.filteredCarOptions = this.carList.filter(item =>
          item.carNumber.includes(query)
        );
      } else {

        this.filteredCarOptions = this.carList.slice(0, 50);
      }
    },

    onAgreeClick() {
      if (this.isAgreeEnabled) {
        this.showDialog = false;
        // 在这里可以添加用户同意后的逻辑
        let param = {};
        param.businessType = 1;
        addNotify(param).then(response => {
          // 处理 addNotify 接口成功的逻辑
          console.log('addNotify success:', response);
          // 可以在这里添加其他逻辑，比如提示用户操作成功
        }).catch(error => {
          // 处理 addNotify 接口失败的逻辑
          console.error('addNotify failed:', error);
          // 可以在这里添加其他逻辑，比如提示用户操作失败
        });
      }
    },
    openNewDriverWindow() {
      const newWindowUrl = 'https://ydxt.citicsteel.com:8099/truckManage/xctgDriverUser'; // 替换为实际要跳转的页面 URL
      window.open(newWindowUrl, '_blank'); // 打开新窗口并跳转至指定 URL
    },
    openNewCarWindow() {
      const newWindowUrl = 'https://ydxt.citicsteel.com:8099/truckManage/xctgDriverCar'; // 替换为实际要跳转的页面 URL
      window.open(newWindowUrl, '_blank'); // 打开新窗口并跳转至指定 URL
    },
    /** 查询批次列表 */
    getList() {
      this.loading = true;
      let param = "measureFlagTemp=" + this.form.measureFlag;
      listContract(param).then(response => {
        let responseData = response.data;
        this.handleResponseData(responseData);
      });
      // this.handleResponseData(this.responseData);
      this.loading = false;
    },
    /** 查询司机信息列表 */
    getDriverList() {
      this.loading = true;
      // listAllDriver().then(response => {
      //   this.driverList = response.data;
      //   this.loading = false;
      // });
      getXctgDriverUserList().then(response => {
        this.driverList = response.data;
        this.filteredDriverOptions = this.driverList,
          this.loading = false;
      });
    },

    /** 查询司机信息列表 */
    getCarList() {
      console.log("success");
      this.loading = true;
      // listAllDriver().then(response => {
      //   this.driverList = response.data;
      //   this.loading = false;
      // });
      getXctgDriverCarList().then(response => {
        this.carList = response.data;
        this.filteredCarOptions = this.carList;
        this.loading = false;
      });
    },



    handleDriverChange() {
      //通过driverId获取司机信息
      if (this.form.driverId != null) {
        this.driverList.forEach(item => {
          if (item.id == this.form.driverId) {
            this.form.name = item.name;
            this.form.idCard = item.idCard;
            this.form.company = item.company;
            this.form.phone = item.phone;
            this.form.photo = item.photo;
            this.form.faceImgList = item.faceImgList;
            this.form.driverLicenseImgs = item.driverLicenseImgs;
            this.form.vehicleLicenseImgs = item.vehicleLicenseImgs;

          }
        });
      }
    },

    handleCarChange() {
      console.log("success");
      //通过driverId获取司机信息
      if (this.form.carId != null) {
        this.carList.forEach(item => {
          if (item.id == this.form.carId) {
            this.form.carNumber = item.carNumber;
            if (item.vehicleEmissionStandards == 1) {
              this.form.vehicleEmissionStandards = "国五";
            } else if (item.vehicleEmissionStandards == 2) {
              this.form.vehicleEmissionStandards = "国六";
            } else if (item.vehicleEmissionStandards == 3) {
              this.form.vehicleEmissionStandards = "新能源";
            } else {
              this.form.vehicleEmissionStandards = "";
            }
            // this.form.vehicleEmissionStandards = item.vehicleEmissionStandards;

          }
        });
      }
    },

    //性别转换
    sexFormat(sex) {
      if (sex == 0) {
        return "未知";
      } else if (sex == 1) {
        return "男";
      } else if (sex == 2) {
        return "女";
      } else {
        return "";
      }

    },

    //车辆排放标准转换
    vehicleEmissionStandardsFormat(vehicleEmissionStandards) {
      if (vehicleEmissionStandards == 1) {
        return "国五";
      } else if (vehicleEmissionStandards == 2) {
        return "国六";
      } else if (vehicleEmissionStandards == 3) {
        return "新能源";
      } else {
        return "";
      }
    },

    // 处理请求数据
    handleResponseData(response) {
      this.contractData = [];
      this.showContractData = [];
      response.forEach(data => {
        let contractInfo = this.contractObj();
        contractInfo.contractNo = data.contractNo;
        contractInfo.contractName = data.contractName;
        contractInfo.validTime = data.validTime;
        contractInfo.currentPage = 1;
        contractInfo.pageSize = 10;
        contractInfo.total = data.tdgcb03List.length;
        contractInfo.isOpen = false; // 合同默认收起
        contractInfo.itemSearchParam = { itemNo: "", itemName: "" };
        if (data.tdgcb03List != null) {
          data.tdgcb03List.forEach(item => {
            let itemInfo = this.itemObj();
            itemInfo.contractNo = data.contractNo;
            itemInfo.contractName = data.contractName;
            itemInfo.itemNo = item.itemNo;
            itemInfo.itemName = item.tdgcb01.itemName;
            itemInfo.itemSpec = item.tdgcb01.itemSpec;
            itemInfo.measureUnit = item.tdgcb01.measureUnit;
            itemInfo.factoryDesc = item.tdgcb01.factoryDesc;
            itemInfo.productionLineDesc = item.tdgcb01.productionLineDesc;
            itemInfo.status = item.status;
            contractInfo.itemList.push(itemInfo);
          })
          contractInfo.searchItemList = contractInfo.itemList.concat();
          contractInfo.showTableList = contractInfo.searchItemList.slice(0, contractInfo.pageSize)
        }
        this.contractData.push(contractInfo);
      })
      this.showContractData = this.contractData.concat();
    },
    // 同步选择信息数据
    handleDifferentData() {
      this.contractData.forEach(contract => {
        this.contractList.forEach(c => {
          console.log()
          if (c.contractNo == contract.contractNo) {
            contract.itemList.forEach(item => {
              c.itemList.forEach(i => {
                if (i.itemNo == item.itemNo) {
                  console.log("befor", item)
                  item.amount = i.amount;
                  item.supplyWeight = i.supplyWeight;
                  console.log("after", item)
                }
              })
            })
          }
        })
      })
      this.itemDataChangeFlag = 0;
    },

    contractObj() {
      return {
        status: 1,
        contractNo: null,
        contractName: null,
        validTime: null,
        itemList: []
      }
    },

    itemObj() {
      return {
        contractNo: null,  // 所属合同编号
        contractName: null,   // 所属合同名
        itemNo: null,   // 物资名称
        itemName: null,  // 物资名称
        amount: null,  // 数量
        itemSpec: null,  // 物资规格
        measureFlag: null, // 是否计量
        measureUnit: null,  // 计量单位
        supplyWeight: null //供货重量
      }
    },

    itemRef(contract) {
      return contract.contractNo + "itemRef"
    },

    /** 新增按钮操作 */
    handleAddContract() {
      // this.reset();
      // this.contractSelection = JSON.parse(JSON.stringify(this.contractList));
      // if(this.contractList.length > 0) this.contractSelection = this.contractList.concat();
      if (this.contractList.length > 0) this.handleDifferentData();
      // 处理已选
      this.showContractData.forEach(c => {
        if (c.isOpen) {
          this.toggleSelection(c);
        }
      })
      this.open = true;
      this.title = "选择物资";
    },

    // 计量改变
    measureChange(e) {
      this.form.measureFlag = e;
      let that = this;
      if (this.contractList.length > 0) {
        // 已选择物资
        this.$confirm('改变是否计量将清除当前已选物资，是否继续"?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function () {
          // 确定
          console.log("确定")
          that.contractList = [];
          that.selectData = {};
          that.contractSelection = [];
          that.getList();
        }).catch(action => {
          // 取消
          if (action == "cancel") {
            console.log(action);
            if (this.form.measureFlag == 0) {
              this.form.measureFlag = 1;
            } else {
              this.form.measureFlag = 0;
            }
          }
        })
      } else {
        this.getList();
      }
    },

    // 取消按钮
    cancel() {
      this.open = false;
      // this.reset();
    },

    // 选择合同确定
    submitContract() {
      // console.log(this.contractSelection);
      this.contractList = JSON.parse(JSON.stringify(this.contractSelection));
      // this.contractList = this.contractSelection.concat();
      // console.log(this.contractList);
      this.handleEmptyContract();
      this.open = false;
    },
    // 展开合同
    openItemSelection(contract) {
      contract.isOpen = true;
      this.toggleSelection(contract);
    },
    // 收起合同
    closeItemSelection(contract) {
      contract.isOpen = false;
    },
    // 处理合同展开收起
    handleItemOpenClose(contract) {
      if (!contract.status) return;
      if (contract.isOpen) {
        this.closeItemSelection(contract);
      } else {
        this.openItemSelection(contract);
      }
    },

    // 判断物资是否可选
    itemIsSelectable(row, index) {
      if (row.status) {
        console.log("是");
        return true;
      } else {
        return false;
      }
    },

    // 提交清单
    submitSupply() {
      if (this.contractList.length == 0) {
        this.msgError("请添加物资");
        return;
      }
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.handleSubmitData()) {
            // console.log(this.form);
            if (this.form.measureFlag == 1) this.form.status = 11;
            addBatch(this.form).then(response => {
              // console.log(response);
              if (response.code == 200) {
                this.msgSuccess("添加成功");
                this.submitCancel();
              }
            })
          }
          ;
        }
      })
    },

    // 处理提交数据
    handleSubmitData() {
      let paramList = [];
      let v = true;
      this.$refs["itemform"].forEach(f => {
        f.validate(valid => {
          if (!valid) {
            v = false
          }
          ; //数量验证不通过
        })
      })
      if (v) {
        this.contractList.forEach((contract, index) => {
          contract.itemList.forEach(item => {
            paramList.push(item);
          })
        })
      }
      this.form.tdgcb05List = paramList;
      return v;
    },

    // 物资选择改变事件
    handleSelectionChange(e, data) {
      let no = data.contractNo;
      if (!this.selectData[`${no}`]) {
        this.$set(this.selectData, `${no}`, []);
      }
      this.selectData[`${no}`] = e;
      let flag = false; // 表示合同还未添加
      this.contractSelection.forEach(contract => {
        if (contract.contractNo == data.contractNo) {
          flag = true;
          // 已有合同则添加物资信息
          // contract.itemList = this.selectData[`${no}`];
          contract.itemList = e;
        }
      })
      if (!flag) {
        // 合同未添加则新增合同
        let contractInfo = this.contractObj();
        contractInfo.contractNo = data.contractNo;
        contractInfo.contractName = data.contractName;
        contractInfo.validTime = data.validTime;
        contractInfo.measureFlag = data.measureFlag;
        // contractInfo.itemList = this.selectData[`${no}`];
        contractInfo.itemList = e;
        this.contractSelection.push(contractInfo);
      }
    },

    // 当前页选择
    toggleSelection(contract) {
      const no = contract.contractNo;
      const rows = this.selectData[`${no}`];
      console.log(rows)
      this.$nextTick(() => {
        if (rows) {
          rows.forEach(row => {
            if (this.delItemData.includes(row)) {
              this.$refs[`${no}`][0].toggleRowSelection(row, false);
              row.amount = null;
              row.supplyWeight = null;
              this.delItemData.splice(this.delItemData.indexOf(row), 1);
            } else {
              this.$refs[`${no}`][0].toggleRowSelection(row, true);
            }
          });
        } else {
          this.$refs[`${no}`][0].clearSelection();
        }
      })
      console.log("del", this.delItemData)
    },

    // 物资列表当前页改变
    handleCurrentChange(currentPage, contract) {
      contract.showTableList = contract.searchItemList.slice(contract.pageSize * (currentPage - 1), contract.pageSize * (currentPage - 1) + contract.pageSize);
    },
    // 合同搜索
    handleContractSearch() {
      let newlist = []
      if (this.contractSearchParam.contractNo == "") {
        this.showContractData = this.contractData.concat();
      } else {
        this.contractData.forEach(contract => {
          if (contract.contractNo.includes(this.contractSearchParam.contractNo)) {
            newlist.push(contract);
          }
        })
        this.showContractData = newlist;
      }
      // 处理已选
      this.showContractData.forEach(c => {
        if (c.isOpen) {
          this.toggleSelection(c);
        }
      })
    },
    /** 重置按钮操作 */
    resetContractSearch() {
      this.resetForm("contractSearchForm");
      this.handleContractSearch();
    },

    //物资搜索
    handleItemSearch(contract) {
      const itemSearchParam = contract.itemSearchParam;
      let newItemList = [];
      if (itemSearchParam.itemName == "" && itemSearchParam.itemNo == "") {
        newItemList = contract.itemList;
      } else {
        contract.itemList.forEach(item => {
          if (item.itemName.includes(itemSearchParam.itemName) && item.itemNo.includes(itemSearchParam.itemNo)) {
            newItemList.push(item);
          }
        })
      }
      contract.searchItemList = newItemList;
      contract.total = newItemList.length;
      contract.currentPage = 1;
      contract.showTableList = contract.searchItemList.slice(0, contract.pageSize);
      // this.toggleSelection(contract);
    },
    // 物资重置
    resetItemSearch(contract) {
      const itemRef = this.itemRef(contract);
      this.$refs[`${itemRef}`][0].resetFields();
      this.handleItemSearch(contract);
    },

    //  获取行键
    getRowKeys(row) {
      return row.itemNo;
    },

    // 物资删除点击事件
    handleItemDel(contract, itemNo) {
      // 物资列表中删除
      this.contractList.forEach(c => {
        if (c.contractNo == contract.contractNo) {
          c.itemList.forEach((i, index) => {
            if (i.itemNo == itemNo) {
              c.itemList.splice(index, 1);
            }
          })
        }
      })
      // 已选项中删除
      Object.keys(this.selectData).forEach(no => {
        if (no == contract.contractNo) {
          this.selectData[`${no}`].forEach(i => {
            if (i.itemNo == itemNo) {
              this.delItemData.push(i);
            }
          })
        }
      })
      this.handleEmptyContract();
    },
    // 处理空合同
    handleEmptyContract() {
      this.contractList = this.contractList.filter(c => c.itemList.length != 0)
    }
  },
}
</script>
