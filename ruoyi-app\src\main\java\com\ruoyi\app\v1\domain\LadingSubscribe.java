package com.ruoyi.app.v1.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 提单预约对象 t_lading_subscribe
 *
 * <AUTHOR>
 * @date 2022-03-21
 */
public class LadingSubscribe extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    private Long id;

    /**
     * start
     */
    private Long start;

    /**
     * end
     */
    private Long end;

    public Long getStart() {
        return start;
    }

    public void setStart(Long start) {
        this.start = start;
    }

    public Long getEnd() {
        return end;
    }

    public void setEnd(Long end) {
        this.end = end;
    }

    /**
     * openId
     */
//    @Excel(name = "openId")
    private String openId;

    /**
     * 工号
     */
    private String workNo;

    /**
     * 姓名
     */
    private String workName;

    @Excel(name = "流水号", width = 30)
    private String flowNo;

    /**
     * 提货码
     */
    @Excel(name = "提货码")
    private String delivyNo;

    /**
     * 提单号
     */
    @Excel(name = "提单号")
    private String billOfLadingNo;

    /**
     * 承运公司
     */
//    @Excel(name = "承运公司")
    private String fleetName;

    /**
     * 提货库区码
     */
    private String stockCode;

    @Excel(name = "提单库区")
    /** 提货库区码 */
    private String stockName;

    /**
     * 经度
     */
//    @Excel(name = "经度")
    private BigDecimal longitude;

    /**
     * 纬度
     */
//    @Excel(name = "纬度")
    private BigDecimal latitude;

    /**
     * 位置
     */
    private String location;

    /**
     * code
     */
    private String code;

    /**
     * name
     */
    private String name;

    /**
     * 截止时间(产销)
     */
//    @Excel(name = "截止时间")
    private String planEndTime;

    /**
     * 客户名称
     */
    @Excel(name = "提货单位")
    private String consignUserName;

    /**
     * 承运商名称
     */
    private String vendorName;

    /**
     * 计划量
     */
    @Excel(name = "计划量")
    private String planWt;

    /**
     * 预约车号
     */
    @Excel(name = "预约车号")
    private String vehicleNo;

    @Excel(name = "车牌颜色")
    private String licensePlateColor;

    /**
     * 司机姓名
     */
    @Excel(name = "司机")
    private String driverName;

    /**
     * 身份证/从业资格证
     */
    @Excel(name = "身份证", width = 30)
    private String idCard;

    /**
     * 司机手机号
     */
    @Excel(name = "联系方式")
    private String phoneNo;

    /**
     * 承运单号
     */
    @Excel(name = "承运单号")
    private String carryCompanyName;

    /**
     * 预约时间
     */
//    @Excel(name = "预约时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date delivyTime;

    /**
     * 预约时间string
     */
//    @Excel(name = "预约时间")
    private String delivyTime1;
    /**
     * 承运单号(产销)
     */
//    @Excel(name = "承运单号")
    private String zuBillNo;


    /**
     * 电文发送状态
     */
    private String status;

    /**
     * 服务评分
     */
//    @Excel(name = "服务评分")
    private String rate;

    /**
     * 服务评价
     */
//    @Excel(name = "服务评价")
    private String service;

    /**
     * 提单备注内容
     */
    private String delivyRemark;

    /**
     * 事件号
     */
    private String eventNo;

    /**
     * 事件
     */
    private String eventContent;

    /**
     * 图片
     */
    private String files;

    /**
     * 预计入场开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "入厂时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date predictEntryBeginDate;

    /**
     * 预计入场结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "出厂时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date predictEntryEndDate;

    private String photo;

    private String driverLicenseImgs;

    private String vehicleLicenseImgs;

    private String passStatus;

    private Date entryTime;

    private String billRemark;

    private boolean filterOneMonth;

    /** 查询开始时间 预约单列表使用 */
    private String queryStartTime;

    /** 查询结束时间 预约单列表使用 */
    private String queryEndTime;

    public String getQueryStartTime() {
        return queryStartTime;
    }

    public void setQueryStartTime(String queryStartTime) {
        this.queryStartTime = queryStartTime;
    }

    public String getQueryEndTime() {
        return queryEndTime;
    }

    public void setQueryEndTime(String queryEndTime) {
        this.queryEndTime = queryEndTime;
    }

    public boolean isFilterOneMonth() {
        return filterOneMonth;
    }

    public void setFilterOneMonth(boolean filterOneMonth) {
        this.filterOneMonth = filterOneMonth;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public String getWorkNo() {
        return workNo;
    }

    public void setWorkNo(String workNo) {
        this.workNo = workNo;
    }

    public String getWorkName() {
        return workName;
    }

    public void setWorkName(String workName) {
        this.workName = workName;
    }

    public void setDelivyNo(String delivyNo) {
        this.delivyNo = delivyNo;
    }

    public String getDelivyNo() {
        return delivyNo;
    }

    public void setBillOfLadingNo(String billOfLadingNo) {
        this.billOfLadingNo = billOfLadingNo;
    }

    public String getStockCode() {
        return stockCode;
    }

    public void setStockCode(String stockCode) {
        this.stockCode = stockCode;
    }

    public String getStockName() {
        return stockName;
    }

    public void setStockName(String stockName) {
        this.stockName = stockName;
    }

    public String getPlanWt() {
        return planWt;
    }

    public void setPlanWt(String planWt) {
        this.planWt = planWt;
    }

    public String getPlanEndTime() {
        return planEndTime;
    }

    public void setPlanEndTime(String planEndTime) {
        this.planEndTime = planEndTime;
    }

    public String getVendorName() {
        return vendorName;
    }

    public void setVendorName(String vendorName) {
        this.vendorName = vendorName;
    }

    public String getConsignUserName() {
        return consignUserName;
    }

    public void setConsignUserName(String consignUserName) {
        this.consignUserName = consignUserName;
    }

    public String getZuBillNo() {
        return zuBillNo;
    }

    public void setZuBillNo(String zuBillNo) {
        this.zuBillNo = zuBillNo;
    }

    public String getFleetName() {
        return fleetName;
    }

    public void setFleetName(String fleetName) {
        this.fleetName = fleetName;
    }

    public String getBillOfLadingNo() {
        return billOfLadingNo;
    }

    public void setVehicleNo(String vehicleNo) {
        this.vehicleNo = vehicleNo;
    }

    public String getVehicleNo() {
        return vehicleNo;
    }

    public String getLicensePlateColor() {
        return licensePlateColor;
    }

    public void setLicensePlateColor(String licensePlateColor) {
        this.licensePlateColor = licensePlateColor;
    }

    public void setDriverName(String driverName) {
        this.driverName = driverName;
    }

    public String getDriverName() {
        return driverName;
    }

    public void setPhoneNo(String phoneNo) {
        this.phoneNo = phoneNo;
    }

    public String getPhoneNo() {
        return phoneNo;
    }

    public void setCarryCompanyName(String carryCompanyName) {
        this.carryCompanyName = carryCompanyName;
    }

    public String getCarryCompanyName() {
        return carryCompanyName;
    }

    public String getIdCard() {
        return idCard;
    }

    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }

    public Date getDelivyTime() {
        return delivyTime;
    }

    public void setDelivyTime(Date delivyTime) {
        this.delivyTime = delivyTime;
    }

    public String getDelivyTime1() {
        return delivyTime1;
    }

    public void setDelivyTime1(String delivyTime1) {
        this.delivyTime1 = delivyTime1;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getRate() {
        return rate;
    }

    public void setRate(String rate) {
        this.rate = rate;
    }

    public String getService() {
        return service;
    }

    public void setService(String service) {
        this.service = service;
    }

    public BigDecimal getLongitude() {
        return longitude;
    }

    public void setLongitude(BigDecimal longitude) {
        this.longitude = longitude;
    }

    public BigDecimal getLatitude() {
        return latitude;
    }

    public void setLatitude(BigDecimal latitude) {
        this.latitude = latitude;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDelivyRemark() {
        return delivyRemark;
    }

    public void setDelivyRemark(String delivyRemark) {
        this.delivyRemark = delivyRemark;
    }

    public String getEventNo() {
        return eventNo;
    }

    public void setEventNo(String eventNo) {
        this.eventNo = eventNo;
    }

    public String getEventContent() {
        return eventContent;
    }

    public void setEventContent(String eventContent) {
        this.eventContent = eventContent;
    }

    public String getFiles() {
        return files;
    }

    public void setFiles(String files) {
        this.files = files;
    }


    public String getFlowNo() {
        return flowNo;
    }

    public void setFlowNo(String flowNo) {
        this.flowNo = flowNo;
    }

    public Date getPredictEntryBeginDate() {
        return predictEntryBeginDate;
    }

    public void setPredictEntryBeginDate(Date predictEntryBeginDate) {
        this.predictEntryBeginDate = predictEntryBeginDate;
    }

    public Date getPredictEntryEndDate() {
        return predictEntryEndDate;
    }

    public void setPredictEntryEndDate(Date predictEntryEndDate) {
        this.predictEntryEndDate = predictEntryEndDate;
    }

    public String getPhoto() {
        return photo;
    }

    public void setPhoto(String photo) {
        this.photo = photo;
    }

    public String getDriverLicenseImgs() {
        return driverLicenseImgs;
    }

    public void setDriverLicenseImgs(String driverLicenseImgs) {
        this.driverLicenseImgs = driverLicenseImgs;
    }

    public String getVehicleLicenseImgs() {
        return vehicleLicenseImgs;
    }

    public void setVehicleLicenseImgs(String vehicleLicenseImgs) {
        this.vehicleLicenseImgs = vehicleLicenseImgs;
    }

    public String getPassStatus() {
        return passStatus;
    }

    public void setPassStatus(String passStatus) {
        this.passStatus = passStatus;
    }

    public Date getEntryTime() {
        return entryTime;
    }

    public void setEntryTime(Date entryTime) {
        this.entryTime = entryTime;
    }

    public String getBillRemark() {
        return billRemark;
    }

    public void setBillRemark(String billRemark) {
        this.billRemark = billRemark;
    }

    @Override
    public String toString() {
        final StringBuffer sb = new StringBuffer("LadingSubscribe{");
        sb.append("id=").append(id);
        sb.append(", start=").append(start);
        sb.append(", end=").append(end);
        sb.append(", openId='").append(openId).append('\'');
        sb.append(", workNo='").append(workNo).append('\'');
        sb.append(", workName='").append(workName).append('\'');
        sb.append(", flowNo='").append(flowNo).append('\'');
        sb.append(", delivyNo='").append(delivyNo).append('\'');
        sb.append(", billOfLadingNo='").append(billOfLadingNo).append('\'');
        sb.append(", fleetName='").append(fleetName).append('\'');
        sb.append(", stockCode='").append(stockCode).append('\'');
        sb.append(", stockName='").append(stockName).append('\'');
        sb.append(", longitude=").append(longitude);
        sb.append(", latitude=").append(latitude);
        sb.append(", location='").append(location).append('\'');
        sb.append(", code='").append(code).append('\'');
        sb.append(", name='").append(name).append('\'');
        sb.append(", planEndTime='").append(planEndTime).append('\'');
        sb.append(", consignUserName='").append(consignUserName).append('\'');
        sb.append(", vendorName='").append(vendorName).append('\'');
        sb.append(", planWt='").append(planWt).append('\'');
        sb.append(", vehicleNo='").append(vehicleNo).append('\'');
        sb.append(", licensePlateColor='").append(licensePlateColor).append('\'');
        sb.append(", driverName='").append(driverName).append('\'');
        sb.append(", idCard='").append(idCard).append('\'');
        sb.append(", phoneNo='").append(phoneNo).append('\'');
        sb.append(", carryCompanyName='").append(carryCompanyName).append('\'');
        sb.append(", delivyTime=").append(delivyTime);
        sb.append(", delivyTime1='").append(delivyTime1).append('\'');
        sb.append(", zuBillNo='").append(zuBillNo).append('\'');
        sb.append(", status='").append(status).append('\'');
        sb.append(", rate='").append(rate).append('\'');
        sb.append(", service='").append(service).append('\'');
        sb.append(", delivyRemark='").append(delivyRemark).append('\'');
        sb.append(", eventNo='").append(eventNo).append('\'');
        sb.append(", eventContent='").append(eventContent).append('\'');
        sb.append(", files='").append(files).append('\'');
        sb.append(", predictEntryBeginDate=").append(predictEntryBeginDate);
        sb.append(", predictEntryEndDate=").append(predictEntryEndDate);
        sb.append(", photo='").append(photo).append('\'');
        sb.append(", driverLicenseImgs='").append(driverLicenseImgs).append('\'');
        sb.append(", vehicleLicenseImgs='").append(vehicleLicenseImgs).append('\'');
        sb.append(", passStatus='").append(passStatus).append('\'');
        sb.append(", entryTime=").append(entryTime);
        sb.append('}');
        return sb.toString();
    }
}
