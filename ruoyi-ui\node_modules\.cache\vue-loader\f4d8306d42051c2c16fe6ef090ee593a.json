{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\leave\\plan\\edit.vue?vue&type=style&index=0&id=7971f6a4&scoped=true&lang=css", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\leave\\plan\\edit.vue", "mtime": 1756084866763}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\@vue\\cli-service\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi5hcHAtY29udGFpbmVyIHsNCiAgcGFkZGluZzogMjBweDsNCn0NCi5ib3gtY2FyZCB7DQogIG1hcmdpbi1ib3R0b206IDIwcHg7DQogIGJvcmRlci1yYWRpdXM6IDVweDsNCn0NCi5jYXJkLWhlYWRlciB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCn0NCi5tYXRlcmlhbC1jYXJkIHsNCiAgbWFyZ2luLXRvcDogMjBweDsNCiAgbWFyZ2luLWJvdHRvbTogMjBweDsNCn0NCi5mb3JtLWZvb3RlciB7DQogIG1hcmdpbi10b3A6IDMwcHg7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCn0NCi5tYXRlcmlhbC1idG4tZ3JvdXAgew0KICBmbG9hdDogcmlnaHQ7DQp9DQoubWF0ZXJpYWwtYnRuLWdyb3VwIC5lbC1idXR0b24gew0KICBtYXJnaW4tbGVmdDogOHB4Ow0KICBib3JkZXItcmFkaXVzOiA0cHg7DQp9DQoubWF0ZXJpYWwtYnRuLWdyb3VwIC5lbC1idXR0b246Zmlyc3QtY2hpbGQgew0KICBtYXJnaW4tbGVmdDogMDsNCn0NCi50aXAtdGV4dCB7DQogIGNvbG9yOiAjZjU2YzZjOw0KICBmb250LXNpemU6IDEycHg7DQogIGxpbmUtaGVpZ2h0OiAxLjI7DQogIG1hcmdpbi10b3A6IDVweDsNCn0NCg=="}, {"version": 3, "sources": ["edit.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqyCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "edit.vue", "sourceRoot": "src/views/leave/plan", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-card class=\"box-card\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <h3>{{ isEdit ? '修改申请' : '新增申请' }}</h3>\r\n      </div>\r\n\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"120px\">\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"计划类型\" prop=\"planType\">\r\n              <el-radio-group v-model=\"form.planType\" @change=\"handlePlanTypeChange\">\r\n                <el-radio :label=\"1\">出厂不返回</el-radio>\r\n                <el-radio :label=\"2\">出厂返回</el-radio>\r\n                <el-radio :label=\"3\">跨区调拨</el-radio>\r\n                <el-radio :label=\"4\">退货申请</el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n          </el-col>\r\n\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"业务类型\" prop=\"businessCategory\">\r\n              <el-radio-group v-model=\"form.businessCategory\" @change=\"handleBusinessCategoryChange\">\r\n                <el-radio v-if=\"form.planType === 1\" :label=\"1\">通用</el-radio>\r\n                <el-radio v-if=\"form.planType === 2\" :label=\"11\">通用</el-radio>\r\n                <el-radio v-if=\"form.planType === 2\" :label=\"12\">委外加工</el-radio>\r\n                <el-radio v-if=\"form.planType === 3\" :label=\"21\">有计划量计量</el-radio>\r\n                <el-radio v-if=\"form.planType === 3\" :label=\"22\">短期</el-radio>\r\n                <el-radio v-if=\"form.planType === 3\" :label=\"23\">钢板（圆钢）</el-radio>\r\n                <el-radio v-if=\"form.planType === 4\" :label=\"31\">通用</el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"是否计量\" prop=\"measureFlag\">\r\n              <el-select v-model=\"form.measureFlag\" placeholder=\"请选择是否计量\" :disabled=\"isSpecialCondition\">\r\n                <el-option label=\"计量\" :value=\"1\"></el-option>\r\n                <el-option label=\"不计量\" :value=\"0\"></el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n\r\n          <el-col :span=\"12\" v-if=\"form.planType === 3 && form.businessCategory === 21\">\r\n            <el-form-item label=\"计划量（吨）\" prop=\"plannedAmount\">\r\n              <el-input-number v-model=\"form.plannedAmount\" :min=\"0\" controls-position=\"right\" placeholder=\"请输入计划量\"></el-input-number>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-row :gutter=\"20\" v-if=\"form.planType === 3 && form.businessCategory === 21\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"开始时间\" prop=\"startTime\">\r\n              <el-tooltip class=\"item\" effect=\"dark\" content=\"开始时间默认为该日期的0时0分0秒\" placement=\"top\">\r\n                <i class=\"el-icon-question\" style=\"margin-left: 2px;\"></i>\r\n              </el-tooltip>\r\n              <el-date-picker\r\n                v-model=\"form.startTime\"\r\n                type=\"date\"\r\n                placeholder=\"选择开始时间\"\r\n                value-format=\"yyyy-MM-dd\"\r\n                :picker-options=\"form.planType === 3 && form.businessCategory === 21 ? datePickerOptions : {}\">\r\n              </el-date-picker>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"结束时间\" prop=\"endTime\">\r\n              <el-tooltip class=\"item\" effect=\"dark\" content=\"结束时间默认为该日期的23时59分59秒\" placement=\"top\">\r\n                <i class=\"el-icon-question\" style=\"margin-left: 2px;\"></i>\r\n              </el-tooltip>\r\n              <el-date-picker\r\n                v-model=\"form.endTime\"\r\n                type=\"date\"\r\n                placeholder=\"选择结束时间\"\r\n                value-format=\"yyyy-MM-dd\"\r\n                :picker-options=\"form.planType === 3 && form.businessCategory === 21 ? datePickerOptions : {}\">\r\n              </el-date-picker>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-row :gutter=\"20\" v-if=\"!(form.planType === 3 && form.businessCategory === 21)\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"有效期\" prop=\"expireTime\">\r\n              <el-tooltip class=\"item\" effect=\"dark\" content=\"有效期默认为该日期的23时59分59秒\" placement=\"top\">\r\n                <i class=\"el-icon-question\" style=\"margin-left: 2px;\"></i>\r\n              </el-tooltip>\r\n              <el-date-picker\r\n                v-model=\"form.expireTime\"\r\n                type=\"date\"\r\n                placeholder=\"选择有效期\"\r\n                value-format=\"yyyy-MM-dd\">\r\n              </el-date-picker>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"申请单位\" prop=\"sourceCompanyCode\">\r\n              <el-select\r\n                v-model=\"form.sourceCompanyCode\"\r\n                placeholder=\"请选择申请单位\"\r\n                filterable\r\n                remote\r\n                reserve-keyword\r\n                :remote-method=\"remoteSearchDepartment\"\r\n                :loading=\"departmentLoading\">\r\n                <template slot=\"selected\">\r\n                  <span>{{ getDepartmentName(form.sourceCompanyCode) }}</span>\r\n                </template>\r\n                <el-option\r\n                  v-for=\"item in departmentOptions\"\r\n                  :key=\"item.id\"\r\n                  :label=\"item.storeName\"\r\n                  :value=\"item.id\">\r\n                </el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"收货单位\" prop=\"receiveCompanyCode\">\r\n              <el-select\r\n                v-model=\"form.receiveCompanyCode\"\r\n                placeholder=\"请选择收货单位\"\r\n                filterable\r\n                remote\r\n                reserve-keyword\r\n                :remote-method=\"form.planType === 3 ? remoteSearchDepartment : remoteSearchCustomer\"\r\n                :loading=\"form.planType === 3 ? departmentLoading : customerLoading\">\r\n                <template slot=\"selected\">\r\n                  <span>{{ form.planType === 3 ? getDepartmentName(form.receiveCompanyCode) : getCustomerName(form.receiveCompanyCode) }}</span>\r\n                </template>\r\n                <el-option\r\n                  v-for=\"item in form.planType === 3 ? departmentOptions : customerOptions\"\r\n                  :key=\"item.id\"\r\n                  :label=\"form.planType === 3 ? item.storeName : item.customerName\"\r\n                  :value=\"item.id\">\r\n                </el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\" v-if=\"form.planType === 2\">\r\n            <el-form-item label=\"返回单位\" prop=\"targetCompanyCode\">\r\n              <el-select\r\n                v-model=\"form.targetCompanyCode\"\r\n                placeholder=\"请选择返回单位\"\r\n                filterable\r\n                remote\r\n                reserve-keyword\r\n                :remote-method=\"remoteSearchDepartment\"\r\n                :loading=\"departmentLoading\">\r\n                <template slot=\"selected\">\r\n                  <span>{{ getDepartmentName(form.targetCompanyCode) }}</span>\r\n                </template>\r\n                <el-option\r\n                  v-for=\"item in departmentOptions\"\r\n                  :key=\"item.id\"\r\n                  :label=\"item.storeName\"\r\n                  :value=\"item.id\">\r\n                </el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n\r\n          <el-col :span=\"12\" v-if=\"form.planType === 4\">\r\n            <el-form-item label=\"退货单位\" prop=\"refundCompanyCode\">\r\n              <el-select\r\n                v-model=\"form.refundCompanyCode\"\r\n                placeholder=\"请选择退货单位\"\r\n                filterable\r\n                remote\r\n                reserve-keyword\r\n                :remote-method=\"remoteSearchDepartment\"\r\n                :loading=\"departmentLoading\">\r\n                <template slot=\"selected\">\r\n                  <span>{{ getDepartmentName(form.refundCompanyCode) }}</span>\r\n                </template>\r\n                <el-option\r\n                  v-for=\"item in departmentOptions\"\r\n                  :key=\"item.id\"\r\n                  :label=\"item.storeName\"\r\n                  :value=\"item.id\">\r\n                </el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\" v-if=\"form.planType === 2\">\r\n            <el-form-item label=\"计划返回时间\" prop=\"plannedReturnTime\">\r\n              <el-date-picker\r\n                v-model=\"form.plannedReturnTime\"\r\n                type=\"date\"\r\n                placeholder=\"选择计划返回时间\"\r\n                value-format=\"yyyy-MM-dd\"\r\n                :picker-options=\"{\r\n                  disabledDate(time) {\r\n                    return time.getTime() < Date.now() - 8.64e7;\r\n                  }\r\n                }\">\r\n              </el-date-picker>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"监装人\" prop=\"monitor\">\r\n              <el-input v-model=\"form.monitor\" placeholder=\"请输入监装人\"></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"物资专管员\" prop=\"specialManager\">\r\n              <el-input v-model=\"form.specialManager\" placeholder=\"请输入物资专管员\"></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"物资类型\" prop=\"itemType\">\r\n              <el-select v-model=\"form.itemType\" placeholder=\"请选择物资类型\">\r\n                <el-option label=\"钢材\" :value=\"1\"></el-option>\r\n                <el-option label=\"钢板\" :value=\"2\"></el-option>\r\n                <el-option label=\"其他\" :value=\"3\"></el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"是否复审\" prop=\"secApproveFlag\">\r\n              <el-select v-model=\"form.secApproveFlag\" placeholder=\"请选择是否复审\">\r\n                <el-option label=\"是\" :value=\"1\"></el-option>\r\n                <el-option label=\"否\" :value=\"0\"></el-option>\r\n              </el-select>\r\n              <div class=\"tip-text\">在写申请单时，一般是不需要复审的，但一些物资比较特殊或者贵重等原因，必须经过更高领导审核(签字)，因此请申请人注意选择</div>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"出厂原因\" prop=\"reason\">\r\n              <el-input v-model=\"form.reason\" placeholder=\"请输入出厂原因\"></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"合同号\" prop=\"contractNo\">\r\n              <el-input v-model=\"form.contractNo\" placeholder=\"请输入合同号\"></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <!-- 图片上传 -->\r\n        <el-form-item label=\"申请图片\">\r\n          <el-upload\r\n            class=\"upload-demo\"\r\n            :action=\"uploadUrl\"\r\n            list-type=\"picture-card\"\r\n            :file-list=\"imgFileList\"\r\n            :limit=\"3\"\r\n            :on-preview=\"handleImgPreview\"\r\n            :on-remove=\"handleImgRemove\"\r\n            :on-success=\"handleImgSuccess\"\r\n            :on-error=\"handleUploadError\"\r\n            :before-upload=\"beforeImgUpload\"\r\n            :on-exceed=\"handleExceed\"\r\n            :data=\"uploadParams\"\r\n            multiple>\r\n            <i class=\"el-icon-plus\"></i>\r\n            <div slot=\"tip\" class=\"el-upload__tip\">只能上传jpg/png文件，且不超过10MB</div>\r\n          </el-upload>\r\n          <el-dialog :visible.sync=\"imgPreviewVisible\">\r\n            <img width=\"100%\" :src=\"imgPreviewUrl\" alt=\"\">\r\n          </el-dialog>\r\n        </el-form-item>\r\n\r\n        <!-- 附件上传 -->\r\n        <el-form-item label=\"申请附件\">\r\n          <el-upload\r\n            class=\"upload-demo\"\r\n            :action=\"uploadUrl\"\r\n            :file-list=\"fileList\"\r\n            :limit=\"3\"\r\n            :on-preview=\"handleFilePreview\"\r\n            :on-remove=\"handleFileRemove\"\r\n            :on-success=\"handleAnnexFileSuccess\"\r\n            :on-error=\"handleUploadError\"\r\n            :before-upload=\"beforeFileUpload\"\r\n            :on-exceed=\"handleExceed\"\r\n            :data=\"uploadParams\">\r\n            <el-button size=\"small\" type=\"primary\">点击上传</el-button>\r\n            <div slot=\"tip\" class=\"el-upload__tip\">可上传任意类型文件，且不超过20MB</div>\r\n          </el-upload>\r\n        </el-form-item>\r\n\r\n        <!-- 物资列表 -->\r\n        <el-card class=\"material-card\" shadow=\"hover\">\r\n          <div slot=\"header\" class=\"clearfix\">\r\n            <span>物资列表</span>\r\n            <div class=\"material-btn-group\">\r\n              <el-button size=\"small\" type=\"primary\" plain icon=\"el-icon-download\" @click=\"handleExportMaterialTemplate\">导出物资模板</el-button>\r\n              <el-button size=\"small\" type=\"success\" plain icon=\"el-icon-upload2\" @click=\"handleImportMaterial\">导入物资</el-button>\r\n              <el-button size=\"small\" type=\"warning\" plain icon=\"el-icon-plus\" @click=\"handleAddMaterial\">添加物资</el-button>\r\n            </div>\r\n          </div>\r\n\r\n          <el-table\r\n            :data=\"form.materials\"\r\n            style=\"width: 100%\"\r\n            border>\r\n            <el-table-column\r\n              type=\"index\"\r\n              width=\"50\"\r\n              label=\"序号\">\r\n            </el-table-column>\r\n            \r\n            <el-table-column\r\n              prop=\"materialName\"\r\n              label=\"物资名称\"\r\n              width=\"180\">\r\n              <template slot-scope=\"scope\">\r\n                <el-select\r\n                  v-model=\"scope.row.materialId\"\r\n                  placeholder=\"请输入物资名称\"\r\n                  filterable\r\n                  remote\r\n                  reserve-keyword\r\n                  :remote-method=\"remoteSearchMaterial\"\r\n                  :loading=\"materialLoading\"\r\n                  @change=\"(value) => handleMaterialSelect(value, scope.row)\">\r\n                  <el-option\r\n                    v-for=\"item in materialOptions\"\r\n                    :key=\"item.id\"\r\n                    :label=\"item.materialName\"\r\n                    :value=\"item.id\">\r\n                  </el-option>\r\n                </el-select>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column\r\n              prop=\"materialSpec\"\r\n              label=\"物资型号规格\"\r\n              width=\"180\">\r\n              <template slot-scope=\"scope\">\r\n                <el-input v-model=\"scope.row.materialSpec\" placeholder=\"请输入物资型号规格\"></el-input>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column\r\n              prop=\"planNum\"\r\n              label=\"计划数量\"\r\n              width=\"180\">\r\n              <template slot-scope=\"scope\">\r\n                <el-input-number v-model=\"scope.row.planNum\" :min=\"0\" controls-position=\"right\" placeholder=\"请输入计划数量\"></el-input-number>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column\r\n              prop=\"measureUnit\"\r\n              label=\"计量单位\"\r\n              width=\"120\">\r\n              <template slot-scope=\"scope\">\r\n                <el-input v-model=\"scope.row.measureUnit\" placeholder=\"请输入计量单位\"></el-input>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column\r\n              prop=\"remark\"\r\n              label=\"备注\">\r\n              <template slot-scope=\"scope\">\r\n                <el-input v-model=\"scope.row.remark\" placeholder=\"请输入备注\"></el-input>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"操作\"\r\n              width=\"120\">\r\n              <template slot-scope=\"scope\">\r\n                <el-button\r\n                  size=\"mini\"\r\n                  type=\"danger\"\r\n                  icon=\"el-icon-delete\"\r\n                  @click=\"handleDeleteMaterial(scope.$index)\">删除</el-button>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </el-card>\r\n\r\n        <div class=\"form-footer\">\r\n          <el-button type=\"primary\" @click=\"submitForm\">{{ isEdit ? '修 改' : '确 定' }}</el-button>\r\n          <el-button @click=\"cancel\">取 消</el-button>\r\n        </div>\r\n      </el-form>\r\n    </el-card>\r\n    \r\n    <!-- 导入物资对话框 -->\r\n    <el-dialog :title=\"'导入物资'\" :visible.sync=\"importVisible\" width=\"400px\" append-to-body>\r\n      <el-upload\r\n        ref=\"upload\"\r\n        :limit=\"1\"\r\n        accept=\".xlsx, .xls\"\r\n        :headers=\"upload.headers\"\r\n        :action=\"upload.url\"\r\n        :disabled=\"upload.isUploading\"\r\n        :on-progress=\"handleFileUploadProgress\"\r\n        :on-success=\"handleMaterialFileSuccess\"\r\n        :auto-upload=\"false\"\r\n        drag>\r\n        <i class=\"el-icon-upload\"></i>\r\n        <div class=\"el-upload__text\">将物资Excel文件拖到此处，或<em>点击上传</em></div>\r\n        <div class=\"el-upload__tip\" slot=\"tip\">只能上传xlsx/xls文件，且不超过5MB</div>\r\n      </el-upload>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitFileForm\">确 定</el-button>\r\n        <el-button @click=\"importVisible = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listDepartment } from \"@/api/leave/department\";\r\nimport { listCustomer } from \"@/api/leave/customer\";\r\nimport { listMaterial } from \"@/api/leave/material\";\r\nimport { addPlan, updatePlan, detailPlan, exportMaterialTemplate, importMaterialList } from \"@/api/leave/plan\";\r\nimport { getToken } from \"@/utils/auth\";\r\n\r\nexport default {\r\n  name: \"EditLeavePlan\",\r\n  data() {\r\n    return {\r\n      // 导入参数\r\n      importVisible: false,\r\n      // 上传参数\r\n      upload: {\r\n        // 是否禁用上传\r\n        isUploading: false,\r\n        // 设置上传的请求头部\r\n        headers: { Authorization: \"Bearer \" + getToken() },\r\n        // 上传的地址\r\n        url: process.env.VUE_APP_BASE_API + \"/web/leave/plan/importMaterialList\"\r\n      },\r\n      // 是否为编辑模式\r\n      isEdit: false,\r\n      // 表单参数\r\n      form: {\r\n        planType: 1,\r\n        businessCategory: 1,\r\n        measureFlag: 1,\r\n        plannedAmount: 0,\r\n        sourceCompanyCode: \"\",\r\n        sourceCompany: \"\",\r\n        receiveCompanyCode: \"\",\r\n        receiveCompany: \"\",\r\n        targetCompanyCode: \"\",\r\n        targetCompany: \"\",\r\n        refundCompanyCode: \"\",\r\n        refundCompany: \"\",\r\n        contractNo: \"\",\r\n        plannedReturnTime: \"\",\r\n        startTime: \"\",\r\n        endTime: \"\",\r\n        expireTime: \"\", // 有效期\r\n        monitor: \"\",\r\n        specialManager: \"\",\r\n        itemType: 1,\r\n        reason: \"\",\r\n        secApproveFlag: 0,\r\n        applyImgUrl: \"\", // 申请图片URL\r\n        applyFileUrl: \"\", // 申请文件URL\r\n        materials: []\r\n      },\r\n      // 表单校验规则\r\n      rules: {\r\n        planType: [\r\n          { required: true, message: \"请选择计划类型\", trigger: \"change\" }\r\n        ],\r\n        businessCategory: [\r\n          { required: true, message: \"请选择业务类型\", trigger: \"change\" }\r\n        ],\r\n        measureFlag: [\r\n          { required: true, message: \"请选择是否计量\", trigger: \"change\" }\r\n        ],\r\n        applyCompany: [\r\n          { required: true, message: \"请选择申请单位\", trigger: \"change\" }\r\n        ],\r\n        receiveCompany: [\r\n          { required: true, message: \"请选择收货单位\", trigger: \"change\" }\r\n        ],\r\n        targetCompany: [\r\n          { required: true, message: \"请选择返回单位\", trigger: \"change\" }\r\n        ],\r\n        monitor: [\r\n          { required: true, message: \"请输入监装人\", trigger: \"blur\" }\r\n        ],\r\n        specialManager: [\r\n          { required: true, message: \"请输入物资专管员\", trigger: \"blur\" }\r\n        ],\r\n        materialType: [\r\n          { required: true, message: \"请选择物资类型\", trigger: \"change\" }\r\n        ],\r\n        leaveReason: [\r\n          { required: true, message: \"请输入出厂原因\", trigger: \"blur\" }\r\n        ],\r\n        plannedReturnTime: [\r\n          { required: true, message: \"请选择计划返回时间\", trigger: \"change\" }\r\n        ]\r\n      },\r\n      // 单位下拉选项\r\n      departmentOptions: [],\r\n      customerOptions: [],\r\n      departmentLoading: false,\r\n      customerLoading: false,\r\n      // 物资下拉选项\r\n      materialOptions: [],\r\n      materialLoading: false,\r\n      \r\n      // 上传相关\r\n      uploadUrl: process.env.VUE_APP_BASE_API + \"/common/uploadMinio\",\r\n      uploadParams: {\r\n        // 上传时可能需要的额外参数\r\n        uploadType: 'leavePlan'\r\n      },\r\n      imgFileList: [], // 图片文件列表\r\n      fileList: [], // 文件列表\r\n      imgPreviewVisible: false, // 图片预览对话框可见性\r\n      imgPreviewUrl: \"\", // 图片预览URL\r\n      \r\n      // 部门和客户选项关联映射\r\n      departmentMap: new Map(), // 部门id到名称的映射\r\n      customerMap: new Map(), // 客户id到名称的映射\r\n      // 添加日期限制对象\r\n      datePickerOptions: {\r\n        disabledDate(time) {\r\n          // 获取当前月份的25号日期\r\n          const currentDate = new Date();\r\n          const year = currentDate.getFullYear();\r\n          const month = currentDate.getMonth();\r\n          const monthLimit = new Date(year, month, 25, 23, 59, 59);\r\n          \r\n          // 禁用超过当月25号的日期\r\n          return time.getTime() > monthLimit.getTime();\r\n        }\r\n      },\r\n    };\r\n  },\r\n  computed: {\r\n    // 特殊条件：跨区调拨且有计划量计量时\r\n    isSpecialCondition() {\r\n      return this.form.planType === 3 && this.form.businessCategory === 21;\r\n    }\r\n  },\r\n  watch: {\r\n    // 监控特殊条件变化，自动设置是否计量为\"计量\"\r\n    isSpecialCondition(val) {\r\n      if (val) {\r\n        this.form.measureFlag = 1;\r\n      }\r\n    },\r\n    // 计划类型、业务类型、计划结束时间变化时，同步有效期\r\n    'form.planType': {\r\n    handler(val) {\r\n      this.syncExpireTime();\r\n    }\r\n  },\r\n  'form.businessCategory': {\r\n    handler(val) {\r\n      this.syncExpireTime();\r\n    }\r\n  },\r\n  'form.endTime': {\r\n    handler(val) {\r\n      this.syncExpireTime();\r\n    }\r\n  }\r\n  },\r\n  created() {\r\n    // 获取路由参数中的applyNo，判断是新增还是编辑\r\n    const applyNo = this.$route.params.applyNo;\r\n    if (applyNo) {\r\n      this.isEdit = true;\r\n      this.getDetail(applyNo);\r\n    } else {\r\n       // 新增时，设置有效期为当前日期+3天 23:59:59\r\n      const now = new Date();\r\n      now.setDate(now.getDate() + 3);\r\n      const yyyy = now.getFullYear();\r\n      const mm = String(now.getMonth() + 1).padStart(2, '0');\r\n      const dd = String(now.getDate()).padStart(2, '0');\r\n      this.form.expireTime = `${yyyy}-${mm}-${dd}`; \r\n      // 初始化一行物资数据\r\n      this.handleAddMaterial();\r\n      // 根据选中的计划类型设置默认业务类型\r\n      this.handlePlanTypeChange(this.form.planType);\r\n      // 初始化表单校验规则\r\n      this.updateFormRules();\r\n    }\r\n  },\r\n  methods: {\r\n    // 同步有效期\r\n    syncExpireTime() {\r\n    if (this.form.planType === 3 && this.form.businessCategory === 21) {\r\n      this.form.expireTime = this.form.endTime;\r\n      }else if (!this.form.expireTime) {\r\n      // 只在有效期为空时设置默认值（3天）\r\n      const now = new Date();\r\n      now.setDate(now.getDate() + 3);\r\n      const yyyy = now.getFullYear();\r\n      const mm = String(now.getMonth() + 1).padStart(2, '0');\r\n      const dd = String(now.getDate()).padStart(2, '0');\r\n      this.form.expireTime = `${yyyy}-${mm}-${dd}`;\r\n    }\r\n    },    \r\n    // 获取申请详情\r\n    getDetail(applyNo) {\r\n      detailPlan(applyNo).then(response => {\r\n        if (response.code === 200) {\r\n          const detail = response.data;\r\n          \r\n          // 填充基本信息\r\n          this.form.id = detail.id;\r\n          this.form.applyNo = detail.applyNo;\r\n          this.form.planType = detail.planType;\r\n          this.form.businessCategory = detail.businessCategory;\r\n          this.form.measureFlag = detail.measureFlag;\r\n          this.form.plannedAmount = detail.plannedAmount || 0;\r\n          \r\n          // 保存单位代码和名称\r\n          this.form.sourceCompanyCode = detail.sourceCompanyCode;\r\n          this.form.sourceCompany = detail.sourceCompany;\r\n          this.form.receiveCompanyCode = detail.receiveCompanyCode;\r\n          this.form.receiveCompany = detail.receiveCompany;\r\n          this.form.targetCompanyCode = detail.targetCompanyCode;\r\n          this.form.targetCompany = detail.targetCompany;\r\n          this.form.refundCompanyCode = detail.refundDepartmentCode;\r\n          this.form.refundCompany = detail.refundDepartment;\r\n          \r\n          // 手动添加已存在的部门和客户到选项数组，以便正确显示选中的值\r\n          // 添加申请单位到部门选项\r\n          if (detail.sourceCompanyCode && detail.sourceCompany) {\r\n            this.addToDepartmentOptions(detail.sourceCompanyCode, detail.sourceCompany);\r\n          }\r\n          \r\n          // 添加收货单位（可能是部门或客户）\r\n          if (detail.receiveCompanyCode && detail.receiveCompany) {\r\n            if (detail.planType === 3) {\r\n              // 跨区调拨时，收货单位是部门\r\n              this.addToDepartmentOptions(detail.receiveCompanyCode, detail.receiveCompany);\r\n            } else {\r\n              // 其他情况，收货单位是客户\r\n              this.addToCustomerOptions(detail.receiveCompanyCode, detail.receiveCompany);\r\n            }\r\n          }\r\n          \r\n          // 添加返回单位到部门选项\r\n          if (detail.targetCompanyCode && detail.targetCompany) {\r\n            this.addToDepartmentOptions(detail.targetCompanyCode, detail.targetCompany);\r\n          }\r\n          \r\n          // 添加退货单位到部门选项\r\n          if (detail.refundDepartmentCode && detail.refundDepartment) {\r\n            this.addToDepartmentOptions(detail.refundDepartmentCode, detail.refundDepartment);\r\n          }\r\n          \r\n          this.form.plannedReturnTime = detail.planReturnTime;\r\n          this.form.startTime = detail.startTime;\r\n          this.form.endTime = detail.endTime;\r\n          this.form.monitor = detail.monitor;\r\n          this.form.specialManager = detail.specialManager;\r\n          this.form.itemType = detail.itemType;\r\n          this.form.reason = detail.reason;\r\n          this.form.contractNo = detail.contractNo;\r\n          this.form.secApproveFlag = detail.secApproveFlag;\r\n          this.form.applyImgUrl = detail.applyImgUrl;\r\n          this.form.applyFileUrl = detail.applyFileUrl;\r\n          \r\n          // 处理图片和文件列表\r\n          this.initFileList();\r\n          \r\n          // 填充物资列表\r\n          if (detail.materials && detail.materials.length > 0) {\r\n            this.form.materials = detail.materials.map(item => ({\r\n              id: item.id,\r\n              materialId: item.materialId,\r\n              materialName: item.materialName,\r\n              materialSpec: item.materialSpec,\r\n              planNum: item.planNum,\r\n              measureUnit: item.measureUnit,\r\n              remark: item.remark\r\n            }));\r\n            \r\n            // 添加物资到选项数组\r\n            detail.materials.forEach(item => {\r\n              if (item.materialId && item.materialName) {\r\n                this.addToMaterialOptions(item.materialId, item.materialName, item.materialSpec, item.measureUnit);\r\n              }\r\n            });\r\n          } else {\r\n            this.handleAddMaterial();\r\n          }\r\n          \r\n          // 更新表单校验规则\r\n          this.updateFormRules();\r\n        }\r\n      });\r\n    },\r\n    // 远程搜索部门\r\n    remoteSearchDepartment(query) {\r\n      if (query !== '') {\r\n        this.departmentLoading = true;\r\n        listDepartment({\r\n          storeName: query,\r\n          pageNum: 1,\r\n          pageSize: 20\r\n        }).then(response => {\r\n          this.departmentLoading = false;\r\n          if (response.code === 200) {\r\n            this.departmentOptions = response.rows;\r\n          }\r\n        }).finally(() => {\r\n          this.departmentLoading = false;\r\n        });\r\n      } else {\r\n        this.departmentOptions = [];\r\n      }\r\n    },\r\n\r\n    // 远程搜索客户\r\n    remoteSearchCustomer(query) {\r\n      if (query !== '') {\r\n        this.customerLoading = true;\r\n        listCustomer({\r\n          customerName: query,\r\n          pageNum: 1,\r\n          pageSize: 20\r\n        }).then(response => {\r\n          this.customerLoading = false;\r\n          if (response.code === 200) {\r\n            this.customerOptions = response.rows;\r\n          }\r\n        }).finally(() => {\r\n          this.customerLoading = false;\r\n        });\r\n      } else {\r\n        this.customerOptions = [];\r\n      }\r\n    },\r\n\r\n    // 业务类型变更时触发\r\n    handleBusinessCategoryChange() {\r\n      // 更新表单校验规则\r\n      this.updateFormRules();\r\n    },\r\n\r\n    // 计划类型变更时触发\r\n    handlePlanTypeChange(val) {\r\n      // 根据计划类型设置默认业务类型\r\n      switch (val) {\r\n        case 1:\r\n          this.form.businessCategory = 1;\r\n          break;\r\n        case 2:\r\n          this.form.businessCategory = 11;\r\n          break;\r\n        case 3:\r\n          this.form.businessCategory = 21;\r\n          break;\r\n        case 4:\r\n          this.form.businessCategory = 31;\r\n          break;\r\n        default:\r\n          break;\r\n      }\r\n\r\n      // 重置不需要的字段\r\n      if (val !== 2) {\r\n        this.form.targetCompany = \"\";\r\n        this.form.plannedReturnTime = \"\";\r\n      }\r\n\r\n      if (val !== 4) {\r\n        this.form.refundCompany = \"\";\r\n      }\r\n\r\n      if (val !== 3 || this.form.businessCategory !== 21) {\r\n        this.form.startTime = \"\";\r\n        this.form.endTime = \"\";\r\n      }\r\n\r\n      // 更新表单校验规则\r\n      this.updateFormRules();\r\n    },\r\n\r\n    // 更新表单校验规则\r\n    updateFormRules() {\r\n      const tempRules = {\r\n        planType: [\r\n          { required: true, message: \"请选择计划类型\", trigger: \"change\" }\r\n        ],\r\n        businessCategory: [\r\n          { required: true, message: \"请选择业务类型\", trigger: \"change\" }\r\n        ],\r\n        measureFlag: [\r\n          { required: true, message: \"请选择是否计量\", trigger: \"change\" }\r\n        ],\r\n        applyCompany: [\r\n          { required: true, message: \"请选择申请单位\", trigger: \"change\" }\r\n        ],\r\n        monitor: [\r\n          { required: true, message: \"请输入监装人\", trigger: \"blur\" }\r\n        ],\r\n        specialManager: [\r\n          { required: true, message: \"请输入物资专管员\", trigger: \"blur\" }\r\n        ],\r\n        materialType: [\r\n          { required: true, message: \"请选择物资类型\", trigger: \"change\" }\r\n        ]\r\n      };\r\n\r\n      // 根据条件添加校验规则\r\n      if (this.form.planType !== 1) {\r\n        tempRules.receiveCompany = [\r\n          { required: true, message: \"请选择收货单位\", trigger: \"change\" }\r\n        ];\r\n      }\r\n\r\n      if (this.form.planType === 2) {\r\n        tempRules.targetCompany = [\r\n          { required: true, message: \"请选择返回单位\", trigger: \"change\" }\r\n        ];\r\n        tempRules.plannedReturnTime = [\r\n          { required: true, message: \"请选择计划返回时间\", trigger: \"change\" }\r\n        ];\r\n      }\r\n\r\n      if (this.form.planType === 4) {\r\n        tempRules.refundCompany = [\r\n          { required: true, message: \"请选择退货单位\", trigger: \"change\" }\r\n        ];\r\n      }\r\n\r\n      if (this.form.planType === 3 && this.form.businessCategory === 21) {\r\n        tempRules.plannedAmount = [\r\n          { required: true, message: \"请输入计划量\", trigger: \"blur\" }\r\n        ];\r\n        tempRules.startTime = [\r\n          { required: true, message: \"请选择开始时间\", trigger: \"change\" }\r\n        ];\r\n        tempRules.endTime = [\r\n          { required: true, message: \"请选择结束时间\", trigger: \"change\" }\r\n        ];\r\n      }\r\n\r\n      // 更新规则\r\n      this.rules = tempRules;\r\n    },\r\n\r\n    // 添加物资\r\n    handleAddMaterial() {\r\n      this.form.materials.push({\r\n        materialId: \"\",\r\n        materialName: \"\",\r\n        materialSpec: \"\",\r\n        planNum: 1,\r\n        measureUnit: \"\",\r\n        remark: \"\"\r\n      });\r\n    },\r\n    // 删除物资\r\n    handleDeleteMaterial(index) {\r\n      this.form.materials.splice(index, 1);\r\n      // 如果删完了，至少保留一行\r\n      if (this.form.materials.length === 0) {\r\n        this.handleAddMaterial();\r\n      }\r\n    },\r\n    // 远程搜索物资\r\n    remoteSearchMaterial(query) {\r\n      if (query !== '') {\r\n        this.materialLoading = true;\r\n        listMaterial({\r\n          materialName: query,\r\n          pageNum: 1,\r\n          pageSize: 20\r\n        }).then(response => {\r\n          this.materialLoading = false;\r\n          if (response.code === 200) {\r\n            this.materialOptions = response.rows;\r\n          }\r\n        }).finally(() => {\r\n          this.materialLoading = false;\r\n        });\r\n      } else {\r\n        this.materialOptions = [];\r\n      }\r\n    },\r\n    // 处理物资选择\r\n    handleMaterialSelect(value, row) {\r\n      // 根据选中的物资ID找到对应的物资详情\r\n      const selectedMaterial = this.materialOptions.find(item => item.id === value);\r\n      if (selectedMaterial) {\r\n        row.materialName = selectedMaterial.materialName;\r\n        // 如果物资信息中有型号规格和单位，可以自动填充\r\n        if (selectedMaterial.materialSpec) {\r\n          row.materialSpec = selectedMaterial.materialSpec;\r\n        }\r\n        if (selectedMaterial.measureUnit) {\r\n          row.measureUnit = selectedMaterial.measureUnit;\r\n        }\r\n      }\r\n    },\r\n    // 初始化文件列表\r\n    initFileList() {\r\n      // 处理图片\r\n      if (this.form.applyImgUrl) {\r\n        try {\r\n          // 尝试解析JSON\r\n          const imgFiles = JSON.parse(this.form.applyImgUrl);\r\n          this.imgFileList = imgFiles.map(item => {\r\n            return {\r\n              name: item.name,\r\n              url: item.url\r\n            };\r\n          });\r\n        } catch (e) {\r\n          console.log(e);\r\n        }\r\n      }\r\n      \r\n      // 处理文件\r\n      if (this.form.applyFileUrl) {\r\n        try {\r\n          // 尝试解析JSON\r\n          const files = JSON.parse(this.form.applyFileUrl);\r\n          this.fileList = files.map(item => {\r\n            return {\r\n              name: item.name,\r\n              url: item.url,\r\n            };\r\n          });\r\n        } catch (e) {\r\n          console.log(e);\r\n        }\r\n      }\r\n    },\r\n    \r\n    // 图片上传成功\r\n    handleImgSuccess(response, file, fileList) {\r\n      console.log(response);\r\n      console.log(file);\r\n      console.log(fileList);\r\n      if (response.code === 200) {\r\n        // 更新图片URL到表单\r\n        // this.imgFileList = fileList;\r\n        \r\n        // 构建JSON格式的图片数据\r\n        /**\r\n         * response格式：\r\n         * {\r\n    \"msg\": \"操作成功\",\r\n    \"originalFileName\": \"006r3PQBjw1f8p3zb1wioj30c80c8jru.jpg\",\r\n    \"fileName\": \"xctg/2025/03/25/da2a4a3f-6962-4831-a4e5-6ecc2397e483.jpg\",\r\n    \"code\": 200,\r\n    \"url\": \"https://ydxt.citicsteel.com:8099/minio/xctg/2025/03/25/da2a4a3f-6962-4831-a4e5-6ecc2397e483.jpg\"\r\n}\r\n         */\r\n\r\n        \r\n        // 只获取originalFileName、url 转换为JSON字符串保存\r\n        const imgData = {\r\n          name: response.originalFileName,\r\n          url: response.url\r\n        };\r\n        this.imgFileList.push(imgData);\r\n        console.log(this.imgFileList);\r\n        this.form.applyImgUrl = JSON.stringify(this.imgFileList);\r\n        console.log(this.form.applyImgUrl);\r\n      } else {\r\n        this.$message.error('图片上传失败');\r\n      }\r\n    },\r\n    \r\n    // 附件上传成功\r\n    handleAnnexFileSuccess(response, file, fileList) {\r\n      console.log(response);\r\n      console.log(file);\r\n      console.log(fileList);\r\n      if (response.code === 200) {\r\n        // 只获取originalFileName、url 转换为JSON字符串保存\r\n        const annexFileData = {\r\n          name: response.originalFileName,\r\n          url: response.url\r\n        };\r\n        this.fileList.push(annexFileData);\r\n        console.log(this.fileList);\r\n        this.form.applyFileUrl = JSON.stringify(this.fileList);\r\n        console.log(this.form.applyFileUrl);\r\n      } else {\r\n        this.$message.error('附件上传失败');\r\n      }\r\n    },\r\n\r\n    // 物资导入文件上传成功处理\r\n    handleMaterialFileSuccess(response, file, fileList) {\r\n      this.upload.isUploading = false;\r\n      this.$refs.upload.clearFiles();\r\n      this.importVisible = false;\r\n      \r\n      if (response.code === 200) {\r\n        this.$message.success(\"导入成功\");\r\n        // 将导入的物资列表添加到当前表单的物资列表中\r\n        if (response.data && response.data.length > 0) {\r\n          // 追加新导入的物资\r\n          const importedMaterials = response.data.map(item => {\r\n            // 如果没有materialId，使用-1作为默认ID，并确保该物资能显示\r\n            const materialId = item.materialId || -1;\r\n            // 物资名称必须有值\r\n            const materialName = item.materialName || \"未知物资\";\r\n            \r\n            // 如果是默认ID的物资，则添加到选项中\r\n            if (materialId === -1) {\r\n              this.addToMaterialOptions(\r\n                materialId,\r\n                materialName,\r\n                item.materialSpec || \"\",\r\n                item.measureUnit || \"\"\r\n              );\r\n            }\r\n            \r\n            return {\r\n              materialId: materialId,\r\n              materialName: materialName,\r\n              materialSpec: item.materialSpec || \"\",\r\n              planNum: item.planNum || 1,\r\n              measureUnit: item.measureUnit || \"\",\r\n              remark: item.remark || ''\r\n            };\r\n          });\r\n          \r\n          // 将导入的物资添加到表单中\r\n          this.form.materials = this.form.materials.concat(importedMaterials);\r\n          \r\n          // 使用nextTick确保视图更新\r\n          this.$nextTick(() => {\r\n            // 触发一次刷新，确保下拉框正确显示\r\n            this.materialOptions = [...this.materialOptions];\r\n          });\r\n        }\r\n      } else {\r\n        this.$message.error(response.msg || \"导入失败\");\r\n      }\r\n    },\r\n    \r\n    // 根据物资名称查询materialId\r\n    queryMaterialIdByName(rowIndex) {\r\n      // 此方法已不再使用，保留空方法以避免可能的调用错误\r\n    },\r\n    \r\n    // 图片预览\r\n    handleImgPreview(file) {\r\n      this.imgPreviewUrl = file.url || (file.response && file.response.data);\r\n      this.imgPreviewVisible = true;\r\n    },\r\n    \r\n    // 文件预览\r\n    handleFilePreview(file) {\r\n      window.open(file.url || (file.response && file.response.data));\r\n    },\r\n    \r\n    // 移除图片\r\n    handleImgRemove(file, fileList) {\r\n      //移除fileList中url与file.url相同的文件\r\n      this.imgFileList = this.imgFileList.filter(item => item.url !== file.url);\r\n      \r\n      // 转换为JSON字符串保存\r\n      this.form.applyImgUrl = JSON.stringify(imgFileList);\r\n    },\r\n    \r\n    // 移除文件\r\n    handleFileRemove(file, fileList) {\r\n      //移除fileList中url与file.url相同的文件\r\n      this.fileList = this.fileList.filter(item => item.url !== file.url);\r\n      \r\n      // 转换为JSON字符串保存\r\n      this.form.applyFileUrl = JSON.stringify(fileList);\r\n    },\r\n    \r\n    // 超出文件数量限制\r\n    handleExceed() {\r\n      this.$message.warning('最多只能上传3个文件');\r\n    },\r\n\r\n    // 表单提交\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          // 验证物资列表\r\n          if (this.form.materials.length === 0) {\r\n            this.$message.error(\"请至少添加一项物资\");\r\n            return;\r\n          }\r\n\r\n          // 验证物资列表的必填项\r\n          let valid = true;\r\n          this.form.materials.forEach((item, index) => {\r\n            if (!item.materialId || !item.materialName || !item.materialSpec || !item.planNum || !item.measureUnit) {\r\n              this.$message.error(`第${index + 1}行物资信息不完整，请填写完整`);\r\n              valid = false;\r\n            }\r\n          });\r\n\r\n          if (!valid) {\r\n            return;\r\n          }\r\n\r\n          // 验证计划量大于0\r\n          if (this.form.planType === 3 && this.form.businessCategory === 21 && (!this.form.plannedAmount || this.form.plannedAmount <= 0)) {\r\n            this.$message.error(\"计划量必须大于0\");\r\n            return;\r\n          }\r\n          //对有效期单独处理\r\n          if (this.form.planType === 3 && this.form.businessCategory === 21) {\r\n            if (this.form.endTime) {\r\n              this.form.expireTime = this.form.endTime + \" 23:59:59\";\r\n            }\r\n          } else if (this.form.expireTime) {\r\n            this.form.expireTime = this.form.expireTime + \" 23:59:59\";\r\n          }\r\n          //对开始结束时间单独处理\r\n          if (this.form.startTime && this.form.endTime) {\r\n            this.form.startTime = this.form.startTime + \" 00:00:00\";\r\n            this.form.endTime = this.form.endTime + \" 23:59:59\";\r\n          }\r\n\r\n          // 根据是否编辑模式调用不同的API\r\n          const apiMethod = this.isEdit ? updatePlan : addPlan;\r\n          const successMsg = this.isEdit ? \"修改成功\" : \"申请提交成功\";\r\n          console.log(this.form);\r\n          // 调用API提交数据\r\n          apiMethod(this.form).then(response => {\r\n            if (response.code === 200) {\r\n              this.$message.success(successMsg);\r\n              this.$tab.closeOpenPage(this.$route);\r\n              // 跳转到列表页面并刷新\r\n              this.$router.push({ \r\n                path: \"/leave/leavePlanList\", \r\n                query: { \r\n                  t: Date.now(),\r\n                  refresh: true // 添加刷新标记\r\n                }\r\n              });\r\n            } else {\r\n              this.$message.error(response.msg || \"提交失败\");\r\n            }\r\n          }).catch(error => {\r\n            console.error(\"提交失败\", error);\r\n            this.$message.error(\"提交过程中发生错误，请稍后再试\");\r\n          });\r\n        }\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.$router.push(\"/leave/plan\");\r\n    },\r\n    // 图片上传前的验证\r\n    beforeImgUpload(file) {\r\n      const isImage = file.type.indexOf('image/') === 0;\r\n      const isLt10M = file.size / 1024 / 1024 < 10;\r\n\r\n      if (!isImage) {\r\n        this.$message.error('只能上传图片格式文件!');\r\n        return false;\r\n      }\r\n      if (!isLt10M) {\r\n        this.$message.error('图片大小不能超过 10MB!');\r\n        return false;\r\n      }\r\n      return true;\r\n    },\r\n\r\n    // 文件上传前的验证\r\n    beforeFileUpload(file) {\r\n      const isLt20M = file.size / 1024 / 1024 < 20;\r\n      if (!isLt20M) {\r\n        this.$message.error('文件大小不能超过 20MB!');\r\n        return false;\r\n      }\r\n      return true;\r\n    },\r\n    \r\n    // 上传错误处理\r\n    handleUploadError(err, file, fileList) {\r\n      console.error(\"上传失败:\", err);\r\n      \r\n      if (err.status === 403) {\r\n        this.$message.error('上传失败：没有权限');\r\n      } else {\r\n        this.$message.error('上传失败：' + (err.message || '未知错误'));\r\n      }\r\n    },\r\n    // 获取部门名称\r\n    getDepartmentName(id) {\r\n      if (!id) return '';\r\n      \r\n      // 查询选项中是否有匹配的\r\n      const dept = this.departmentOptions.find(item => item.id == id);\r\n      if (dept) {\r\n        return dept.storeName;\r\n      }\r\n      \r\n      // 根据不同的字段ID返回对应的中文名称\r\n      if (id === this.form.sourceCompanyCode && this.form.sourceCompany) {\r\n        return this.form.sourceCompany;\r\n      }\r\n      \r\n      if (id === this.form.receiveCompanyCode && this.form.receiveCompany && this.form.planType === 3) {\r\n        return this.form.receiveCompany;\r\n      }\r\n      \r\n      if (id === this.form.targetCompanyCode && this.form.targetCompany) {\r\n        return this.form.targetCompany;\r\n      }\r\n      \r\n      if (id === this.form.refundCompanyCode && this.form.refundCompany) {\r\n        return this.form.refundCompany;\r\n      }\r\n      \r\n      // 否则显示ID\r\n      return `ID: ${id}`;\r\n    },\r\n    \r\n    // 获取客户名称\r\n    getCustomerName(id) {\r\n      if (!id) return '';\r\n      \r\n      // 查询选项中是否有匹配的\r\n      const customer = this.customerOptions.find(item => item.id == id);\r\n      if (customer) {\r\n        return customer.customerName;\r\n      }\r\n      \r\n      // 如果是收货单位且不是跨区调拨，则使用已有的中文名称\r\n      if (id === this.form.receiveCompanyCode && this.form.receiveCompany && this.form.planType !== 3) {\r\n        return this.form.receiveCompany;\r\n      }\r\n      \r\n      // 否则显示ID\r\n      return `ID: ${id}`;\r\n    },\r\n    // 添加部门到选项数组方法\r\n    addToDepartmentOptions(id, name) {\r\n      // 检查是否已存在相同ID的选项\r\n      if (!this.departmentOptions.some(item => item.id == id)) {\r\n        this.departmentOptions.push({\r\n          id: id,\r\n          storeName: name\r\n        });\r\n      }\r\n    },\r\n    \r\n    // 添加客户到选项数组方法\r\n    addToCustomerOptions(id, name) {\r\n      // 检查是否已存在相同ID的选项\r\n      if (!this.customerOptions.some(item => item.id == id)) {\r\n        this.customerOptions.push({\r\n          id: id,\r\n          customerName: name\r\n        });\r\n      }\r\n    },\r\n    \r\n    // 添加物资到选项数组方法\r\n    addToMaterialOptions(id, name, spec, unit) {\r\n      // 检查是否已存在相同ID的选项\r\n      if (!this.materialOptions.some(item => item.id == id)) {\r\n        this.materialOptions.push({\r\n          id: id,\r\n          materialName: name,\r\n          materialSpec: spec,\r\n          measureUnit: unit\r\n        });\r\n      }\r\n    },\r\n    // 导出物资模板\r\n    handleExportMaterialTemplate() {\r\n      exportMaterialTemplate().then(response => {\r\n        this.download(response.msg);\r\n      });\r\n    },\r\n    \r\n    // 显示导入物资对话框\r\n    handleImportMaterial() {\r\n      this.importVisible = true;\r\n    },\r\n    \r\n    // 文件上传中处理\r\n    handleFileUploadProgress() {\r\n      this.upload.isUploading = true;\r\n    },\r\n    \r\n    // 提交上传文件\r\n    submitFileForm() {\r\n      this.$refs.upload.submit();\r\n    },\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.app-container {\r\n  padding: 20px;\r\n}\r\n.box-card {\r\n  margin-bottom: 20px;\r\n  border-radius: 5px;\r\n}\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n.material-card {\r\n  margin-top: 20px;\r\n  margin-bottom: 20px;\r\n}\r\n.form-footer {\r\n  margin-top: 30px;\r\n  text-align: center;\r\n}\r\n.material-btn-group {\r\n  float: right;\r\n}\r\n.material-btn-group .el-button {\r\n  margin-left: 8px;\r\n  border-radius: 4px;\r\n}\r\n.material-btn-group .el-button:first-child {\r\n  margin-left: 0;\r\n}\r\n.tip-text {\r\n  color: #f56c6c;\r\n  font-size: 12px;\r\n  line-height: 1.2;\r\n  margin-top: 5px;\r\n}\r\n</style>\r\n"]}]}